<!--
 * FilePath     : \src\components\selector\noonSelector.vue
 * Author       : 马超
 * Date         : 2023-09-25 09:52
 * LastEditors  : 来江禹
 * LastEditTime : 2024-08-15 15:12
 * Description  : 午别选择器
 * CodeIterationRecord:
-->
<template>
  <div class="noon-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="noonIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="item in noonOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "午别"
  },
  modelValue: {
    type: String
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 140
  },
  showBlankOptions: {
    type: Boolean,
    default: false
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let noonIDs = useVModel(props, "modelValue", emits);

let noonOptions = ref<Array<Record<any, any>>>([]);
const defaultType = {
  label: "全天",
  value: ""
};
const params: SettingDictionaryParams = {
  settingType: "PositionManagement",
  settingTypeCode: "JobPositions",
  settingTypeValue: "NoonType",
  index: Math.random()
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  noonOptions.value = datas;
  if (props.showBlankOptions) {
    noonOptions.value.unshift(defaultType);
  }
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, noonOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.noon-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
