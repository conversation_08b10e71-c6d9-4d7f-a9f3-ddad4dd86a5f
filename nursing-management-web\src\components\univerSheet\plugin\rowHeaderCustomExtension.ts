/*
 * FilePath     : \src\components\univerSheet\plugin\rowHeaderCustomExtension.ts
 * Author       : 苏军志
 * Date         : 2024-09-29 18:13
 * LastEditors  : 苏军志
 * LastEditTime : 2024-11-13 17:07
 * Description  : 行号渲染
 * CodeIterationRecord:
 */
import type { IScale } from "@univerjs/core";
import type { SpreadsheetSkeleton, UniverRenderingContext } from "@univerjs/engine-render";
import {
  DEFAULT_FONTFACE_PLANE,
  FIX_ONE_PIXEL_BLUR_OFFSET,
  getColor,
  MIDDLE_CELL_POS_MAGIC_NUMBER,
  SheetExtension
} from "@univerjs/engine-render";

const UNIQUE_KEY = "RowHeaderCustomExtension";
export class rowHeaderCustomExtension extends SheetExtension {
  private startIndex: number = 0;
  private endIndex: number = 0;
  private headerFontColor: number[] = [];
  private headerBackgroundColor: number[] = [];
  constructor(startIndex?: number, endIndex?: number, headerFontColor?: number[], headerBackgroundColor?: number[]) {
    super();
    this.startIndex = startIndex || 0;
    this.endIndex = endIndex || 0;
    this.headerFontColor = headerFontColor || [0, 0, 0];
    this.headerBackgroundColor = headerBackgroundColor || [255, 255, 255];
  }
  override uKey = UNIQUE_KEY;
  // zIndex必须大于10
  override get zIndex() {
    return 11;
  }
  override draw(ctx: UniverRenderingContext, parentScale: IScale, spreadsheetSkeleton: SpreadsheetSkeleton) {
    const { rowColumnSegment, rowHeaderWidth = 0 } = spreadsheetSkeleton;
    const { startRow, endRow } = rowColumnSegment;
    if (!spreadsheetSkeleton) {
      return;
    }
    const { rowHeightAccumulation, columnTotalWidth, columnWidthAccumulation, rowTotalHeight } = spreadsheetSkeleton;
    if (!rowHeightAccumulation || !columnWidthAccumulation || columnTotalWidth === undefined || rowTotalHeight === undefined) {
      return;
    }
    ctx.getContextAttributes();
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.beginPath();
    ctx.lineWidth = 1;
    ctx.translateWithPrecisionRatio(FIX_ONE_PIXEL_BLUR_OFFSET, FIX_ONE_PIXEL_BLUR_OFFSET);
    ctx.font = `14px ${DEFAULT_FONTFACE_PLANE}`;
    let preRowPosition = 0;
    const rowHeightAccumulationLength = rowHeightAccumulation.length;
    ctx.fillStyle = getColor([248, 249, 250]);
    ctx.clearRect(0, 0, rowHeaderWidth, rowHeightAccumulation[endRow]);
    ctx.fillRect(0, 0, rowHeaderWidth - MIDDLE_CELL_POS_MAGIC_NUMBER, rowHeightAccumulation[endRow]);
    let headerHeight = 0;
    for (let r = startRow - 1; r <= endRow; r++) {
      if (r < 0 || r > rowHeightAccumulationLength - 1) {
        continue;
      }
      const rowEndPosition = rowHeightAccumulation[r];

      if (preRowPosition === rowEndPosition) {
        // 跳过隐藏行
        continue;
      }
      const middleCellPos = preRowPosition + (rowEndPosition - preRowPosition) / 2;
      if (this.endIndex !== 0 && r <= this.endIndex) {
        ctx.fillStyle = getColor([217, 217, 217]);
        ctx.fillRect(0, rowEndPosition - MIDDLE_CELL_POS_MAGIC_NUMBER, rowHeaderWidth, 1);
      }
      ctx.fillStyle = getColor([0, 0, 0]);
      if (r >= this.startIndex && this.endIndex !== 0 && r <= this.endIndex) {
        ctx.fillText(
          (this.startIndex === 0 ? r + 1 : r - this.startIndex + 1).toString(),
          rowHeaderWidth / 2,
          middleCellPos + MIDDLE_CELL_POS_MAGIC_NUMBER
        );
      }
      if (this.startIndex > 0 && r < this.startIndex) {
        headerHeight += rowEndPosition - preRowPosition;
      }
      preRowPosition = rowEndPosition;
    }
    if (headerHeight > 0) {
      ctx.fillStyle = getColor(this.headerBackgroundColor);
      ctx.fillRect(0, 0, rowHeaderWidth - MIDDLE_CELL_POS_MAGIC_NUMBER, headerHeight - MIDDLE_CELL_POS_MAGIC_NUMBER);
      ctx.fillStyle = getColor(this.headerFontColor);
      ctx.fillText("序号", rowHeaderWidth / 2, headerHeight / 2 + MIDDLE_CELL_POS_MAGIC_NUMBER);
      ctx.fillStyle = getColor([192, 192, 192]);
      ctx.fillRect(0, -1, rowHeaderWidth, 1);
      ctx.fillRect(0, headerHeight - MIDDLE_CELL_POS_MAGIC_NUMBER, rowHeaderWidth, 1);
    }
    ctx.stroke();
  }
}
