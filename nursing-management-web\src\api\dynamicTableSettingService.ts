/*
 * FilePath     : \src\api\dynamicTableSettingService.ts
 * Author       : 张现忠
 * Date         : 2024-05-15 14:42
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-05-28 20:07
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";
const baseUrl: string = "/DynamicTableSetting";

export class dynamicTableSettingService {
  private static getDynamicTableListApi: string = baseUrl + "/GetDynamicTableList";
  private static recoverDynamicTableSettingApi: string = baseUrl + "/RecoverDynamicTableSetting";
  private static saveDynamicTableSettingListApi: string = baseUrl + "/SaveDynamicTableSettingList";
  private static getDynamicTableHeaderApi: string = baseUrl + "/GetDynamicTableHeader";

  /**
   * @description: 获取表格动态列配置
   * @param params
   * @return
   */
  public static GetDynamicTableList(params: any) {
    return http.get(this.getDynamicTableListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 恢复默认设置
   * @param params
   * @return
   */
  public static RecoverDynamicTableSetting(params: any) {
    return http.post(this.recoverDynamicTableSettingApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存方法
   * @param params
   * @return
   */
  public static SaveDynamicTableSettingList(params: any) {
    return http.post(this.saveDynamicTableSettingListApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取动态表头数据
   * @param params
   * @return
   */
  public static GetDynamicTableHeader(params: any) {
    return http.get(this.getDynamicTableHeaderApi, params, { loadingText: Loading.LOAD });
  }
}
