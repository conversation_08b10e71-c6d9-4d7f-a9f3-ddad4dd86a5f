/*
 * FilePath     : \src\views\trainingManagement\hooks\useTrainees.ts
 * Author       : 张现忠
 * Date         : 2024-09-20 11:19
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-12-19 10:34
 * Description  : 人员培训记录页面hooks,主要与数据库交互数据以及页面中的table呈现样式、drawer的打开方式等的配置信息
 * CodeIterationRecord:
 */
import type { dynamicFormData } from "zhytech-ui";
import type { TableColumnCtx as tableColumnCtx } from "element-plus/es/components/table/src/table-column/defaults";
import type { traineeView } from "../types/traineeView";
import type { timelineItemView } from "../types/timelineItemView";
const { validateRule } = useForm();
// 不同抽屉的标题内容
let drawerNameToTitle = {
  timeline: "培训时间线",
  teacherEvaluation: "评价学生",
  learnerEvaluation: "评价老师",
  courseRecommendations: "课程建议"
};
// 抽屉的drawerName的值类型
type drawerNameType = keyof typeof drawerNameToTitle;
type MType = keyof typeof MessageType;
/**
 * @description: 培训学员管理页面钩子
 */
export function useTrainees() {
  /**
   * table中的列属性（除操作列外）
   */
  const columns = ref<Partial<tableColumnCtx<traineeView> & { cvtPxWidth: Number; dateFormat: boolean }>[]>([
    { prop: "employeeName", label: "姓名", cvtPxWidth: 100, align: "center" },
    { prop: "departmentName", label: "部门", cvtPxWidth: 120, align: "center" },
    { prop: "capabilityLevel", label: "层级", cvtPxWidth: 80, align: "center" },
    { prop: "progress", label: "培训进度", cvtPxWidth: 80, align: "center" },
    { prop: "learningCount", label: "培训次数", cvtPxWidth: 80, align: "center" },
    { prop: "learn", label: "已学习时长", cvtPxWidth: 90, align: "center" },
    { prop: "teacherEvaluation", label: "讲师评价", cvtPxWidth: 300 },
    { prop: "lastTrainingTime", label: "最后培训时间", align: "center", cvtPxWidth: 120, dateFormat: true }
  ]);
  /**
   * table属性
   */
  const tableProps = computed(() => ({
    stripe: true,
    border: true,
    "highlight-current-row": true
  }));

  const drawerOptions = ref<DrawerOptions>({
    drawerTitle: "培训记录",
    showDrawer: false,
    drawerSize: "40%"
  });
  /**
   * 时间线item默认属性
   */
  let defaultTimelineProp: Partial<timelineItemView> = {
    type: "text",
    // 是否为空心
    hollow: true,
    center: false,
    // color:"#33ddef",
    nodeType: "primary"
  };
  const timelineItems = ref<timelineItemView[]>([]);
  /**
   * @description: 设置时间线呈现内容（同时设置默认属性）
   * @param items 在时间线上呈现的内容
   * @returns {void}
   */
  const setTimelineItems = (items: timelineItemView[]): void => {
    items.forEach((timelineItem: timelineItemView) => {
      for (const key in defaultTimelineProp) {
        timelineItem[key] ??= defaultTimelineProp[key];
      }
    });
    timelineItems.value = items;
  };
  /**
   * @description: 设置抽屉保存方法
   * @param saveMethod 保存方法逻辑
   * @param callBack 保存方法成功回调函数
   * @returns
   */
  const setDrawerSaveEvent = (saveMethod: () => void | boolean | Promise<boolean>, callBack?: Function) => {
    drawerOptions.value.confirm = async () => {
      // 调用保存方法
      const successFlag = await saveMethod();
      // 保存成功后的逻辑，比如关闭抽屉
      successFlag && callBack?.();
    };
  };
  /**
   * @description: 打开对应功能的抽屉
   * @param drawerName 抽屉名称（用来判断打开抽屉时，呈现那部分的内容）
   * @param drawerSize 抽屉大小
   * @param showConfirm 显示保存按钮 true ：显示 false：不显示
   * @returns
   */
  const openDrawer = (drawerName: drawerNameType, drawerSize: string, showConfirm: boolean = false) => {
    drawerOptions.value.showConfirm = showConfirm;
    drawerOptions.value.drawerName = drawerName;
    drawerOptions.value.drawerTitle = drawerNameToTitle[drawerName];
    drawerOptions.value.drawerSize = drawerSize;
    drawerOptions.value.showDrawer = true;
  };
  /**
   * @description: 获取人员列表
   * @param trainee 学员记录view
   * @returns {Promise<any[]>} 学员培训信息集合
   */
  const getTraineeList = async (trainingRecordID: string): Promise<any[]> => {
    let responseData: any = await traineeService.getTraineeList({ trainingRecordID: trainingRecordID });
    responseData && !responseData.length && trainingRecordID && showMessage("success", "没有查询到人员培训记录！！");
    // 返回对象有值的时候，保证返回数据为数组格式
    return responseData ? [].concat(responseData) : [];
  };
  /**
   * @description: 删除学员记录信息
   * @param row 学员信息
   * @returns
   */
  const deleteTrainee = async (row: traineeView) => {
    let delParams = {
      trainEmployeeID: row.employeeID,
      trainingLearnerID: row.trainingLearnerID
    };
    let delSuccessFlag = await traineeService.deleteTrainee(delParams);

    delSuccessFlag ? showMessage("success", "删除成功!") : showMessage("error", "删除失败！");
    return delSuccessFlag;
  };
  /**
   * @description: 设置班长或取消班长
   * @param row 学员信息
   * @returns
   */
  const setTraineeMonitor = async (row: traineeView) => {
    let params: Pick<traineeView, "employeeID" | "monitorFlag" | "trainingLearnerID"> = {
      employeeID: row.employeeID,
      trainingLearnerID: row.trainingLearnerID,
      monitorFlag: row.monitorFlag
    };
    let setMonitorSuccessFlag = await traineeService.setTraineeMonitor(params);
    let successTip = row.monitorFlag ? "任命班长成功!!" : "取消班长成功";
    let failTip = row.monitorFlag ? "任命班长失败!!" : "取消班长失败";
    setMonitorSuccessFlag ? showMessage("success", successTip) : showMessage("success", failTip);
    return setMonitorSuccessFlag;
  };

  /**
   * @description: 获取呈现的人员培训时间线数据
   * @param row 点击的人员培训记录行数据
   * @return 返回获取到拼接好的时间线书
   */
  const getTrainTimeline = async (row: traineeView) => {
    let params: Pick<traineeView, "employeeID" | "trainingLearnerID"> = {
      employeeID: row.employeeID,
      trainingLearnerID: row.trainingLearnerID
    };
    // 获取某位学员的培训时间线
    let timeLine: any = await traineeService.getTrainTimeline(params);
    return timeLine ? [].concat(timeLine) : [];
  };
  /**
   * @description: 获取培训评价模板数据
   * @param trainingRecordID 培训清单ID
   * @param evaluationType 评价类别
   * @return 返回评价模版数据
   */
  const getEvaluationFormView = async (trainingRecordID: string, evaluationType: string) => {
    const evaluationFormData = ref<dynamicFormData<Record<string, any>>>({
      datas: {},
      components: [],
      props: {}
    });
    let params = {
      trainingRecordID: trainingRecordID,
      evaluationType: evaluationType
    };
    await trainingService.getEvaluationFormView(params).then((result: any) => {
      if (result) {
        evaluationFormData.value = result;
      }
    });
    return evaluationFormData.value;
  };
  /**
   * @description: 保存培训评价记录
   * @param view
   */
  const saveEvaluationData = async (view: Record<string, any>): Promise<boolean> => {
    return await trainingService.saveEvaluationData(view);
  };
  /**
   * @description: 保存课程建议
   * @param courseRecommendationsRefs Form表单信息
   * @param recommendationsFormData 当前保存记录
   * @return
   */
  const saveCourseRecommendations = async (courseRecommendationsRefs: any, recommendationsFormData: Record<string, any>) => {
    if (!(await validateRule(courseRecommendationsRefs))) {
      showMessage("warning", "请填写必填项");
      return false;
    }
    let params = {
      trainingLearnerID: recommendationsFormData.trainingLearnerID,
      courseSatisfaction: recommendationsFormData.courseSatisfaction,
      courseRecommendations: recommendationsFormData.courseRecommendations
    };
    let setMonitorSuccessFlag = await traineeService.saveCourseRecommendations(params);
    let messageType: MType = setMonitorSuccessFlag ? "success" : "error";
    let message = setMonitorSuccessFlag ? "保存成功" : "保存失败";
    showMessage(messageType, message);
    return setMonitorSuccessFlag;
  };
  return {
    // 学员培训信息呈现列配置
    columns,
    // 表格属性
    tableProps,
    // 抽屉配置项
    drawerOptions,
    // 时间线数据
    timelineItems,
    //#region 后端交互相关方法
    /**
     * @description: 获取人员列表
     * @param trainee 学员记录view
     * @returns {Promise<any[]>} 学员培训信息集合
     */
    getTraineeList,
    /**
     * @description: 删除学员记录信息
     * @param row 学员信息
     * @returns
     */
    deleteTrainee,
    /**
     * @description: 设置班长或取消班长
     * @param row 学员信息
     * @returns
     */
    setTraineeMonitor,
    /**
     * @description: 获取呈现的人员培训时间线数据
     * @param row 点击的人员培训记录行数据
     * @return 返回获取到拼接好的时间线书
     */
    getTrainTimeline,
    //#endregion

    //#region 抽屉相关方法
    /**
     * @description: 设置抽屉保存方法
     * @param saveMethod 保存方法逻辑
     * @returns
     */
    setDrawerSaveEvent,
    /**
     * @description: 打开对应功能的抽屉
     * @param drawerName 抽屉名称（用来判断打开抽屉时，呈现那部分的内容）
     * @returns
     */
    openDrawer,
    /**
     * @description: 设置时间线呈现内容（同时设置默认属性）
     * @param items 在时间线上呈现的内容
     * @returns {void}
     */
    setTimelineItems,
    //#endregion
    /**
     * @description: 获取培训评价模板数据
     * @param trainingRecordID 培训清单ID
     * @param evaluationType 评价类别
     * @return 返回评价模版数据
     */
    getEvaluationFormView,
    /**
     * @description: 保存培训评价记录
     * @param view
     */
    saveEvaluationData,
    /**
     * @description: 保存课程满意度和建议
     * @param view
     */
    saveCourseRecommendations
  };
}
