<!--
 * FilePath     : /src/views/annualPlan/quarterPlan/index.vue
 * Author       : 杨欣欣
 * Date         : 2024-12-28 11:22
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 16:10
 * Description  : 季度计划维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="quarter-plan-maintain" headerHeight="auto">
    <template #header>
      <div class="first-header">
        <annual-plan-header v-model.quarter="quarter" :year="annual" />
        <span>
          <el-button :disabled="quarterPlanStatus" type="success" @click="publishQuarterPlan"
            >{{ quarterPlanStatus ? "已" : "" }}发布</el-button
          >
          <template v-if="!planManagementStore.readOnly">
            <el-button @click="openBatchImportDialog">导入</el-button>
            <el-button type="primary" @click="saveWorks">保存</el-button>
          </template>
        </span>
      </div>
      <div class="second-header">
        <el-badge :value="keyAndRoutineWorksCount[0]" class="check-tag-badge">
          <el-check-tag checked>重点</el-check-tag>
        </el-badge>
        <el-badge :value="keyAndRoutineWorksCount[1]" class="check-tag-badge">
          <el-check-tag :checked="showRoutineWorks" @change="() => (showRoutineWorks = !showRoutineWorks)">常规</el-check-tag>
        </el-badge>
      </div>
    </template>
    <div class="qp-work-plan-body">
      <!-- 平铺的分类和明细项 -->
      <div class="left-wrapper" ref="leftWrapper">
        <el-empty class="work-empty" v-if="!qpWorksByTypeList?.length">
          <template #description>
            <span class="empty-desc">
              暂无数据，你可以通过<span class="import-link" @click="openBatchImportDialog">导入</span>来挑选要添加的工作
            </span>
          </template>
        </el-empty>
        <work-table
          v-else
          v-for="(typeAndWorks, index) in qpWorksByTypeList"
          ref="workTables"
          :key="typeAndWorks.typeID"
          v-model="qpWorksByTypeList[index]"
          period="quarter"
          :planStatus="quarterPlanStatus"
          :showRoutineWorks="showRoutineWorks"
          @update="updateWork"
          @delete="deleteWork"
          @editPrincipal="openPrincipalDialog"
          @editWorkReference="openWorkQuoteDialog"
          @resetSort="resetWorksSort"
        />
      </div>
      <div class="right-wrapper">
        <el-anchor :container="leftWrapper">
          <el-anchor-link
            v-for="byTypeGroup in qpWorksByTypeList"
            :key="byTypeGroup.typeID"
            :href="`#type-${byTypeGroup.typeID}`"
            :title="byTypeGroup.typeName"
          />
        </el-anchor>
      </div>
    </div>
    <!-- 批量导入弹窗 -->
    <work-batch-import-dialog
      v-model="batchImportDialogVisible"
      :departmentID
      :leftPanelData
      :rightPanelTemplate
      @confirm="handleImportWorks"
    />
    <!-- 负责人选择弹窗 -->
    <principal-selector-dialog
      v-model="principalSelectorVisible"
      :principals="principalDialogCurrentWork?.principals!"
      @confirm="handleUpdateWorkPrincipals"
    />
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanTime } from "@/hooks/usePlanTime";
import { quarterPlanMaintainService } from "@/api/annualPlan/quarterPlanMaintainService";
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
import workTable from "../components/workTable.vue";
import workBatchImportDialog from "../components/workBatchImportDialog.vue";
import principalSelectorDialog from "../components/principalSelectorDialog.vue";
import workSelector from "../components/workSelector.vue";
import type { planWorkImportVo } from "../types/planWorkImportVo";
import type { getCanImportQpWorksQuery, getQuarterPlanQuickReferenceVosQuery, saveQuarterWorksCommand } from "../types/quarterPlanTypes";
import type { importWorkDto } from "../types/importWorkDto";
import type { planAndWorksVo, planWork } from "../types/planAndWorksVo";
import { workType } from "../types/common";
import { usePrincipalDialog } from "../maintain/hooks/usePrincipalSelectorDialog";
import type { Action, MessageBoxState } from "element-plus/es/components/message-box/src/message-box.type";

const { getPlanAnnual, getPlanQuarter } = usePlanTime();
const annual = getPlanAnnual();
const planManagementStore = usePlanManagementStore();
const { departmentID, annualPlanMainID, quarterPlanMainID } = storeToRefs(planManagementStore);
const quarter = ref<number>(getPlanQuarter().quarter);

onMounted(async () => {
  await planManagementStore.setAnnualPlanMainID();
  await planManagementStore.setQuarterPlanMainID(quarter);
  await init();
});
watch(departmentID, async () => {
  resetVariables();
  await planManagementStore.setAnnualPlanMainID(true);
  planManagementStore.setQuarterPlanMainID(quarter, true).then(async (res) => res && (await init()));
});
watch(quarter, async (newValue) => {
  resetVariables();
  planManagementStore.setQuarterPlanMainID(newValue, true).then(async (res) => res && (await init()));
});

/**
 * @description: 初始化
 */
const init = async () => {
  if (!planManagementStore.annualPlanPublished) {
    showMessage("warning", "请先发布年度计划！");
    return;
  }
  if (!quarterPlanMainID.value) {
    return;
  }
  await Promise.all([getWorks(), getPlanStatus()]);
};

//#region 表格
const qpWorksByTypeList = ref<planAndWorksVo[]>([]);
const workTables = useTemplateRef<InstanceType<typeof workTable>[]>("workTables");
/**
 * @description: 变量重置
 */
const resetVariables = () => {
  qpWorksByTypeList.value = [];
  quarterPlanStatus.value = false;
  planManagementStore.setReadOnly(false);
};
/**
 * @description: 获取计划工作
 * @param annualPlanMainID 年度计划主键
 * @return
 */
const getWorks = async () => {
  if (!quarterPlanMainID.value) {
    return [];
  }
  const params = {
    annualPlanMainID: annualPlanMainID.value,
    quarterPlanMainID: quarterPlanMainID.value
  };
  qpWorksByTypeList.value = await quarterPlanMaintainService.getQuarterWorks(params);
};
/**
 * @description: 更新工作类型
 * @param workView 工作
 * @return
 */
const updateWork = (workView: planWork) => {
  if (!workView.quarterPlanDetailID) {
    showMessage("error", "工作主键不能为空，请联系管理员");
    return;
  }
  quarterPlanMaintainService.updateQuarterWork({
    quarterPlanDetailID: workView.quarterPlanDetailID,
    workContent: workView.workContent,
    requirement: workView.requirement,
    workType: workView.workType,
    sort: workView.sort,
    principalName: workView.principalName,
    principals: workView.principals
  });
};
/**
 * @description: 删除工作
 * @param quarterPlanDetailID 季度计划工作主键
 * @return
 */
const deleteWork = async (quarterPlanDetailID: string) => {
  await quarterPlanMaintainService.deleteQuarterWork({ quarterPlanDetailID });
  await getWorks();
};
/**
 * @description: 批量保存工作
 * @param works
 * @return
 */
const saveWorks = async () => {
  // 搜集已勾选行
  if (!workTables.value) {
    showMessage("error", "发生内部错误，请联系管理员");
    return [];
  }
  const toSaveWorks = workTables.value.flatMap((workTableRef) => workTableRef.getToSaveWorks());
  const params: saveQuarterWorksCommand = {
    quarterPlanMainID: quarterPlanMainID.value,
    workViews: toSaveWorks.map((planWork) => ({
      quarterPlanDetailID: planWork.quarterPlanDetailID!,
      typeID: planWork.typeID,
      apInterventionID: planWork.apInterventionID,
      workType: planWork.workType,
      sort: planWork.sort,
      workContent: planWork.workContent,
      requirement: planWork.requirement,
      isTemp: planWork.isTemp,
      principalName: planWork.principalName,
      principals: planWork.principals
    }))
  };
  const res = await quarterPlanMaintainService.saveQuarterWorks(params);
  if (res) {
    showMessage("success", "保存成功");
    nextTick(() => {
      workTables.value?.forEach((workTable) => {
        workTable.clearSelection();
      });
    });
    await getWorks();
  }
};

/**
 * @description: 重排序
 * @param typeID 分类字典ID
 * @param planWorkIDAndSort 工作ID及新序号
 * @param resolve 重排序后的回调函数
 * @return
 */
const resetWorksSort = async (typeID: number, planWorkIDAndSort: Record<string, number>, resolve: any) => {
  const params = {
    quarterPlanMainID: quarterPlanMainID.value,
    typeID: typeID,
    planWorkIDAndSort
  };
  const successSortFlag = await quarterPlanMaintainService.resetQuarterPlanWorksSort(params);
  resolve(successSortFlag);
};
//#endregion

//#region 过滤与统计
const showRoutineWorks = ref<boolean>(false);
// 计算重点工作数量
const keyAndRoutineWorksCount = computed(() =>
  qpWorksByTypeList.value.reduce(
    (total, group) => {
      const currentTypeKeyWorksCount = group.children.filter((work) => work.workType === workType.Key).length;
      const currentTypeRoutineWorksCount = group.children.length - currentTypeKeyWorksCount;
      total[0] = total[0] + currentTypeKeyWorksCount;
      total[1] = total[1] + currentTypeRoutineWorksCount;
      return total;
    },
    [0, 0]
  )
);
//#endregion

//#region 发布
onUnmounted(() => {
  planManagementStore.setReadOnly(false);
});
const quarterPlanStatus = ref<boolean>(false);
/**
 * @description: 获取计划发布状态
 */
const getPlanStatus = async () => {
  quarterPlanStatus.value = await quarterPlanMaintainService.getQuarterPlanStatus({ quarterPlanMainID: quarterPlanMainID.value });
  planManagementStore.setReadOnly(quarterPlanStatus.value);
};
/**
 * @description: 发布季度计划
 */
const publishQuarterPlan = async () => {
  if (!quarterPlanMainID.value) {
    showMessage("error", "暂无数据，请先维护季度计划");
    return;
  }

  // 检查重点工作是否都填写了内容
  const allKeyWorksHaveContent = qpWorksByTypeList.value
    .flatMap((group) => group.children)
    .filter((work) => work.workType === workType.Key)
    .every((work) => work.workContent);

  if (!allKeyWorksHaveContent) {
    showMessage("error", "请填写所有重点工作的内容！");
    return;
  }

  confirmBox("确认要发布计划吗？", "", async (flag: boolean) => {
    if (!flag) {
      return;
    }
    const params = {
      quarterPlanMainID: quarterPlanMainID.value
    };
    quarterPlanStatus.value = await quarterPlanMaintainService.publishQuarterPlan(params);
    planManagementStore.setReadOnly(quarterPlanStatus.value);
  });
};
//#endregion

// 锚点（右侧）部分
const leftWrapper = ref<HTMLElement>();

const isFirstImport = computed<boolean>(() => !qpWorksByTypeList.value.length);
//#region 批量导入弹窗
const batchImportDialogVisible = ref<boolean>(false);
const leftPanelData = ref<planWorkImportVo[]>([]);
const rightPanelTemplate = ref<planAndWorksVo[]>([]);
/**
 * @description: 打开批量导入弹窗
 */
const openBatchImportDialog = async () => {
  if (qpWorksByTypeList.value.length) {
    rightPanelTemplate.value = qpWorksByTypeList.value;
  }
  await Promise.all([planManagementStore.setAnnualPlanMainID(), planManagementStore.setQuarterPlanMainID(quarter)]);
  const params: getCanImportQpWorksQuery = {
    annual,
    quarter: toValue(quarter),
    departmentID: toValue(departmentID),
    annualPlanMainID: planManagementStore.annualPlanMainID,
    quarterPlanMainID: planManagementStore.quarterPlanMainID
  };
  leftPanelData.value = await quarterPlanMaintainService.getCanImportQpWorksGroupByPlanThenType(params);
  batchImportDialogVisible.value = true;
};
/**
 * @description: 处理批量导入确认
 */
const handleImportWorks = async (workViews: importWorkDto[]) => {
  if (!workViews.length) {
    showMessage("warning", "没有选择要导入的工作");
    return;
  }
  const params = {
    annualPlanMainID: planManagementStore.annualPlanMainID,
    quarterPlanMainID: planManagementStore.quarterPlanMainID,
    annual,
    quarter: quarter.value,
    departmentID: departmentID.value,
    workViews: workViews,
    isFirstImport: isFirstImport.value
  };
  const newID = await quarterPlanMaintainService.importQuarterWorks(params);
  if (!newID) {
    showMessage("error", "导入失败");
  }
  if (isFirstImport.value) {
    quarterPlanMainID.value = newID;
  }
  batchImportDialogVisible.value = false;
  getWorks();
};
//#endregion

// 当前操作的工作项
//#region 快捷参考弹窗
/**
 * @description: 打开快捷参考弹窗
 * @param work 点击的工作
 * @return
 */
const openWorkQuoteDialog = async (work: planWork, selectRowFunc: (row: planWork) => void) => {
  if (!work.apInterventionID) {
    showMessage("warning", "没有可以参考的工作");
    return;
  }
  const params: getQuarterPlanQuickReferenceVosQuery = {
    annual,
    quarter: quarter.value,
    departmentID: departmentID.value,
    apInterventionID: work.apInterventionID
  };
  const quickRefWorks = await quarterPlanMaintainService.getQuarterPlanQuickReferenceVos(params);
  if (!quickRefWorks.length) {
    showMessage("warning", "没有可以参考的工作");
    return;
  }
  // TODO：后续将MessageBox封装为hook，方便复用
  let currentSelectWork: planWorkImportVo["children"][number]["children"][number] | undefined = undefined;
  ElMessageBox({
    message: h(workSelector, {
      planToWorkMap: quickRefWorks.reduce((map, view) => {
        map.set(view.planName, view.children[0].children[0]);
        return map;
      }, new Map<string, planWorkImportVo["children"][number]["children"][number]>()),
      onSelect: (work: planWorkImportVo["children"][number]["children"][number]) => {
        currentSelectWork = work;
      }
    }),
    customStyle: { maxWidth: "448px" },
    showCancelButton: true,
    beforeClose: (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action !== "confirm") {
        currentSelectWork = undefined;
        done();
        return;
      }
      if (!work || !currentSelectWork) {
        done();
        return;
      }
      work.workContent = currentSelectWork.workContent;
      work.requirement = currentSelectWork.requirement;
      selectRowFunc(work);
      done();
    }
  });
};
//#endregion

//#region 负责人选择弹窗
const { principalSelectorVisible, principalDialogCurrentWork, openPrincipalDialog, handleUpdateWorkPrincipals } = usePrincipalDialog();
//#endregion
</script>
<style scoped lang="scss">
.quarter-plan-maintain {
  gap: 8px;
  background-color: #f3f3f3;
  :deep(.base-header) {
    padding-bottom: 8px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    .first-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: auto;
      width: 100%;
    }
    .second-header {
      display: flex;
      gap: 8px;
      .check-tag-badge {
        .el-badge__content {
          top: 8px;
          right: 16px;
        }
      }
    }
  }
  .qp-work-plan-body {
    height: 100%;
    display: flex;
    .left-wrapper {
      width: 85%;
      overflow-y: auto;
      scrollbar-width: none;
      .work-empty {
        height: 100%;
        .empty-desc {
          font-size: 16px;
          color: #909399;
        }
        .import-link {
          color: #409eff;
          cursor: pointer;
          text-decoration: underline;
        }
      }
      &::-webkit-scrollbar {
        display: none;
      }
    }
    .right-wrapper {
      flex: 1;
      min-width: 0;
      padding-left: 48px;
      .quarter-title {
        line-height: 24px;
        letter-spacing: 1px;
        font-weight: 600;
        font-size: 20px;
      }
      .el-anchor__link {
        font-size: 18px;
        letter-spacing: 1px;
        line-height: 1.6;
        &:not(.is-active) {
          color: #707070;
        }
      }
    }
  }
}
</style>
