<!--
 * FilePath     : \src\views\setting\employeeRole\index.vue
 * Author       : 张现忠
 * Date         : 2024-03-07 14:21
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-20 08:47
 * Description  : 人员角色维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="employee-role" :drawerOptions="drawerOptions">
    <template #header>
      <department-selector v-permission:DL="13" v-model="departmentID" @change="getEmployeeRoleList"></department-selector>
      <el-button class="add-button" @click="addOrModify()" v-permission:B="1">新增</el-button>
    </template>
    <el-table :data="employeeRoleList" border stripe height="100%">
      <el-table-column prop="employeeName" label="姓名" align="center" :width="convertPX(160)" />
      <el-table-column prop="departmentName" label="部门" align="center" :width="convertPX(250)" />
      <el-table-column label="角色" align="left" :min-width="convertPX(130)">
        <template #default="scope">
          <el-tag class="role-tag" effect="dark" v-for="(roleName, index) in scope.row.roleNames" :key="index">{{ roleName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" label="维护人" align="center" :width="convertPX(160)" />
      <el-table-column prop="modifyDateTime" label="维护时间" align="center" :width="convertPX(160)">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(80)" align="center">
        <template #default="scope">
          <el-tooltip content="修改">
            <i @click="addOrModify(scope.row)" class="iconfont icon-edit" v-permission:B="3"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i @click="deleteRow(scope.row)" class="iconfont icon-delete" v-permission:B="4"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="submitRefs" class="form-style" label-width="auto" :model="currRow" :rules="rules" :validate-on-rule-change="false">
        <el-form-item label="部门：" prop="departmentID">
          <department-selector
            v-permission:DL="13"
            label=""
            v-model="currRow.departmentID"
            @change="getEmployeeRoleList"
            :width="386"
          ></department-selector>
        </el-form-item>
        <el-form-item label="姓名：" prop="employeeID">
          <employee-selector label="" v-model="currRow.employeeID" :disabled="disabled" :departmentID="currRow.departmentID" :width="386" />
        </el-form-item>
        <el-form-item label="角色：" prop="authorityRoleIDs">
          <el-checkbox-group v-model="currRow.authorityRoleIDs">
            <el-checkbox :value="item.value" v-for="(item, index) in roleOptions" :key="index">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import type { employeeRoleView } from "./types/employeeRoleView";
const rules = ref({
  employeeID: [{ required: true, message: "请选择人员", trigger: "blur" }],
  authorityRoleIDs: [{ required: true, message: "请选择角色", trigger: "blur" }]
});
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
const departmentID = ref<number>(userStore.departmentID);
const disabled = ref(false);
const employeeRoleList = ref<Array<employeeRoleView>>();
const currRow = ref<employeeRoleView>({} as employeeRoleView);
const submitRefs = shallowRef();
let roleOptions = ref<Array<Record<string, any>>>([]);
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  showCancel: true,
  drawerSize: "50%",
  cancel: () => (drawerOptions.value.showDrawer = false),
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveEmployeeRole(currRow.value);
  }
});
onMounted(async () => {
  getEmployeeRoleList();
  roleOptions.value = await useDictionaryData().getAuthorityRoles();
});
/**
 * @description: 新增和修改按钮点击业务处理逻辑
 * @param row
 * @return
 */
const addOrModify = (row?: employeeRoleView) => {
  disabled.value = Boolean(row);
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerTitle = !row ? "新增人员角色" : "修改人员角色";
  currRow.value = common.clone(row) || ({} as employeeRoleView);
  currRow.value.departmentID = departmentID.value;
};
/**
 * @description: 保存员工角色
 * @param row
 * @return
 */
const saveEmployeeRole = (row: any) => {
  drawerOptions.value.showDrawer = false;
  let params = {
    employeeID: row.employeeID,
    authorityRoleIDs: row.authorityRoleIDs
  };
  employeeRoleService.SaveEmployeeRole(params).then((respBool: any) => {
    if (respBool) {
      showMessage("success", "保存成功");
      getEmployeeRoleList();
      return;
    }
    showMessage("error", "保存失败");
  });
};
/**
 * @description: 获取员工角色列表
 * @return
 */
const getEmployeeRoleList = () => {
  let params = {
    departmentID: departmentID.value
  };
  employeeRoleService.getEmployeeRoleList(params).then((respData: any) => {
    if (respData) {
      employeeRoleList.value = respData;
      return;
    }
    showMessage("error", "获取角色失败");
  });
};
/**
 * @description: 删除行
 * @param row
 * @return
 */
const deleteRow = (row: employeeRoleView) => {
  deleteConfirm("确定要删除么？", (flag: Boolean) => {
    if (!flag) {
      return;
    }
    deleteEmployeeRole(row);
  });
};
/**
 * @description: 删除员工角色配置
 * @param row
 * @return
 */
const deleteEmployeeRole = (row: employeeRoleView) => {
  employeeRoleService
    .DeleteEmployeeRole({
      employeeID: row.employeeID
    })
    .then((respBool: any) => {
      if (respBool) {
        showMessage("success", "删除成功");
        getEmployeeRoleList();
        return;
      }
      showMessage("error", "删除失败");
    });
};
</script>

<style lang="scss">
.employee-role {
  height: 100%;
  width: 100%;
  .role-tag {
    margin-right: 10px;
  }
  .form-style {
    width: 80%;
  }
}
</style>
