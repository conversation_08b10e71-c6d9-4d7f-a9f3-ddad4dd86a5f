/*
 * FilePath     : \src\hooks\useCascaderFullValue.ts
 * Author       : 苏军志
 * Date         : 2023-09-19 18:23
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:49
 * Description  : 递归构造所选子数据对应上级数据构成的数组
 * CodeIterationRecord:  使用递归函数，构造返回数组，如传入子科室ID，
 * 构造返回其上级部门ID以及子部门ID数组集合，
 * 数据示例：选择科室ID:2，返回(子部门ID以及上级部门ID集合)ret[0]: [70,50,2],ret[1] L类型集合
 */
export function useCascaderFullValue() {
  // 比对级联选择器的数据（第一层的子集）
  const findFullValue = <V, L>(value: V, options: L[], valueName: string, childName: string) => {
    let retValue: V[] = [];
    let retItem: L[] = [];
    // 如果children数据为空，返回
    if (!options) {
      return retValue;
    }
    // 循环children数据
    options.forEach((option) => {
      const optionValue = (option as any)[valueName];
      // children的第一层自己就找到，赋值，跳出本次循环
      if (optionValue === value) {
        retItem.unshift(option);
        retValue.unshift(optionValue);
        return;
      }
      // children的第一层没有，递归调用
      const childValue = findFullValue(value, (option as any)[childName], valueName, childName);
      if (childValue?.length) {
        // 找到之后返回赋值
        const value = childValue[0] as V[];
        const item = childValue[1] as L[];
        if (value?.length) {
          retValue.unshift(optionValue);
          retValue = [...retValue, ...value];
        }
        if (item.length) {
          retItem.unshift(option);
          retItem = [...retItem, ...item];
        }
      }
    });
    return [retValue, retItem];
  };
  return {
    /**
     * @description: 根据传入值获取：①对应节点所在的各级菜单的值所组成的数组（多选为二维数组）②对应节点的对象（多选为对象数组）
     * @param selectVale 级联选择器选择的值
     * @param options 级联选择器的所有数据
     * @param valueName 级联选择器的数据类型value名字
     * @param childName 级联选择器的数据类型children名字
     * @param multipleFlag 是否是多选
     * @returns
     */
    getCascaderFullValue<V, L>(selectVale: V | V[], options: L[], valueName: string, childName: string, multipleFlag?: boolean) {
      // 定义级联选择器value返回类型
      let values: V[] | Array<V[]> = [];
      //  定义级联选择器数据类型对应的返回类型
      let items: L | L[] = [];
      // 值以及数组非空判断
      // eslint-disable-next-line eqeqeq
      if (!selectVale || !options.length) {
        return [values, items];
      }
      let value: V[] = [];
      if (!multipleFlag) {
        // 不是多选，放入selectVale
        value.push(selectVale as V);
      } else {
        // 是多选，放入selectVale数组
        value = selectVale as V[];
      }
      // 定义需要给return对象赋值的value返回类型
      let retValues: Array<V[]> = [];
      // 定义需要给return对象赋值的级联选择器数据类型
      let retItems: Array<L[]> = [];
      // 循环选择的数据，在级联选择器的所有数据内比对寻找
      value.forEach((val) => {
        let itemValue: V[] = [];
        let tempItems: L[] = [];
        // 循环级联选择器的所有数据
        options.forEach((option) => {
          // 获取级联选择器选择的value值
          const optionValue = (option as any)[valueName];
          // 假如在第一层数据就找到选择的数据，赋值，跳出本次循环继续比对
          if (optionValue === val) {
            itemValue.unshift(optionValue);
            tempItems.unshift(option);
            // 跳出本次循环继续循环的作用，找多选数据
            return;
          }
          // 比对第一层的children数据
          const childValue = findFullValue(val, (option as any)[childName], valueName, childName);
          if (childValue?.length) {
            // 找到之后返回赋值
            const value = childValue[0] as V[];
            const item = childValue[1] as L[];
            if (value?.length) {
              itemValue.unshift(optionValue);
              itemValue = [...itemValue, ...value];
            }
            if (item.length) {
              tempItems.unshift(option);
              tempItems = [...tempItems, ...item];
            }
          }
        });
        retValues.push(itemValue);
        retItems.push(tempItems);
      });
      if (!multipleFlag) {
        // 单选返回有value的一维数组
        values = retValues[0];
        items = retItems[0][retItems[0].length - 1];
      } else {
        // 多选返回有value的二维数组
        values = retValues;
        retItems.forEach((item) => {
          (items as L[]).push(item[item.length - 1]);
        });
      }
      return [values, items];
    }
  };
}
