export enum menuEnum {
  //================工具栏菜单=============
  /**
   * @description: 撤销
   */
  UNDO = "univer.command.undo",
  /**
   * @description: 恢复
   */
  REDO = "univer.command.redo",
  /**
   * @description: 格式刷
   */
  FORMAT_PAINTER = "sheet.command.set-once-format-painter",
  /**
   * @description: 加粗
   */
  SET_BOLD = "sheet.command.set-range-bold",
  /**
   * @description: 斜体
   */
  SET_ITALIC = "sheet.command.set-range-italic",
  /**
   * @description: 下划线
   */
  SET_UNDERLINE = "sheet.command.set-range-underline",
  /**
   * @description: 删除线
   */
  SET_STROKE = "sheet.command.set-range-stroke",
  /**
   * @description: 字体
   */
  SET_FONT_FAMILY = "sheet.command.set-range-font-family",
  /**
   * @description: 字号
   */
  SET_FONT_SIZE = "sheet.command.set-range-fontsize",
  /**
   * @description: 边框
   */
  SET_BORDER_BASIC = "sheet.command.set-border-basic",
  /**
   * @description: 合并单元格
   */
  MERGE = "sheet.command.add-worksheet-merge",
  /**
   * @description: 字体颜色
   */
  SET_COLOR = "sheet.command.set-range-text-color",
  /**
   * @description: 背景色
   */
  SET_BACKGROUND_COLOR = "sheet.command.set-background-color",
  /**
   * @description: 水平对齐
   */
  SET_HORIZONTAL_ALIGN = "sheet.command.set-horizontal-text-align",
  /**
   * @description: 垂直对齐
   */
  SET_VERTICAL_ALIGN = "sheet.command.set-vertical-text-align",
  /**
   * @description: 换行方式
   */
  SET_TEXT_WRAP = "sheet.command.set-text-wrap",
  /**
   * @description: 文字旋转
   */
  SET_TEXT_ROTATION = "sheet.command.set-text-rotation",
  /**
   * @description: 函数
   */
  INSERT_FUNCTION = "formula-ui.operation.insert-function",
  /**
   * @description: 钱币格式
   */
  SET_CURRENCY = "sheet.command.numfmt.set.currency",
  /**
   * @description: 增加小数点位数
   */
  ADD_DECIMAL = "sheet.command.numfmt.add.decimal.command",
  /**
   * @description: 减少小数点位数
   */
  SUBTRACT_DECIMAL = "sheet.command.numfmt.subtract.decimal.command",
  /**
   * @description: 百分比
   */
  SET_PERCENT = "sheet.command.numfmt.set.percent",
  /**
   * @description: 单元格格式
   */
  OPEN_NUMFMT = "sheet.operation.open.numfmt.panel",
  /**
   * @description: 单元格权限
   */
  ADD_PROTECTION = "sheet.command.add-range-protection-from-toolbar",
  /**
   * @description: 打开评论面板开关
   */
  THREAD_COMMENT_TOGGLE = "thread-comment-ui.operation.toggle-panel",
  //================右键菜单=============
  /**
   * @description: 复制
   */
  COPY = "sheet.command.copy",
  /**
   * @description: 粘贴
   */
  PASTE = "sheet.command.paste",
  /**
   * @description: 选择性粘贴
   */
  PASTE_SPECIAL = "sheet.menu.paste-special",
  /**
   * @description: 保护行列
   */
  PERMISSION = "sheet.contextMenu.permission",
  /**
   * @description: 插入-右移
   */
  INSERT_MOVE_RIGHT = "sheet.command.insert-range-move-right-confirm",
  /**
   * @description: 插入-下移
   */
  INSERT_MOVE_DOWN = "sheet.command.insert-range-move-down-confirm",
  /**
   * @description: 清除-清除内容
   */
  CLEAR_CONTENT = "sheet.command.clear-selection-content",
  /**
   * @description: 清除-清除格式
   */
  CLEAR_FORMAT = "sheet.command.clear-selection-format",
  /**
   * @description: 清除-清除全部
   */
  CLEAR_ALL = "sheet.command.clear-selection-all",
  /**
   * @description: 删除-左移
   */
  DELETE_MOVE_LEFT = "sheet.command.delete-range-move-left-confirm",
  /**
   * @description: 删除-上移
   */
  DELETE_MOVE_UP = "sheet.command.delete-range-move-up-confirm",
  /**
   * @description: 插入行列
   */
  CELL_INSERT = "sheet.menu.cell-insert",
  /**
   * @description: 删除行列
   */
  MENU_DELETE = "sheet.menu.delete",
  /**
   * @description: 冻结行列
   */
  SHEET_FROZEN = "sheet.menu.sheet-frozen",
  /**
   * @description: 显示评论编辑框
   */
  SHOW_COMMENT = "sheets.operation.show-comment-modal",
  //================行号右键菜单=============
  /**
   * @description: 隐藏选中行
   */
  HIDE_ROW = "sheet.command.hide-row-confirm",
  /**
   * @description: 行高
   */
  SET_ROW_HEIGHT = "sheet.command.set-row-height",
  /**
   * @description: 行高自适应
   */
  SET_ROW_AUTO_HEIGHT = "sheet.command.set-row-is-auto-height",
  //================列号右键菜单=============
  /**
   * @description: 隐藏选中列
   */
  HIDE_COL = "sheet.command.hide-col-confirm",
  /**
   * @description: 列宽
   */
  SET_COL_WIDTH = "sheet.command.set-worksheet-col-width",
  /**
   * @description: 列宽自适应
   */
  SET_COL_AUTO_WIDTH = "sheet.command.set-col-auto-width",
  /**
   * @description: 插入行
   */
  INSERT_ROW = "sheet.menu.row-insert",
  /**
   * @description: 删除选中行
   */
  REMOVE_ROW = "sheet.command.remove-row-confirm",
  /**
   * @description: 冻结
   */
  HEADER_SHEET_FROZEN = "sheet.header-menu.sheet-frozen"
}
