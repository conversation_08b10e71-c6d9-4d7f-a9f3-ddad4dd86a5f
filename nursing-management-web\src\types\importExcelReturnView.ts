/*
 * relative     : \nursing-management-web\src\types\importExcelReturnView.ts
 * Author       : 张现忠
 * Date         : 2025-02-26 11:02
 * LastEditors  : 张现忠
 * LastEditTime : 2025-04-02 11:54
 * Description  :
 * CodeIterationRecord:
 */

/**
 * 导入Excel返回的视图数据类型
 */
declare interface ImportExcelReturnView {
  /**
   * 解析后的树结构数据，当isLegacyFormat=true时，该字段为空
   */
  tree?: ParsedExcelNode[];
  /**
   *  当isLegacyFormat=true时，记录层级列的名称（excel中的原始名称）
   */
  hierarchyColumns?: string[];
  /**
   * 当isLegacyFormat=true时，记录固定列的名称（excel中的原始名称）
   */
  fixedColumns?: string[];
  /**
   * data中没有层级的题目
   */
  noHierarchyQuestions?: Record<string, any>[];
}
/**
 * 解析后的树结构数据类型
 */
declare interface ParsedExcelNode {
  /**
   * 节点ID（唯一ID，多层级拼接而成的唯一标识 leafId_parentId_level）
   */
  id: string;
  /**
   * excel中的原始列名称
   */
  name: string;
  /**
   * 节点层级（层级列的顺序 从左到右）
   */
  level: number;
  /**
   * 子节点（下一层级存在时，不为空）
   */
  children: ParsedExcelNode[];
  /**
   * 叶子节点（固定列 的数据，最小层级，存数据集）
   */
  questions: Record<string, any>[];
}
