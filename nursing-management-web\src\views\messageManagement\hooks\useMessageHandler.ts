import type { messageRecordView } from "../types/messageRecordView";

export function useMessageHandler() {
  const { userStore } = useStore();
  // 消息列表
  const messageList = ref<messageRecordView[]>([]);
  const messageType = ref<string>();
  const departmentIDs = ref<number[]>([userStore.departmentID]);
  const submitRefs = shallowRef();
  const messageTypeList = ref<Record<any, any>[]>([]);
  const { validateRule } = useForm();
  const params: SettingDictionaryParams = {
    settingType: "Common",
    settingTypeCode: "MessageManagement",
    settingTypeValue: "MessageType",
    index: Math.random()
  };
  // 工作重点类型
  const focusOfWorkType: string = "1";
  // 系统通知类型
  const systemMessageType: string = "99";
  // 全院部门序号
  const wholeHospitalDepartmentID: number = 999999;

  // 抽屉选项
  const drawerOptions = reactive<DrawerOptions>({
    drawerTitle: "",
    showDrawer: false,
    drawerSize: "70%"
  });
  // 编辑消息
  const editMessage = ref<messageRecordView>({} as any);

  // 表单验证规则
  const rules = {
    messageType: [{ required: true, message: "请选择消息类型", trigger: "blur" }],
    departmentIDs: [{ required: true, message: "请选择接收部门", trigger: "blur" }],
    messageTitle: [{ required: true, message: "请输入消息标题", trigger: "blur" }],
    messageContent: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!value.replace(/<[^>]*>/g, "").length) {
            callback("请输入消息内容");
            return;
          }
          callback();
        }
      }
    ]
  };
  /**
   * @description: 验证数据是否满足规则
   */
  const validateForm = async () => {
    if (!editMessage.value.messageContent) {
      showMessage("warning", "请输入消息内容");
      return;
    }
    return await validateRule(submitRefs);
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    messageTypeList.value = datas;
  });
  /**
   * @description: 获取消息列表
   */
  const getMessageList = async () => {
    if (!messageType.value) {
      return;
    }
    let params = {
      messageType: messageType.value,
      departmentIDs: JSON.stringify(departmentIDs.value ?? [wholeHospitalDepartmentID])
    };
    const responseData: any = await messageManagementService.getMessageList(params);
    messageList.value = responseData || [];
  };

  /**
   * @description: 新增消息记录
   */
  const addMessage = async (messageStatus: string = "0") => {
    editMessage.value.messageStatus = messageStatus;
    (await validateForm()) &&
      (await messageManagementService.addMessage(editMessage.value).then((respBool: any) => {
        if (!respBool) {
          showMessage("error", "新增失败");
          return;
        }
        toggleDrawer(false);
        showMessage("success", "新增成功");
        getMessageList();
      }));
  };
  const showMessagePreview = ref<boolean>(false);
  /**
   * @description: 显示编辑弹窗
   * @param message 点击的消息记录信息
   * @return
   */
  const showEditDialog = async (message: any, drawerName: "edit" | "preview" = "edit") => {
    if (drawerName === "edit") {
      toggleDrawer(true, drawerName);
    } else {
      showMessagePreview.value = true;
    }
    editMessage.value = common.clone(toRaw(message));
    await messageManagementService.getMessageDetail({ messageRecordID: message.messageRecordID }).then((respData: any) => {
      if (respData) {
        editMessage.value.messageContent = respData;
      }
    });
  };
  /**
   * @description: 更新消息记录
   */
  const updateMessage = async (messageStatus: string = "0") => {
    editMessage.value.messageStatus = messageStatus;
    console.log(editMessage.value);
    (await validateForm()) &&
      (await messageManagementService.updateMessage(toRaw(editMessage.value)).then((respBool: any) => {
        if (!respBool) {
          showMessage("error", "修改失败");
          return;
        }
        toggleDrawer(false);
        showMessage("success", "修改成功");
        getMessageList();
      }));
  };

  /**
   * @description: 删除消息
   * @param messageRecordID 消息记录ID
   * @return
   */
  const deleteMessage = async (messageRecordID: string) => {
    deleteConfirm("", async (flag: boolean) => {
      if (!flag) {
        return;
      }
      await messageManagementService.deleteMessage({ messageRecordID }).then((respBool: any) => {
        if (!respBool) {
          showMessage("error", "删除失败");
          return;
        }
        showMessage("success", "删除成功");
        getMessageList();
      });
    });
  };

  /**
   * @description: 显示新增消息对话框
   */
  const showAddDialog = (isSystemNoticePage: boolean) => {
    toggleDrawer(true, "add");
    editMessage.value = {
      messageTitle: "",
      messageType: isSystemNoticePage ? systemMessageType : messageType.value !== systemMessageType ? messageType.value : "",
      messageContent: ""
    } as any;
    changeMessageType(editMessage.value.messageType);
  };
  // 要发送更新消息的序号
  const messageRecordID = ref<string>("");
  // 要发送的自定义消息内容
  const preUpdateMessage = ref<Record<string, string | number>>({});
  const preUpdateRef = shallowRef();
  const showPreUpdateMessageDialog = (messageID: string) => {
    messageRecordID.value = messageID;
    toggleDrawer(true, "preUpdate");
  };
  /**
   * @description: 发送更新前消息
   */
  const sendPreUpdateMessage = async () => {
    if (await validateRule(preUpdateRef)) {
      const params = { messageRecordID: messageRecordID.value, customMessage: preUpdateMessage.value.message };
      messageManagementService.sendSystemPreUpdateMessage(params).then((ret: any) => {
        if (ret) {
          showMessage("success", "发送成功");
          toggleDrawer(false);
        }
      });
    }
  };
  /**
   * @description: 切换弹窗显示
   * @param flag 显示状态
   * @param drawerName 显示的弹窗名称（区分不同弹窗）
   * @return
   */
  const toggleDrawer = (flag: boolean, drawerName?: "add" | "edit" | "preUpdate") => {
    drawerName && (drawerOptions.drawerName = drawerName);
    drawerOptions.showDrawer = flag;
    if (drawerName === "add") {
      drawerOptions.drawerTitle = "新增消息";
      drawerOptions.confirm = addMessage;
    }
    if (drawerName === "edit") {
      drawerOptions.drawerTitle = "修改消息";
      drawerOptions.confirm = updateMessage;
    }
    if (drawerName === "preUpdate") {
      drawerOptions.drawerTitle = "系统发布前通知";
      drawerOptions.confirmText = "发送";
      drawerOptions.confirm = sendPreUpdateMessage;
    }
  };
  /**
   * @description: 发布/取消发布消息
   * @param messageRecordID 消息记录ID
   * @param messageStatus
   * @return
   */
  const publishMessage = (messageRecordID: string, messageStatus: string) => {
    if (!messageRecordID) {
      showMessage("error", "无法获取到发布的记录");
      return;
    }
    messageManagementService.publishMessage({ messageRecordID, messageStatus }).then((respBool: any) => {
      if (!respBool) {
        showMessage("error", `${messageStatus === "0" ? "取消" : ""}发布失败`);
        return;
      }
      showMessage("success", `${messageStatus === "0" ? "取消" : ""}发布成功`);
      getMessageList();
    });
  };

  /**
   * @description: 切换消息类型时，若为系统更新类型默认置3天
   * @param type 类型
   */
  const changeMessageType = (type: string) => {
    editMessage.value.departmentDisabled = false;
    editMessage.value.departmentIDs = [];
    // 工作重点 默认当前部门且不能修改
    if (type === focusOfWorkType && userStore.departmentID) {
      editMessage.value.departmentIDs = [userStore.departmentID];
      departmentIDs.value = [userStore.departmentID];
      editMessage.value.departmentDisabled = true;
      return;
    }
    // 系统通知 默认置顶3天，默认全院且不能修改
    if (type === systemMessageType) {
      editMessage.value.isTop = true;
      editMessage.value.topDays = 3;
      editMessage.value.departmentIDs = [wholeHospitalDepartmentID];
      editMessage.value.departmentDisabled = true;
      departmentIDs.value = [wholeHospitalDepartmentID];
      return;
    }
  };

  return {
    // 工作重点类型
    focusOfWorkType,
    // 消息记录列表集合
    messageList,
    // 部门
    departmentIDs,
    // 消息类型
    messageType,
    // 弹窗配置项
    drawerOptions,
    // 表单dom实例 ref
    submitRefs,
    // 选中的编辑记录
    editMessage,
    // 消息预览
    showMessagePreview,
    // 表单验证规则
    rules,
    // 选择消息类型
    messageTypeList,
    // 系统通知类型
    systemMessageType,
    // 要发送的自定义消息内容
    preUpdateMessage,
    // 发送消息form对象
    preUpdateRef,
    /**
     * @description: 获取消息列表
     * @return
     */
    getMessageList,
    /**
     * @description: 显示新增消息对话框
     * @return
     */
    showAddDialog,
    /**
     * @description: 显示编辑弹窗
     * @param message 点击的消息记录信息
     * @return
     */
    showEditDialog,
    /**
     * @description: 删除消息
     * @param messageRecordID 消息记录ID
     * @return
     */
    deleteMessage,
    /**
     * @description: 发布消息
     * @param message 消息记录信息
     * @return
     */
    publishMessage,
    /**
     * @description: 切换消息类型
     * @param type 类型
     * @return
     */
    changeMessageType,
    /**
     * @description: 显示发送更新前消息窗口
     */
    showPreUpdateMessageDialog
  };
}
