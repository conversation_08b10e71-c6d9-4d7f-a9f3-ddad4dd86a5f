<!--
 * FilePath     : \src\components\tag.vue
 * Author       : 郭鹏超
 * Date         : 2023-08-28 09:22
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 11:44
 * Description  : 标签组件
 * CodeIterationRecord:
-->
<template>
  <el-tag
    :closeStatus="closeStatus"
    class="tag"
    :style="{ height: tagHeight, width: tagWidth }"
    @close="emit('remove')"
    :disable-transitions="true"
    :closable="closeable"
    :type="styleType"
  >
    <slot v-if="$slots.default"> </slot>
    <el-tooltip v-else :effect="props.effect" :content="props.title" :placement="props.placement" :show-after="500">
      <slot>
        {{ props.name }}
      </slot>
    </el-tooltip>
  </el-tag>
</template>

<script setup lang="ts">
const props = defineProps({
  name: {
    type: String,
    default: undefined
  },
  title: {
    type: String,
    default: undefined
  },
  color: {
    type: String,
    default: ""
  },
  styleType: {
    type: String as () => "primary" | "success" | "warning" | "info" | "danger",
    default: "primary"
  },
  // 是否可删除
  closeable: {
    type: Boolean,
    default: false
  },
  closeStatus: {
    type: String as () => "always" | "hover" | "undefined",
    default: "hover"
  },
  // 内容对齐方式
  contentAlign: {
    type: String as () => "left" | "center" | "right" | "body-left",
    default: "center"
  },
  // 提示主题
  effect: {
    type: String,
    default: "light"
  },
  // 提示位置
  placement: {
    type: String as () => "bottom",
    default: "bottom"
  },
  width: {
    type: [String, Number],
    default: ""
  },
  height: {
    type: [String, Number],
    default: ""
  }
});
const convertPX: any = inject("convertPX");
const tagWidth = computed(() => common.fixStylePX(props.width, convertPX));
const tagHeight = computed(() => common.fixStylePX(props.height, convertPX));
const emit = defineEmits(["remove"]);
</script>

<style lang="scss">
$tag-close-color: #ff0000;
.tag.el-tag {
  width: 100%;
  height: 100%;
  min-width: 50px;
  min-height: 30px;
  display: flex;
  margin-top: 5px;
  background-color: #ffffff;
  .el-tag__close {
    display: none;
  }
  .el-tag__content {
    padding: 5px 2px;
    font-size: 18px;
    line-height: 26px;
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: v-bind(color);
    text-align: v-bind(contentAlign) !important;
  }
  &:hover .el-tag__content {
    white-space: normal;
  }
}
@mixin show-close-btn() {
  .el-tag__close,
  .el-tag__close:hover {
    background-color: transparent;
    color: $tag-close-color;
    font-weight: bold;
    font-size: 18px;
    margin: 0 10px 5px 0;
    display: initial;
  }
}
[closeStatus="always"] {
  @include show-close-btn();
}
[closeStatus="hover"] {
  &:hover {
    @include show-close-btn();
  }
}
</style>
