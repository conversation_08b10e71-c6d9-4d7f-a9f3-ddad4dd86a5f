/*
 * FilePath     : \src\views\trainingManagement\types\trainingRecord.ts
 * Author       : 来江禹
 * Date         : 2024-06-04 09:37
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-14 11:51
 * Description  : 培训记录View
 * CodeIterationRecord:
 */
export interface trainingRecordView {
  /**
   * 培训记录ID(主键ID)
   */
  trainingRecordID: string;
  /**
   * 课程ID组
   */
  courseSettingIDArr: Array<string>;
  /**
   * 课程名称
   */
  courseSettingName: string;
  /**
   * 培训地点
   */
  trainingLocation: string;
  /**
   * 培训地点名称
   */
  trainingLocationName: string;
  /**
   * 培训方式
   */
  trainingMethod: string;
  /**
   * 培训方式名称
   */
  trainingMethodName: string;
  /**
   * 培训内容
   */
  trainingContent: string;
  /**
   * 培训目标
   */
  trainingTarget: string;
  /**
   * 培训讲师
   */
  trainingLecturer: string;
  /**
   * 培训讲师名字
   */
  trainingLecturerName: string;
  /**
   * 培训主持人
   */
  trainingHost: string;
  /**
   * 培训主持人名字
   */
  trainingHostName: string;
  /**
   * 开始时间
   */
  startDateTime?: Date;
  /**
   * 结束时间
   */
  endDateTime?: Date;
  /**
   * 状态
   */
  statusCode: string;
  /**
   * 状态名称
   */
  statusName: string;
  /**
   * 考核记录ID
   */
  examinationRecordID: string;
  /**
   * 课程名称
   */
  evaluationID: string;
  /**
   * 护士长问卷评价ID
   */
  headNurseEvaluationID: string;
  /**
   * 科室ID
   */
  departmentID: number;
  /**
   * 医院序号
   */
  hospitalID: string;
  /**
   * 修改人
   */
  modifyEmployeeID: string;
  /**
   * 报名条件内容
   */
  signUpConditionContent: string;
  /**
   * 隐藏条件表达式
   */
  signUpConditionExpression: string;
  /**
   * 报名条件选择内容
   */
  signUpConditions: Array<Record<string, any>>;
  /**
   * 文件列表
   */
  files?: Array<Record<string, any>>;
  /**
   * 文件数据
   */
  fileInfoList?: Array<Record<string, any>>;
  /**
   * 培训群组ID
   */
  trainingClassMainID?: string;
  /**
   * 是否需要签到
   */
  signInFlag: boolean;
  /**
   * 二维码刷新时间（0不刷新）
   */
  qrCodeRefreshTime: number;
}
