/*
 * FilePath     : \nursing-management-web\src\views\trainingManagement\types\timeLineItemView.ts
 * Author       : 张现忠
 * Date         : 2024-09-22 11:52
 * LastEditors  : 张现忠
 * LastEditTime : 2024-09-25 18:18
 * Description  : 时间线项目内容
 * CodeIterationRecord:
 */
/**
 * 表示时间线上的一个项目。
 * 可以是卡片类型或文本类型。
 */
export interface timelineItemView {
  [key: string]: any;
  /**
   * 项目的类型。
   * 'card': 表示这是一个卡片项目，通常包含标题和描述。
   * 'text': 表示这是一个纯文本项目。
   */
  type: "card" | "text";

  /**
   * 项目的时间戳。
   * 通常以字符串形式表示，如 "2018/4/12"。
   */
  timestamp: string;

  /**
   * 内容的放置位置。
   * 'top': 内容显示在时间线上方。
   * 'bottom': 内容显示在时间线下方。
   */
  placement: "top" | "bottom";

  /**
   * 是否将项目居中显示。
   * true: 项目在时间线上居中显示。
   * false: 项目在时间线上靠左或靠右显示。
   */
  center: boolean;

  /**
   * 卡片类型项目的标题。
   * 仅在 type 为 'card' 时使用。
   */
  title?: string;

  /**
   * 卡片类型项目的描述。
   * 仅在 type 为 'card' 时使用。
   */
  description?: string;

  /**
   * 文本类型项目的内容。
   * 仅在 type 为 'text' 时使用。
   */
  content?: string;
  /**
   * 时间线节点
   * true 空心 false 实心
   */
  hollow?: boolean;
  /**
   * 时间线节点类型
   * 'primary' | 'success' | 'warning' | 'danger' | 'info'
   */
  nodeType?: "primary" | "success" | "warning" | "danger" | "info";
}
