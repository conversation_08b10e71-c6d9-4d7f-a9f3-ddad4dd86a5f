<!--
 * FilePath     : \src\components\selector\messageTypeSelector.vue
 * Author       : 张现忠
 * Date         : 2024-10-20 11:22
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-13 10:41
 * Description  : 消息类型选择器
 * CodeIterationRecord: 消息类型选择器，用于选择消息类型，包括系统通知和工作重点
 -->
<template>
  <div class="message-type-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="messageTypeID"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option
        v-for="(item, index) in messageTypeOptions"
        :disabled="optionDisableFunc(item)"
        :key="index"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "消息类型"
  },
  modelValue: {
    type: [String, Array<String>]
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 240
  },
  optionDisableFunc: {
    type: Function,
    default: () => false
  },
  default: {
    type: Boolean,
    default: true
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let messageTypeID = useVModel(props, "modelValue", emits);
let messageTypeOptions = ref<Array<Record<string, any>>>([]);
onMounted(async () => {
  // 如果传值了就使用传的值，否则就使用默认值
  let { list } = toRefs(props);
  if (list?.value && list.value.length && list.value.length > 0) {
    messageTypeOptions.value = list.value;
  } else {
    await getOptions();
  }
  // 如果数据为空，默认第一条项目
  if (!props.default) {
    return;
  }
  if (
    !messageTypeID.value ||
    (messageTypeOptions.value?.length &&
      !messageTypeOptions.value.some((item: Record<string, string>) => item.value === messageTypeID.value))
  ) {
    messageTypeID.value = messageTypeOptions.value[0].value;
    change(messageTypeID.value as string);
  }
});
/**
 * @description 获取消息类型选项
 * @returns {Promise<void>}
 */
const getOptions = async (): Promise<void> => {
  const params: SettingDictionaryParams = {
    settingType: "Common",
    settingTypeCode: "MessageManagement",
    settingTypeValue: "MessageType",
    index: Math.random()
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    messageTypeOptions.value = datas;
  });
};
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, messageTypeOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.message-type-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
