<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\personalInformation\components\table\personalTable.vue
 * Author       : 来江禹
 * Date         : 2023-08-01 09:41
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:22
 * Description  : 人员个人信息表格组件
 * CodeIterationRecord: 3671-作为护理管理人员，我需要护士个人信息档案，以便查看护士相关档案信息
-->
<template>
  <el-table :data="tableData" border stripe>
    <el-table-column
      v-for="(item, index) in headerList"
      :key="index"
      :prop="item.prop"
      :label="item.label"
      :width="convertPX(item.width)"
      :min-width="convertPX(item.minWidth)"
    >
      <template v-slot="scope">
        <div v-if="item.prop === 'controls'">
          <el-tooltip content="删除">
            <i v-permission:B="3" class="iconfont icon-delete" @click.stop="handleDelete(scope.row)"></i>
          </el-tooltip>
        </div>
        <div v-else>
          {{ scope.row[item.prop] }}
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup lang="ts">
import tableHeader from "./tableHeader";
const convertPX: any = inject("convertPX");
const props = defineProps({
  // 表格数据
  tableData: {
    type: Object,
    default: () => {
      return {};
    }
  },
  // 参数配置名称
  headerName: {
    type: String,
    default: () => {
      return "";
    }
  },
  // 父组件传入的删除方法
  onDelete: {
    type: Function,
    required: true
  }
});
const headerList = tableHeader[props.headerName];
const handleDelete = (row: any) => {
  if (typeof props.onDelete === "function") {
    props.onDelete(row);
  }
};
</script>
