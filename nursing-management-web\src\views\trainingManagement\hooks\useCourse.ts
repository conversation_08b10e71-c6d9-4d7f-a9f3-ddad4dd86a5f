/*
 * FilePath     : \nursing-management-web\src\views\trainingManagement\hooks\useCourse.ts
 * Author       : 张现忠
 * Date         : 2024-04-07 11:54
 * LastEditors  : 陈超然
 * LastEditTime : 2025-01-02 09:00
 * Description  : 课程列表数据和相关操作逻辑钩子
 * CodeIterationRecord:
 */

import { ref, onMounted } from "vue";
import { trainingService } from "@/api/trainingService";
import type { courseSetting } from "../types/courseSetting";

/**
 * @description 课程列表数据和相关操作逻辑
 */
export function useCourseLogic() {
  const courseList = ref([]);
  const editingCourse = ref({} as courseSetting);
  const courseTypeID = ref<string>("");
  /**
   * @description 获取课程列表数据
   * @param courseTypeID - 课程类型
   * @returns Promise
   */
  const getCourseSettingList = async (courseTypeValue?: string): Promise<any> => {
    let params = {
      courseTypeID: courseTypeValue ?? courseTypeID.value
    };
    courseList.value = await trainingService.getCourseSettingList(params);
  };

  /**
   * @description 添加课程
   * @param course - 待添加的课程
   * @returns
   */
  const saveCourseSetting = async (course: courseSetting) => {
    let params = { ...course };
    let formData = common.convertObjectToFormData(params);
    await trainingService.saveCourseSetting(formData).then((respBool: any) => {
      if (!respBool) {
        showMessage("error", "保存失败！");
        return false;
      }
    });
    // 保存成功后刷新课程列表
    await getCourseSettingList(courseTypeID.value);
    return true;
  };

  /**
   * @description 编辑课程
   * @param course - 待编辑的课程
   * @return
   */
  const editCourse = (course: courseSetting) => {
    editingCourse.value = course; // 设置当前正在编辑的课程
  };

  /**
   * @description 删除课程
   * @param courseSettingID - 待删除的课程的ID
   * @return
   */
  const deleteCourseSetting = (courseSettingID: string) => {
    deleteConfirm("确定要删除么？", (confirmFlag: Boolean) => {
      if (!confirmFlag) {
        return;
      }
      trainingService.deleteCourseSetting({ courseSettingID: courseSettingID }).then((res: any) => {
        if (res) {
          showMessage("success", "删除成功!");
          getCourseSettingList();
          return;
        }
        showMessage("error", "删除失败!");
      });
    });
  };
  /**
   * @description 搜索课程
   * @param courseName - 待搜索的课程名称
   * @return
   */
  const searchCourseSetting = async (courseName: string) => {
    let params = {
      courseName: courseName,
      courseTypeID: courseTypeID.value
    };
    await trainingService.searchCourseSetting(params).then((res: any) => {
      if (!res) {
        showMessage("error", "查询失败!");
        return;
      }
      if (res.length === 0) {
        showMessage("warning", "没有查询到相关课程!");
        return;
      }
      courseList.value = res;
    });
  };

  // 在组件创建时获取课程列表数据
  onMounted(async () => await getCourseSettingList());

  // 返回从逻辑中获取的响应式数据和方法
  return {
    // 课程类别ID
    courseTypeID,
    // 课程列表数据
    courseList,
    /**
     * @description 获取课程列表数据
     * @param courseTypeValue - 课程类型
     * @returns Promise<void>
     */
    getCourseSettingList,
    /**
     * @description 保存课程
     * @param course - 待保存的课程
     * @returns Promise<void>
     */
    saveCourseSetting,
    /**
     * @description 编辑课程
     * @param course - 待编辑的课程
     * @return
     */
    editCourse,
    /**
     * @description 删除课程
     * @param courseSettingID - 待删除的课程的ID
     * @return
     */
    deleteCourseSetting,
    /**
     * @description 搜索课程
     * @param courseName - 待搜索的课程名称
     * @return
     */
    searchCourseSetting
  };
}
