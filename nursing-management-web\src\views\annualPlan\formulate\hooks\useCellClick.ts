/*
 * FilePath     : \src\views\annualPlan\hooks\useCellClick.ts
 * Author       : 杨欣欣
 * Date         : 2024-02-13 17:07
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-24 14:54
 * Description  : 年度计划-计划制定-表格单元格点击事件
 * CodeIterationRecord:
 */
// eslint-disable-next-line id-match
import type { Ref } from "vue";
export function useCellClick(principalClick: (row: any) => void, tableRef?: Ref<Record<string, any> | undefined>) {
  // eslint-disable-next-line no-spaced-func
  const cellClickCallBackMap = new Map<string, (...args: any[]) => void>([
    ["projectDetailSelector", (row: Record<string, any>) => (row.isProjectDetailSelectorEdit = true)],
    ["interventionSelector", (row: Record<string, any>) => (row.isInterventionSelectorEdit = true)],
    ["isInterventionInputEdit", (row: Record<string, any>) => (row.isInterventionInputEdit = true)],
    ["principal", (row: Record<string, any>) => principalClick(row)]
  ]);
  const drawerRow = ref<Record<string, any>>();
  return {
    drawerRow,
    /**
     * @description: 单元格点击回调
     * @param row 当前行
     * @param column 当前列
     * @param cell 当前单元格
     * @return
     */
    handleCellClick(row: Record<string, any>, column: Record<string, any>, cell: HTMLElement) {
      const identify = common.getDataSet(cell, "data-column-identify", true);
      if (!identify) {
        return;
      }
      drawerRow.value = row;
      // 先尝试转换为数字（月份）
      const month = Number(identify);
      // 若非数字，则执行回调
      if (isNaN(month)) {
        cellClickCallBackMap.get(identify)?.(row);
        return;
      }
      // 月份单元格处理
      row.planMonths ??= [];
      row.planMonths.includes(month) ? row.planMonths.splice(row.planMonths.indexOf(month), 1) : row.planMonths.push(month);
      if (tableRef?.value) {
        nextTick(() => {
          tableRef.value!.toggleRowSelection(toValue(row), true);
        });
      }
    }
  };
}
