<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\qcFormSelector.vue
 * Author       : 郭鹏超
 * Date         : 2023-09-07 16:22
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-02-18 10:42
 * Description  :质控主题下拉框组件
 * CodeIterationRecord:
-->
<template>
  <div class="qc-form-selector">
    <label v-if="label">{{ label + "：" }}</label>
    <el-select clearable :disabled="disabled" class="form-select" v-model="qcFormID">
      <el-option v-for="item in formOptions" :key="item.hierarchicalQCFormID" :label="item.formName" :value="item.hierarchicalQCFormID" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: undefined
  },
  qcLevel: {
    type: String,
    required: true
  },
  formType: {
    type: String,
    default: undefined
  },
  label: {
    type: String
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  departmentID: {
    type: Array<Number>,
    default: undefined
  }
});
onMounted(() => {
  getFormData();
});
watch(
  () => props.qcLevel,
  async () => await getFormData()
);
watch(
  () => props.formType,
  async () => await getFormData()
);
let formOptions = ref<any[]>([]);
const emit = defineEmits(["update:modelValue", "change"]);
const qcFormID = useVModel(props, "modelValue", emit);
const { modelValue: chooseFormID } = props;
const getFormData = async () => {
  if (!chooseFormID) {
    qcFormID.value = undefined;
  }
  let params = {
    level: props.qcLevel,
    formType: props.formType,
    departmentIDs: props.departmentID
  };
  await hierarchicalQCService.getQCFormByLevel(params).then((res: any) => {
    formOptions.value = res;
    // 无数据或者多条数据时，清空已选
    if ((formOptions.value?.length ?? 0) !== 1) {
      return;
    }
    // 一条数据直接赋值
    qcFormID.value = formOptions.value[0]?.hierarchicalQCFormID;
  });
  emit(
    "change",
    formOptions.value.find((option) => option.hierarchicalQCFormID === props.modelValue)
  );
};
watch(qcFormID, (val) => {
  if (!qcFormID.value && chooseFormID) {
    qcFormID.value = chooseFormID;
  }
  emit(
    "change",
    formOptions.value.find((option) => option.hierarchicalQCFormID === val)
  );
});
</script>

<style lang="scss">
.qc-form-selector {
  display: inline-block;
  .form-select {
    width: 300px;
  }
}
</style>
