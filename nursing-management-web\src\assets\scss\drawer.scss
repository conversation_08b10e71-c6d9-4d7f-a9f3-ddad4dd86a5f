.el-overlay {
  z-index: 1800 !important;
}
.el-drawer {
  .el-drawer__header {
    @include l-gradient-bg(right, darken($base-color, 10%), lighten($base-color, 20%));
    height: 30px;
    padding: 5px 0;
    margin-bottom: 10px;
    color: #ffffff;
    padding-left: 14px;
    font-size: 22px;
    .el-drawer__title {
      font-size: initial;
    }
    .el-drawer__close {
      font-size: 28px;
      color: #ff0000 !important;
    }
  }

  .el-drawer__body {
    padding: 10px 30px;
    box-sizing: border-box;
  }

  .el-drawer__footer {
    padding: 10px 30px;
  }
}
