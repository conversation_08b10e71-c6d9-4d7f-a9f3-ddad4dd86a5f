<!--
 * FilePath     : \src\components\truncatedToolTip.vue
 * Author       : 杨欣欣
 * Date         : 2023-09-07 18:07
 * LastEditors  : 苏军志
 * LastEditTime : 2023-09-16 08:57
 * Description  : 内容超出`row`行数时，显示`tooltip`，否则不显示
                  参考：https://github.com/cyounggao/vue3-vite-admin/blob/master/src/components/Text.vue
 * CodeIterationRecord:
-->
<template>
  <div class="vue-text">
    <el-tooltip popper-class="popper" v-if="isShowHover" :content="value" placement="bottom" effect="light">
      <div ref="textRef" :style="textStyle">
        {{ value }}
      </div>
    </el-tooltip>
    <div v-else ref="textRef" :style="textStyle">
      {{ value }}
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  value: {
    type: String,
    default: ""
  },
  row: {
    type: [Number, String],
    default: 0
  },
  width: {
    type: String,
    default: "100%"
  }
});
let isShowHover = ref(false);
const textRef = ref(undefined);
const textStyle = ref<any>({
  width: props.width
});
watchPostEffect(() => {
  if (!textRef.value) {
    return;
  }
  const styles = getComputedStyle(textRef.value);
  let lineHeight = Number(styles.lineHeight.replace("px", ""));
  let height = Number(styles.height.replace("px", ""));
  const rowNumber = Number(props.row);
  if (height > lineHeight * rowNumber) {
    isShowHover.value = true;
    Object.assign(textStyle.value, {
      height: `${lineHeight * rowNumber}px`,
      overflow: "hidden",
      textOverflow: "ellipsis",
      display: "-webkit-box",
      webkitLineClamp: rowNumber,
      webkitBoxOrient: "vertical"
    });
  } else {
    isShowHover.value = false;
    Object.assign(textStyle.value, {
      width: props.width
    });
  }
});
</script>
<style lang="scss">
.popper {
  max-width: 180px;
  max-height: 94px;
  overflow: hidden;
}
</style>
