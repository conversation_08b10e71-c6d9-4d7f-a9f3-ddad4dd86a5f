<!--
 * FilePath     : \src\components\file\imgPreview.vue
 * Author       : 马超
 * Date         : 2024-12-19 16:08
 * LastEditors  : 马超
 * LastEditTime : 2024-12-19 16:13
 * Description  : 
 * CodeIterationRecord: 
 -->
<template>
  <div class="img-preview">
    <div class="title">{{ imgPreviewData.recordTitle }}</div>
    <div class="img-body-main">
      <div class="img-body-main-content" v-for="(item, index) in imgPreviewData.imageList" :key="index">
        <p class="img-content">{{ item.imageTitle }}</p>
        <el-image
          class="body-main-image"
          v-for="(childItem, childIndex) in item.imageSrcList"
          :key="childIndex"
          :src="childItem"
          :preview-src-list="previewSrcList"
          :initial-index="getInitialIndex(index, childIndex)"
        ></el-image>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface ImageItem {
  imageTitle: string;
  imageSrcList: string[];
}
interface ImgPreviewData {
  recordTitle: string;
  imageList: ImageItem[];
}
const props = defineProps({
  imgPreviewData: {
    type: Object as () => ImgPreviewData,
    required: true
  }
});
const previewSrcList = ref<string[]>([]);
watch(
  () => props.imgPreviewData,
  (val) => {
    if (val.imageList) {
      previewSrcList.value = [];
      val.imageList.forEach((element) => {
        previewSrcList.value = previewSrcList.value.concat(element.imageSrcList);
      });
    }
  },
  { deep: true, immediate: true }
);
/**
 * 获取图片索引
 * @param parentIndex 父级索引
 * @param childIndex 子级索引
 * @returns
 */
const getInitialIndex = (parentIndex: number, childIndex: number): number => {
  let index = 0;
  for (let i = 0; i < parentIndex; i++) {
    index += props.imgPreviewData.imageList[i].imageSrcList.length;
  }
  index += childIndex;
  return index;
};
</script>
<style lang="scss">
.img-preview {
  height: 100%;
  .title {
    border-width: 1px 1px 0 1px;
    border-style: solid;
    border-color: $border-color;
    height: 40px;
    padding-left: 20px;
    overflow: auto;
  }
  .img-body-main {
    border: 1px solid $border-color;
    height: calc(100% - 35px);
    overflow: auto;
    .img-body-main-content {
      padding-left: 60px;
      border-bottom: 1px solid $border-color;
      .img-content {
        font-size: 18px;
        color: #000;
      }
      .body-main-image {
        height: 100px;
        width: 120px;
        margin: 0 15px 10px 15px;
        padding: 5px;
        border: 1px solid $border-color;
        &:first-child {
          margin-left: 25px;
        }
      }
    }
  }
}
</style>
