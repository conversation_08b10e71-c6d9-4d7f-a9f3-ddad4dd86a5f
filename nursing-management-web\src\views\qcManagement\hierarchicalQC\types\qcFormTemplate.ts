/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\qcFormTemplate.ts
 * Author       : 郭鹏超
 * Date         : 2023-11-07 15:58
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-13 12:07
 * Description  :三级质控组件参数View
 * CodeIterationRecord:
 */
import type { formListView } from "zhytech-ui";
/**
 * @description: 模板参数
 */
export interface formParam {
  /**
   * 保存模板的方法
   */
  saveFormMethod: Function;
  /**
   * 模板ID
   */
  formID: string | undefined;
  /**
   * 模板质控级别
   */
  formLevel: string | undefined;
  /**
   * 模板类型
   */
  qcFormType: string | undefined;
  /**
   * 模板名称
   */
  formName: string | undefined;
  /**
   * 适用部门
   */
  departmentID: number | undefined;
  /**
   * 主题属性不可编辑
   */
  subjectPropDisabled: boolean | undefined;
  /**
   * 表单列表，用于批量添加表单组件
   */
  formList?: formListView[];
}
