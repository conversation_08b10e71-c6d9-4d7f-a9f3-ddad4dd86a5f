<!--
 * FilePath     : \src\views\post\departmentPost.vue
 * Author       : LX
 * Date         : 2023-08-03 09:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:34
 * Description  : 部门岗位
-->
<template>
  <base-layout class="department-post" :drawerOptions="drawerOptions">
    <template #header>
      <span class="header-label">岗位名称:</span>
      <el-input v-model="post" placeholder="岗位名称" class="post-name" @change="filterPosts"></el-input>
      <file class="right-file" :fileOption="fileOption" @getExcelData="getExcelData"></file>
      <el-button class="add-button" @click="addRecord" v-permission:B="1">新增</el-button>
    </template>
    <el-table
      v-dragSort="{ el: '.el-table__body-wrapper tbody', handleClass: '.drag-handle', callBack: dragEnd }"
      row-key="departmentPostID"
      :data="departmentPosts"
      stripe
      border
      height="100%"
    >
      <el-table-column prop="departmentPostName" label="岗位名称" class-name="drag-handle" :min-width="convertPX(100)"></el-table-column>
      <el-table-column prop="shortName" label="名称简写" :width="convertPX(110)" align="center"></el-table-column>
      <el-table-column prop="postType" label="岗位类型" :width="convertPX(160)" align="center"></el-table-column>
      <el-table-column prop="postNature" label="岗位性质" :width="convertPX(120)" align="center"></el-table-column>
      <el-table-column label="夏季工作时间" :min-width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-html="scope.row.summerTimeRange"></span>
        </template>
      </el-table-column>
      <el-table-column label="冬季工作时间" :min-width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-html="scope.row.winterTimeRange"></span>
        </template>
      </el-table-column>
      <el-table-column prop="attendanceDays" label="考勤天数" :min-width="convertPX(65)" align="center"></el-table-column>
      <el-table-column
        prop="halfDayAttendanceCalcDescription"
        label="半天考勤计算方式"
        :min-width="convertPX(100)"
        align="center"
      ></el-table-column>
      <el-table-column prop="postShift" label="岗位班别" :min-width="convertPX(65)" align="center"></el-table-column>
      <el-table-column label="状态" :width="convertPX(90)" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.statusCode"
            active-value="1"
            inactive-value="0"
            @change="updateDepartmentPost([scope.row], 'StatusCode')"
          />
        </template>
      </el-table-column>
      <el-table-column label="参与排班日统计" :min-width="convertPX(100)" align="center">
        <template #default="scope">
          <el-switch v-model="scope.row.dailyStatisticalMark" @change="updateDailyStatisticalMark(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="参与排班月统计" :min-width="convertPX(100)" align="center">
        <template #default="scope">
          <el-switch v-model="scope.row.monthlyStatisticalMark" @change="updateDepartmentPost([scope.row], 'MonthlyStatisticalMark')" />
        </template>
      </el-table-column>
      <el-table-column label="排班预览" align="center" :min-width="convertPX(65)">
        <template #default="scope">
          <el-tag :style="{ color: scope.row.color ?? '#000000', backgroundColor: scope.row.backGroundColor ?? '#FFFFFF' }" size="large">{{
            scope.row.shortName
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="modifyPerson" label="维护人" :width="convertPX(120)" align="center"></el-table-column>
      <el-table-column label="维护时间" :width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(90)" align="center">
        <template #default="scope">
          <i v-permission:B="3" @click="editRow(scope.row)" class="iconfont icon-edit"></i>
        </template>
      </el-table-column>
    </el-table>

    <!-- 修改和新增弹窗 -->
    <template #drawerContent>
      <div class="form-row">
        <el-form ref="submitRefs" label-width="auto" :model="currRow" :rules="rules">
          <el-form-item label="岗位：" prop="postID">
            <!-- 岗位名称 -->
            <post-selector label="" v-model="currRow.postID" @change="changePost"></post-selector>
          </el-form-item>
          <el-form-item label="岗次：" prop="shiftValue">
            <!-- 岗次 -->
            <el-input-number v-model="currRow.shiftValue" placeholder="请输入岗次" :min="0"> </el-input-number>
          </el-form-item>
          <el-form-item label="名称简写：" prop="briefName">
            <el-input v-model="currRow.briefName" placeholder="名称简写"> </el-input>
          </el-form-item>
          <el-form-item label="考勤天数：" prop="attendanceDays">
            <el-input-number v-model.number="currRow.attendanceDays" placeholder="考勤天数" :step="0.5" :min="0" :max="3">
            </el-input-number>
          </el-form-item>
          <el-form-item label="半天考勤计算方式：" prop="halfDayAttendanceCalc">
            <el-select v-model="currRow.halfDayAttendanceCalc" placeholder="请选择半天考勤计算方式" :disabled="halfDayDisabled">
              <el-option
                v-for="(halfDayAttendanceCalc, index) in halfDayAttendanceCalcList"
                :key="index"
                :label="halfDayAttendanceCalc.halfDayAttendanceCalcName"
                :value="halfDayAttendanceCalc.halfDayAttendanceCalcID"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="岗位班别：" prop="postShiftID">
            <el-select v-model="currRow.postShiftID" placeholder="请选择岗位班别">
              <el-option
                v-for="(postShift, index) in postShiftList"
                :key="index"
                :label="postShift.postShiftName"
                :value="postShift.postShiftID"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否启用：" prop="statusCode">
            <el-switch v-model="currRow.statusCode" active-value="1" inactive-value="0" />
          </el-form-item>
          <el-form-item label="参与排班日统计：">
            <el-switch v-model="currRow.dailyStatisticalMark" />
          </el-form-item>
          <el-form-item label="参与排班月统计：">
            <el-switch v-model="currRow.monthlyStatisticalMark" />
          </el-form-item>
          <el-form-item label="夏季工作时间1：">
            <el-time-select type="datetime" v-model="currRow.summerBeginTime1" start="00:00" step="00:30" end="23:59" />
            <el-time-select type="datetime" v-model="currRow.summerEndTime1" start="00:00" step="00:30" end="23:59" />
          </el-form-item>
          <el-form-item label="夏季工作时间2：">
            <el-time-select type="datetime" v-model="currRow.summerBeginTime2" start="00:00" step="00:30" end="23:59" />
            <el-time-select type="datetime" v-model="currRow.summerEndTime2" start="00:00" step="00:30" end="23:59" />
          </el-form-item>
          <el-form-item label="冬季工作时间1：">
            <el-time-select type="datetime" v-model="currRow.winterBeginTime1" start="00:00" step="00:30" end="23:59" />
            <el-time-select type="datetime" v-model="currRow.winterEndTime1" start="00:00" step="00:30" end="23:59" />
          </el-form-item>
          <el-form-item label="冬季工作时间2：">
            <el-time-select type="datetime" v-model="currRow.winterBeginTime2" start="00:00" step="00:30" end="23:59" />
            <el-time-select type="datetime" v-model="currRow.winterEndTime2" start="00:00" step="00:30" end="23:59" />
          </el-form-item>
          <el-form-item label="排班显示颜色：" prop="color">
            <el-color-picker v-model="currRow.color"></el-color-picker>
          </el-form-item>
          <el-form-item label="排班显示背景色：" prop="backGroundColor">
            <el-color-picker v-model="currRow.backGroundColor"></el-color-picker>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import { settingDictionaryService } from "@/api/settingDictionaryService";
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
const submitRefs = ref({}) as any;
const rules = reactive({
  postID: [{ required: true, message: "请选择岗位", trigger: "change" }],
  briefName: [{ required: true, message: "请选输入名称简写", trigger: "change" }],
  attendanceDays: [{ required: true, message: "请输入考勤天数", trigger: "change" }],
  halfDayAttendanceCalc: [{ required: true, message: "请选择半天考勤计算方式", trigger: "change" }]
});
const currRow = ref<Record<string, any>>({});
const postList = ref<Record<string, any>[]>([]);
const post = ref<string>("");
const departmentPosts = ref<Record<string, any>[]>([]);
const departmentPostList = ref<Record<string, any>[]>([]);
const postShiftList = ref<Record<string, any>[]>([]);
const halfDayAttendanceCalcList = ref<Record<string, any>[]>([]);
const halfDayDisabled = ref<boolean>(false);
const defaultHalfDayAttendanceCalc: string = "1";
// 监听修改工作时间
watch(
  [
    () => currRow.value.summerBeginTime1,
    () => currRow.value.summerEndTime1,
    () => currRow.value.summerBeginTime2,
    () => currRow.value.summerEndTime2,
    () => currRow.value.winterBeginTime1,
    () => currRow.value.winterEndTime1,
    () => currRow.value.winterBeginTime2,
    () => currRow.value.winterEndTime2
  ],
  () => {
    // 如果冬季或这夏季都有两个时间段，半天考勤计算方式设为默认，且不可修改
    if (
      currRow.value.summerBeginTime1 &&
      currRow.value.summerEndTime1 &&
      currRow.value.summerBeginTime2 &&
      currRow.value.summerEndTime2 &&
      currRow.value.winterBeginTime1 &&
      currRow.value.winterEndTime1 &&
      currRow.value.winterBeginTime2 &&
      currRow.value.winterEndTime2
    ) {
      currRow.value.halfDayAttendanceCalc = defaultHalfDayAttendanceCalc;
      halfDayDisabled.value = true;
    } else {
      halfDayDisabled.value = false;
    }
  }
);
let exportExcelData = ref<any>([
  {
    departmentName: ""
  }
]);

let templateColumn = ref<{}>({
  departmentName: "部门名称",
  postID: "岗位编码",
  departmentPostName: "部门岗位名称",
  shortName: "名称简写",
  attendanceDays: "考勤天数",
  halfDayAttendanceCalc: "半天考勤计算方式",
  postShift: "岗位班别",
  statusCode: "状态(0停用、1启用)",
  summerBeginTime1: "夏季岗位1工作开始时间(格式:08:00)",
  summerEndTime1: "夏季岗位1工作结束时间(格式:08:00)",
  summerBeginTime2: "夏季岗位2工作开始时间(格式:08:00)",
  summerEndTime2: "夏季岗位2工作结束时间(格式:08:00)",
  winterBeginTime1: "冬季岗位1工作开始时间(格式:08:00)",
  winterEndTime1: "冬季岗位1工作结束时间(格式:08:00)",
  winterBeginTime2: "冬季岗位2工作开始时间(格式:08:00)",
  winterEndTime2: "冬季岗位2工作结束时间(格式:08:00)"
});

onMounted(() => {
  getDepartmentPostList();
  getPostList();
  getPostShiftList();
  getHalfDayAttendanceCalcList();
});

let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    await saveAndEditRecord();
  },
  cancel: async () => (drawerOptions.value.showDrawer = false)
});
/**
 * description: 获取岗位字典
 */
const getPostList = () => {
  // 通过hooks从数据库获取数据
  let { getPostData } = useDictionaryData();
  getPostData().then((datas) => {
    datas.forEach((post) => {
      postList.value.push({
        postID: post.value,
        departmentPostName: post.label
      });
    });
  });
};

/**
 * description: 获取岗位班别字典
 */
const getPostShiftList = () => {
  let params = {
    settingType: "PositionManagement",
    settingTypeCode: "PostShift"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    datas.forEach((postShift: Record<string, any>) => {
      postShiftList.value.push({
        postShiftID: postShift.value,
        postShiftName: postShift.label
      });
    });
  });
};
const getHalfDayAttendanceCalcList = () => {
  let params = {
    settingType: "PositionManagement",
    settingTypeCode: "HalfDayAttendanceCalc"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    datas.forEach((data: Record<string, any>) => {
      halfDayAttendanceCalcList.value.push({
        halfDayAttendanceCalcID: data.value,
        halfDayAttendanceCalcName: data.label
      });
    });
  });
};
/**
 * @description:获取部门岗位清单
 */
const getDepartmentPostList = () => {
  let param = { departmentID: userStore.departmentID };
  postService.getDepartmentPostList(param).then((res: any) => {
    if (res) {
      departmentPostList.value = res;
      departmentPosts.value = res;
    }
  });
};

/**
 * @description: 拖拽排序
 * @param newIndex 新下标
 * @param oldIndex 旧下标
 */
const dragEnd = (newIndex: number, oldIndex: number) => {
  // 互换位置
  const row = departmentPosts.value.splice(oldIndex, 1)[0];
  departmentPosts.value.splice(newIndex, 0, row);
  departmentPosts.value.forEach((row, index) => (row.sort = index + 1));
  // 更新Sort字段
  updateDepartmentPost(departmentPosts.value, "Sort");
};

/**
 * @description: 更新 参与排班日统计，同组post一起更新
 * @param row 当前行数据
 */
const updateDailyStatisticalMark = (row: Record<string, any>) => {
  let rows = [row];
  const postID = row.postID;
  departmentPosts.value.forEach((departmentPost) => {
    if (departmentPost.postID === postID && departmentPost.departmentPostID !== row.departmentPostID) {
      departmentPost.dailyStatisticalMark = row.dailyStatisticalMark;
      rows.push(departmentPost);
    }
  });
  updateDepartmentPost(rows, "DailyStatisticalMark");
};

/**
 * @description: 更新部门岗位信息
 * @param rows 要更新的部门岗位
 * @param field 要更新的字段
 */
const updateDepartmentPost = (rows: Record<string, any>[], field: string) => {
  let param = {
    departmentPosts: rows,
    field: field
  };
  // 此处成功与否都无需做特殊处理
  postService.updateDepartmentPost(param);
};

/**
 * @description: 过滤部门岗位
 */
const filterPosts = () => {
  if (post.value.length > 0) {
    departmentPosts.value = departmentPostList.value.filter((item: any) => item.departmentPostName.indexOf(post.value) > -1);
  } else {
    departmentPosts.value = departmentPostList.value;
  }
};

/**
 * description: 新增记录
 * return {*}
 */
const addRecord = () => {
  drawerOptions.value.drawerTitle = "新增部门岗位";
  drawerOptions.value.showDrawer = true;
  currRow.value = {
    // 状态默认启用
    statusCode: "1",
    // 出勤天数默认1
    attendanceDays: 1,
    // 半天考勤计算方式默认 考勤天数/2
    halfDayAttendanceCalc: defaultHalfDayAttendanceCalc
  };
};

/**
 * description: 保存和修改记录
 * return {*}
 * param {*} flag
 */
const saveAndEditRecord = async () => {
  let params: any = {
    postID: currRow.value.postID,
    shiftValue: currRow.value.shiftValue,
    briefName: currRow.value.briefName,
    departmentID: userStore.departmentID,
    statusCode: currRow.value.statusCode ?? false,
    dailyStatisticalMark: currRow.value.dailyStatisticalMark ?? false,
    monthlyStatisticalMark: currRow.value.monthlyStatisticalMark ?? false,
    addEmployeeID: userStore.employeeID,
    departmentPostID: currRow.value.departmentPostID,
    summerBeginTime1: currRow.value.summerBeginTime1,
    summerEndTime1: currRow.value.summerEndTime1,
    summerBeginTime2: currRow.value.summerBeginTime2,
    summerEndTime2: currRow.value.summerEndTime2,
    winterBeginTime1: currRow.value.winterBeginTime1,
    winterEndTime1: currRow.value.winterEndTime1,
    winterBeginTime2: currRow.value.winterBeginTime2,
    winterEndTime2: currRow.value.winterEndTime2,
    attendanceDays: currRow.value.attendanceDays,
    halfDayAttendanceCalc: currRow.value.halfDayAttendanceCalc,
    postShiftID: currRow.value.postShiftID,
    color: currRow.value.color,
    backGroundColor: currRow.value.backGroundColor
  };
  await postService.saveDepartmentPost(params).then((respFlag: any) => {
    let showMess = "操作失败";
    if (respFlag === "Add") {
      showMess = "新增成功";
    }
    if (respFlag === "modify") {
      showMess = "修改成功";
    }

    if (respFlag) {
      showMessage("success", showMess);
      getDepartmentPostList();
    } else {
      showMessage("warning", showMess);
    }
    drawerOptions.value.showDrawer = false;
    post.value = "";
  });
};

/**
 * description:修改记录
 * param {*} row
 * return {*}
 */
const editRow = (row: any) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  currRow.value = {};
  currRow.value.postID = row.postID;
  drawerOptions.value.drawerTitle = "编辑岗位";
  drawerOptions.value.showDrawer = true;
  /**
   * description: 岗次
   * param {*}
   * return {*}
   */
  let lastChar = row.departmentPostName?.match(/\d+$/);
  currRow.value.shiftValue = lastChar ? Number(lastChar[0]) : undefined;
  currRow.value.departmentPostID = row.departmentPostID;
  currRow.value.briefName = row.shortName;
  let summaryArr = replaceStr(row.summerTimeRange);
  let winterArr = replaceStr(row.winterTimeRange);
  currRow.value.summerBeginTime1 = summaryArr[0];
  currRow.value.summerEndTime1 = summaryArr[1];
  currRow.value.summerBeginTime2 = summaryArr[2];
  currRow.value.summerEndTime2 = summaryArr[3];
  currRow.value.winterBeginTime1 = winterArr[0];
  currRow.value.winterEndTime1 = winterArr[1];
  currRow.value.winterBeginTime2 = winterArr[2];
  currRow.value.winterEndTime2 = winterArr[3];
  currRow.value.statusCode = row.statusCode;
  currRow.value.dailyStatisticalMark = row.dailyStatisticalMark;
  currRow.value.monthlyStatisticalMark = row.monthlyStatisticalMark;
  currRow.value.addEmployeeID = userStore.employeeID;
  currRow.value.attendanceDays = row.attendanceDays;
  currRow.value.postShiftID = row.postShiftID;
  currRow.value.color = row.color;
  currRow.value.backGroundColor = row.backGroundColor;
  currRow.value.halfDayAttendanceCalc = row.halfDayAttendanceCalc;
  currRow.value.halfDayAttendanceCalcDescription = row.halfDayAttendanceCalcDescription;
};

/**
 * description: 分割时间字符串
 * param {*} param
 * return {*}
 */
const replaceStr = (param: String) => {
  let timePairs = param.split("<br />");
  let timeSegments: String[] = [];

  timePairs.forEach((pair) => {
    timeSegments.push(...pair.split("-"));
  });

  return timeSegments;
};

/**
 * description:  Excel 导出参数
 * param {*}
 * return {*}
 */
const exportExcelOption = reactive<ExportExcelView[]>([
  {
    buttonName: "导出模板",
    fileName: "部门岗位",
    sheetName: "部门岗位",
    columnData: templateColumn,
    tableData: exportExcelData.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位",
    sheetName: "岗位字典",
    columnData: {
      postID: "岗位编码",
      departmentPostName: "部门岗位名称"
    },
    tableData: postList.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位",
    sheetName: "岗位班别字典",
    columnData: {
      postShiftID: "岗位班别编码",
      postShiftName: "部门岗位班别名称"
    },
    tableData: postShiftList.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位",
    sheetName: "半天考勤计算方式字典",
    columnData: {
      halfDayAttendanceCalcID: "半天考勤计算方式编码",
      halfDayAttendanceCalcName: "半天考勤计算方式名称"
    },
    tableData: halfDayAttendanceCalcList.value
  }
]);

/**
 * description:  Excel 导入参数
 * param {*}
 * return {*}
 */
const importExcelOption = reactive<ImportExcelView>({
  columnData: templateColumn,
  buttonName: "导入数据"
});

/**
 * description: Excel导入数据处理
 * param {*} data
 * return {*}
 */
const getExcelData = async (importData: any) => {
  if (importData.length === 0) {
    return;
  }
  let params: any[] = [];
  importData.forEach((importItem: any) => {
    let param: any = {
      postID: importItem.postID,
      shiftValue: extractNumbers(importItem.departmentPostName),
      briefName: importItem.shortName,
      departmentID: userStore.departmentID,
      attendanceDays: importItem.attendanceDays,
      halfDayAttendanceCalc: importItem.halfDayAttendanceCalc,
      postShiftID: importItem.postShiftID,
      statusCode: importItem.statusCode,
      dailyStatisticalMark: false,
      monthlyStatisticalMark: false,
      addEmployeeID: userStore.employeeID,
      summerBeginTime1: importItem.summerBeginTime1 ? datetimeUtil.formatDate(importItem.summerBeginTime1, "hh:mm") : "",
      summerEndTime1: importItem.summerEndTime1 ? datetimeUtil.formatDate(importItem.summerEndTime1, "hh:mm") : "",
      summerBeginTime2: importItem.summerBeginTime2 ? datetimeUtil.formatDate(importItem.summerBeginTime2, "hh:mm") : "",
      summerEndTime2: importItem.summerEndTime2 ? datetimeUtil.formatDate(importItem.summerEndTime2, "hh:mm") : "",
      winterBeginTime1: importItem.winterBeginTime1 ? datetimeUtil.formatDate(importItem.winterBeginTime1, "hh:mm") : "",
      winterEndTime1: importItem.winterEndTime1 ? datetimeUtil.formatDate(importItem.winterEndTime1, "hh:mm") : "",
      winterBeginTime2: importItem.winterBeginTime2 ? datetimeUtil.formatDate(importItem.winterBeginTime2, "hh:mm") : "",
      winterEndTime2: importItem.winterEndTime2 ? datetimeUtil.formatDate(importItem.winterEndTime2, "hh:mm") : "",
      color: "#000000",
      backGroundColor: "#FFFFFF"
    };
    params.push(param);
  });

  if (params.length === 0) {
    return;
  }

  await postService.batchSaveDepartmentPost(params).then(() => {
    getDepartmentPostList();
  });
};

/**
 * description: 拆分字符串
 * param {*} param
 * return {*}
 */
const extractNumbers = (param: String) => {
  // 使用正则表达式匹配所有数字
  let match = param?.match(/\d+/g);
  // 如果找到数字，则返回数组，否则返回空数组
  return match ? match[0] : undefined;
};

/**
 * description: file组件参数
 * param {*}
 * return {*}
 */
const fileOption = reactive<FilePropsView>({
  typeArr: ["exportExcel", "importExcel"],
  exportExcelOption,
  importExcelOption
});

/**
 * description: 新增岗次触发函数
 * param {*}
 * return {*}
 */
const changePost = () => {
  currRow.value.shiftValue = undefined;
  currRow.value.briefName = undefined;
  /** 处理默认岗次 */
  let shiftValueArr: any[] = [];
  departmentPosts.value.forEach((departmentPost) => {
    if (departmentPost.postID === currRow.value.postID && extractNumbers(departmentPost.departmentPostName)) {
      shiftValueArr.push(extractNumbers(departmentPost.departmentPostName));
    }
  });
  currRow.value.shiftValue = shiftValueArr.length === 0 ? 1 : Math.max(...shiftValueArr) + 1;
};
</script>

<style lang="scss">
.department-post {
  width: 100%;
  .header-label {
    margin-left: 20px;
  }
  .post-name {
    width: 240px;
    margin-left: 10px;
  }
  .right-file {
    display: block;
    float: right;
  }
  .drag-handle {
    cursor: n-resize;
  }
  .form-row {
    justify-content: flex-start;
    .el-form-item {
      .el-select {
        width: 150px;
      }
      .el-input {
        width: 150px;
      }
    }
  }

  .button-style {
    margin-right: 10px;
    margin-top: 18px;
    float: right;

    border-color: #14d8d8 !important;
    color: #ffffff !important;
  }
  .add-button {
    background: #14d8d8 !important;
  }
}
</style>
