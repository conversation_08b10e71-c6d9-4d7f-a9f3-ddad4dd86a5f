<!--
 * FilePath     : \src\views\trainingManagement\trainingLearner\index.vue
 * Author       : 张现忠
 * Date         : 2024-09-22 11:18
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:04
 * Description  : 人员培训管理页面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="training-learner" :drawerOptions="drawerOptions">
    <template #header>
      <department-selector v-permission:DL="13" v-model="departmentID" label="培训部门" showWholeHospital @change="filterData" />
      <employee-selector
        v-permission:DL="13"
        v-model="trainEmployeeID"
        :departmentID="departmentID"
        label="培训人"
        :filterable="true"
        :showAll="true"
        @change="filterData"
      />
    </template>
    <!-- table部分 -->
    <el-table :data="trainingTableData" v-bind="tableProps" height="100%">
      <el-table-column v-for="col in columns" :key="col.prop" v-bind="col" :min-width="convertPX(col.cvtPxWidth)">
        <template #default="scope" v-if="col.dateFormat">
          <span v-formatTime="{ value: scope.row[col.prop!], type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(250)" align="center">
        <template #default="scope">
          <el-tooltip content="学习">
            <i @click="study()" class="iconfont icon-learn"></i>
          </el-tooltip>
          <el-tooltip content="查看培训时间线">
            <i @click="handleTimelineView(scope.row)" class="iconfont icon-timeline"></i>
          </el-tooltip>
          <el-tooltip :content="!scope.row.monitorFlag ? '设置班长' : '取消班长'">
            <i
              v-permission:B="30"
              @click="handleTraineeMonitor(scope.row)"
              :class="['iconfont icon-squad-leader', { 'icon-squad-leader__isMonitor': scope.row.monitorFlag }]"
            ></i>
          </el-tooltip>
          <el-tooltip content="点赞">
            <i v-permission:B="31" @click="handleThumbsUp()" class="iconfont icon-like-it"></i>
          </el-tooltip>
          <el-tooltip content="学习评价">
            <i v-permission:B="32" @click="handleComment(scope.row, true)" class="iconfont icon-evaluation"></i>
          </el-tooltip>
          <el-tooltip content="培训评价">
            <i @click="handleComment(scope.row, false)" class="iconfont icon-be-evaluated"></i>
          </el-tooltip>
          <el-tooltip content="建议">
            <i @click="courseRecommendations(scope.row)" class="iconfont icon-ecommend"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-permission:B="4" @click="handleDelete(scope.row)" class="iconfont icon-delete"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 抽屉弹窗部分 -->
    <template #drawerContent>
      <!-- 时间线组件 -->
      <el-timeline v-if="drawerOptions.drawerName == 'timeline'">
        <el-timeline-item
          v-for="(item, index) in timelineItems"
          :key="index"
          :timestamp="item.timestamp"
          :placement="item.placement"
          :center="item.center"
          :hollow="item.hollow"
          :color="item.color"
          :type="item.nodeType"
        >
          <template v-if="item.type === 'text'">
            <h3>{{ item.title }}</h3>
            <span>{{ item.description }}</span>
          </template>
        </el-timeline-item>
      </el-timeline>
      <zhy-form-renderer
        v-if="drawerOptions.drawerName == 'learnerEvaluation'"
        ref="rendererForm"
        :formData="evaluationFormData"
        watching
        @change="getDetails"
      />
      <zhy-form-renderer
        v-if="drawerOptions.drawerName == 'teacherEvaluation'"
        ref="rendererForm"
        :formData="evaluationFormData"
        watching
        @change="getDetails"
      />
      <el-form
        v-if="drawerOptions.drawerName == 'courseRecommendations'"
        label-width="150"
        :model="recommendationsFormData"
        :rules="rules"
        ref="courseRecommendationsRefs"
      >
        <el-form-item label="课程满意度（100分）：" prop="courseSatisfaction">
          <el-input-number v-model="recommendationsFormData.courseSatisfaction" :min="1" :max="100" />
        </el-form-item>
        <el-form-item label="课程建议：" prop="courseRecommendations">
          <el-input
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 10 }"
            v-model="recommendationsFormData.courseRecommendations"
            placeholder="请输入建议"
          ></el-input>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>

<script lang="ts" setup>
import type { dynamicFormData } from "zhytech-ui";
import type { traineeView } from "../types/traineeView";
import { useTrainees } from "../hooks/useTrainees";
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const { userStore } = useStore() as any;
const {
  columns,
  tableProps,
  drawerOptions,
  timelineItems,
  getTraineeList,
  deleteTrainee,
  setTraineeMonitor,
  getTrainTimeline,
  setDrawerSaveEvent,
  setTimelineItems,
  openDrawer,
  getEvaluationFormView,
  saveEvaluationData,
  saveCourseRecommendations
} = useTrainees();
// table 数据
const trainingData = ref<traineeView[]>([]);
const trainingTableData = ref<traineeView[]>([]);
const departmentID = ref<number>(userStore.departmentID);
const trainEmployeeID = ref<string>();
const currTrainee = ref<traineeView>();
const { query }: any = useRoute();
const evaluationType = ref<string>();
const evaluationFormData = ref<dynamicFormData<Record<string, any>>>({
  datas: {},
  components: [],
  props: {}
});
const saveEvaluationView = ref<Record<string, any>>({});
const recommendationsFormData = ref<Record<string, any>>({});
const courseRecommendationsRefs = ref<any>();
// 表单验证
const rules = ref({
  courseSatisfaction: [{ required: true, message: "请输入课程满意度（100分）：", trigger: "blur" }],
  courseRecommendations: [{ required: true, message: "请输入建议", trigger: "blur" }]
});
// 挂载的时候刷新数据
onMounted(() => {
  refreshData();
});
/**
 * @description: 刷新获取学员信息
 * @param searchView 查询条件（不传值时，直接获取最新的查询状态）
 * @returns
 */
const refreshData = async () => {
  drawerOptions.value.showDrawer = false;
  trainingData.value = await getTraineeList(query.trainingRecordID);
  await filterData();
};
/**
 * @description: 删除学员信息
 * @param row 点击的学员信息行
 * @returns
 */
const handleDelete = async (row: traineeView) => {
  confirmBox("确定要删除当前学员的培训信息么？", proxy.$t("tip.systemTip"), async (flag: boolean) => {
    // 实现删除逻辑
    flag && (await deleteTrainee(row)) && refreshData();
  });
};
/**
 * @description: 查看学员培训流程
 * @param row 点击的学员信息行
 * @returns
 */
const handleTimelineView = async (row: traineeView) => {
  let timelineItems = await getTrainTimeline(row);
  // 设置时间线内容
  setTimelineItems(timelineItems ?? []);
  openDrawer("timeline", "40%");
};
/**
 * @description: 点评触发逻辑
 * @param row 点击的学员信息行数据
 * @param isForTrainee true:老师对学生的结业评价 false:学生对老师的评价
 * @returns
 */
const handleComment = async (row: traineeView, isForTrainee: boolean) => {
  isForTrainee ? (evaluationType.value = "1") : (evaluationType.value = "2");
  currTrainee.value = common.clone(toRaw(row));
  // 设置评价保存逻辑
  saveEvaluationView.value.trainingLearnerID = row.trainingLearnerID;
  saveEvaluationView.value.trainingRecordID = query.trainingRecordID;
  saveEvaluationView.value.evaluationType = evaluationType.value;
  saveEvaluationView.value.beEvaluationEmployeeID = isForTrainee ? row.employeeID : "";
  setDrawerSaveEvent(async () => await saveEvaluationData(saveEvaluationView.value), refreshData);
  evaluationFormData.value = await getEvaluationFormView(query.trainingRecordID, evaluationType.value);
  openDrawer(isForTrainee ? "teacherEvaluation" : "learnerEvaluation", "100%", true);
};
/**
 * @description: 设置班长
 * @param row 点击的学员信息行
 * @returns
 */
const handleTraineeMonitor = async (row: traineeView) => {
  let currRow = common.clone(toRaw(row));
  // 调整班长头衔
  currRow.monitorFlag = !currRow.monitorFlag;
  (await setTraineeMonitor(currRow)) && refreshData();
};
/**
 * @description: 处理点赞（老师对学生的标签点赞）
 * @return
 */
const handleThumbsUp = async () => {
  showMessage("info", "暂不开放");
};
/**
 * @description: 获取评价明细内容
 * @param data
 * @return
 */
const getDetails = (data: Record<string, any>[]) => {
  saveEvaluationView.value.detailList = data;
};
/**
 * @description: 培训学习
 * @param row 学员信息行
 * @return
 */
const study = async () => {
  showMessage("info", "暂不开放");
};
/**
 * @description: 课程满意度及建议
 * @param row 学员信息行
 * @return
 */
const courseRecommendations = async (row: Record<string, any>) => {
  recommendationsFormData.value = row ? common.clone(row) : {};
  setDrawerSaveEvent(
    async () => await saveCourseRecommendations(courseRecommendationsRefs.value, recommendationsFormData.value),
    refreshData
  );
  openDrawer("courseRecommendations", "40%", true);
};
/**
 * @description: 筛选数据
 */
const filterData = async () => {
  trainingTableData.value = trainingData.value.filter(
    (item: any) =>
      (departmentID.value === item.departmentID || departmentID.value === 999999) &&
      (!trainEmployeeID.value || trainEmployeeID.value === item.employeeID)
  );
};
</script>

<style lang="scss">
.training-learner {
  width: auto;
  height: 100%;
  .icon-squad-leader {
    &__isMonitor {
      color: #ff0000;
    }
  }
}
</style>
