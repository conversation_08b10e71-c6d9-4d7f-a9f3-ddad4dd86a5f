<!--
 * FilePath     : \src\views\scheduling\adjustSchedule.vue
 * Author       : 张现忠
 * Date         : 2023-08-23 16:02
 * LastEditors  : 马超
 * LastEditTime : 2025-03-02 11:07
 * Description  : 调班申请功能
 * CodeIterationRecord:
-->
<template>
  <base-layout class="adjust-schedule" :drawerOptions="drawerOptions">
    <template #header>
      <span v-permission:S="12">
        <span class="header-label">显示全部</span>
        <el-switch v-model="showAllSwitch" @change="getAdjustSchedule" />
      </span>
      <span class="header-label">申请日期：</span>
      <el-date-picker
        class="date-range-picker"
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        @change="filterRecords"
      />
      <el-button class="add-button" @click="addOrModify()" v-permission:B="1">新增</el-button>
    </template>
    <el-table :data="showRecords" border stripe height="100%">
      <el-table-column prop="addEmployee" label="申请人" align="center" :min-width="convertPX(110)" />
      <el-table-column label="调班日期" align="center" :min-width="convertPX(120)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.adjustDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="adjustNoonTypeName" label="调班午别" align="center" :min-width="convertPX(120)" />
      <el-table-column prop="autoSchedule" label="自动排班" align="center" :min-width="convertPX(60)">
        <template v-slot="{ row }">
          <span>{{ row.autoSchedule ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="adjustDepartmentPost" label="调班岗" align="center" :min-width="convertPX(130)" />
      <el-table-column prop="targetEmployee" label="换班人" align="center" :min-width="convertPX(110)" />
      <el-table-column label="换班日期" align="center" :min-width="convertPX(120)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.targetDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="targetNoonTypeName" label="换班午别" align="center" :min-width="convertPX(110)" />
      <el-table-column prop="targetDepartmentPost" label="换班岗" align="center" :min-width="convertPX(130)" />
      <el-table-column label="状态" align="center" :width="convertPX(145)">
        <template v-slot="scope">
          <el-tag :type="getApproveStatusTag(scope.row.statusCode)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" :min-width="convertPX(125)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :label="i18nText.operation" :width="convertPX(120)" align="center">
        <template #default="scope">
          <el-tooltip :content="i18nText.modify">
            <i
              @click="addOrModify(scope.row)"
              v-visibilityHidden="getButtonShow(scope.row)"
              class="iconfont icon-edit"
              v-permission:B="3"
            ></i>
          </el-tooltip>

          <el-tooltip :content="i18nText.revoke">
            <i
              v-if="showRevokeFlag"
              class="iconfont icon-revoke"
              v-permission:B="26"
              v-visibilityHidden="showRevokeButton(scope.row.statusCode, scope.row.addEmployeeID)"
              @click="startRevoke(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i
              @click="deleteRow(scope.row)"
              v-visibilityHidden="getButtonShow(scope.row)"
              class="iconfont icon-delete"
              v-permission:B="4"
            ></i>
          </el-tooltip>
          <el-tooltip content="提交审批">
            <i
              v-permission:B="3"
              v-visibilityHidden="!scope.row.approveFlag"
              @click="manualSubmissionApprove('AdjustSchedule', scope.row.adjustScheduleRecordID, getAdjustSchedule)"
              class="iconfont icon-save"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName === 'addOrModify'"
        ref="submitRefs"
        class="form-style"
        label-width="auto"
        :model="currRow"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <el-form-item label="申请人：" prop="addEmployeeID">
          <employee-selector label="" v-model="currRow.addEmployeeID" disabled :departmentID="userStore.departmentID" :width="386" />
        </el-form-item>
        <el-form-item label="调班日期：" prop="adjustDate">
          <el-date-picker
            v-model="currRow.adjustDate"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            placeholder="选择日期"
          ></el-date-picker>
          <noon-selector
            v-model="currRow.adjustNoonType"
            :showBlankOptions="true"
            :clearable="true"
            :label="''"
            @select="changeAdjustNoon"
          ></noon-selector>
        </el-form-item>
        <el-form-item label="调班岗：" prop="adjustDepartmentPost">
          <el-input class="department-post-input" v-model="currRow.adjustDepartmentPost" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="换班人：" prop="targetEmployeeID">
          <employee-selector
            label=""
            v-model="currRow.targetEmployeeID"
            :departmentID="userStore.departmentID"
            :width="386"
            @select="getTargetEmployeeName"
          />
        </el-form-item>
        <el-form-item label="换班日期：" prop="targetDate">
          <el-date-picker
            v-model="currRow.targetDate"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            placeholder="选择日期"
          ></el-date-picker>
          <noon-selector
            v-model="currRow.targetNoonType"
            :showBlankOptions="true"
            :clearable="true"
            :label="''"
            @select="changeTargetNoon"
          ></noon-selector>
        </el-form-item>
        <el-form-item label="换班岗：" prop="targetDepartmentPost">
          <el-input class="department-post-input" v-model="currRow.targetDepartmentPost" :disabled="true"></el-input>
        </el-form-item>
        <!-- 新添加的自动调整排班开关 -->
        <el-form-item label="自动调整排班：" prop="autoSchedule">
          <el-switch
            v-model="currRow.autoSchedule"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="true"
            :inactive-value="false"
          ></el-switch>
        </el-form-item>
        <el-form-item label="申请原因：" prop="reason">
          <el-input type="textarea" v-model="currRow.reason" maxlength="200" show-word-limit rows="3" />
        </el-form-item>
      </el-form>
      <revoke-approval
        v-if="drawerOptions.drawerName === 'revoke'"
        v-model:drawer-options="drawerOptions"
        :revokeFormData="revokeFormData!"
        :id="currRow.adjustScheduleRecordID"
        :isSource="true"
        @refreshData="getAdjustSchedule()"
      />
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import revokeApproval from "@/views/approveManagement/approveRecord/components/revokeApproval.vue";
// #region 响应式变量
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const { userStore } = useStore() as any;
const { getApproveStatusTag } = useStatusTag();
const { showRevokeFlag, manualSubmissionApprove } = useApproval();
const showAllSwitch = ref(true);
const tableDatas = ref<Record<string, any>[]>([]);
const showRecords = ref<Record<string, any>[]>([]);
const currRow = ref<Record<string, any>>({});
// 默认 近一个月
const dateRange = ref<[string, string]>([
  datetimeUtil.addDate(Date.now(), -30, "yyyy-MM-dd"),
  datetimeUtil.formatDate(Date.now(), "yyyy-MM-dd")
]);
// 换班人姓名
let targetEmployeeName = "";
// 检核午别
let checkNoonFlag = false;
// 申请午别
const adjustNoonType = ref<string>("");
// 换班午别
const targetNoonType = ref<string>("");
// 是否校验申请排班
let isValidateAdjust = false;
// 是否验证换班排班
let isValidateTarget = false;
// 用来获取提交的form数据，用来进行参数验证
const submitRefs = ref<any>();
const { getButtonShow, showRevokeButton } = useButton();
const errors = ref<string[]>([]);
const revokeFormData = ref<Record<string, any>[]>();
// 抽屉参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveRecord(currRow.value);
  }
});
// 规则验证rule
const rules = reactive({
  targetDate: [
    {
      required: true,
      message: "请选择换班日期",
      trigger: "change"
    }
  ],
  adjustDate: [
    {
      required: true,
      message: "请选择调班日期",
      trigger: "change"
    }
  ],
  adjustDepartmentPost: [
    {
      required: true,
      message: "请选择申请调整岗",
      trigger: "change"
    }
  ],
  targetEmployeeID: [
    {
      required: true,
      message: "请选择换班人",
      trigger: "change"
    }
  ],
  targetDepartmentPost: [
    {
      required: true,
      message: "请选择调整岗位",
      trigger: "change"
    }
  ],
  reason: [
    {
      required: true,
      message: "请输入调整原因",
      trigger: "blur"
    }
  ]
});
// #endregion
onMounted(() => {
  getAdjustSchedule();
});
// #region 前端的预处理逻辑
/**
 * @description: 禁止选择当前时间之前的日期
 * @param time 组件下拉框中的所有时间点
 * @return
 */
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7;
};
/**
 * @description: 新增或修改记录
 * @param row
 */
const addOrModify = (row?: Record<string, any>) => {
  isValidateAdjust = false;
  isValidateTarget = false;
  targetEmployeeName = "";
  drawerOptions.value.drawerName = "addOrModify";
  if (row) {
    drawerOptions.value.drawerTitle = "修改-调班申请";
    currRow.value = common.clone(row);
  } else {
    drawerOptions.value.drawerTitle = "新增-调班申请";
    currRow.value = { addEmployeeID: userStore.employeeID, autoSchedule: true };
  }
  toggleDrawer(true);
};
/**
 * @description: 获取换班人姓名
 * @param employee
 */
const getTargetEmployeeName = (employee: Record<string, any>) => {
  targetEmployeeName = employee.label;
};
// 验证选择项函数，用于检查是否有未选择的午别，并返回第一个发现的错误
const validateSelects = () => {
  errors.value = []; // 初始化错误列表
  // 检查是否需要进行午别检查
  if (!checkNoonFlag) {
    return null; // 如果不需要检查，则直接返回
  }
  // 如果目标午别未选择，同时调班午别已选择，添加错误
  if (!currRow.value.targetNoonType && currRow.value.adjustNoonType) {
    errors.value.push("请选择相应换班午别");
  }
  // 如果调班午别未选择，同时目标午别已选择，添加错误
  if (!currRow.value.adjustNoonType && currRow.value.targetNoonType) {
    errors.value.push("请选择相应调班午别");
  }
  // 如果存在错误，返回第一个错误，否则返回null表示无错误
  return errors.value.length > 0 ? errors.value[0] : null;
};
/**
 * @description: 监听校验申请人排班信息
 */
watch(
  [() => currRow.value.addEmployeeID, () => currRow.value.adjustDate, () => currRow.value.adjustNoonType],
  async () => {
    // 第一次不触发排班校验
    if (!isValidateAdjust) {
      isValidateAdjust = true;
      return;
    }
    if (currRow.value.addEmployeeID && currRow.value.adjustDate) {
      let params = {
        departmentID: userStore.departmentID,
        datetime: currRow.value.adjustDate,
        employeeID: currRow.value.addEmployeeID,
        noonType: currRow.value.adjustNoonType
      };
      const adjustScheduling = await getSchedulingPostByEmployeeID(params);
      if (adjustScheduling) {
        currRow.value.adjustDepartmentPost = adjustScheduling;
      } else {
        currRow.value.adjustDepartmentPost = undefined;
        rules.adjustDepartmentPost[0].message = `在${currRow.value.adjustDate}${adjustNoonType.value ?? ""}未找到您的排班信息！`;
        submitRefs.value.validateField("adjustDepartmentPost");
      }
    }
  },
  { deep: true }
);
/**
 * @description: 监听校验换班排班信息
 */
watch(
  [() => currRow.value.targetEmployeeID, () => currRow.value.targetDate, () => currRow.value.targetNoonType],
  async () => {
    // 第一次不触发排班校验
    if (!isValidateTarget) {
      isValidateTarget = true;
      return;
    }
    if (currRow.value.targetEmployeeID && currRow.value.targetDate) {
      let params = {
        departmentID: userStore.departmentID,
        datetime: currRow.value.targetDate,
        employeeID: currRow.value.targetEmployeeID,
        noonType: currRow.value.targetNoonType
      };
      const targetScheduling = await getSchedulingPostByEmployeeID(params);
      if (targetScheduling) {
        currRow.value.targetDepartmentPost = targetScheduling;
      } else {
        currRow.value.targetDepartmentPost = undefined;
        rules.targetDepartmentPost[0].message = `在${currRow.value.targetDate}${targetNoonType.value ?? ""}未找到${
          targetEmployeeName || currRow.value.targetEmployeeID
        }的排班信息！`;
        submitRefs.value.validateField("targetDepartmentPost");
      }
    }
  },
  { deep: true }
);
/**
 * @description: 关闭抽屉
 */
const toggleDrawer = (switchFlag: boolean) => {
  drawerOptions.value.showDrawer = switchFlag;
};
// #endregion
// #region 后端交互逻辑
/**
 * @description: 获取调班申请记录
 */
const getAdjustSchedule = () => {
  let params = {
    employeeID: userStore.employeeID,
    departmentID: userStore.departmentID,
    deptSwitch: showAllSwitch.value
  };
  schedulingService.getAdjustScheduleRecord(params).then((respData: any) => {
    if (!respData) {
      showRecords.value = [];
      tableDatas.value = [];
      return;
    }
    tableDatas.value = respData;
    filterRecords(dateRange.value);
  });
};
import { useUtils } from "../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 新增保存
 * @param {*} params
 */
const saveRecord = async (params: any) => {
  // 保存前检核
  checkNoonFlag = true;
  const message = validateSelects();
  if (message) {
    showMessage("warning", message);
    checkNoonFlag = false;
    return;
  }
  await schedulingService.saveAdjustScheduleRecord(params).then((res: any) => {
    if (!res.recordSaveFlag) {
      return;
    } else if (!res.approveSaveFlag) {
      showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
    } else {
      showMessage("success", "保存成功");
    }
    toggleDrawer(false);
    getAdjustSchedule();
    checkNoonFlag = false;
  });
};
/**
 * @description: 删除调班申请
 * @param {*} row
 */
const deleteRow = async (row: any) => {
  if (!row.adjustScheduleRecordID) {
    return;
  }
  deleteConfirm("", (res: boolean) => {
    if (res) {
      schedulingService.deleteAdjustScheduleRecord({ adjustScheduleRecordID: row.adjustScheduleRecordID }).then((respBool: any) => {
        if (respBool) {
          showMessage("success", "删除成功");
          getAdjustSchedule();
        }
      });
    }
  });
};
/**
 * @description: 获取员工指定日期的排班
 * @param params
 */
const getSchedulingPostByEmployeeID = async (params: any): Promise<string> => {
  let schedulingDetail: string = "";
  await schedulingService.getSchedulingPostByEmployeeID(params).then((result: any) => {
    if (result) {
      schedulingDetail = result;
    }
  });
  return schedulingDetail;
};
/**
 * @description: 根据申请日期过滤记录
 * @param dateRange 日期范围
 * @return
 */
const filterRecords = (dateRange: [string, string] | undefined) => {
  if (!dateRange) {
    showRecords.value = tableDatas.value;
    return;
  }
  let [startDate, endDate] = dateRange;
  let records = tableDatas.value.filter((record: Record<string, any>) => {
    return (
      datetimeUtil.formatDate(record.addDateTime, "yyyy-MM-dd") >= startDate &&
      datetimeUtil.formatDate(record.addDateTime, "yyyy-MM-dd") <= endDate
    );
  });
  showRecords.value = records;
};
// 获取申请午别
const changeAdjustNoon = (value: any) => {
  adjustNoonType.value = value?.label;
};
// 获取换班午别
const changeTargetNoon = (value: any) => {
  targetNoonType.value = value?.label;
};
/**
 * @description: 开始撤销
 * @param row 调班申请记录
 * @returns
 */
const startRevoke = (row: any) => {
  currRow.value = common.clone(row);
  revokeFormData.value = [
    { label: "申请人", value: currRow.value.addEmployee },
    { label: "调班日期", value: datetimeUtil.formatDate(currRow.value.adjustDate, "yyyy-MM-dd") },
    { label: "调班午别", value: currRow.value.adjustNoonTypeName },
    { label: "调班岗", value: currRow.value.adjustDepartmentPost },
    { label: "换班人", value: currRow.value.targetEmployee },
    { label: "换班日期", value: datetimeUtil.formatDate(currRow.value.targetDate, "yyyy-MM-dd") },
    { label: "换班午别", value: currRow.value.targetNoonTypeName },
    { label: "换班岗", value: currRow.value.targetDepartmentPost }
  ];
  drawerOptions.value.drawerTitle = "撤销-调班申请";
  drawerOptions.value.drawerName = "revoke";
  toggleDrawer(true);
};
// #endregion
const i18nText = computed(() => {
  return {
    add: proxy.$t("button.add"),
    buttonSave: proxy.$t("button.save"),
    cancel: proxy.$t("button.cancel"),
    delete: proxy.$t("button.delete"),
    modify: proxy.$t("button.modify"),
    operation: proxy.$t("label.operation"),
    revoke: proxy.$t("button.revoke")
  };
});
// 用于调整时间选择器的宽度与其他选择器宽度相同
const datePickerWidth = computed(() => `${convertPX(386)}px`);
</script>
<style lang="scss">
.adjust-schedule {
  height: 100%;
  width: 100%;
  .header-label {
    margin-left: 10px;
    margin-right: 10px;
  }
  .date-range-picker {
    width: 360px;
  }
  .form-style {
    margin-top: 20px;
    .el-form-item__label-wrap {
      align-items: center;
    }
    .el-date-editor {
      .el-input__wrapper {
        width: v-bind(datePickerWidth);
      }
    }
    .department-post-input {
      width: 386px;
    }
  }
  .is-error {
    border-color: red;
  }
}
</style>
