<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\departmentCheckBox.vue
 * Author       : 马超
 * Date         : 2025-02-18 14:49
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-04-11 17:02
 * Description  : 部门多选组件
 * CodeIterationRecord: 部门多选组件
 -->
<template>
  <div class="department-check-box">
    <div class="top-warp">
      <!-- 修改此处 -->
      <el-select :model-value="selectedDistrict" @update:model-value="handleDistrictChange" :disabled="disabled">
        <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div class="bottom-warp">
      <!-- 修改此处 -->
      <el-checkbox-group :model-value="checkedDepartments" @update:model-value="$emit('update:checkedDepartments', $event)">
        <div v-for="(department, index) in departmentGroups" :key="index" class="checkbox-group-warp">
          <el-checkbox :value="department.value">
            <span :title="department.label">{{ department.label }}</span>
          </el-checkbox>
          <div class="count">
            {{ department[countField] || 0 }}
          </div>
        </div>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  // 区域选项
  districtOptions: {
    type: Array as PropType<Array<{ value: any; label: string }>>,
    default: () => []
  },
  // 当前选中区域值
  selectedDistrict: {
    type: [Number, String],
    default: undefined
  },
  // 部门分组数据
  departmentGroups: {
    type: Array as PropType<
      Array<{
        value: any;
        label: string;
        noSupervisionCount?: string;
        noVisitCount?: string;
      }>
    >,
    default: () => []
  },
  // 计数字段名
  countField: {
    type: String as PropType<"noSupervisionCount" | "noVisitCount">,
    default: "noSupervisionCount"
  },
  // 是否禁用选择器
  disabled: Boolean,
  // 选中的部门列表
  checkedDepartments: {
    type: Array as PropType<any[]>,
    required: true
  }
});
const emit = defineEmits(["update:selectedDistrict", "update:checkedDepartments", "district-change"]);
const handleDistrictChange = (value: any) => {
  emit("update:selectedDistrict", value);
  emit("district-change", value);
};
</script>

<style lang="scss" scoped>
.department-check-box {
  /* 保持原有样式不变 */
  float: left;
  border: 1px solid #d6d4d4;
  width: 300px;
  padding: 0 10px;
  box-sizing: border-box;
  .top-warp {
    margin-top: 10px;
    margin-bottom: 20px;
  }
  .bottom-warp {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .checkbox-group-warp {
      border-bottom: 1px solid #ddd;
      width: 100%;
      @include flex-aline(row, space-between);
      .count {
        width: 65px;
        text-align: center;
        height: 100%;
        font-size: 20px;
        background-color: #e6a23c;
        border-radius: 20px;
        color: #fff;
      }
    }
  }
}
</style>
