<!--
 * FilePath     : \src\components\selector\settingDictionarySelector.vue
 * Author       : 张现忠
 * Date         : 2024-07-20 17:25
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-09-06 14:44
 * Description  : SettingDictionary配置字典配置下拉选择器
 * CodeIterationRecord:
 -->
<template>
  <div class="setting-dictionary-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="selectedValue"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      @change="handleChange"
    >
      <el-option v-for="(item, index) in options" :key="index" :label="item.value" :value="item.key" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: ""
  },
  modelValue: {
    type: [String, Array<string>]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 240
  },
  // settingTypeCode \settingTypeValue
  settingParams: {
    type: Object as PropType<{ settingTypeCode: string; settingTypeValue: string }>,
    required: true
  }
});
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let selectedValue = useVModel(props, "modelValue", emits);
let options = ref<Array<{ key: string; value: string }>>([]);
onMounted(async () => {
  if (!props.settingParams || props.settingParams.settingTypeCode === "" || props.settingParams.settingTypeValue === "") {
    return;
  }
  // 获取字典数据
  let params = {
    settingTypeCode: props.settingParams.settingTypeCode,
    settingTypeValues: [props.settingParams.settingTypeValue]
  };
  let optionDatas = await useDictionaryData().getSettingDictionaryByCodeValue(params);
  optionDatas && (options.value = optionDatas[props.settingParams.settingTypeValue]);
});
/**
 * 选择数据异动时触发事件
 * @param value 异动数据
 */
const handleChange = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, options.value, "SettingValue", props.multiple, emits);
};
</script>
<style lang="scss">
.setting-dictionary-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
