/*
 * FilePath     : \src\utils\Date.js
 * Author       : 苏军志
 * Date         : 2023-06-05 18:54
 * LastEditors  : 苏军志
 * LastEditTime : 2023-08-31 20:56
 * Description  :重写Date无参数的构造函数
 * CodeIterationRecord:
 */

let oldDate = Date;
Date = function (...params) {
  // 只重写无参数的构造函数
  if (params.length == 0) {
    // 获取服务器时间和本地时间差值
    let timeDiff = JSON.parse(sessionStorage.getItem("timeDiff"));
    // 矫正时间并返回
    return new oldDate(new oldDate().getTime() + timeDiff);
  }
  return new oldDate(...params);
};
// 继承原型函数
Date.prototype = oldDate.prototype;
// 继承静态函数
Date.now = oldDate.now;
Date.parse = oldDate.parse;
Date.UTC = oldDate.UTC;
// 将原有的Date无参数构造函数放到新Date的_localDate方法
Date.localDate = function () {
  return new oldDate();
};
