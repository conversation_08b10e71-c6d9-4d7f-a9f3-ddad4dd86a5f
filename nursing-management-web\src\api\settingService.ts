/*
 * FilePath     : \src\api\settingService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 来江禹
 * LastEditTime : 2024-08-06 16:14
 * Description  : 获取配置相关内容
 * CodeIterationRecord:
 */

import http from "@/utils/http";

export class settingService {
  private static getHospitalListApi: string = "/setting/GetHospitalList";
  private static getLanguageApi: string = "/setting/GetLanguage";
  private static getApiUrlByCodeApi: string = "/setting/GetApiUrlByCode";
  // 获取医院列表
  public static getHospitalList(params?: any) {
    return http.get(this.getHospitalListApi, params, { loadingText: Loading.LOAD });
  }
  // 获取语言列表
  public static getLanguage(params?: any) {
    return http.get(this.getLanguageApi, params, { loadingText: Loading.LOAD });
  }
  // 获取api地址
  public static getApiUrlByCode(params?: any) {
    return http.get(this.getApiUrlByCodeApi, params, { loadingText: Loading.LOAD });
  }
}
