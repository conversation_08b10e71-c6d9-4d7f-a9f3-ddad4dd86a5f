/*
 * FilePath     : \src\api\hierarchicalQCService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 马超
 * LastEditTime : 2025-06-23 10:18
 * Description  : 用户相关Api接口
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { dynamicFormData, formAttribute } from "zhytech-ui";
import qs from "qs";
export class hierarchicalQCService {
  private static baseUrl: string = "/HierarchicalQC";
  private static getHierarchicalQCFormListApi: string = this.baseUrl + "/GetHierarchicalQCFormList";
  private static getQCFormByLevelApi: string = this.baseUrl + "/GetQCFormByLevel";
  private static getHierarchicalQCRecordListApi: string = this.baseUrl + "/GetHierarchicalQCRecordList";
  private static getHierarchicalQCMainListApi: string = this.baseUrl + "/GetHierarchicalQCMainList";
  private static deleteHierarchicalQCRecordApi: string = this.baseUrl + "/DeleteHierarchicalQCRecord";
  private static deleteHierarchicalQCMainApi: string = this.baseUrl + "/DeleteHierarchicalQCMain";
  private static subjectPlanSaveApi: string = this.baseUrl + "/SubjectPlanSave";
  private static getSubjectTableViewApi: string = this.baseUrl + "/GetSubjectTableView";
  private static getAssignDepartmentListApi: string = this.baseUrl + "/GetAssignDepartmentList";
  private static getAssignEmployeeListApi: string = this.baseUrl + "/GetAssignEmployeeList";
  private static saveSubjectAssignApi: string = this.baseUrl + "/SaveSubjectAssign";
  private static getSubjectAssignViewApi: string = this.baseUrl + "/GetSubjectAssignView";
  private static deleteSubjectApi: string = this.baseUrl + "/DeleteSubject";
  private static getHierarchicalQCRemarkApi: string = this.baseUrl + "/GetHierarchicalQCRemark";
  private static saveHierarchicalQCRemarkApi: string = this.baseUrl + "/SaveHierarchicalQCRemark";
  private static getHierarchicalQCDetailsApi: string = this.baseUrl + "/GetHierarchicalQCDetails";
  private static saveHierarchicalQCMainAndDetailsApi: string = this.baseUrl + "/SaveHierarchicalQCMainAndDetails";
  private static getTrackTableDataApi: string = this.baseUrl + "/GetTrackTableData";
  private static getHierarchicalQCListByEmployeeApi: string = this.baseUrl + "/GetHierarchicalQCListByEmployee";
  private static getQCFormTypeApi: string = this.baseUrl + "/GetQCFormType";
  private static getQCSubjectOptionApi: string = this.baseUrl + "/GetQCSubjectOption";
  private static getNormalWorkingTableDataApi: string = this.baseUrl + "/GetNormalWorkingTableData";
  private static stopHierarchicalQCApprovalApi: string = this.baseUrl + "/StopHierarchicalQCApproval";
  private static submitForApprovalApi: string = this.baseUrl + "/SubmitForApproval";
  private static getQCAssessListApi: string = this.baseUrl + "/GetQCAssessList";
  private static saveQCFormApi: string = this.baseUrl + "/SaveQCForm";
  private static deleteQCFormApi: string = this.baseUrl + "/DeleteQCForm";
  private static saveQCSubjectFormApi: string = this.baseUrl + "/SaveQCSubjectForm";
  private static getQCAssessViewApi: string = this.baseUrl + "/GetQCAssessView";
  private static getCriticalPatientVisitsRecordApi: string = this.baseUrl + "/GetCriticalPatientVisitsRecord";
  private static getVisitsQcAssessViewApi: string = this.baseUrl + "/GetVisitsQcAssessView";
  private static saveVisitsRecordApi: string = this.baseUrl + "/SaveVisitsRecord";
  private static getVisitsQcRecordApi: string = this.baseUrl + "/GetVisitsQcRecord";
  private static getQCEmployeeOptionsApi: string = this.baseUrl + "/GetQCEmployeeOptions";
  private static deleteAllVisitsRecordApi: string = this.baseUrl + "/DeleteAllVisitsRecord";
  private static deleteVisitsRecordApi: string = this.baseUrl + "/DeleteVisitsRecord";
  private static saveProblemRectificationDataApi: string = this.baseUrl + "/SaveProblemRectificationData";
  private static confirmRectificationApi: string = this.baseUrl + "/ConfirmRectification";
  private static deleteQCFileApi: string = this.baseUrl + "/DeleteQCFile";
  private static getGuidanceAndImprovementApi: string = this.baseUrl + "/GetGuidanceAndImprovement";
  private static getDeparmentToQCFormTypeApi: string = this.baseUrl + "/GetDeparmentToQCFormType";
  private static getQCSubjectSelectOptionsApi: string = this.baseUrl + "/GetQCSubjectSelectOptions";
  private static getQuestionTitlesApi: string = this.baseUrl + "/GetQuestionTitles";
  private static getQcMainViewsApi: string = this.baseUrl + "/GetQcMainViews";
  private static getPreviewImageApi: string = this.baseUrl + "/GetPreviewImage";
  private static getSupervisionRecordApi: string = this.baseUrl + "/GetSupervisionRecord";
  private static getSensitiveQcAssessViewApi: string = this.baseUrl + "/GetSensitiveQcAssessView";
  private static saveSensitiveRecordApi: string = this.baseUrl + "/SaveSensitiveRecord";
  private static getSensitiveQcRecordApi: string = this.baseUrl + "/GetSensitiveQcRecord";
  private static deleteSensitiveRecordApi: string = this.baseUrl + "/DeleteSensitiveRecord";
  private static deleteAllSupervisionRecordApi: string = this.baseUrl + "/DeleteAllSupervisionRecord";
  private static saveReadRecordApi: string = this.baseUrl + "/SaveReadRecord";
  private static getQcResultShowReadApi: string = this.baseUrl + "/GetQcResultShowRead";
  private static updateQcResultReadStatusApi: string = this.baseUrl + "/UpdateQcResultReadStatus";
  private static saveIgnoreVisitsRecordApi: string = this.baseUrl + "/SaveIgnoreVisitsRecord";

  public static getQCFormByLevel(params: any) {
    return http.post(this.getQCFormByLevelApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  public static getHierarchicalQCFormList(params?: any) {
    return http.get(this.getHierarchicalQCFormListApi, params, { loadingText: Loading.LOAD });
  }
  public static getHierarchicalQCRecordList(params: any) {
    return http.get(this.getHierarchicalQCRecordListApi, params, { loadingText: Loading.LOAD });
  }
  public static getHierarchicalQCMainList(params: any) {
    return http.get(this.getHierarchicalQCMainListApi, params, { loadingText: Loading.LOAD });
  }
  public static deleteHierarchicalQCRecord(params: any) {
    return http.post(this.deleteHierarchicalQCRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  public static deleteHierarchicalQCMain(params: any) {
    return http.post(this.deleteHierarchicalQCMainApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  public static saveSubjectPlanSave(params: any) {
    return http.post(this.subjectPlanSaveApi, params, { loadingText: Loading.SAVE });
  }
  public static getSubjectTableView(params: any) {
    return http.get(this.getSubjectTableViewApi, params, { loadingText: Loading.LOAD });
  }
  public static getAssignDepartmentList(params?: any) {
    return http.get(this.getAssignDepartmentListApi, params, { loadingText: Loading.LOAD });
  }
  public static getAssignEmployeeList(params?: any) {
    return http.get(this.getAssignEmployeeListApi, params, { loadingText: Loading.LOAD });
  }
  public static saveSubjectAssign(params: any) {
    return http.post(this.saveSubjectAssignApi, params, { loadingText: Loading.SAVE });
  }
  public static getSubjectAssignView(params?: any) {
    return http.get(this.getSubjectAssignViewApi, params, { loadingText: Loading.LOAD });
  }
  public static deleteSubject(params?: any) {
    return http.get(this.deleteSubjectApi, params, { loadingText: Loading.DELETE });
  }
  public static getHierarchicalQCRemark(params?: any) {
    return http.get(this.getHierarchicalQCRemarkApi, params, { loadingText: Loading.LOAD });
  }
  public static saveHierarchicalQCRemark(params: { hQCRemarkID?: string; hQCAssessListID: number; remark: String }) {
    return http.post(this.saveHierarchicalQCRemarkApi, params, { loadingText: Loading.SAVE });
  }

  public static getHierarchicalQCDetails(params: any) {
    return http.get(this.getHierarchicalQCDetailsApi, params, { loadingText: Loading.LOAD });
  }
  public static saveHierarchicalQCMainAndDetails(params: any) {
    return http.post(this.saveHierarchicalQCMainAndDetailsApi, params, { loadingText: Loading.SAVE, contentType: "multipart/form-data" });
  }
  public static getTrackTableData(params?: any) {
    return http.get(this.getTrackTableDataApi, params, { loadingText: Loading.LOAD });
  }

  public static getHierarchicalQCListByEmployee(params: any) {
    return http.get(this.getHierarchicalQCListByEmployeeApi, params, { loadingText: Loading.LOAD });
  }

  public static getQCFormType(params: any) {
    return http.get(this.getQCFormTypeApi, params, { loadingText: Loading.LOAD });
  }

  public static getQCSubjectOption(params: any) {
    return http.get(this.getQCSubjectOptionApi, params, { loadingText: Loading.LOAD });
  }
  public static getNormalWorkingTableData(params: any) {
    return http.get(this.getNormalWorkingTableDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 停止质控审批
   */
  public static async stopHierarchicalQCApproval(params: any) {
    return await http.post(this.stopHierarchicalQCApprovalApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }

  /**
   * @description: 提交审批记录
   * @param params
   * @return
   */
  public static async submitForApproval(params: any) {
    return await http.post(this.submitForApprovalApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取质控字典
   * @param params
   * @return
   */
  public static async getQCAssessList(params?: any) {
    return await http.get(this.getQCAssessListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存质控模板
   * @param params
   * @return
   */
  public static async saveQCForm(params?: any) {
    return await http.post(this.saveQCFormApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除质控模板
   * @param params
   * @return
   */
  public static async deleteQCForm(params: any) {
    return await http.post(this.deleteQCFormApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存质控主题的模板
   * @param params
   * @return
   */
  public static async saveQCSubjectForm(params: any) {
    return await http.post(this.saveQCSubjectFormApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取质控主题的模板
   * @param params
   * @return
   */
  public static async getQCAssessView(params: any) {
    return await http.get(this.getQCAssessViewApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取危重患者访视主记录
   * @param params
   * @return
   */
  public static async getCriticalPatientVisitsRecord(params: any) {
    return await http.post(this.getCriticalPatientVisitsRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取访视质控内容
   * @param params
   * @return
   */
  public static async getVisitsQcAssessView(params: any) {
    return (await http.get(this.getVisitsQcAssessViewApi, params, { loadingText: Loading.LOAD })) as Promise<
      dynamicFormData<formAttribute>
    >;
  }
  /**
   * @description: 访视记录保存
   * @param params
   * @return
   */
  public static async saveVisitsRecord(params: any) {
    return await http.post(this.saveVisitsRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取访视质控记录
   * @param params
   * @return
   */
  public static async getVisitsQcRecord(params: any) {
    return await http.get(this.getVisitsQcRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 节点式督导获取质控人员
   * @param params
   * @return
   */
  public static async getQCEmployeeOptions(params: any) {
    return await http.get(this.getQCEmployeeOptionsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除所有访视记录
   * @param params
   * @return
   */
  public static async deleteAllVisitsRecord(params: any) {
    return await http.post(this.deleteAllVisitsRecordApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  /**
   * @description: 根据访视记录主键集合删除访视记录
   * @param params
   * @return
   */
  public static async deleteVisitsRecord(params: any) {
    return await http.post(this.deleteVisitsRecordApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存问题整改记录
   * @param params
   * @return
   */
  public static async saveProblemRectificationData(params: any) {
    return await http.post(this.saveProblemRectificationDataApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 确认整改
   * @param params
   * @return
   */
  public static async confirmRectification(params: any) {
    return await http.post(this.confirmRectificationApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除质控文件
   * @param params
   * @return
   */
  public static async deleteQCFile(params: any) {
    return await http.post(this.deleteQCFileApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 获取质控维护记录中的评价和指导内容
   * @param params 质控维护记录ID
   * @return
   */
  public static async getGuidanceAndImprovement(params: any) {
    return await http.get(this.getGuidanceAndImprovementApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:
   * @param params
   * @return
   */
  public static async getDeparmentToQCFormType() {
    return await http.get(this.getDeparmentToQCFormTypeApi, {}, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取主题下拉options
   */
  public static async getQCSubjectSelectOptions(params: any) {
    return await http.get(this.getQCSubjectSelectOptionsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取评价标准项目
   */
  public static async getQuestionTitles(params: any) {
    return await http.get(this.getQuestionTitlesApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取评价标准项目
   * @param params 维护记录ID
   */
  public static async getQcMainViews(params: any) {
    return await http.get(this.getQcMainViewsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取图片预览
   * @param params
   */
  public static async getPreviewImage(params: any) {
    return await http.get(this.getPreviewImageApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取督导记录
   * @param params
   */
  public static async getSupervisionRecord(params: any) {
    return await http.post(this.getSupervisionRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取敏感质控评价
   * @param params
   */
  public static async getSensitiveQcAssessView(params: any) {
    return (await http.get(this.getSensitiveQcAssessViewApi, params, { loadingText: Loading.LOAD })) as Promise<
      dynamicFormData<formAttribute>
    >;
  }
  /**
   * @description: 保存敏感质控记录
   * @param params
   */
  public static async saveSensitiveRecord(params: any) {
    return await http.post(this.saveSensitiveRecordApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取敏感质控记录
   * @param params
   */
  public static async getSensitiveQcRecord(params: any) {
    return await http.get(this.getSensitiveQcRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除敏感质控记录
   * @param params
   */
  public static async deleteSensitiveRecord(params: any) {
    return await http.post(this.deleteSensitiveRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 删除所有督导记录
   * @param params
   */
  public static async deleteAllSupervisionRecord(params: any) {
    return await http.post(this.deleteAllSupervisionRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存阅读记录
   * @param params
   */
  public static async saveReadRecord(params: any) {
    return await http.post(this.saveReadRecordApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取质控结果是否显示阅读按钮
   * @param params
   */
  public static async getQcResultShowRead(params: any) {
    return await http.get(this.getQcResultShowReadApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 更新质控结果阅读状态
   * @param params
   */
  public static async updateQcResultReadStatus(params: any) {
    return await http.post(this.updateQcResultReadStatusApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存忽略记录
   * @param params
   */
  public static async saveIgnoreVisitsRecord(params: any) {
    return await http.post(this.saveIgnoreVisitsRecordApi, params, { loadingText: Loading.SAVE });
  }
}
