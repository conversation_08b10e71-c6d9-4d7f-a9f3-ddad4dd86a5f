/*
 * FilePath     : \src\hooks\useUserInteraction.ts
 * Author       : 苏军志
 * Date         : 2024-11-13 18:30
 * LastEditors  : 苏军志
 * LastEditTime : 2024-11-22 15:48
 * Description  : 用户在interval内无任何交互操作时执行callback回调
 * CodeIterationRecord:
 */
/* eslint-disable */
export function useUserNoInteraction(interval: number = 30000, callback: Function) {
  let countdownSeconds: number = interval / 1000;
  let timeoutId: any = undefined;
  /**
   * @description: 打开定时器
   */
  const startTimer = () => {
    countdownSeconds = interval / 1000;
    let firstTick = true;
    const tick = async () => {
      if (!firstTick) {
        countdownSeconds--;
      }
      if (countdownSeconds > 0) {
        timeoutId = setTimeout(tick, 1000);
      } else {
        // 达到指定的事件，触发回调，重置定时器
        await callback()
          .then(() => {
            resetTimer();
          })
          .catch((err: any) => {
            console.error(err);
            resetTimer();
          });
      }
      firstTick = false;
    };
    tick();
  };
  /**
   * @description: 停止定时器
   */
  const stopTimer = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  };
  /**
   * @description: 重置定时器
   */
  const resetTimer = () => {
    stopTimer();
    startTimer();
  };
  /**
   * @description: 新增事件监听
   */
  const addEventListener = () => {
    window.addEventListener("mousemove", resetTimer);
    window.addEventListener("mousedown", resetTimer);
    window.addEventListener("touchstart", resetTimer);
    window.addEventListener("touchmove", resetTimer);
    window.addEventListener("wheel", resetTimer);
  };
  /**
   * @description: 移除事件监听
   */
  const removeEventListener = () => {
    window.removeEventListener("mousemove", resetTimer);
    window.removeEventListener("mousedown", resetTimer);
    window.removeEventListener("touchstart", resetTimer);
    window.removeEventListener("touchmove", resetTimer);
    window.addEventListener("wheel", resetTimer);
  };
  /**
   * @description: 销毁事件
   */
  onBeforeUnmount(() => {
    removeEventListener();
    stopTimer();
  });

  return {
    /**
     * @description: 开始监听
     */
    startWatch() {
      addEventListener();
      resetTimer();
    },
    /**
     * @description: 停止监听
     */
    stopWatch() {
      removeEventListener();
      stopTimer();
    }
  };
}
