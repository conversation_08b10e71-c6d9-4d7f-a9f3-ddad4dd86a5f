<!--
 * FilePath     : \src\views\scheduling\schedulingMarkSettings.vue
 * Author       : 马超
 * Date         : 2024-06-19 09:44
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-07 17:44
 * Description  : 排班标记设定
 * CodeIterationRecord: 排班标记设定
 -->
<template>
  <base-layout class="scheduling-mark-settings" :showHeader="false">
    <!-- 暂时屏蔽新增功能 -->
    <!-- <template #header>
      <el-button class="right-button" type="primary" @click="addRow">新增</el-button>
    </template> -->
    <el-table class="scheduling-mark-settings-table" :data="tableDatas" border height="100%" @row-click="rowClick">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column label="标记" prop="icon" align="center"> </el-table-column>
      <el-table-column label="文本" prop="text" align="center"> </el-table-column>
      <el-table-column label="前景色" width="100" align="center">
        <template v-slot="{ row }">
          <el-color-picker v-model="row.color" :clearable="false" popper-class="my-color-picker"></el-color-picker>
        </template>
      </el-table-column>
      <el-table-column label="背景色" width="100" align="center">
        <template v-slot="{ row }">
          <el-color-picker v-model="row.backGroundColor"></el-color-picker>
        </template>
      </el-table-column>
      <el-table-column label="预览效果" align="center">
        <template v-slot="{ row }">
          <el-tag :style="{ color: row.color, backgroundColor: row.backGroundColor }" size="large">{{ row.icon + "：" + row.text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center">
        <template v-slot="{ row }">
          <el-tooltip content="保存">
            <i class="iconfont icon-edit" @click="saveOrEdit(row)"></i>
          </el-tooltip>
          <!-- 暂时屏蔽删除功能 -->
          <!-- <el-tooltip content="删除">
            <i class="iconfont icon-delete" @click="deleteRow(row.administrationIconID)"></i>
          </el-tooltip> -->
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script setup lang="ts">
const { userStore } = useStore();
const tableDatas = ref<any>();

onMounted(() => {
  getSchedulingMarkSettings();
});
/**
 * @description: 获取部门人员剩余休假天数
 */
const getSchedulingMarkSettings = () => {
  let params = {
    departmentID: userStore.departmentID
  };
  schedulingService.getSchedulingMarkSettings(params).then((result: any) => {
    tableDatas.value = result;
  });
};
/**
 * @description: 保存
 */
const saveOrEdit = (row: any) => {
  row.userID = userStore.employeeID;
  row.groupID = userStore.departmentID;
  schedulingService.saveSchedulingMarkSettings(row).then(() => {
    showMessage("success", "保存成功");
    getSchedulingMarkSettings();
  });
};

/**
 * @description: 删除
 * @param administrationIconID
 * @return
 * 功能暂时关闭
 */
const deleteRow = (administrationIconID: number) => {
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let params = {
      administrationIconID: administrationIconID,
      userID: userStore.employeeID
    };
    schedulingService.deleteSchedulingMarkSettings(params).then(() => {
      showMessage("success", "删除成功");
      getSchedulingMarkSettings();
    });
  });
};
/**
 * @description: 新增行
 * @return
 * 功能暂时关闭
 */
const addRow = () => {
  tableDatas.value.push({
    icon: "",
    text: "",
    color: "#000000",
    backGroundColor: "#FFFFFF",
    remark: ""
  });
};
/**
 * @description: 行点击触发编辑
 * @param row
 * @return
 */
const rowClick = (row: any) => {
  row.editFlag = true;
};
/**
 * @description: 输入框失焦
 * @param row
 * @return
 */
// const inputBlur = (row: any) => {
//   row.editFlag = false;
// };
</script>

<style lang="scss">
.scheduling-mark-settings {
  .scheduling-mark-settings {
    ::v-deep(.el-color-picker) {
      width: 100% !important;
      box-sizing: border-box;
    }
  }
  .custom-row-class {
    height: 50px;
  }
  // .scheduling-mark-settings-table .scheduling-mark-settings-input-number {
  //   width: 100%;
  // }
}
.el-color-dropdown__link-btn {
  display: none;
}
</style>
