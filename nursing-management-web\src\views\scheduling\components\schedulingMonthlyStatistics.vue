<!--
 * FilePath     : \src\views\scheduling\components\schedulingMonthlyStatistics.vue
 * Author       : 苏军志
 * Date         : 2024-07-07 18:44
 * LastEditors  : 苏军志
 * LastEditTime : 2024-11-13 09:43
 * Description  : 排班月统计
 * CodeIterationRecord:
 -->

<template>
  <div v-if="showFlag" class="scheduling-monthly-statistics" :style="dragDivStyle">
    <div class="header" ref="monthlyStatistics">
      <span>{{ `排班岗位月统计（${month}月）` }}</span>
      <div class="export-excel-style">
        <export-excel :exportExcelOption="exportExcelOption">
          <i class="iconfont icon-download"></i>
        </export-excel>
      </div>
      <i class="iconfont icon-add close" @click="showFlag = false"></i>
    </div>
    <el-table :data="monthlyStatisticsData" border stripe highlight-current-row height="100%">
      <el-table-column label="姓名" :width="convertPX(95)" align="center" fixed>
        <template v-slot="scope">
          {{ scope.row.employeeName }}
        </template>
      </el-table-column>
      <el-table-column label="可休天数" :min-width="convertPX(63)" align="center">
        <template v-slot="scope">
          {{ scope.row.employeeRemainingRestDays }}
        </template>
      </el-table-column>
      <el-table-column label="出勤" :min-width="convertPX(63)" align="center">
        <template v-slot="scope">
          {{ scope.row.attendanceDays }}
        </template>
      </el-table-column>
      <el-table-column label="中午值班" :width="convertPX(63)" class-name="duty-column" align="center">
        <template v-slot="scope">
          {{ scope.row.noonDutyDays }}
        </template>
      </el-table-column>
      <el-table-column label="休" :min-width="convertPX(63)" class-name="rest-column" align="center">
        <template v-slot="scope">
          {{ scope.row.restDays }}
        </template>
      </el-table-column>
      <el-table-column label="年休" :min-width="convertPX(63)" class-name="rest-column" align="center">
        <template v-slot="scope">
          {{ scope.row.annualRestDays }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(postCondition, index) in statisticsPostColumns"
        :key="index"
        :label="postCondition.conditionKey"
        :min-width="convertPX(60)"
        align="center"
      >
        <template v-slot="scope">
          {{ scope.row[postCondition.conditionValue] }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { useSchedulingStatistics } from "../hooks/useSchedulingStatistics";
const convertPX: any = inject("convertPX");
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  month: {
    type: String,
    required: true
  },
  noonDutyMarkID: {
    type: Number
  },
  /**
   * @description: 排班表对象
   */
  shiftSchedulingTable: {
    type: Object as PropType<TableView>,
    required: true
  },
  /**
   * @description: 统计岗位列
   */
  statisticsPostColumns: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => []
  },
  /**
   * @description: 人员可修天数集合
   */
  employeeRemainingRestDaysDict: {
    type: Object as PropType<Record<string, number | undefined>>,
    default: () => {}
  },
  /**
   * @description: 导出Excel参数
   */
  exportExcelOption: {
    type: Array as PropType<ExportExcelView[]>,
    default: () => []
  }
});
// 双向绑定
const emits = defineEmits(["update:modelValue"]);
const showFlag = useVModel(props, "modelValue", emits);
const { getStatisticsMonthlyData } = useSchedulingStatistics();
// 设置排班岗位月统计窗口拖动
const monthlyStatistics = ref<HTMLElement>();
const dragDivStyle = ref<string>();
const { x, y, style } = useDraggable(monthlyStatistics, {
  initialValue: { x: 80, y: 184 }
});
// 拖动时 边界检测
watch(
  style,
  () => {
    if (x.value < 0) {
      x.value = 0;
    }
    if (document.documentElement.offsetWidth - x.value < convertPX(700)) {
      x.value = document.documentElement.offsetWidth - convertPX(700);
    }
    if (y.value < 0) {
      y.value = 0;
    }
    if (document.documentElement.offsetHeight - y.value < convertPX(540)) {
      y.value = document.documentElement.offsetHeight - convertPX(540);
    }
    dragDivStyle.value = `left:${x.value}px;top:${y.value}px;`;
  },
  { immediate: true }
);

const monthlyStatisticsData = ref<Record<string, any>[]>([]);

// 监听表格数据，如果月数据弹窗打开状态，实时统计
watch(
  () => props.shiftSchedulingTable.rows,
  () => {
    monthlyStatisticsData.value = getStatisticsMonthlyData(
      props.month,
      props.shiftSchedulingTable,
      props.employeeRemainingRestDaysDict,
      props.statisticsPostColumns,
      props.noonDutyMarkID
    );
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss">
.scheduling-monthly-statistics {
  position: fixed;
  max-width: 80%;
  width: auto;
  height: 500px;
  z-index: 10000;
  box-shadow: 10px 40px 40px 10px rgba(0, 0, 0, 0.2);
  .header {
    cursor: move;
    display: flex;
    justify-content: space-between;
    padding: 5px 10px 5px 20px;
    color: #ffffff;
    @include l-gradient-bg(right, darken($base-color, 10%), lighten($base-color, 20%));
    .close {
      color: #ff0000;
      transform: rotate(45deg);
    }
  }
  .rest-column .cell {
    color: #ff00ff;
  }
  .duty-column .cell {
    color: #ff0000;
  }
  .export-excel-style {
    // 按钮位置放到右边
    margin-left: auto;
  }
}
</style>
