/*
 * FilePath     : \src\views\messageManagement\types\messageRecordView.ts
 * Author       : 张现忠
 * Date         : 2024-10-19 15:06
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-09 08:48
 * Description  : 消息记录视图模型接口
 * CodeIterationRecord:
 */
/**
 * 消息记录视图模型接口
 */
export interface messageRecordView {
  /**
   * 消息记录ID
   */
  messageRecordID?: string;
  /**
   * 消息标题
   */
  messageTitle: string;
  /**
   * 消息类型
   */
  messageType: string;
  /**
   * 消息类型描述
   */
  messageTypeDescription: string;
  /**
   * 消息内容
   */
  messageContent: string;
  /**
   * 优先级
   * 0-普通，1-重要，2-紧急
   */
  priority: string;
  /**
   * 优先级描述
   */
  priorityDescription: string;
  /**
   * 状态
   * 0-草稿，1-已发布，2-已撤回
   */
  messageStatus: string;
  /**
   * 状态描述
   */
  statusDescription: string;
  /**
   * 发布时间
   */
  publishTime?: Date;
  /**
   * 添加人员ID
   */
  addEmployeeID: string;
  /**
   * 添加人员姓名
   */
  addEmployeeName: string;
  /**
   * 添加时间
   */
  addDateTime: Date;
  /**
   * 修改人员ID
   */
  modifyEmployeeID: string;
  /**
   * 修改人员姓名
   */
  modifyEmployeeName: string;
  /**
   * 修改时间
   */
  modifyDateTime: Date;
  /**
   * 置顶
   */
  isTop: boolean;
  /**
   * 置顶天数
   */
  topDays?: number;
  /**
   * 部门
   */
  departmentIDs?: number[];
  /**
   * 部门是否禁用
   */
  departmentDisabled?: boolean;
}
