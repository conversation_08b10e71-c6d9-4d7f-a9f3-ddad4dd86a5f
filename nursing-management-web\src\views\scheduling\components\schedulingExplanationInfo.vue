<!--
 * FilePath     : \src\views\scheduling\components\schedulingExplanationInfo.vue
 * Author       : 苏军志
 * Date         : 2024-04-09 09:18
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:59
 * Description  : 排班说明
 * CodeIterationRecord:
 -->
<template>
  <div class="scheduling-explanation-info">
    <!-- 标准休假天数说明 -->
    <div v-if="requiredAttendanceDays" class="required-attendance-days">{{ requiredAttendanceDays }}</div>
    <!-- 正常岗位工作时间说明 -->
    <div class="post-description" v-if="postDescriptionList?.length">
      <el-tag type="success" effect="plain" v-for="(postDescription, index) in postDescriptionList" :key="index">{{
        `${postDescription.postName}：${postDescription.workingTimeRange}`
      }}</el-tag>
    </div>
    <!-- 休假岗说明 -->
    <div class="rest-description" v-if="restDescriptionList?.length">
      <el-tag class="rest-explain" effect="plain"> 注意: 红色表示预约休息 </el-tag>
      <el-tag class="rest" effect="plain" v-for="(restDescription, index) in restDescriptionList" :key="index">
        {{ restDescription }}
      </el-tag>
    </div>
    <!-- 排班标记说明 -->
    <div class="shift-scheduling-mark-description" v-if="shiftSchedulingMarkList?.length">
      <template v-for="(shiftSchedulingMark, index) in shiftSchedulingMarkList" :key="index">
        <el-tag v-if="shiftSchedulingMark.remark" effect="plain" :style="{ color: shiftSchedulingMark.color || '#000000' }">
          {{ shiftSchedulingMark.remark }}
        </el-tag>
      </template>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps({
  // 标准休息天数说明
  requiredAttendanceDays: {
    type: String,
    default: ""
  },
  // 岗位说明
  postDescriptionList: {
    type: Array as () => Record<string, any>[],
    default: () => []
  },
  // 休假说明
  restDescriptionList: {
    type: Array as () => string[],
    default: () => []
  },
  // 排班标记说明
  shiftSchedulingMarkList: {
    type: Array as () => Record<string, any>[],
    default: () => []
  }
});
</script>
<style lang="scss">
.scheduling-explanation-info {
  .required-attendance-days {
    font-size: 16px;
    color: #ff0000;
    font-weight: bold;
  }
  .rest-description {
    .el-tag {
      &.rest {
        color: #ff00ff;
      }
      &.rest-explain {
        color: #ff0000;
        font-weight: bold;
      }
    }
  }
  .el-tag {
    margin-right: 10px;
  }
}
</style>
