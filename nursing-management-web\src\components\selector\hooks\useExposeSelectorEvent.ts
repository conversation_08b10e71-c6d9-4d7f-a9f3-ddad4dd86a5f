/*
 * FilePath     : \src\components\selector\hooks\useExposeSelectorEvent.ts
 * Author       : 苏军志
 * Date         : 2023-09-16 11:44
 * LastEditors  : 苏军志
 * LastEditTime : 2023-09-19 20:10
 * Description  : 暴漏选择器组件事件的hooks
 * CodeIterationRecord:
 */
/**
 *暴漏选择器组件事件的hooks
 * @returns
 */
export function useExposeSelectorEvent() {
  return {
    /**
     * change事件
     * @param value 异动值
     * @param emits 事件定义
     */
    exposeChange<T>(value: T, emits: any) {
      emits("change", value);
    },
    /**
     * select事件
     * @param value 异动值
     * @param list 下拉选项
     * @param isMultiple 是否多选
     * @param emits 事件定义
     */
    exposeSelect<V, L>(value: V | V[], list: L[], valueName: string, isMultiple: boolean, emits: any) {
      const newList = toRaw(list);
      let fun = (val: L) => (val as any)[valueName] === value;
      if (isMultiple) {
        fun = (val: L) => (value as V[]).includes((val as any)[valueName]);
      }
      const selectItems = useArrayFilter(newList, (val: L) => fun(val));
      let data: L | L[] = toRaw(selectItems.value);
      if (!isMultiple && data.length) {
        data = (data as L[])[0];
      }
      emits("select", data);
    }
  };
}
