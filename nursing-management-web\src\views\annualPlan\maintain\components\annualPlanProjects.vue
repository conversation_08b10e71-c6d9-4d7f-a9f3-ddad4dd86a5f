<!--
 * FilePath     : \src\views\annualPlan\maintain\components\annualPlanProjects.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-22 08:39
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 16:55
 * Description  : 目标任务列表
 * CodeIterationRecord:
 -->
<template>
  <drag
    v-model="list"
    class="draggable-content"
    draggableViewClass="drag-item-view"
    :draggableOption="getDraggableOptions()"
    :disabled="readOnly"
    @moved="maintainStore.resetPlanProjectSort($event.element)"
    @added="maintainStore.resetPlanProjectSort($event.element, sourcePlanGroup)"
  >
    <template #content="{ element }">
      <tag
        color="#000000"
        class="tag-item"
        contentAlign="body-left"
        closable
        :closeStatus="readOnly ? 'undefined' : 'hover'"
        @remove="maintainStore.deletePlanProject(element)"
        @dblclick="maintainStore.openDialog('project', element)"
        @contextmenu.stop.prevent="showContextMenu"
      >
        <span>{{ element.sort + ". " + element.content }}</span>
        <span v-html="useDetailIcon().getHtmlMark(Number(element.markID))"></span>
        <span v-if="sessionStore.debugMode" class="debug-id">{{ `#${element.detailID}` }} </span>
      </tag>
    </template>
  </drag>
</template>
<script setup lang="ts">
import { useDetailIcon } from "../../hooks/useDetailIcon";
import type { planGroup, planProject } from "../../types/annualPlanMain";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
const { readOnly } = storeToRefs(usePlanManagementStore());

const { sourcePlanGroup } = defineProps<{
  sourcePlanGroup: planGroup;
}>();
const list = defineModel<planProject[]>({
  default: () => undefined
});
const { sessionStore } = useStore();
const { proxy } = getCurrentInstance() as any;
const maintainStore = useAnnualPlanMaintainStore();
/**
 * @description: 获取拖拽配置
 * @return
 */
const getDraggableOptions = () => {
  return {
    itemKey: "detailID",
    groupName: "projectGroup",
    put: "projectGroup",
    pull: "projectGroup",
    ghostClass: "goal-item-drag-ghost"
  };
};

//#region 右键菜单
/**
 * @description: 打开右键菜单
 * @param event 鼠标事件
 * @return
 */
const showContextMenu = (event: MouseEvent) => {
  if (readOnly.value) {
    return;
  }
  let itemEl: HTMLElement = event.target as HTMLElement;
  // 选中当前明细
  while (!itemEl?.classList?.contains("tag-item")) {
    itemEl = itemEl.parentElement!;
  }
  itemEl.classList.add("item-active");
  proxy.$showContextMenu({
    x: event.clientX,
    y: event.clientY,
    items: [
      {
        label: "新增项目",
        icon: "iconfont icon-add-project",
        onClick: () => openDialogWithNewDetail()
      }
    ],
    // 关闭时，移除选中状态
    onClose: () => itemEl?.classList.remove("item-active")
  });
};
/**
 * @description: 右键新增明细
 * @return
 */
const openDialogWithNewDetail = () => {
  const newDetail: Partial<planProject> = {
    detailID: `temp_${common.guid()}`,
    mainID: sourcePlanGroup.mainID,
    mainGoalID: sourcePlanGroup.mainGoalID,
    groupID: sourcePlanGroup.groupID,
    content: "",
    markID: ""
  };
  maintainStore.openDialog("project", newDetail);
};
defineExpose({
  addDetailByContextMenu: openDialogWithNewDetail
});
//#endregion
</script>
<style scoped lang="scss">
.draggable-content {
  :deep(.goal-item-drag-ghost) {
    opacity: 0.2;
  }
  .drag {
    margin: 4px 8px;
    display: flex;
    flex-direction: column;
    > .drag-item-view {
      .tag-item {
        border: none;
        cursor: pointer;
        .debug-id {
          margin-left: 8px;
          display: none;
        }
        &:hover,
        &.item-active {
          background-color: lighten($base-color, 40%);
          border: 1px solid $base-color;
          .debug-id {
            display: block;
          }
        }
      }
    }
    .row-footer {
      margin-top: 8px;
    }
  }
}
</style>
