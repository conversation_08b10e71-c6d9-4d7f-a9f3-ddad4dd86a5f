<!--
 * FilePath     : \src\views\annualPlan\dictionary\annualInterventionList.vue
 * Author       : 杨欣欣
 * Date         : 2023-11-04 11:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-04 17:05
 * Description  : 年度计划-执行项目字典维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="annual-intervention-list">
    <template #header>
      <annual-plan-header :year="year" />
      <div>
        <el-button v-permission:B="1" type="primary" @click="saveIntervention" class="right-button">保存</el-button>
        <el-button v-permission:B="1" @click="addIntervention" class="add-button">{{ i18nText.add }}</el-button>
      </div>
    </template>
    <el-table ref="interventionTableRef" class="annual-intervention-list-table" :data="interventionList" stripe border>
      <el-table-column type="selection" align="center" />
      <el-table-column prop="sort" :label="i18nText.sort" :width="convertPX(70)">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="interventionContent" :label="i18nText.projectContent" :min-width="convertPX(250)">
        <template #default="{ row }">
          <el-input
            v-if="row.editing"
            v-model.lazy="row.interventionContent"
            v-focus
            autosize
            type="textarea"
            @keyup.enter="row.editing = false"
          />
          <span v-else>{{ row.interventionContent }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="addEmployeeName" :label="i18nText.addPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.addDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" :label="i18nText.modifyPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.modifyDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column :label="i18nText.operation" :width="convertPX(80)" align="center">
        <template #default="{ row, $index }">
          <el-tooltip :content="i18nText.edit">
            <i v-permission:B="3" @click="editRow(row)" class="iconfont icon-edit" />
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i v-permission:B="4" @click="deleteRow(row.annualInterventionID, $index)" class="iconfont icon-delete" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";

const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const interventionList = ref<Record<string, any>[]>([]);
const deletedInterventionIDs = ref<number[]>([]);
const managementStore = usePlanManagementStore();
const year = usePlanTime().getPlanAnnual();

onMounted(async () => await getAnnualInterventionList());
watch(
  () => managementStore.departmentID,
  async () => {
    await getAnnualInterventionList();
  }
);
/**
 * @description: 获取年度计划执行项目字典
 */
const getAnnualInterventionList = async () => {
  const params = {
    departmentID: managementStore.departmentID
  };
  interventionList.value = await annualPlanSettingService.getAnnualInterventionList(params);
};

/**
 * @description: 新增记录
 */
const addIntervention = () => {
  const row = {
    editing: true
  };
  interventionList.value.unshift(row);
  interventionTableRef.value?.toggleRowSelection(row, true);
};

/**
 * @description: 编辑当前行
 * @param row 当前行数据
 * @return
 */
const editRow = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  row.editing = true;
  interventionTableRef.value?.toggleRowSelection(row, true);
};
const interventionTableRef = ref();
/**
 * @description: 保存记录
 */
const saveIntervention = () => {
  const rows = interventionTableRef.value?.getSelectionRows();
  if (!rows?.length) {
    showMessage("warning", "请选择要保存的记录");
    return;
  }
  const params = {
    interventions: rows,
    departmentID: managementStore.departmentID,
    deletedInterventionIDs: deletedInterventionIDs.value
  };
  annualPlanSettingService.saveAnnualInterventionList(params).then(() => {
    getAnnualInterventionList();
    showMessage("success", "保存成功");
  });
};

/**
 * @description: 删除当前行
 * @param row 当前行数据
 * @return
 */
const deleteRow = async (annualInterventionID: number, index: number) => {
  confirmBox("确定删除？", proxy.$t("tip.systemTip"), (flag: boolean) => {
    if (!flag) {
      return;
    }
    deletedInterventionIDs.value.push(annualInterventionID);
    interventionList.value.splice(index, 1);
    showMessage("success", "删除成功");
  });
};
// 多语言处理
const i18nText = computed(() => {
  return {
    sort: proxy.$t("label.sort"),
    add: proxy.$t("button.add"),
    delete: proxy.$t("tip.delete"),
    edit: proxy.$t("tip.edit"),
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    buttonSave: proxy.$t("button.save"),
    cancel: proxy.$t("button.cancel"),
    projectContent: proxy.$t("annualProjectList.projectContent"),
    organizationType: proxy.$t("annualProjectList.organizationType"),
    year: proxy.$t("annualProjectList.year"),
    addPerson: proxy.$t("annualProjectList.addPerson"),
    addDateTime: proxy.$t("annualProjectList.addDateTime"),
    modifyPerson: proxy.$t("annualProjectList.modifyPerson"),
    modifyDateTime: proxy.$t("annualProjectList.modifyDateTime")
  };
});
</script>
<style lang="scss">
.annual-intervention-list {
  .el-select {
    width: 160px;
  }
  .annual-intervention-list-table {
    height: 100%;
  }
}
</style>
