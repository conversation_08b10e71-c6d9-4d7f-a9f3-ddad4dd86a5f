/*
 * FilePath     : \src\types\employee.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-16 18:58
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 07:57
 * Description  : 
 * CodeIterationRecord: 
 */
declare interface EmployeeInformation {
  /**
   * 工号
   */
  employeeID: string;
  /**
   * 姓名
   */
  employeeName: string;
  /**
   * 简拼
   */
  namePinyin: string;
  /**
   * 部门ID
   */
  departmentID: number;
    /**
   * 所属部门
   */
  departmentName: string;
}

/**
 * 员工ID到员工选项对象的映射
 */
declare interface EmployeeKeyPairs {
  /**
   * 员工ID
   */
  [key: string]: EmployeeAsOption;
}

/**
 * 用于下拉、选择器等的员工选项对象
 */
declare interface EmployeeAsOption {
  /**
   * 员工ID
   */
  id: string;
  /**
   * 员工姓名
   */
  name: string;
  /**
   * 员工姓名简拼
   */
  namePinyin?: string;
  /**
   * 所属部门名称
   */
  departmentName?: string;
}