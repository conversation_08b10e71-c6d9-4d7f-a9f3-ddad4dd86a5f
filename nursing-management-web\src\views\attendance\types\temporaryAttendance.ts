/*
 * FilePath     : \src\views\attendance\types\temporaryAttendance.ts
 * Author       : 来江禹
 * Date         : 2024-02-16 16:27
 * LastEditors  : 来江禹
 * LastEditTime : 2024-02-22 16:08
 * Description  : 临时出勤数据类型
 * CodeIterationRecord:
 */

/**
 * 临时出勤数据类型
 */
export interface temporaryAttendance {
  /**
   * 临时出勤记录ID
   */
  temporaryAttendanceRecordID?: number;
  /**
   * 出勤人
   */
  attendanceEmployee?: string;
  /**
   * 出勤人ID
   */
  attendanceEmployeeID?: string;
  /**
   * 出勤日期
   */
  attendanceDate?: Date;
  /**
   * 出勤小时
   */
  attendanceHours?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 部门ID
   */
  departmentID?: number;
  /**
   * 临时出勤岗位
   */
  temporaryAttendanceName?: string;
  /**
   * 临时出勤岗位表示ID
   */
  temporaryAttendanceID?: number;
}
