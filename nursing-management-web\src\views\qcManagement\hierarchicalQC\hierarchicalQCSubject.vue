<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hierarchicalQCSubject.vue
 * Author       : 郭鹏超
 * Date         : 2023-07-26 10:07
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 19:18
 * Description  : 质控主题维护画面
 * CodeIterationRecord: 无
-->
<template>
  <base-layout class="level-qc-subject" headerHeight="55" :drawerOptions="drawerOptions">
    <template #header>
      <div class="left">
        <label>年月:</label>
        <el-date-picker
          type="monthrange"
          range-separator="至"
          format="YYYY-MM"
          value-format="YYYY-MM"
          v-model="searchView.yearMonthRange"
          class="header-yearMonth-range"
        >
        </el-date-picker>
        <label>组织:</label>
        <department-switch-cascader
          @select="pageOptions.changeFormType(searchView)"
          :disabled="pageOption.formDisabled"
          v-model="searchView.departmentID"
        >
        </department-switch-cascader>
        <label>类别:</label>
        <form-type-selector label="" :width="150" v-model="searchView.formType" :qcType="searchView.qcType"></form-type-selector>
      </div>
      <el-button @click="subjectEdit()" v-permission:B="1" class="subject-add-button add-button">新增</el-button>
    </template>
    <!-- 主题表格 -->
    <el-table class="subject-table" :data="subjectDataList" border height="100%">
      <el-table-column prop="formType" label="主题类别" :min-width="convertPX(150)" />
      <el-table-column prop="formName" label="主题名称" :min-width="convertPX(300)" />
      <el-table-column label="开始日期" align="center" :width="convertPX(130)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" :width="convertPX(130)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="addDepartmentName" label="维护组织" :width="convertPX(130)" />
      <el-table-column label="主题结果报告" :width="convertPX(160)" v-if="pageOption.qcType != 'normalWorkingFormType'">
        <template #default="scope">
          <tag
            v-if="scope.row.reportFileName"
            :closeable="(scope.row.verifierEmployeeIDs ?? []).includes(userStore.employeeID)"
            @remove="deleteQCFile(scope.row)"
            :name="scope.row.reportFileName"
            @click="downloadFile(scope.row)"
            :title="scope.row.reportFileName"
          ></tag>
        </template>
      </el-table-column>
      <el-table-column prop="employeeName" label="维护人" :width="convertPX(100)" />
      <el-table-column prop="employeeName" align="center" label="维护时间" :width="convertPX(150)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDate, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="qcDepartmentCount"
        label="质控部门数量"
        :width="convertPX(80)"
        v-if="pageOption.qcType != 'normalWorkingFormType'"
      />
      <el-table-column label="质控指派" align="center" :width="convertPX(80)" v-if="pageOption.qcType != 'normalWorkingFormType'">
        <template #default="scope">
          <span v-if="scope.row.formTypeCode == '6'"></span>
          <span v-else-if="scope.row.assignFlag">已指派</span>
          <span class="assign-false" v-else>未指派</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" :width="convertPX(250)">
        <template #default="scope">
          <el-tooltip content="修改">
            <i
              v-visibilityHidden="getButtonAuthority(scope.row.employeeID)"
              v-permission:B="3"
              class="iconfont icon-edit"
              @click="subjectEdit(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="指派" v-if="scope.row.formTypeCode != '6'">
            <i
              v-visibilityHidden="getButtonAuthority(scope.row.employeeID)"
              v-permission:B="1"
              class="iconfont icon-assign"
              @click="clickSubjectAssign(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="质控主题模板维护">
            <i
              v-visibilityHidden="getButtonAuthority(scope.row.employeeID)"
              v-permission:B="3"
              class="iconfont icon-more"
              @click="editFormTemplate(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="主题复制">
            <i v-permission:B="1" class="iconfont icon-copy" @click="subjectEdit(scope.row, true)"></i>
          </el-tooltip>
          <el-tooltip content="上传质控报告">
            <upload-file
              :autoUpload="true"
              :showFileList="false"
              :fileInfo="fileInfo"
              :header="fileHeader"
              :action="action"
              :fileAssociations="getFileAssociations(scope.row.hierarchicalQCSubjectID)"
              @upload-success="getSubjectData"
            ></upload-file>
          </el-tooltip>
          <el-tooltip content="导出评价标准">
            <export-excel v-permission:B="6" class="header-export" :exportExcelOption="createExportParamFunc(scope.row)">
              <i class="iconfont icon-print" />
            </export-excel>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              v-visibilityHidden="getButtonAuthority(scope.row.employeeID)"
              v-permission:B="4"
              class="iconfont icon-delete"
              @click="deleteSubject(scope.row)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <!-- 主题维护 -->
      <el-form
        v-if="drawerOptions.drawerName == 'subjectEdit'"
        class="subject-add-form"
        :model="subjectView"
        ref="ruleFormRef"
        :rules="rules"
        label-suffix="："
      >
        <el-form-item label="维护组织" prop="departmentID">
          <department-switch-cascader
            v-model="subjectView.departmentID"
            :disabled="!!subjectView.hierarchicalQCSubjectID"
            @select="pageOptions.changeFormType(subjectView)"
          >
          </department-switch-cascader>
        </el-form-item>
        <el-form-item label="字典类型" prop="formType">
          <form-type-selector
            label=""
            :disabled="subjectView.formTypeDisabled"
            v-model="subjectView.formType"
            :qcLevel="subjectView.hierarchicalQCFormLevel"
            :qcType="subjectView.qcType"
            @change="!subjectView.hierarchicalQCSubjectID && (subjectView.hierarchicalQCFormID = undefined)"
          ></form-type-selector>
        </el-form-item>
        <el-form-item label="字典主题" prop="hierarchicalQCFormID">
          <qcFormSelector
            :disabled="!!subjectView.hierarchicalQCSubjectID"
            @change="getQcSubjectName"
            v-model="subjectView.hierarchicalQCFormID"
            :qcLevel="subjectView.hierarchicalQCFormLevel"
            :formType="subjectView.formType"
            :departmentID="pageOption.qcType === 'normalWorkingFormType' ? [subjectView.departmentID!] : [subjectView.departmentID!, 999999]"
          ></qcFormSelector>
        </el-form-item>
        <el-form-item label="主题月份" prop="yearMonth">
          <el-date-picker
            :disabled="!!subjectView.hierarchicalQCSubjectID && !subjectView.copyFlag"
            @change="getQcSubjectName(), getFirstAndLastDayOfMonth()"
            format="YYYY-MM"
            value-format="YYYY-MM"
            class="header-date"
            v-model="subjectView.yearMonth"
            type="month"
          />
        </el-form-item>
        <el-form-item label="新增主题" prop="formName">
          <el-input v-model="subjectView.formName" />
        </el-form-item>
        <el-form-item prop="startDate" label="时间范围">
          <el-col :span="11">
            <el-date-picker
              :disabled="!!subjectView.hierarchicalQCSubjectID"
              v-model="subjectView.startDate"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              :disabled-date="(date:Date)=> date > new Date(subjectView.endDate) "
            />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-date-picker
              :disabled-date="(date:Date)=> date < new Date(subjectView.startDate)"
              :disabled="!!subjectView.hierarchicalQCSubjectID"
              v-model="subjectView.endDate"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-col>
        </el-form-item>
        <el-form-item v-if="pageOption.showImplementationDateFlag" prop="implementationStartDate" label="实施时间">
          <el-date-picker
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            class="header-implementation-date"
            v-model="subjectView.implementationStartDate"
            type="datetime"
          />
        </el-form-item>
        <el-form-item v-if="pageOptions.qcType != 'normalWorkingFormType'" label="达标分数：" prop="minPassingScore">
          <el-input-number v-model="subjectView.minPassingScore" :min="1" :max="100" />
        </el-form-item>
      </el-form>
      <!-- 主题指派 -->
      <subjectAssign v-if="drawerOptions.drawerName == 'subjectAssign'" ref="subjectAssignRef"></subjectAssign>
      <form-template v-if="drawerOptions.drawerName == 'editFormTemplate'" :formParams="formParams"> </form-template>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
const route = useRoute();
const convertPX: any = inject("convertPX");
let { sessionStore, userStore } = useStore();
import type { dynamicFormData } from "zhytech-ui";
import formTemplate from "./components/formTemplate.vue";
import formTypeSelector from "./components/qcFormTypeSelector.vue";
import subjectAssign from "./components/subjectAssign.vue";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
import { subjectControl } from "./hooks/useSubjectControl";
import { subjectClass } from "./types/hierarchicalQCSubjectView";
import type { formParam } from "./types/qcFormTemplate";
import type { skipSubjectView } from "./types/subjectMaintenanceView";
const { getButtonAuthority } = useQcCommonMethod();
const props = defineProps({
  subjectMaintenanceView: {
    type: Object as () => skipSubjectView | undefined,
    default: () => {
      return undefined;
    }
  }
});
onBeforeMount(async () => {
  await getUploadQCReportUrl();
  await getSubjectData();
});
import qcOptions from "./setting/index";
import hierarchicalQCSubject from "./setting/hierarchicalQCSubject";
// 差异配置初始化
const pageOptions = ref<qcOptions>(
  new qcOptions("hierarchicalQCSubject", { ...props.subjectMaintenanceView, routerQcLevel: route.params.qcLevel as string })
);
const pageOption = ref<hierarchicalQCSubject>(pageOptions.value.pageOption as hierarchicalQCSubject);
import type { subjectSearchView } from "./types/subjectSearchView";
// 搜索条件初始化
const searchView = ref<subjectSearchView>({
  yearMonthRange: [datetimeUtil.getNowDate("yyyy-MM"), datetimeUtil.getNowDate("yyyy-MM")],
  qcType: pageOptions.value.qcType,
  qcLevel: pageOption.value.qcLevel,
  departmentID: pageOption.value.departmentID,
  formType: pageOption.value.formType
});
const subjectDataList = ref<Array<any>>([]);
watch(searchView.value, () => getSubjectData());
/**
 * description: 主题数据获取
 * return {*}
 */
const getSubjectData = async () => {
  let params = {
    ...searchView.value,
    startYearMonth: searchView.value.yearMonthRange![0],
    endYearMonth: searchView.value.yearMonthRange![1]
  };
  await hierarchicalQCService.getSubjectTableView(params).then((res: any) => {
    subjectDataList.value = res;
  });
};
const subjectView = reactive(new subjectClass());
const { getFirstAndLastDayOfMonth, saveSubjectPlan, deleteSubject, getQcSubjectName, deleteQCFile } = subjectControl(
  getSubjectData,
  subjectView
);

const ruleFormRef = ref();
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false
});
/**
 * description: 主题新增修改初始化
 * param {*} item
 * return {*}
 */
const subjectEdit = async (item?: Record<string, any>, copyFlag: boolean = false) => {
  setDrawerOptions("新增主题", "35%", "subjectEdit", true, true, true, saveSubjectPlanCheck);
  if (item) {
    drawerOptions.value.drawerTitle = copyFlag ? "复制主题" : "修改主题";
    subjectView.hierarchicalQCSubjectID = item.hierarchicalQCSubjectID;
    subjectView.hierarchicalQCFormLevel = item.hierarchicalQCFormLevel;
    subjectView.formType = item.formTypeCode;
    subjectView.hierarchicalQCFormID = item.hierarchicalQCFormID;
    subjectView.yearMonth = item.yearMonth;
    subjectView.formName = item.formName;
    subjectView.startDate = item.startDate;
    subjectView.endDate = item.endDate;
    subjectView.copyFlag = copyFlag;
    subjectView.templateCode = item.templateCode;
    subjectView.departmentID = item.departmentID;
    subjectView.minPassingScore = item.minPassingScore;
    subjectView.formTypeDisabled = true;
  } else {
    subjectView.reset();
    subjectView.formType = pageOption.value.formType;
    subjectView.formTypeDisabled = pageOption.value.formDisabled;
    subjectView.hierarchicalQCFormLevel = pageOption.value.qcLevel;
    subjectView.departmentID = searchView.value.departmentID ?? pageOption.value.departmentID;
    getFirstAndLastDayOfMonth();
  }
  if (pageOption.value.showImplementationDateFlag) {
    subjectView.implementationStartDate = item?.implementationStartDate ?? datetimeUtil.getNow("yyyy-MM-dd hh:mm");
  }
  pageOptions.value.changeFormType(subjectView);
};

/**
 * description: 主题保存检核
 * return {*}
 */
const rules = ref({
  hierarchicalQCFormLevel: [{ required: true, message: "请选择质控级别", trigger: "blur" }],
  formType: [{ required: true, message: "请选择字典类型", trigger: "blur" }],
  hierarchicalQCFormID: [{ required: true, message: "请选择字典主题", trigger: "blur" }],
  yearMonth: [{ required: true, message: "请选择主题月份", trigger: "blur" }],
  formName: [{ required: true, message: "请填写主题名称", trigger: "blur" }],
  startDate: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
  endDate: [{ required: true, message: "请选择结束时间", trigger: "blur" }],
  departmentID: [{ required: true, message: "请选择维护组织", trigger: "blur" }]
});
/**
 * description: form表单检核 保存
 * return {*}
 */
const saveSubjectPlanCheck = () => {
  nextTick(() => {
    if (!ruleFormRef.value) {
      return;
    }
    ruleFormRef.value.validate(async (valid: any) => {
      if (valid) {
        await saveSubjectPlan();
        drawerOptions.value.showDrawer = false;
        getSubjectData();
      } else {
        return false;
      }
    });
  });
};

const subjectAssignRef = ref<any | undefined>(undefined);
/**
 * description: 指派初始化
 * param {*} subject
 * return {*}
 */
let clickSubjectAssign = async (subject: Record<string, any>) => {
  setDrawerOptions(`考核指派 -- ${subject.formName}`, "100%", "subjectAssign", true, true, true, saveSubjectAssign);
  nextTick(() => {
    subjectAssignRef.value?.subjectAssign(subject);
  });
};
/**
 * description: 指派保存
 * return {*}
 */
const saveSubjectAssign = async () => {
  // 保存不通过直接返回
  if (!(await subjectAssignRef.value?.saveSubjectAssign())) {
    return;
  }
  drawerOptions.value.showDrawer = false;
  getSubjectData();
};
const formParams = ref<formParam>({} as formParam);
let hierarchicalQCSubjectID: string;
/**
 * @description: 修改质控主题模板
 * @param subject
 * @return
 */
const editFormTemplate = async (subject: Record<string, any>) => {
  hierarchicalQCSubjectID = subject.hierarchicalQCSubjectID;
  formParams.value.formID = subject.templateCode;
  formParams.value.formLevel = subject.hierarchicalQCFormLevel;
  formParams.value.qcFormType = subject.formType;
  formParams.value.formName = subject.formName;
  formParams.value.departmentID = subject.departmentID;
  formParams.value.subjectPropDisabled = true;
  formParams.value.saveFormMethod = saveQCForm;
  setDrawerOptions("质控主题模板维护", "100%", "editFormTemplate", false, false, true);
  drawerOptions.value.className = "edit-form-template-drawer";
};
/**
 * @description: 保存质控模板
 * @param saveData 模板数据
 * @return
 */
const saveQCForm = (saveData: dynamicFormData<Record<string, any>>) => {
  const params: Record<string, any> = {
    hierarchicalQCSubjectID: hierarchicalQCSubjectID,
    formTemplateView: saveData
  };
  hierarchicalQCService.saveQCSubjectForm(params).then((res: any) => {
    if (res) {
      drawerOptions.value.showDrawer = false;
      showMessage("success", "保存成功！");
      getSubjectData();
    }
  });
};
const setDrawerOptions = (
  title: string,
  size: string,
  name: string,
  showCancel: boolean,
  showConfirm: boolean,
  showDrawer: boolean,
  confirm?: Function
) => {
  drawerOptions.value.drawerTitle = title;
  drawerOptions.value.drawerSize = size;
  drawerOptions.value.drawerName = name;
  drawerOptions.value.showCancel = showCancel;
  drawerOptions.value.showConfirm = showConfirm;
  drawerOptions.value.showDrawer = showDrawer;
  confirm && (drawerOptions.value.confirm = () => confirm());
};

const fileInfo = ref<[]>();
const action = ref<string>();
const fileHeader = ref<Object>({ "Management-Token": sessionStore.token });
const getFileAssociations = (subjectID: string) => {
  return {
    sourceID: subjectID,
    userID: userStore.employeeID,
    userName: userStore.userName
  };
};
/**
 * @description: 获取文件地址
 */
const getUploadQCReportUrl = async () => {
  let params = {
    settingCode: "UploadQCReport",
    index: Math.random()
  };
  settingService.getApiUrlByCode(params).then((res: any) => {
    if (res) {
      action.value = res.apiUrl;
    }
  });
};
/**
 * @description: 下载文件
 * @param url 文件地址
 * @return
 */
const downloadFile = (param: any) => {
  if (!param.reportFileUrl) {
    showMessage("info", "找不到文件");
    return;
  }
  const link = document.createElement("a");
  link.href = param.reportFileUrl;
  link.download = param.reportFileName + "." + param.reportFileType;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
/**
 * @description: 创建组装导出Excel文件的函数
 * @params templateCode 模板编码
 * @params formName 表单名称
 */
const createExportParamFunc = ({ templateCode, formName }: Record<string, any>) => {
  return async () => {
    const questionTitles = (await hierarchicalQCService.getQuestionTitles({ templateCode })) as Array<Record<number, string>>;
    const levelFields = new Set<string>();
    questionTitles.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (!isNaN(Number(key))) {
          levelFields.add(key);
        }
      });
    });
    const sortedLevelFields = Array.from(levelFields).sort((a, b) => Number(a) - Number(b));
    const columnData: Record<string, string> = {};
    sortedLevelFields.forEach((field, index) => {
      columnData[`level${index + 1}`] = `第${index + 1}级`;
    });
    return [
      {
        fileName: formName,
        sheetName: formName,
        columnData,
        tableData: questionTitles.map((item) => {
          const rowData: Record<string, string> = {};
          sortedLevelFields.forEach((field, index) => {
            rowData[`level${sortedLevelFields.length - index}`] = (item as any)[field] || "";
          });
          return rowData;
        })
      }
    ] as ExportExcelView[];
  };
};
</script>
<style lang="scss">
.level-qc-subject {
  height: 100%;
  width: 100%;
  .base-header {
    padding: 0 10px;
    @include flex-aline(row, space-between);
    .left {
      @include flex-aline();
    }
    .header-yearMonth-range {
      width: 200px;
    }
    .subject-add-button {
      float: right;
      margin-top: 10px;
    }
  }
  .subject-table {
    .assign-false {
      color: #ff0000;
    }
    .iconfont {
      margin-right: 10px;
    }
  }
  .subject-add-form {
    height: 100%;
    padding: 20px 0;
    .header-subject {
      width: 350px;
    }
    .text-center {
      text-align: center;
    }
    .header-date {
      width: 200px;
    }
    .header-implementation-date {
      width: 280px;
    }
    .header-yearMonthrange {
      width: 200px;
    }
  }
  .edit-form-template-drawer {
    .el-drawer__header {
      margin-bottom: 0;
    }
    .el-drawer__body {
      padding: 0 0 10px 0;
    }
    .el-drawer__footer {
      padding: 0;
    }
  }
  .el-upload.is-drag {
    display: inline-flex;
  }
}
</style>
