<!--
 * FilePath     : \src\views\annualPlan\maintain\components\annualPlanIndicators.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-24 15:45
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 16:54
 * Description  : 策略指标列表
 * CodeIterationRecord:
 -->
<template>
  <drag
    v-model="list"
    class="draggable-content"
    draggableViewClass="drag-item-view"
    :disabled="readOnly"
    :draggableOption="getDraggableOptions()"
    @moved="dragInOrResetSort($event.element)"
    @added="dragInOrResetSort($event.element)"
  >
    <template #content="{ element }">
      <tag
        color="#000000"
        class="tag-item"
        contentAlign="body-left"
        closable
        :closeStatus="readOnly ? 'undefined' : 'hover'"
        @remove="maintainStore.deleteIndicator(element)"
        @dblclick="maintainStore.openDialog('indicator', element)"
        @contextmenu.stop.prevent="showContextMenu"
      >
        <span v-if="element.localShowName">{{
          element.sort +
          ". " +
          element.localShowName +
          (element.operator === "=" || !element.operator ? "" : element.operator) +
          (element.referenceValue ?? "") +
          (element.unit ?? "")
        }}</span>
        <span>{{ element.remark ? `（${element.remark}）` : "" }}</span>
        <span v-html="getHtmlMark(Number(element.markID))" />
        <span v-if="sessionStore.debugMode" class="debug-id">{{ `#${element.detailID}` }} </span>
      </tag>
    </template>
  </drag>
</template>
<script setup lang="ts">
import { useDetailIcon } from "../../hooks/useDetailIcon";
import type { planGroup, planIndicator } from "../../types/annualPlanMain";
import type { annualIndicatorList } from "./../../types/annualPlanDictionary";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";
import { usePlanTime } from "@/hooks/usePlanTime";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
const { readOnly } = storeToRefs(usePlanManagementStore());

const { sourcePlanGroup } = defineProps<{
  sourcePlanGroup: planGroup;
}>();
const list = defineModel<planIndicator[]>({
  default: () => []
});
const { proxy } = getCurrentInstance() as any;
const isIndicatorList = (indicatorOrDict: annualIndicatorList | planIndicator): indicatorOrDict is annualIndicatorList =>
  "indicatorContent" in indicatorOrDict;
const { sessionStore } = useStore();

//#region 列表
const maintainStore = useAnnualPlanMaintainStore();
const { getHtmlMark } = useDetailIcon();
/**
 * @description: 获取拖拽配置
 * @return
 */
const getDraggableOptions = () => {
  return {
    itemKey: "detailID",
    groupName: "indicatorGroup",
    put: ["indicator", "indicatorGroup"],
    pull: "indicatorGroup",
    ghostClass: "goal-item-drag-ghost"
  };
};

const dragInOrResetSort = (indicatorOrDict: annualIndicatorList | planIndicator) => {
  if (!isIndicatorList(indicatorOrDict)) {
    // 拖入指标，说明是由一个分组拖到另一分组，走重排序Action
    maintainStore.resetPlanIndicatorSort(indicatorOrDict, sourcePlanGroup);
  } else {
    if (!isIndicatorList(indicatorOrDict)) {
      return;
    }
    (indicatorOrDict as unknown as planIndicator).mainID = sourcePlanGroup.mainID;
    (indicatorOrDict as unknown as planIndicator).mainGoalID = sourcePlanGroup.mainGoalID;
    (indicatorOrDict as unknown as planIndicator).groupID = sourcePlanGroup.groupID;
    (indicatorOrDict as unknown as planIndicator).year = usePlanTime().getPlanAnnual();
    (indicatorOrDict as unknown as planIndicator).localShowName = indicatorOrDict.indicatorContent;
    (indicatorOrDict as unknown as planIndicator).operator = "";
    (indicatorOrDict as unknown as planIndicator).unit = "";
    (indicatorOrDict as unknown as planIndicator).markID = "";
    maintainStore.openDialog("indicator", indicatorOrDict);
  }
};
/**
 * @description: 右键新增明细
 * @return
 */
const openDialogWithNewDetail = () => {
  const newDetail: Partial<planIndicator> = {
    mainID: sourcePlanGroup.mainID,
    mainGoalID: sourcePlanGroup.mainGoalID,
    groupID: sourcePlanGroup.groupID,
    year: usePlanTime().getPlanAnnual(),
    localShowName: "",
    operator: "",
    unit: "",
    markID: ""
  };
  maintainStore.openDialog("indicator", newDetail);
};
/**
 * @description: 打开右键菜单
 * @param event 鼠标事件
 * @return
 */
const showContextMenu = (event: MouseEvent) => {
  if (readOnly.value) {
    return;
  }
  let itemEl: HTMLElement = event.target as HTMLElement;
  // 选中当前明细
  while (!itemEl?.classList?.contains("tag-item")) {
    itemEl = itemEl.parentElement!;
  }
  itemEl.classList.add("item-active");
  proxy.$showContextMenu({
    x: event.clientX,
    y: event.clientY,
    items: [
      {
        label: "新增指标",
        icon: "iconfont icon-add-indicator",
        onClick: () => openDialogWithNewDetail()
      }
    ],
    // 关闭时，移除选中状态
    onClose: () => itemEl?.classList.remove("item-active")
  });
};
//#endregion
defineExpose({
  addDetailByContextMenu: openDialogWithNewDetail
});
</script>
<style scoped lang="scss">
.draggable-content {
  :deep(.goal-item-drag-ghost) {
    opacity: 0.2;
  }
  .drag {
    margin: 4px 8px;
    display: flex;
    flex-direction: column;
    > .drag-item-view {
      .tag-item {
        border: none;
        cursor: pointer;
        .debug-id {
          margin-left: 8px;
          display: none;
        }
        &:hover,
        &.item-active {
          background-color: lighten($base-color, 40%);
          border: 1px solid $base-color;
          .debug-id {
            display: block;
          }
        }
      }
    }
    .row-footer {
      margin-top: 8px;
    }
  }
}
</style>
