<!--
 * FilePath     : \src\views\employeeManagement\employeeTeachingRelationMaintenance\index.vue
 * Author       : 胡长攀
 * Date         : 2023-11-29 18:18
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-07 16:37
 * Description  : 无证护士带教关系维护
 -->

<template>
  <base-layout class="employee-teaching-relation-maintenance">
    <template #header>
      <el-button v-permission:B="2" class="right-button" type="primary" @click="bachSaveTeachRelation">保存</el-button>
      <el-button v-permission:B="1" class="add-button" @click="addTeachRelation">新增</el-button>
    </template>
    <el-table ref="employeeTeachingRelationTable" :data="employeeTeachRelationViews" row-key="rowKey" height="100%" border stripe>
      <el-table-column type="selection" align="center" :width="50" />
      <el-table-column label="姓名" :width="convertPX(200)">
        <template v-slot="scope">
          <span v-if="!scope.row.editFlag">
            {{ scope.row.employeeName }}
          </span>
          <el-select v-else v-model="scope.row.employeeID" placeholder="请选择员工" @change="changeEmployee(scope.row)" filterable>
            <el-option
              v-for="item in employeeListViews"
              :key="item.employeeID"
              :label="item.employeeName"
              :value="item.employeeID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="capabilityLevel" label="层级" :width="convertPX(120)" align="center" />
      <el-table-column prop="gender" label="性别" :width="convertPX(80)" align="center" />
      <el-table-column label="带教老师姓名" :width="convertPX(200)">
        <template v-slot="scope">
          <span v-if="!scope.row.editFlag">{{ scope.row.teacherName }}</span>
          <el-select
            v-else
            v-model="scope.row.teacherEmployeeID"
            placeholder="请选择带教老师"
            @change="changeTeacherEmployee(scope.row)"
            filterable
          >
            <el-option
              v-for="item in teacherListViews"
              :key="item.employeeID"
              :label="item.employeeName"
              :value="item.employeeID"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="teacherCapabilityLevel" label="带教老师层级" align="center" :width="convertPX(160)" />
      <el-table-column label="开始日期" align="center" :width="convertPX(200)">
        <template v-slot="scope">
          <span v-if="!scope.row.editFlag" v-formatTime="{ value: scope.row.startDate, type: 'date' }"></span>
          <el-date-picker
            v-else
            v-model="scope.row.startDate"
            value-format="YYYY-MM-DD"
            class="employee-teach-date-picker"
            type="date"
            placeholder="请选择日期"
            :disabled-date="(val:any)=>pickerOptions(scope.row.endDate,val)"
          />
        </template>
      </el-table-column>
      <el-table-column label="结束日期" align="center" :width="convertPX(200)">
        <template v-slot="scope">
          <span v-if="!scope.row.editFlag" v-formatTime="{ value: scope.row.endDate, type: 'date' }"></span>
          <el-date-picker
            v-else
            v-model="scope.row.endDate"
            value-format="YYYY-MM-DD"
            class="employee-teach-date-picker"
            type="date"
            placeholder="请选择日期"
            :disabled-date="(val:any)=>pickerOptions(val,scope.row.startDate)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" :min-width="convertPX(200)">
        <template v-slot="scope">
          <span v-if="!scope.row.editFlag">{{ scope.row.remark }}</span>
          <el-input v-else v-model="scope.row.remark" />
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(150)" align="center">
        <template v-slot="scope">
          <el-tooltip content="保存">
            <i v-permission:B="2" @click="saveTeachRelation(scope.row)" class="iconfont icon-save"></i>
          </el-tooltip>
          <el-tooltip content="修改">
            <i v-permission:B="3" @click="editTeachRelation(scope.row)" class="iconfont icon-edit"></i>
          </el-tooltip>
          <el-tooltip content="停止">
            <i
              v-permission:B="17"
              v-visibilityHidden="
                ((scope.row.endDate && new Date(scope.row.endDate) > currentDate) || !scope.row.endDate) &&
                scope.row.employeeTeachingRelationID !== 0
              "
              @click="stopTeachRelation(scope.row)"
              class="iconfont icon-stop"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-permission:B="4" @click="deleteTeachRelation(scope.row)" class="iconfont icon-delete"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
import type { employeeTeachingRelationMaintenance } from "./types/employeeTeachingRelationMaintenance";
let { userStore } = useStore();
const convertPX: any = inject("convertPX");
// 带教关系数据
let employeeTeachRelationViews = ref<employeeTeachingRelationMaintenance[]>([]);
// 无证护士列表
let employeeListViews: employeeTeachingRelationMaintenance[] = [];
// 具有教师资格员工列表
let teacherListViews: employeeTeachingRelationMaintenance[] = [];
let currentDate = new Date(datetimeUtil.getNowDate());
// 病区ID
let employeeTeachingRelationTable = ref();
onMounted(async () => {
  // 初始化
  await getEmployeeTeachingRelationView();
});

/**
 * @description: 开始时间不可大于结束时间
 */
const pickerOptions = (date1: any, date2: any) => {
  if (!date1 || !date2) {
    return;
  }
  let dateTime1 = datetimeUtil.formatDate(date1, "yyyy-MM-dd");
  let dateTime2 = datetimeUtil.formatDate(date2, "yyyy-MM-dd");
  return dateTime1 < dateTime2;
};

/**
 * description: 获取页面初始化数据
 * return {*}
 */
const getEmployeeTeachingRelationView = async () => {
  await employeeService.getEmployeeTeachingRelationView({ departmentID: userStore.departmentID }).then((data: any) => {
    if (data) {
      employeeTeachRelationViews.value = data.employeeTeachingRelationList;
      employeeListViews = data.studyEmployeeList;
      teacherListViews = data.teacherEmployeeList;
    }
  });
  nextTick(() => {
    employeeTeachingRelationTable.value?.doLayout();
  });
};
/**
 * description: 新增带教关系选项
 * return {*}
 */
const addTeachRelation = () => {
  if (!employeeTeachRelationViews.value) {
    employeeTeachRelationViews.value = [];
  }
  let addRow: employeeTeachingRelationMaintenance = {
    rowKey: Math.random(),
    employeeTeachingRelationID: 0,
    employeeName: "",
    capabilityLevel: "",
    gender: "",
    teacherCapabilityLevel: "",
    editFlag: true
  };
  selectRow(addRow, true);
  employeeTeachRelationViews.value.unshift(addRow);
};
/**
 * description: 保存单个带教关系
 * return {*}
 */
const saveTeachRelation = async (row: employeeTeachingRelationMaintenance) => {
  // 检核行数据
  if (!checkRow(row)) {
    return;
  }
  await employeeService.saveTeachRelation(row).then((data: any) => {
    if (data) {
      showMessage("success", "保存成功");
    }
  });
  await getEmployeeTeachingRelationView();
};
/**
 * @description: 批量保存带教关系数据
 */
const bachSaveTeachRelation = async () => {
  let selectRows: employeeTeachingRelationMaintenance[] = employeeTeachingRelationTable.value?.getSelectionRows();
  if (!selectRows || selectRows.length <= 0) {
    showMessage("warning", "请勾选您要保存的数据");
    return;
  }
  for (let index = 0; index < selectRows.length; index++) {
    const selectRow = selectRows[index];
    if (!checkRow(selectRow)) {
      return;
    }
  }
  await employeeService.bachSaveTeachRelation(selectRows).then((data: any) => {
    if (data) {
      showMessage("success", "保存成功");
    }
  });
  await getEmployeeTeachingRelationView();
};
const editTeachRelation = (row: employeeTeachingRelationMaintenance) => {
  if (row.employeeTeachingRelationID === 0) {
    return;
  }
  row.editFlag = !row.editFlag;
  selectRow(row, row.editFlag);
};
/**
 * description: 停止带教关系
 * return {*}
 */
const stopTeachRelation = async (row: employeeTeachingRelationMaintenance) => {
  if ((row.endDate && new Date(row.endDate) <= currentDate) || (row.startDate && new Date(row.startDate) > currentDate)) {
    return;
  }
  confirmBox("确定要停止此条带教记录？", "带教记录停止", async (flag: Boolean) => {
    if (flag) {
      let params = {
        employeeTeachingRelationID: row.employeeTeachingRelationID
      };
      await employeeService.stopTeachRelation(params).then((data: any) => {
        if (data) {
          showMessage("success", "停止成功");
        }
      });
      getEmployeeTeachingRelationView();
    }
  });
};
/**
 * @description: 删除带教关系
 */
const deleteTeachRelation = async (row: employeeTeachingRelationMaintenance) => {
  confirmBox("确定要删除此条带教记录？", "带教记录删除", async (flag: Boolean) => {
    if (flag) {
      if (row.employeeTeachingRelationID === 0) {
        employeeTeachRelationViews.value.splice(0, 1);
        return;
      }
      let params = {
        employeeTeachingRelationID: row.employeeTeachingRelationID
      };
      await employeeService.deleteTeachRelation(params).then((data: any) => {
        if (data) {
          showMessage("success", "删除成功");
        }
      });
      await getEmployeeTeachingRelationView();
    }
  });
};
/**
 * @description: 获取员工信息
 */
const changeEmployee = (row: employeeTeachingRelationMaintenance) => {
  let employee = employeeListViews.find((item: employeeTeachingRelationMaintenance) => {
    return item.employeeID === row.employeeID;
  });
  if (!employee) {
    return;
  }
  row.capabilityLevel = employee.capabilityLevel;
  row.gender = employee.gender;
};
/**
 * @description: 获取带教老师信息
 */
const changeTeacherEmployee = (row: employeeTeachingRelationMaintenance) => {
  let employee = teacherListViews.find((item: employeeTeachingRelationMaintenance) => {
    return item.employeeID === row.teacherEmployeeID;
  });
  if (!employee) {
    return;
  }
  row.teacherCapabilityLevel = employee.capabilityLevel;
};
/**
 * @description: 选中行
 */
const selectRow = (row: employeeTeachingRelationMaintenance, isSelect: Boolean) => {
  nextTick(() => {
    employeeTeachingRelationTable.value?.toggleRowSelection(row, isSelect);
  });
};
/**
 * @description: 检核行数据
 */
const checkRow = (row: employeeTeachingRelationMaintenance) => {
  if (!row.employeeID) {
    showMessage("error", "请选择员工！");
    return false;
  }
  if (!row.teacherEmployeeID) {
    showMessage("warning", "请选择带教老师！");
    return false;
  }
  if (!row.startDate) {
    showMessage("warning", "开始时间不能为空！");
    return false;
  }
  return true;
};
</script>
<style lang="scss">
.employee-teaching-relation-maintenance {
  width: 100%;
  .employee-teach-date-picker {
    width: 90%;
  }
}
</style>
