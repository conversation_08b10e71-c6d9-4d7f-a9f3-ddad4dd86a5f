<!--
 * FilePath     : \src\views\annualPlan\components\workCard.vue
 * Author       : 杨欣欣
 * Date         : 2025-02-26 08:37
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-03-22 17:19
 * Description  : 工作卡片
 * CodeIterationRecord:
 -->
<template>
  <div class="work-card">
    <div class="work-content">
      <div class="content-text">{{ content }}</div>
      <div class="requirement-text">{{ tip }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
defineProps<{
  content: string;
  tip?: string;
}>();
</script>
<style scoped lang="scss">
.work-card {
  flex: 0 0 calc(33.33% - 16px);
  margin: 4px;
  box-sizing: border-box;
  padding: 8px;
  border: 1px solid $border-color;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #ffffff;
  position: relative;
  &:hover {
    box-shadow: 1px 2px 4px #99999980;
  }
}
.work-content {
  display: flex;
  flex-direction: column;
  letter-spacing: 0.4px;
  gap: 4px;
  .content-text {
    font-size: 18px;
    line-height: 1.4;
    color: #000000;
    font-weight: 400;
  }

  .requirement-text {
    font-size: 12px;
    line-height: 1.3;
    color: #999999;
  }
}
</style>
