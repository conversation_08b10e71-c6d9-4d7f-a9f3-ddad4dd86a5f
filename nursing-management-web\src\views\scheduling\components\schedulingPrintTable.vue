<!--
 * FilePath     : \src\views\scheduling\components\schedulingPrintTable.vue
 * Author       : 苏军志
 * Date         : 2024-01-25 11:06
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-18 14:37
 * Description  : 打印排班表
 * CodeIterationRecord:
 -->
<template>
  <table id="print-scheduling-table" border="1" cellpadding="5" cellspacing="0">
    <thead>
      <tr v-for="(title, index) in tableData.prinTableTitles" :key="index">
        <th v-for="(column, cIndex) in title" :key="cIndex" :colspan="column.colspan" :style="column.style">{{ column.title }}</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(data, index) in tableData.tableData" :key="index">
        <td v-for="(cell, index) in data" :key="index" v-html="boldCell(cell)"></td>
      </tr>
    </tbody>
  </table>
</template>
<script setup lang="ts">
import printJS from "print-js";
const props = defineProps({
  tableData: {
    type: Object as PropType<Record<string, any>>,
    required: true
  },
  schedulingType: {
    type: String,
    default: "1"
  },
  holidayList: {
    type: Array as PropType<Record<string, any>[]>,
    required: true
  }
});
const emits = defineEmits(["close"]);
watch(
  () => props.tableData,
  (newValue) => {
    if (Reflect.ownKeys(newValue).length) {
      // 解决onPrintDialogClose不触发问题
      // 在打印事件触发之前，添加派发一个focus聚焦事件，然后点击取消或打印，清除focus事件。
      const focuser = setInterval(() => window.dispatchEvent(new Event("focus")), 500);
      nextTick(() => {
        printJS({
          printable: "print-scheduling-table", // 所有数据
          type: "html",
          documentTitle: `${props.tableData.title}(打印时间：${datetimeUtil.getNow("yyyy-MM-dd hh:mm")})`,
          style: `@page{ size:${
            props.schedulingType === "1" ? "landscape" : "auto"
          }; margin:10mm 5mm 0mm 5mm; width: 100%;} @media print{ #print-scheduling-table { width: 100%; margin: 0 auto;}`,
          targetStyles: ["*"],
          onPrintDialogClose: () => {
            clearInterval(focuser);
            emits("close");
          }
        });
      });
    }
  },
  { immediate: true, deep: true }
);
/**
 * @description：加粗休假
 * @param cell 单元格
 */
const boldCell = (cell: string) => {
  let cellChars = cell.split("<br/>");
  let holidaySet = new Set(props.holidayList.map((item) => item.localLabel));
  let result = "";
  cellChars.forEach((char, index) => {
    if (holidaySet.has(char)) {
      result += `<strong>${char}</strong>`;
    } else {
      result += char;
    }
    if (index !== cellChars.length - 1) {
      result += "<br/>";
    }
  });
  return result;
};
</script>
<style lang="scss">
#print-scheduling-table {
  height: 0;
  width: 100%;
}
</style>
