/*
 * FilePath     : \src\hooks\usePlanTime.ts
 * Author       : 杨欣欣
 * Date         : 2025-01-06 10:47
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-31 10:15
 * Description  : 计划时间hook
 * CodeIterationRecord:
 */

const quarterTextMap = new Map<number, string>([
  [1, "第一季度"],
  [2, "第二季度"],
  [3, "第三季度"],
  [4, "第四季度"]
]);
const monthTextMap = new Map<number, string>([
  [1, "一月"],
  [2, "二月"],
  [3, "三月"],
  [4, "四月"],
  [5, "五月"],
  [6, "六月"],
  [7, "七月"],
  [8, "八月"],
  [9, "九月"],
  [10, "十月"],
  [11, "十一月"],
  [12, "十二月"]
]);
export function usePlanTime() {
  return {
    /**
     * @description: 获取当前需维护的年度，最后一月时切换到下一年度
     */
    getPlanAnnual: () => {
      const now = new Date();
      return now.getMonth() === 11 ? now.getFullYear() + 1 : now.getFullYear();
    },
    /**
     * @description: 获取当前需维护的季度，最后一月时切换到下一季度
     */
    getPlanQuarter: () => {
      const now = new Date();
      const month = now.getMonth(); // 0-11
      const currentQuarter = Math.floor((month % 12) / 3) + 1;
      // 检查是否是季度的最后一个月
      const isLastMonthOfQuarter = (month + 1) % 3 === 0;
      if (isLastMonthOfQuarter) {
        // 如果是第四季度的最后一个月，返回下一年的第一季度
        const nextQuarter = currentQuarter === 4 ? 1 : currentQuarter + 1;
        return {
          quarter: nextQuarter,
          text: quarterTextMap.get(nextQuarter) || ""
        };
      }
      return {
        quarter: currentQuarter,
        text: quarterTextMap.get(currentQuarter) || ""
      };
    },
    /**
     * @description: 获取当前需维护的月份，最后一周时切换到下一月
     */
    getPlanMonth: () => {
      const today = new Date();
      const currentMonth = today.getMonth() + 1;
      const currentYear = today.getFullYear();

      const lastDayOfMonth = new Date(currentYear, currentMonth, 0);
      const lastDate = lastDayOfMonth.getDate();
      const startOfLastWeek = lastDate - 6;

      const returnMonth =
        today.getDate() >= startOfLastWeek && today.getDate() <= lastDate ? (currentMonth === 12 ? 1 : currentMonth + 1) : currentMonth;
      return {
        text: monthTextMap.get(returnMonth) || "",
        month: returnMonth
      };
    }
  };
}
