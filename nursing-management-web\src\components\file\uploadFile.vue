<!--
 * FilePath     : \nursing-management-web\src\components\file\uploadFile.vue
 * Author       : 张现忠
 * Date         : 2024-01-04 09:20
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-18 08:37
 * Description  : 上传附件组件
 * CodeIterationRecord: header中必须传入token ：  "Management-Token": token
 -->
<!-- 1.上传多个文件也是分批多次上传 -->
<template>
  <el-upload
    class="upload-file"
    ref="uploadRef"
    :drag="drag"
    :headers="header"
    :data="fileAssociations"
    :action="localAction"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="beforeUpload"
    multiple
    :limit="limit"
    list-type="text"
    :before-remove="handleRemove"
    :on-exceed="handleExceed"
    :auto-upload="autoUpload"
    :http-request="customUpload"
    :on-change="change"
    :accept="fileType"
    :show-file-list="showFileList"
    v-model:file-list="fileList"
  >
    <el-button v-if="useBtn" class="iconfont icon-upload upload-btn"> 文件上传 </el-button>
    <div v-if="!useBtn">
      <i class="iconfont icon-upload"></i>
      <span v-if="!useBtn && !drag">
        <div class="el-upload__text">拖拽到此处或 <em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只支持后缀 .pdf, .docx, .mp3, .mp4</div>
      </span>
    </div>
  </el-upload>
</template>

<script setup lang="ts">
//#region 引入，定义变量
import common from "@/utils/common";
import type { UploadUserFile } from "element-plus";
const uploadRef = ref();
const localAction = ref();
// 文件列表，没有双向监听
const fileList = ref<UploadUserFile[]>([]);
const emit = defineEmits(["upload-success", "upload-fail", "change"]);
type UploadFileType = Omit<UploadUserFile, "response"> & {
  fileID: String;
  response?: { code: number; data: any; message: String };
};
const fileType = ref<string>(".pdf,.docx,.mp3,.mp4");
const props = defineProps({
  action: {
    type: String
  },
  limit: {
    type: Number,
    default: 100
  },
  fileAssociations: {
    type: Object,
    default: () => {
      return {
        sourceID: "default",
        fileClass: FileClass.MessageAttachment
      };
    }
  },
  customUpload: {
    type: Function,
    default: undefined
  },

  header: {
    type: Object,
    default: () => {
      return {};
    }
  },
  style: {
    type: Object,
    default: () => {
      return {};
    }
  },
  fileInfo: {
    type: Array<Object>
  },
  autoUpload: {
    type: Boolean,
    default: true
  },
  drag: {
    type: Boolean,
    default: true
  },
  showFileList: {
    type: Boolean,
    default: true
  },
  useBtn: {
    type: Boolean,
    default: false
  }
});
//#endregion

//#region 初始化
onMounted(() => {
  // 获取上传文件的地址
  localAction.value = props.action || common.session("serverUrl") + "/file/upload";
  if (props.fileInfo && props.fileInfo.length > 0) {
    fileList.value = props.fileInfo.map((file: any) => {
      return { name: file.fileName, url: file.url, fileID: file.fileID };
    });
    return;
  }
  if (props.fileAssociations && props.fileAssociations.sourceID) {
    fileService
      .getFileListByClassAndSource({
        fileClass: props.fileAssociations.fileClass,
        sourceID: props.fileAssociations.sourceID
      })
      .then((respList: any) => {
        if (!Array.isArray(respList)) {
          showMessage("error", "获取文件列表失败");
        }
        fileList.value = respList.map((fileInfo: any): UploadFileType => {
          return {
            name: fileInfo.fileName,
            url: fileInfo.url,
            fileID: fileInfo.fileID
          };
        });
      });
  }
});
//#endregion

//#region 上传组件逻辑处理
/**
 * @description: 上传成功后的逻辑处理（请求成功后）
 * @param response
 * @param file
 * @return
 */
const handleSuccess = (response: any, file: File) => {
  if (response && response.code == 1) {
    showMessage("success", ` 文件<em>${file.name}</em> 上传成功`);
    emit("upload-success", response, file);
    return;
  }
  if (response && response.message) {
    showMessage("error", response.message);
    emit("upload-fail", response, file);
    return;
  }
};
/**
 * @description: 网络请求错误，才会走到此处
 * @param response 网络请求的异常返回
 * @param file 上传的文件讯息
 * @return
 */
const handleError = (response: any, file: File) => {
  showMessage("error", `上传文件${file?.name}失败,请检查网络或者系统，无法找到文件服务器`);
};
/**
 * @description: 上传文件前检核
 * @param file
 * @return
 */
const beforeUpload = (file: File) => {
  const isAllowedType = /\/(pdf|msword|vnd.openxmlformats-officedocument.wordprocessingml.document|mp3|mp4)$/.test(file.type);
  return new Promise((resolve, reject) => {
    if (!isAllowedType) {
      showMessage("warning", "系统暂不支持此格式");
    }
    resolve(!!isAllowedType);
  });
};
/**
 * @description: 限制最大上传文件数
 * @param files
 * @return
 */
const handleExceed = (files: FileList) => {
  showMessage("warning", `上传文件数量超出最大上传文件数，最大上传文件数:${props.limit}`);
};
/**
 * @description: 删除文件
 * @param file 选择的文件信息
 * @return
 */
const handleRemove = async (file: UploadFileType) => {
  // 上传检核不通过时，从upload-list中清除ready上传的文件
  uploadRef.value?.clearFiles(["ready"]);
  let fileID = file.response ? file.response.data : file.fileID;
  if (!fileID) {
    return false;
  }
  await fileService.deleteFile({ fileID }).then((respBool: any) => {
    respBool && showMessage("success", "删除成功！");
    if (respBool == null || !respBool) {
      showMessage("error", "删除失败！");
      return false;
    }
    return respBool;
  });
};
const width = computed(() => {
  return props.style.width ? props.style.width + "px" : "100%";
});
/**
 * @description: 文件状态改变时的钩子，添加文件、上传成功和上传失败时都会被调用
 * @param uploadFile
 * @return
 */
const change = (uploadFile: any) => {
  if (uploadFile.status === "success" && uploadFile.response.code === 0) {
    showMessage("error", uploadFile.response.message || "文件上传失败！");
    handleRemove(uploadFile);
    return;
  }
  // 判断是否自动上传
  if (!props.autoUpload) {
    fileList.value.push(uploadFile.raw);
    emit("change", fileList.value);
  }
};
//#endregion
</script>
<style lang="scss">
.upload-file {
  width: v-bind(width);
  display: inline;
  .el-upload-dragger {
    border: none;
    background-color: transparent;
    box-shadow: none;
    padding: 0;
    .upload-btn {
      float: left;
      background: #14d8d8 !important;
      border-color: #14d8d8 !important;
      color: #ffffff !important;
    }
  }
}
</style>
