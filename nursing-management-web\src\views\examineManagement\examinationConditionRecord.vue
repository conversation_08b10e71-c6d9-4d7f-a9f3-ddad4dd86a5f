<!--
 * relative     : \nursing-management-web\src\views\examineManagement\examinationConditionRecord.vue
 * Author       : 张现忠
 * Date         : 2025-01-20 14:18
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-31 17:05
 * Description  : 考核试卷-组卷规则条件记录
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="examination-condition-record" :drawerOptions="drawerOptions">
    <template #header>
      <el-button class="add-button" @click="addConditionRecord">新增</el-button>
    </template>
    <el-table :data="conditionData" border>
      <el-table-column prop="conditionName" label="规则名称" :min-width="180"> </el-table-column>
      <el-table-column prop="conditionContent" label="条件明细内容" :min-width="300"> </el-table-column>
      <el-table-column prop="score" label="总分" align="center" :width="convertPX(80)"> </el-table-column>
      <el-table-column prop="addEmployeeName" label="新增人" align="center" :width="convertPX(100)"> </el-table-column>
      <el-table-column prop="addDateTime" label="新增日期" align="center" :width="convertPX(140)">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" label="修改人" align="center" :width="convertPX(100)"> </el-table-column>
      <el-table-column prop="modifyDateTime" label="修改时间" align="center" :width="convertPX(140)">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(120)">
        <template #default="{ row }">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" v-permission:B="3" @click.stop="editConditionRecord(row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteConditionRecord(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <examination-condition-form v-model="editConditionRecordID" ref="conditionFormRef"></examination-condition-form>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import type { examinationConditionRecordView } from "./types/examinationConditionRecordView";
const convertPX: any = inject("convertPX");
const conditionFormRef = shallowRef();
const conditionData = ref<examinationConditionRecordView[]>([]);
const props = defineProps({
  isComponentFlag: {
    type: Boolean,
    default: false
  }
});
const editConditionRecordID = ref<string>("");
const drawerOptions = ref<DrawerOptions>({
  drawerName: "examinationConditionRecordDrawer",
  drawerTitle: "组卷规则",
  showDrawer: false,
  drawerSize: "70%",
  cancelText: "取消",
  confirmText: "确认",
  cancel: () => {
    drawerOptions.value.showDrawer = false;
  },
  confirm: async () => {
    await conditionFormRef.value.save().then((respData: any) => {
      if (respData) {
        getConditionRecords();
        drawerOptions.value.showDrawer = false;
      }
    });
  }
});
onBeforeMount(() => {
  getConditionRecords();
});
/**
 * @description: 新增考核组卷规则条件记录
 */
const addConditionRecord = () => {
  props.isComponentFlag && (drawerOptions.value.drawerSize = "85%");
  drawerOptions.value.drawerTitle = "新增组卷规则";
  drawerOptions.value.showDrawer = true;
  editConditionRecordID.value = "";
};
/**
 * @description: 编辑考核组卷规则条件记录
 * @param row 考核组卷规则条件记录
 */
const editConditionRecord = async (row: any) => {
  props.isComponentFlag && (drawerOptions.value.drawerSize = "85%");
  editConditionRecordID.value = row.examinationConditionRecordID;
  drawerOptions.value.drawerTitle = "编辑组卷规则";
  drawerOptions.value.showDrawer = true;
};

// #region 后端交互逻辑
/**
 * @description: 获取考核组卷规则条件记录
 */
const getConditionRecords = () => {
  conditionService.getExaminationConditionRecord().then((respData: any) => {
    conditionData.value = respData;
  });
};

/**
 * @description: 刪除考核组卷规则条件记录
 * @param row 考核组卷规则条件记录
 */
const deleteConditionRecord = (row: any) => {
  deleteConfirm("是否确定刪除？", (value: any) => {
    if (!value) {
      return;
    }
    let params = {
      examinationConditionRecordID: row.examinationConditionRecordID
    };
    conditionService.deleteExaminationConditionRecord(params).then((respData: any) => {
      respData && getConditionRecords();
    });
  });
};
// #endregion
</script>

<style lang="scss">
.examination-condition-record {
  .form-input {
    width: 50%;
    min-width: 200px;
  }
  .examine-condition-wrapper {
    max-height: 400px;
  }
  .el-form-item {
    margin-top: 0;
    & ~ .el-form-item {
      margin-top: 15px;
    }
    &.difficulty-level-wrapper {
      margin-top: 8px;
    }
  }
}
</style>
