{"name": "nursing-management-web", "version": "0.0.0", "private": true, "scripts": {"dev": " nvm use 18.16.0 && vite --force", "build-old": "npm run fix-memory-limit & run-p type-check build-only", "build": "npm run fix-memory-limit & run-p build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "fix-memory-limit": "cross-env LIMIT=5000 increase-memory-limit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "analyze": "vite build --mode analyze"}, "volta": {"node": "18.16.0"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.3.3", "@stomp/stompjs": "^7.0.0", "@swc/core": "1.10.1", "@types/lodash-es": "^4.17.7", "@types/qs": "^6.9.7", "@univerjs/core": "^0.4.2", "@univerjs/design": "^0.4.2", "@univerjs/docs": "^0.4.2", "@univerjs/docs-ui": "^0.4.2", "@univerjs/engine-formula": "^0.4.2", "@univerjs/engine-render": "^0.4.2", "@univerjs/facade": "^0.4.2", "@univerjs/sheets": "^0.4.2", "@univerjs/sheets-crosshair-highlight": "^0.4.2", "@univerjs/sheets-formula": "^0.4.2", "@univerjs/sheets-formula-ui": "^0.4.2", "@univerjs/sheets-hyper-link": "^0.4.2", "@univerjs/sheets-hyper-link-ui": "^0.4.2", "@univerjs/sheets-thread-comment": "^0.4.2", "@univerjs/sheets-thread-comment-base": "^0.4.2", "@univerjs/sheets-ui": "^0.4.2", "@univerjs/thread-comment": "^0.4.2", "@univerjs/thread-comment-ui": "^0.4.2", "@univerjs/ui": "^0.4.2", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vueuse/core": "^10.4.1", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "dayjs": "^1.11.11", "docxtemplater": "^3.40.1", "eact": "^0.0.1-security", "el-table-infinite-scroll": "^3.0.3", "element-plus": "2.6.1", "file-saver": "^2.0.5", "jszip-utils": "^0.1.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.2.6", "pinia-plugin-persist": "^1.0.0", "pizzip": "^3.1.4", "postcss-loader": "^7.3.3", "postcss-pxtorem": "^6.0.0", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.11.2", "react": "^18.3.1", "react-dom": "^18.3.1", "rxjs": "^7.8.1", "sass": "1.77.0", "sortablejs": "^1.15.0", "vue": "3.5.11", "vue-i18n": "9.3.0-beta.19", "vue-qrcode": "^2.2.2", "vue-router": "^4.2.0", "vue3-html2pdf": "^1.1.2", "xlsx": "^0.18.5", "zhytech-ui": "^1.1.26"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.1", "@types/file-saver": "^2.0.6", "@types/node": "^18.16.8", "@typescript-eslint/eslint-plugin": "^5.59.1", "@univerjs/vite-plugin": "^0.5.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "cross-env": "^7.0.3", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "html-docx-js-typescript": "^0.1.5", "increase-memory-limit": "^1.0.7", "node-sass": "8.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.77.0", "sass-loader": "^13.3.1", "style-loader": "^3.3.3", "terser": "^5.18.0", "typescript": "~5.0.4", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "vite": "^5.0.0", "vite-plugin-legacy-swc": "^1.2.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-top-level-await": "^1.3.1", "vite-plugin-vue-devtools": "^7.4.0", "vue-tsc": "^1.6.4"}}