<!--
 * FilePath     : \src\views\scheduling\remainingRestDays.vue
 * Author       : 胡长攀
 * Date         : 2024-03-04 10:53
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-03 19:20
 * Description  : 剩余休假天数维护
 -->
<template>
  <base-layout class="remaining-rest-days" headerHeight="auto">
    <template #header>
      年份：
      <el-date-picker
        class="year-select"
        type="year"
        format="YYYY"
        value-format="YYYY"
        v-model="selectYear"
        :clearable="false"
        @change="getRemainingRestDaysData"
      ></el-date-picker>
      <el-button class="right-button" v-permission:B="2" type="primary" @click="save">保存</el-button>
    </template>
    <el-table
      v-if="remainingRestDaysTableData.rows?.length"
      class="remaining-rest-days-table"
      :data="remainingRestDaysTableData.rows"
      row-class-name="custom-row-class"
      border
      height="100%"
      @cell-click="handleClick"
    >
      <template v-for="(column, index) in remainingRestDaysTableData.columns" :key="index">
        <el-table-column v-if="column.childColumns?.length" :label="column.name">
          <el-table-column
            v-for="(child, childIndex) in column.childColumns"
            :key="childIndex"
            :label="child.name"
            :property="child.key"
            :min-width="convertPX(child.width ? child.width : 50)"
            align="center"
          >
          </el-table-column>
        </el-table-column>
        <el-table-column v-else :label="column.name" align="center">
          <template v-slot="{ row }">
            <span v-if="!row[column.key].isEdit" :data-month="column.key">{{ row[column.key].days }}</span>
            <el-input-number
              v-else
              class="remaining-rest-days-input-number"
              v-focus
              :step="0.5"
              :max="200"
              controls-position="right"
              :step-strictly="true"
              v-model="currentEditCell.days"
              @blur="row[column.key].isEdit = false"
            />
          </template>
        </el-table-column>
      </template>
    </el-table>
  </base-layout>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
const remainingRestDaysTableData = ref<Record<string, Record<string, any>>>({});
const selectYear = ref(datetimeUtil.getNowDate("yyyy"));
const saveViews: Set<Record<string, any>> = new Set<Record<string, any>>([]);
const currentEditCell = ref<Record<string, any>>({});
const { ignoreUpdates } = watchIgnorable(
  () => currentEditCell.value.days,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      saveViews.add(currentEditCell.value);
    }
  }
);
onMounted(() => {
  getRemainingRestDaysData();
});
/**
 * @description: 获取部门人员剩余休假天数
 */
const getRemainingRestDaysData = () => {
  let params = {
    year: selectYear.value,
    departmentID: userStore.departmentID
  };
  schedulingService.getRemainingRestDaysData(params).then((result: any) => {
    remainingRestDaysTableData.value = result;
  });
};
/**
 * @description: 保存
 */
const save = () => {
  if (saveViews.size === 0) {
    showMessage("warning", "没有保存的数据");
    return;
  }
  let saveViewsArr = Array.from(saveViews);
  schedulingService.saveRemainingRestDaysData(saveViewsArr).then(() => {
    showMessage("success", "保存成功");
    getRemainingRestDaysData();
    // 保存成功后清除保存的集合
    saveViews.clear();
  });
};
/**
 * @description: 单元格左键点击回调
 * @param row 当前行
 * @param column 当前列
 * @param cell 当前单元格
 * @return
 */
const handleClick = (row: Record<string, any>, column: Record<string, any>, cell: HTMLElement) => {
  const key = getDataAttribute(cell, "data-month");
  if (!key) {
    return;
  }
  const item = row[key];
  // 不触发watch
  ignoreUpdates(() => (currentEditCell.value = item));
  item.isEdit = true;
};
/**
 * @description: 获取单元格上的data-set
 * @param el 单元格元素
 * @param attr data-set名称
 * @return
 */
const getDataAttribute = (el: Element, attr: string): string | undefined | null => {
  if (el.hasAttribute(attr)) {
    return el.getAttribute(attr);
  }
  for (let child of el.children) {
    return getDataAttribute(child, attr);
  }
  return undefined;
};
</script>

<style lang="scss">
.remaining-rest-days {
  .year-select {
    width: 120px;
  }
  .custom-row-class {
    height: 55px;
  }
  .remaining-rest-days-table .remaining-rest-days-input-number {
    width: 100%;
  }
}
</style>
