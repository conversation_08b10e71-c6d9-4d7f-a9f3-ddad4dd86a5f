/*
 * FilePath     : \src\views\annualPlan\hooks\useDetailIcon.ts
 * Author       : 杨欣欣
 * Date         : 2023-11-05 15:41
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-24 14:53
 * Description  : 年度计划-明细图标hooks
 * CodeIterationRecord:
 */
let markSettings: AdministrationIcon[] = [];
useDictionaryData()
  .getIconsByModuleType("AnnualPlan")
  .then((data) => (markSettings = data));

/**
 * @description: 年度计划-明细图标hooks
 */
export function useDetailIcon() {
  return {
    markSettings,
    /**
     * @description: 获取html标记
     * @param target 配置对象或者配置ID
     * @return
     */
    getHtmlMark: (target: AdministrationIcon | number, showRemark: boolean = false) => {
      const setting =
        typeof target === "number" ? markSettings.find((markSetting) => markSetting.administrationIconID === target) : (target as AdministrationIcon);
      let icon: string | undefined;
      let color: string | undefined;
      let remark: string | undefined;
      if (setting) {
        ({ icon, color, remark } = setting);
      }
      if (!icon || !color) {
        return "";
      }
      return `<span>
      <span style="color:${color}">${icon}</span>
      ${showRemark ? `<span>(${remark})</span>` : ""}
      </span>`;
    }
  };
}
