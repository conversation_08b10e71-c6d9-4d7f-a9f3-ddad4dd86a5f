/*
 * FilePath     : \nursing-management-web\src\api\attendanceService.ts
 * Author       : 苏军志
 * Date         : 2023-12-18 20:05
 * LastEditors  : 陈超然
 * LastEditTime : 2024-02-29 09:45
 * Description  : 考勤相关逻辑
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";

export class attendanceService {
  private static checkAttendanceEditApi: string = "/attendance/CheckAttendanceEdit";
  private static createAttendanceRecordApi: string = "/attendance/CreateAttendanceRecord";
  private static getAttendanceDatasApi: string = "/attendance/GetAttendanceDatas";
  private static saveAttendanceApi: string = "/attendance/SaveAttendance";
  private static getTemporaryAttendancePostSettingApi: string = "/attendance/GetTemporaryAttendancePostSetting";
  private static deleteTemporaryAttendanceDataApi: string = "/attendance/DeleteTemporaryAttendanceData";
  private static getTemporaryAttendanceListApi: string = "/attendance/GetTemporaryAttendanceList";
  private static saveTemporaryAttendanceDataApi: string = "/attendance/SaveTemporaryAttendanceData";
  private static syncAttendanceDataToOAApi: string = "/attendance/SyncAttendanceDataToOA";
  private static attendanceApprovalApi: string = "/attendance/AttendanceApproval";
  private static cancelAttendanceApprovalApi: string = "/attendance/cancelAttendanceApproval";
  private static getAttendanceStatusApi: string = "/attendance/GetAttendanceStatus";
  /**
   * @description: 检核考勤是否被编辑过Api
   * @param params
   * @return true:已编辑过；false:未被编辑过
   */
  public static checkAttendanceEdit(params: any) {
    return http.post(this.checkAttendanceEditApi, qs.stringify(params));
  }
  /**
   * @description: 生成考勤表
   * @param params
   * @return
   */
  public static createAttendanceRecord(params: any) {
    return http.post(this.createAttendanceRecordApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取考勤表数据
   * @param params
   * @return
   */
  public static getAttendanceDatas(params: any) {
    return http.get(this.getAttendanceDatasApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存手动调整的考勤数据
   * @param params
   * @return
   */
  public static saveAttendance(params: any) {
    return http.post(this.saveAttendanceApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取临时出勤岗位标识配置
   * @param params
   * @return
   */
  public static getTemporaryAttendancePostSetting(params: any) {
    return http.get(this.getTemporaryAttendancePostSettingApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除临时出勤记录
   * @param params
   * @return
   */
  public static deleteTemporaryAttendanceData(params: any) {
    return http.post(this.deleteTemporaryAttendanceDataApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 获取病区临时出勤记录
   * @param params
   * @return
   */
  public static getTemporaryAttendanceList(params: any) {
    return http.get(this.getTemporaryAttendanceListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存临时出勤记录
   * @param params
   * @return
   */
  public static saveTemporaryAttendanceData(params: any) {
    return http.post(this.saveTemporaryAttendanceDataApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 考勤信息回传OA
   * @param params
   * @return
   */
  public static SyncAttendanceDataToOA(params: any) {
    return http.get(this.syncAttendanceDataToOAApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 考勤审核
   * @param params
   * @return
   */
  public static AttendanceApproval(params: any) {
    return http.get(this.attendanceApprovalApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 取消考勤审核
   * @param params
   * @return
   */
  public static CancelAttendanceApproval(params: any) {
    return http.get(this.cancelAttendanceApprovalApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取考勤状态
   * @param params
   * @return
   */
  public static GetAttendanceStatus(params: any) {
    return http.get(this.getAttendanceStatusApi, params, { loadingText: Loading.LOAD });
  }
}
