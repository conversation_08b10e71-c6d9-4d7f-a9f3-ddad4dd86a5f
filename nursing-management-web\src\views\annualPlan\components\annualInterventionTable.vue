<!--
 * FilePath     : \src\views\annualPlan\components\annualInterventionTable.vue
 * Author       : 杨欣欣
 * Date         : 2023-11-08 14:35
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-06-07 09:05
 * Description  : 年度计划-执行项目表格
 * CodeIterationRecord:
 -->
<template>
  <base-layout :showHeader="false" :drawerOptions="drawerOptions" class="annual-intervention-table">
    <el-table
      row-key="annualPlanInterventionMainID"
      class="main-table"
      ref="tableRef"
      :data="annualInterventions"
      @cell-click="handleCellClick"
      border
    >
      <el-table-column type="selection" :width="convertPX(40)" align="center" />
      <el-table-column class-name="sort" prop="sort" label="序号" :width="convertPX(55)" align="center" />
      <el-table-column v-if="Boolean(projectDetails?.length)" label="目标任务">
        <template #default="{ row }">
          <el-select
            v-if="row.isProjectDetailSelectorEdit"
            filterable
            v-model="row.projectDetailID"
            popper-class="project-detail-popper"
            @change="selectRow(row)"
          >
            <el-option
              v-for="projectDetail in projectDetails"
              :key="projectDetail.detailID"
              :title="projectDetail.content"
              :label="projectDetail.content"
              :value="projectDetail.detailID"
            />
          </el-select>
          <div v-else data-column-identify="projectDetailSelector">
            {{ projectDetails?.find((projectDetail) => projectDetail.detailID === row.projectDetailID)?.content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #header>
          分解目标任务字典
          <el-tooltip placement="right">
            <template #content>
              <div class="reference-intervention-help">
                <p>可搜索；若无符合的字典，留空即可</p>
              </div>
            </template>
            <i class="iconfont icon-info"></i>
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <el-select
            v-if="row.isInterventionSelectorEdit"
            filterable
            allow-create
            :modelValue="row.interventionID"
            @update:modelValue="updateRowAndUsedInterventionIDs($event, row)"
            popper-class="project-list-popper"
            :disabled="!row.projectDetailID"
          >
            <el-option
              v-for="intervention in cloneAnnualInterventionList"
              :key="intervention.annualInterventionID"
              :title="intervention.interventionContent"
              :label="intervention.interventionContent"
              :value="intervention.annualInterventionID"
              :disabled="usedInterventionIDs.includes(intervention.annualInterventionID)"
            />
          </el-select>
          <div v-else data-column-identify="interventionSelector">
            {{
              cloneAnnualInterventionList.find((intervention:Record<string, any>) => intervention.annualInterventionID === row.interventionID)
                ?.interventionContent
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分解目标任务">
        <template #default="{ row }">
          <el-input
            v-if="row.isInterventionInputEdit"
            v-model="row.localShowName"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
            :disabled="!row.projectDetailID"
            @change="selectRow(row)"
          />
          <div v-else data-column-identify="isInterventionInputEdit">{{ row.localShowName }}</div>
        </template>
      </el-table-column>
      <!-- 一~十二月 -->
      <el-table-column v-for="[key, value] of monthMap" :label="value" :key="key" align="center" :width="convertPX(60)">
        <template #default="{ row }">
          <span :data-column-identify="key" class="plan-month">
            {{ Boolean(row.planMonths?.find((planMonth: number) => planMonth == key)) ? "○" : "" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="负责人" :width="convertPX(150)" align="center" prop="principalName">
        <template #default="{ row }">
          <span data-column-identify="principal">{{ row.principalGroupName || row.principalName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(60)" align="center">
        <template #default="{ $index }">
          <el-tooltip v-if="!readOnly" content="删除">
            <i v-permission:B="4" @click="deleteIntervention($index)" class="iconfont icon-delete" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <principal-selector
        ref="principalSelector"
        v-model:principalIDs="employeeIDs"
        :customGroupName="customGroupName"
        :recommendGroups="recommendPrincipalGroups"
        :createJointEmployeeNamesFunc="createJointEmployeeNamesFunc"
        :hook="useRecommendOptions"
      />
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import { useAnnualPlanFormulateStore } from "../formulate/hooks/useAnnualPlanFormulateStore";
import { useCellClick } from "../formulate/hooks/useCellClick";
import { usePrincipalSelector } from "@/components/selector/hooks/usePrincipalSelector";
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
const { proxy } = getCurrentInstance() as any;
const { readOnly } = storeToRefs(usePlanManagementStore());
const convertPX: any = inject("convertPX");
const { mainGoalID, projectDetails, projectDetailID, annualInterventionList, monthMap, recommendPrincipalGroups } = defineProps<{
  mainGoalID: string;
  projectDetails?: Record<string, any>[];
  projectDetailID?: string;
  annualInterventionList: Record<string, any>[];
  monthMap: Map<number, string>;
  recommendPrincipalGroups: Record<string, any>[];
}>();
//#region 表格
const annualInterventions = defineModel<Record<string, any>[]>({
  required: true
});
const tableRef = ref<Record<string, any>>();
/**
 * @description: 删除分解目标任务
 * @param index
 * @return
 */
const deleteIntervention = (index: number) => {
  confirmBox("确定删除？", proxy.$t("tip.systemTip"), (flag: boolean) => {
    if (!flag) {
      return;
    }
    const row = annualInterventions.value[index];
    if (!row.annualPlanInterventionMainID.includes("temp_")) {
      removeUsedInterventionID(row.interventionID);
      annualPlanInterventionService.deleteAnnualIntervention({ interventionMainID: row.annualPlanInterventionMainID });
    }
    annualInterventions.value.splice(index, 1);
    showMessage("success", "删除成功");
  });
};
//#endregion

//#region 负责人选择
const { employeeIDs, customGroupName, init, confirm, useRecommendOptions, createJointEmployeeNamesFunc } = usePrincipalSelector({
  refKey: "principalSelector"
});
//#endregion

//#region 字典列
// 拷贝执行项目列表，用于后续生成保存View时，判断是否是新增的执行项目
const cloneAnnualInterventionList = computed(() => {
  return common.clone(annualInterventionList);
});
const { addUsedInterventionID, removeUsedInterventionID } = useAnnualPlanFormulateStore();
const { usedInterventionIDs } = storeToRefs(useAnnualPlanFormulateStore());
/**
 * @description: 当分解目标任务字典ID发生变动时，需更新当前行并更新已使用的分解目标任务字典集合
 * @param selectInterventionID 新选中的执行项目ID
 * @param row 当前行
 * @return
 */
const updateRowAndUsedInterventionIDs = (selectInterventionID: number, row: Record<string, any>) => {
  // 注意：切不可改为v-model+change回调实现，因为需要读取之前的interventionID，而change回调中无法获取
  selectRow(row);
  if (selectInterventionID !== 0) {
    addUsedInterventionID(selectInterventionID);
    // 如果此前有选择过分解目标任务字典，则需要从已使用执行项目中移除
    if (row.interventionID) {
      removeUsedInterventionID(row.interventionID);
    }
    const intervention = annualInterventionList.find((intervention) => intervention.annualInterventionID === selectInterventionID);
    row.localShowName = intervention?.interventionContent;
  }
  row.interventionID = selectInterventionID;
};
//#endregion

//#region 单元格点击
const initRowData = (row: any) => {
  init(row.principalIDs, row.principalGroupName);
  drawerOptions.value.showDrawer = true;
};
const { drawerRow, handleCellClick } = readOnly.value ? ({} as Record<string, any>) : useCellClick(initRowData, tableRef);
//#endregion

const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "编辑负责人",
  showDrawer: false,
  drawerSize: "55%",
  showConfirm: true,
  confirm: () => {
    if (!tableRef.value || !drawerRow.value) {
      return;
    }
    nextTick(() => {
      tableRef.value!.toggleRowSelection(toValue(drawerRow), true);
    });
    confirm(drawerRow);
    drawerOptions.value.showDrawer = false;
  }
});

/**
 * @description: 选中当前行
 * @param row 当前行
 * @return
 */
const selectRow = (row: Record<string, any>) => {
  nextTick(() => {
    tableRef.value!.toggleRowSelection(row, true);
  });
};

defineExpose({
  /**
   * @description: 新增行
   */
  addRow: () => {
    if (!projectDetailID && !projectDetails?.length) {
      showMessage("warning", "当前目标未创建任何工作项目，暂无法新增执行项目！请转到【计划维护】页面为目标维护工作项目再重试！");
      return;
    }
    const row = {
      annualPlanInterventionMainID: `temp_${common.guid()}`,
      projectDetailID: projectDetailID,
      annualPlanMainGoalID: mainGoalID,
      planMonths: [] as any[],
      isProjectDetailSelectorEdit: true,
      isInterventionSelectorEdit: true,
      isInterventionInputEdit: true,
      isPrincipalEdit: true
    };
    if (!annualInterventions.value) {
      annualInterventions.value = [row];
    } else {
      annualInterventions.value.push(row);
    }
    selectRow(row);
  },
  /**
   * @description: 获取待保存数据
   */
  getSaveInterventions: () => {
    // 过滤掉未填写计划执行项目名称的项
    const selections = tableRef.value!.getSelectionRows().filter((m: Record<string, any>) => m.localShowName);
    // 清除interventionID不是数字的（即新定义的执行项目）
    const isNumber = (value: any) => typeof value === "number" && !isNaN(value);
    selections.forEach((m: Record<string, any>) => {
      if (!isNumber(m.interventionID)) {
        m.interventionID = undefined;
      }
    });
    return selections;
  }
});
</script>
<style lang="scss">
/*
此处不可移至annual-intervention-table内部，因为气泡弹窗都是在body下
*/
.reference-intervention-help {
  font-size: 15px;
  line-height: 0;
}
.project-list-popper {
  width: 0;
  background-color: #ffffff;
}
.annual-intervention-table {
  .main-table {
    height: 100%;
    .el-scrollbar__bar.is-horizontal {
      display: none !important;
    }
    td:not(.sort) {
      cursor: pointer;
    }
    .el-select {
      width: 100%;
    }
    .plan-month {
      font-size: 45px;
    }
  }
}
</style>
