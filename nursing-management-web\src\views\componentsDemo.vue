<!--
 * FilePath     : \src\views\componentsDemo.vue
 * Author       : 郭鹏超
 * Date         : 2023-08-31 11:37
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-03-28 15:11
 * Description  : 组件演示
 * CodeIterationRecord:
-->
<template>
  <div class="components-demo">
    <File class="file-demo" :fileOption="fileOption" @getExcelData="getExcelData"></File>
    <el-table stripe highlight-current-row :data="tableData" border height="100%" ref="excelEditor">
      <el-table-column prop="departmentName" label="部门" min-width="80" />
      <el-table-column prop="employeeName" label="姓名" min-width="80" />
      <el-table-column prop="hrpEmployeeID" label="HRP编码" min-width="80" />
      <el-table-column prop="gender" label="性别" min-width="80" />
      <el-table-column prop="nation" label="民族" min-width="80" />
      <el-table-column prop="birthdate" label="出生日期" min-width="80" />
      <el-table-column prop="age" label="年龄" min-width="80" />
    </el-table>
  </div>
</template>
<script setup lang="ts">
const excelEditor = ref<any | undefined>(undefined);
// 表格数据
let tableData = ref<any[]>([
  {
    employeeID: "108687",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "900580",
    employeeName: "张锦",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1992-08-12 00:00:00",
    age: 31,
    entryDate: "2015-07-11 00:00:00",
    entryAge: 8.3,
    homeAddress: "河南省长垣县惠民佳苑12-1-301",
    actualAddress: "宏力医院青年公寓B座303室",
    nationCode: "01",
    nativePlace: "河南省长垣县",
    fileID: "HL-11632",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N4",
    capabilityLevelID: 5,
    promotionDate: "2011-06-16 00:00:00",
    professionalLevel: "主管护师",
    professionalCode: "25300",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "108707",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "900579",
    employeeName: "李雪艳",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1992-08-13 00:00:00",
    age: 31,
    entryDate: "2015-07-11 00:00:00",
    entryAge: 8.3,
    homeAddress: "河南省西华县逍遥镇李寨村四组",
    actualAddress: "食博园北区23号楼1单元4楼西户",
    nationCode: "01",
    nativePlace: "河南省西华县",
    fileID: "HL-11640",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N2",
    capabilityLevelID: 3,
    promotionDate: "2011-06-08 00:00:00",
    professionalLevel: "主管护师",
    professionalCode: "25300",
    obtainingDate: "2022-07-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "128354",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "901068",
    employeeName: "吴玉娟",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1983-09-21 00:00:00",
    age: 40,
    entryDate: "2008-07-13 00:00:00",
    entryAge: 15.3,
    homeAddress: "河南省范县陈庄乡杨吴庄",
    actualAddress: "宏力学校家属院3号楼1单元2楼西",
    nationCode: "01",
    nativePlace: "河南省范县",
    fileID: "HL-9798",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: null,
    capabilityLevelID: null,
    promotionDate: "2010-04-20 00:00:00",
    professionalLevel: "副主任护师",
    professionalCode: "25200",
    obtainingDate: "2022-12-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "134885",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "900576",
    employeeName: "刘旭满",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1986-11-20 00:00:00",
    age: 36,
    entryDate: "2010-07-12 00:00:00",
    entryAge: 13.3,
    homeAddress: "河南省濮阳市南乐县西邵乡刘苑村",
    actualAddress: "宏力医院家属院2号楼2单元4楼东",
    nationCode: "01",
    nativePlace: "河南省南乐县",
    fileID: "HL-10311",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N4",
    capabilityLevelID: 5,
    promotionDate: "2010-09-06 00:00:00",
    professionalLevel: "主管护师",
    professionalCode: "25300",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "142180",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "900575",
    employeeName: "郭冰滑",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1989-05-22 00:00:00",
    age: 34,
    entryDate: "2012-04-13 00:00:00",
    entryAge: 11.5,
    homeAddress: "河南省滑县牛屯镇黄默村",
    actualAddress: "宏力医院青年公寓C625",
    nationCode: "01",
    nativePlace: "河南省滑县",
    fileID: "HL-10650",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N4",
    capabilityLevelID: 5,
    promotionDate: "2010-09-08 00:00:00",
    professionalLevel: "主管护师",
    professionalCode: "25300",
    obtainingDate: "2020-09-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "147431",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "900583",
    employeeName: "李建苹",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1990-06-09 00:00:00",
    age: 33,
    entryDate: "2013-07-11 00:00:00",
    entryAge: 10.3,
    homeAddress: "河南省卫辉市李源屯镇大李湾村4组008号",
    actualAddress: "宏力医院青年公寓B1224",
    nationCode: "01",
    nativePlace: "河南省卫辉市",
    fileID: "HL-11117",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N4",
    capabilityLevelID: 5,
    promotionDate: "2010-10-31 00:00:00",
    professionalLevel: "",
    professionalCode: "254",
    obtainingDate: "2018-05-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "157067",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902087",
    employeeName: "张莉华",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1995-10-27 00:00:00",
    age: 28,
    entryDate: "2017-07-09 00:00:00",
    entryAge: 6.3,
    homeAddress: "河南省卫辉市安都乡饮马庄村",
    actualAddress: "宏力医院青年公寓A座806",
    nationCode: "01",
    nativePlace: "河南省卫辉市",
    fileID: "HL-12052",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N1",
    capabilityLevelID: 2,
    promotionDate: "2011-12-07 00:00:00",
    professionalLevel: "护师",
    professionalCode: "25400",
    obtainingDate: "2019-06-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "157977",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902170",
    employeeName: "魏宇捷",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1996-11-18 00:00:00",
    age: 26,
    entryDate: "2018-07-10 00:00:00",
    entryAge: 5.3,
    homeAddress: "河南省新乡市卫辉南关南街67号",
    actualAddress: "青年公寓A座211",
    nationCode: "01",
    nativePlace: "河南省卫辉市",
    fileID: "HL-12117",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N2",
    capabilityLevelID: 3,
    promotionDate: "2012-03-13 00:00:00",
    professionalLevel: "",
    professionalCode: "254",
    obtainingDate: "2019-06-01 00:00:00",
    firstDegree: "中专",
    firstDegreeCode: "40",
    highestDegree: "中专",
    highestDegreeCode: "40"
  },
  {
    employeeID: "158501",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902286",
    employeeName: "马莹",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1996-05-08 00:00:00",
    age: 27,
    entryDate: "2018-07-10 00:00:00",
    entryAge: 5.3,
    homeAddress: "河南省安阳市滑县牛屯镇大马村",
    actualAddress: "青年公寓A1124",
    nationCode: "01",
    nativePlace: "河南省滑县",
    fileID: "HL-12218",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N2",
    capabilityLevelID: 3,
    promotionDate: "2012-03-12 00:00:00",
    professionalLevel: "护师",
    professionalCode: "25400",
    obtainingDate: "2020-09-01 00:00:00",
    firstDegree: "中专",
    firstDegreeCode: "40",
    highestDegree: "中专",
    highestDegreeCode: "40"
  },
  {
    employeeID: "159236",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902368",
    employeeName: "张蕊蕊",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1997-10-02 00:00:00",
    age: 26,
    entryDate: "2019-07-16 00:00:00",
    entryAge: 4.3,
    homeAddress: "河南省滑县老庙乡北塔邱村203号",
    actualAddress: "宏力医院青年公寓A518室",
    nationCode: "01",
    nativePlace: "河南省滑县",
    fileID: "HL-12280",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N2",
    capabilityLevelID: 3,
    promotionDate: "2012-05-28 00:00:00",
    professionalLevel: "",
    professionalCode: "254",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "159615",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902512",
    employeeName: "袁晓莹",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1997-11-14 00:00:00",
    age: 25,
    entryDate: "2019-07-16 00:00:00",
    entryAge: 4.3,
    homeAddress: "河南省滑县慈周寨乡北李庄村545号",
    actualAddress: "宏力医院青年公寓A座812室",
    nationCode: "01",
    nativePlace: "河南省滑县",
    fileID: "HL-12412",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N2",
    capabilityLevelID: 3,
    promotionDate: "2012-05-30 00:00:00",
    professionalLevel: "",
    professionalCode: "254",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "160185",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902599",
    employeeName: "张瑞霞",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1995-08-12 00:00:00",
    age: 28,
    entryDate: "2020-04-02 00:00:00",
    entryAge: 3.5,
    homeAddress: "河南省商丘市睢县平岗镇定张存",
    actualAddress: "宏力医院青年公寓A座606室",
    nationCode: "01",
    nativePlace: "河南省睢县",
    fileID: "HL-12487",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N1",
    capabilityLevelID: 2,
    promotionDate: "2012-08-09 00:00:00",
    professionalLevel: "护师",
    professionalCode: "25400",
    obtainingDate: "2020-09-01 00:00:00",
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "160528",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "902689",
    employeeName: "张梦薇",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1999-12-24 00:00:00",
    age: 23,
    entryDate: "2020-07-22 00:00:00",
    entryAge: 3.2,
    homeAddress: "河南省滑县白道口镇陈营村",
    actualAddress: "青年公寓A516",
    nationCode: "01",
    nativePlace: "河南省滑县",
    fileID: "HL-12567",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N1",
    capabilityLevelID: 2,
    promotionDate: "2012-10-13 00:00:00",
    professionalLevel: "",
    professionalCode: "255",
    obtainingDate: "2019-05-01 00:00:00",
    firstDegree: "中专",
    firstDegreeCode: "40",
    highestDegree: "中专",
    highestDegreeCode: "40"
  },
  {
    employeeID: "161226",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "903030",
    employeeName: "王晴",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1999-07-23 00:00:00",
    age: 24,
    entryDate: "2021-07-15 00:00:00",
    entryAge: 2.3,
    homeAddress: "河南省安阳市内黄县井店镇李河道村",
    actualAddress: "宏力医院青年公寓A座615室",
    nationCode: "01",
    nativePlace: "河南省安阳市内黄县",
    fileID: "HL-12831",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N1",
    capabilityLevelID: 2,
    promotionDate: "2013-03-24 00:00:00",
    professionalLevel: "护士",
    professionalCode: "25500",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "161230",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "903038",
    employeeName: "韩宝璐",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "1999-07-29 00:00:00",
    age: 24,
    entryDate: "2021-07-15 00:00:00",
    entryAge: 2.3,
    homeAddress: "河南省长垣市樊相镇八匹黑马寨村",
    actualAddress: "宏力医院青年公寓B座312室",
    nationCode: "01",
    nativePlace: "河南省长垣市",
    fileID: "HL-12836",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N1",
    capabilityLevelID: 2,
    promotionDate: "2013-03-26 00:00:00",
    professionalLevel: "护士",
    professionalCode: "25500",
    obtainingDate: "2021-04-01 00:00:00",
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  },
  {
    employeeID: "162870",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "903714",
    employeeName: "王晗",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "2001-02-26 00:00:00",
    age: 22,
    entryDate: "2023-07-10 00:00:00",
    entryAge: 0.3,
    homeAddress: "河南省新乡市长垣市高店社区",
    actualAddress: "宏力医院青年公寓A座210室",
    nationCode: "01",
    nativePlace: "河南省,新乡市,长垣县",
    fileID: "HL-13411",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N0",
    capabilityLevelID: 1,
    promotionDate: "2011-02-23 00:00:00",
    professionalLevel: "",
    professionalCode: null,
    obtainingDate: null,
    firstDegree: "本科",
    firstDegreeCode: "20",
    highestDegree: "本科",
    highestDegreeCode: "20"
  },
  {
    employeeID: "162898",
    departmentID: 209,
    departmentName: "骨科二病区",
    hrpEmployeeID: "903759",
    employeeName: "马瑶瑶",
    genderCode: "20",
    gender: "女",
    nation: "汉族",
    birthdate: "2002-03-16 00:00:00",
    age: 21,
    entryDate: "2023-07-10 00:00:00",
    entryAge: 0.3,
    homeAddress: "河南省许昌市建安区灵井镇南马庄",
    actualAddress: "宏力医院青年公寓A座617室",
    nationCode: "01",
    nativePlace: "河南省,许昌市,许昌县",
    fileID: "HL-13438",
    jobCategoryCode: "10",
    jobCategory: "长期聘用",
    capabilityLevel: "N0",
    capabilityLevelID: 1,
    promotionDate: "2011-02-24 00:00:00",
    professionalLevel: "",
    professionalCode: null,
    obtainingDate: null,
    firstDegree: "大专",
    firstDegreeCode: "30",
    highestDegree: "大专",
    highestDegreeCode: "30"
  }
]);
// Excel导入参数
const importExcelOption = reactive<ImportExcelView>({
  columnData: {
    departmentName: "部门",
    employeeName: "姓名",
    hrpEmployeeID: "HRP编码",
    gender: "性别",
    nation: "民族",
    birthdate: "出生日期",
    age: "年龄"
  },
  buttonName: "Excel文件数据导入"
});
// 得到excel导入数据
const getExcelData = (data: any) => {
  tableData.value = data;
  exportWordOption.wordData = data;
};

// Excel 导出参数
const exportExcelOption = reactive<ExportExcelView[]>([
  {
    buttonName: "生成Excel文件",
    fileName: "人员清单",
    sheetName: "testSheet",
    columnData: importExcelOption.columnData,
    tableData: tableData.value
  }
]);

// pdf导出参数
const exportPdfOption = reactive<ExportPdfView>({
  buttonName: "生成PDF文件",
  fileName: "人员清单",
  element: undefined
});
onMounted(() => {
  exportPdfOption.element = excelEditor.value.$el;
  console.log(excelEditor.value.$el);
});

// word导出参数
const exportWordOption = reactive<ExportWordView>({
  buttonName: "生成Word文件",
  fileName: "人员清单",
  wordData: tableData.value,
  templateWordUrl: "/static/wordTemplate/test.docx"
});

// file组件参数
const fileOption = reactive<FilePropsView>({
  typeArr: ["importExcel", "exportExcel", "exportPdf", "exportWord"],
  importExcelOption,
  exportExcelOption,
  exportWordOption,
  exportPdfOption
});
</script>
<style lang="scss">
.components-demo {
  height: 100%;
  background-color: #ffffff;
  .file-demo {
    float: right;
  }
}
</style>
