/*
 * FilePath     : /src/views/annualPlan/maintain/hooks/useAnnualPlanIndicatorActions.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-27 14:48
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 20:36
 * Description  : 年度计划策略指标状态与行为
 * CodeIterationRecord:
 */
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import type { planGroup, planIndicator } from "../../types/annualPlanMain";
import { showMessage } from "@/utils/message";
import type { MaybeRefOrGetter } from "vue";
import type { annualIndicatorList } from "../../types/annualPlanDictionary";
import { min } from "lodash-es";

export default function useAnnualPlanIndicatorActions(
  calculateNewSortByParent: <T, P extends { sort: number }>(
    array: { [key: string]: T[] } | T[],
    parentItems: MaybeRefOrGetter<P[]>,
    groupKey: keyof P & keyof T
  ) => Map<string, number>,
  convertToObject: <S extends { sort: number }>(items: { [key: string]: S[] } | S[], itemKey: keyof S) => Record<string, number>
) {
  const planManagementStore = usePlanManagementStore();
  const maintainStore = useAnnualPlanMaintainStore();
  const indicatorNewSortByGroup = computed(() =>
    calculateNewSortByParent(maintainStore.indicatorsByGroupID, maintainStore.planGroups, "groupID")
  );
  const isIndicatorList = (indicatorOrDict: annualIndicatorList | planIndicator): indicatorOrDict is annualIndicatorList =>
    "indicatorContent" in indicatorOrDict;
  maintainStore.$onAction(({ name, after }) => {
    if (name === "addUnExpandedPlanGoalIDs") {
      after(async (result) => {
        if (!result?.length) {
          return;
        }
        const params = {
          mainID: planManagementStore.annualPlanMainID,
          mainGoalIDs: result
        };
        await setPlanIndicators(params);
      });
    }
  });

  /**
   * @description: 获取并设置完整的策略指标数据
   * @param params 参数
   * @return
   */
  const setPlanIndicators = async (params: { mainID: string; mainGoalIDs: string[] }) => {
    const resultArr = await annualPlanMainService.getIndicatorDetails(params);
    maintainStore.$patch(() => {
      maintainStore.planGroups
        .filter((group) => params.mainGoalIDs.includes(group.mainGoalID))
        .forEach((group) => {
          maintainStore.indicatorsByGroupID[group.groupID] = resultArr.filter((planIndicator) => planIndicator.groupID === group.groupID);
        });
    });
  };

  return {
    /**
     * @description: 新增策略指标到目标分组
     * @param newIndicator 新策略指标
     * @return
     */
    addNewPlanIndicatorToPlanGroup: async (newIndicator: planIndicator) => {
      newIndicator.sort = indicatorNewSortByGroup.value.get(newIndicator.groupID)!;
      // 从新指标序号开始，更新所有后续指标的序号
      maintainStore.planGroups.forEach((group) => {
        const groupIndicators = maintainStore.indicatorsByGroupID[group.groupID];
        if (groupIndicators) {
          groupIndicators.filter((indicator) => indicator.sort >= newIndicator.sort).forEach((indicator) => (indicator.sort += 1));
        }
      });
      // 添加到分组
      if (!isIndicatorList(newIndicator)) {
        const targetGroup = maintainStore.indicatorsByGroupID[newIndicator.groupID];
        targetGroup.push(newIndicator);
      }
      // 持久化变更
      const params = {
        detail: newIndicator,
        departmentID: planManagementStore.departmentID
      };
      ({ detailID: newIndicator.detailID, annualIndicatorID: newIndicator.annualIndicatorID } =
        await annualPlanMainService.addIndicatorDetail(params));
    },
    /**
     * @description: 更新策略指标
     * @param updateIndicator 待更新的策略指标
     * @return
     */
    updatePlanIndicator: async (updateIndicator: planIndicator) => {
      const params = {
        detail: updateIndicator,
        departmentID: planManagementStore.departmentID
      };
      const result = await annualPlanMainService.updateIndicatorDetail(params);
      if (!result) {
        showMessage("error", "更新指标失败");
      }
    },
    /**
     * @description: 重新计算策略指标的Sort
     * @param draggedIndicator 被拖拽的策略指标
     * @param sourcePlanGroup 当前所在分组
     * @return
     */
    resetPlanIndicatorSort: async (draggedIndicator: planIndicator, sourcePlanGroup: planGroup | undefined = undefined) => {
      let isInGroup = true;
      if (sourcePlanGroup) {
        isInGroup = false;
        draggedIndicator.mainGoalID = sourcePlanGroup.mainGoalID;
        draggedIndicator.groupID = sourcePlanGroup.groupID;
      }

      if (isInGroup) {
        const groupID = draggedIndicator.groupID;
        const groupIndicators = maintainStore.indicatorsByGroupID[groupID];
        let startSort = min(groupIndicators.map(({ sort }) => sort))!;
        maintainStore.indicatorsByGroupID[groupID].forEach((planIndicator) => {
          startSort !== planIndicator.sort && (planIndicator.sort = startSort);
          startSort += 1;
        });
      } else {
        let indicatorSort = 1;
        maintainStore.planGroups.forEach((group) => {
          maintainStore.indicatorsByGroupID[group.groupID]?.forEach((planIndicator) => (planIndicator.sort = indicatorSort++));
        });
      }
      const params = {
        mainID: planManagementStore.annualPlanMainID,
        draggedPlanIndicator: draggedIndicator,
        planIndicatorIDAndSort: convertToObject(maintainStore.indicatorsByGroupID, "detailID")
      };
      await annualPlanMainService.resetAnnualPlanIndicatorsSort(params);
    },
    /**
     * @description: 删除策略指标
     * @param deletePlanIndicator 待删除的策略指标
     * @return
     */
    deleteIndicator: async (deletePlanIndicator: planIndicator) => {
      await deleteConfirm("确定要删除么？", async (flag: Boolean) => {
        if (!flag) {
          return;
        }
        const targetGroupIndicators = maintainStore.indicatorsByGroupID[deletePlanIndicator.groupID];
        const deleteIndex = targetGroupIndicators.findIndex((planIndicator) => planIndicator.detailID === deletePlanIndicator.detailID);
        if (deleteIndex === -1) {
          showMessage("error", "删除策略指标失败：未找到策略指标");
          return;
        }
        targetGroupIndicators.splice(deleteIndex, 1);
        maintainStore.planGroups.forEach((group) => {
          const groupIndicators = maintainStore.indicatorsByGroupID[group.groupID];
          if (groupIndicators) {
            groupIndicators.filter((indicator) => indicator.sort > deletePlanIndicator.sort).forEach((indicator) => (indicator.sort -= 1));
          }
        });
        // 持久化变更
        const params = {
          mainID: deletePlanIndicator.mainID,
          detailID: deletePlanIndicator.detailID
        };
        const result = await annualPlanMainService.deleteIndicatorDetail(params);
        if (!result) {
          showMessage("error", "删除指标失败");
        }
      });
    }
  };
}
