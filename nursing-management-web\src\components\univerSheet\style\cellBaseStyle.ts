/*
 * FilePath     : \src\components\univerSheet\style\cellBaseStyle.ts
 * Author       : 苏军志
 * Date         : 2024-11-11 08:57
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-25 19:05
 * Description  :
 * CodeIterationRecord:
 */
import { HorizontalAlign, VerticalAlign, WrapStrategy } from "@univerjs/core";
export const cellBaseStyle = {
  // 水平居中
  ht: HorizontalAlign.CENTER,
  // 垂直居中
  vt: VerticalAlign.MIDDLE,
  tb: WrapStrategy.WRAP,
  cl: { rgb: "#000000" },
  fs: 10,
  bd: {
    // 上边框
    t: {
      s: 1, // 边框样式
      cl: {
        // 边框颜色
        rgb: "#c0c0c0"
      }
    },
    // 下边框
    b: {
      s: 1, // 边框样式
      cl: {
        // 边框颜色
        rgb: "#c0c0c0"
      }
    },
    // 左边框
    l: {
      s: 1, // 边框样式
      cl: {
        // 边框颜色
        rgb: "#c0c0c0"
      }
    },
    // 右边框
    r: {
      s: 1, // 边框样式
      cl: {
        // 边框颜色
        rgb: "#c0c0c0"
      }
    }
  }
};
