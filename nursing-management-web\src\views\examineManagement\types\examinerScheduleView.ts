/*
 * FilePath     : \src\views\examineManagement\types\examinerScheduleView.ts
 * Author       : 张现忠
 * Date         : 2025-03-10 17:00
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-06 16:24
 * Description  : 监考计划相关类型声明
 * CodeIterationRecord:
 */

/**
 * 监考计划列表视图接口
 */
export interface examinerScheduleView extends examinerScheduleForm {
  /**
   * 考试名称
   */
  examinationName: string;
  /**
   * 监考人员姓名
   */
  examinerName: string;
  /**
   * 新增时间
   */
  addDateTime: string;
  /**
   * 新增人姓名
   */
  addEmployeeName: string;
  /**
   * 最后修改时间
   */
  modifyDateTime: string;
  /**
   * 最后修改人姓名
   */
  modifyEmployeeName: string;
}

/**
 * 监考计划筛选条件接口
 */
export interface examinerScheduleForm {
  /**
   * 监考计划主键ID
   */
  examinerScheduleID?: string;
  /**
   * 考试记录ID
   */
  examinationRecordIDs: string[];
  /**
   * 监考人员ID
   */
  examiners: string[];
  /**
   * 监考日期
   */
  scheduleDate: Date | string;
  /**
   * 时间段
   */
  scheduleTimeRange: string[];
  /**
   * 状态:0未被预约，1已被预约
   */
  statusCode: string;
  /**
   * 是否批量生成监考计划
   */
  batchFlag?: boolean;
  /**
   * 批量生成的间隔天数
   */
  batchInterval?: number;
  /**
   * 批量生成的截至日期
   */
  batchEndDate?: Date | string;
  /**
   * 考核地点
   */
  location?: string;
}
