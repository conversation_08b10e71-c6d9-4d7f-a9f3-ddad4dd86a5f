/*
 * FilePath     : \src\views\annualPlan\hooks\usePlanManagementStore.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-23 10:32
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 09:20
 * Description  :
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";

/**
 * @description: 计划管理共享状态
 */
export const usePlanManagementStore = defineStore("annualPlan", () => {
  const departmentID = ref<number>(useStore().userStore.departmentID);
  const annualPlanMainID = ref<string>("");
  const annualPlanPublished = ref<boolean>(false);
  const quarterPlanMainID = ref<string>("");
  const monthlyPlanMainID = ref<string>("");
  const readOnly = ref<boolean>(false);

  /**
   * @description: 设置只读
   * @param value 新值
   * @return
   */
  const setReadOnly = (value: boolean) => (readOnly.value = value);
  /**
   * @description: 设置年度计划主键ID
   */
  const setAnnualPlanMainID = async (reset: boolean = false) => {
    if (!reset && annualPlanMainID.value) {
      return;
    }
    const params = {
      departmentID: departmentID.value,
      year: usePlanTime().getPlanAnnual()
    };
    ({ item1: annualPlanMainID.value, item2: annualPlanPublished.value } = await annualPlanMainService.getAnnualPlanMainIDAndStatus(
      params
    ));
  };
  /**
   * @description: 设置季度计划主键ID
   * @param quarter 季度
   * @param reset 是否重新设置ID
   * @return
   */
  const setQuarterPlanMainID = async (quarter: MaybeRefOrGetter<number>, reset: boolean = false) => {
    if (!reset && quarterPlanMainID.value) {
      return false;
    }
    if (!annualPlanMainID.value) {
      showMessage("warning", "请先制定年度计划");
      return false;
    }
    const params = {
      annualPlanMainID: annualPlanMainID.value,
      quarter: toValue(quarter)
    };
    quarterPlanMainID.value = await quarterPlanMaintainService.getQuarterPlanMainID(params);
    return true;
  };
  /**
   * @description: 设置月度计划主键ID
   * @param month 月份
   * @param reset 是否重新设置ID
   * @return
   */
  const setMonthlyPlanMainID = async (month: MaybeRefOrGetter<number>, reset: boolean = false) => {
    if (!reset && monthlyPlanMainID.value) {
      return false;
    }
    if (!annualPlanMainID.value) {
      showMessage("warning", "请先制定年度计划");
      return false;
    }
    const params = {
      annualPlanMainID: annualPlanMainID.value,
      month: toValue(month)
    };
    monthlyPlanMainID.value = await monthlyPlanMaintainService.getMonthlyPlanMainID(params);
    return true;
  };
  return {
    departmentID,
    annualPlanMainID,
    annualPlanPublished,
    quarterPlanMainID,
    monthlyPlanMainID,
    readOnly,
    setReadOnly,
    setAnnualPlanMainID,
    setQuarterPlanMainID,
    setMonthlyPlanMainID
  };
});
