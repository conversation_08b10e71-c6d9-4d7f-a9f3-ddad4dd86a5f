<template>
  <div ref="container" :class="['univer-sheet', { 'hidden-menu-more': config.showMenuList?.length }]"></div>
</template>

<script setup lang="ts">
import { Univer, UniverInstanceType, Workbook, LocaleType, LogLevel, Tools } from "@univerjs/core";
import type { IWorkbookData, IWorksheetData, IRange } from "@univerjs/core";
import { defaultTheme, greenTheme } from "@univerjs/design";
import { UniverDocsPlugin } from "@univerjs/docs";
import { UniverDocsUIPlugin } from "@univerjs/docs-ui";
import { UniverFormulaEnginePlugin } from "@univerjs/engine-formula";
import { UniverRenderEnginePlugin } from "@univerjs/engine-render";
import { UniverSheetsPlugin, SetSelectionsOperation, RangeProtectionPermissionEditPoint } from "@univerjs/sheets";
import { UniverSheetsFormulaPlugin } from "@univerjs/sheets-formula";
import { UniverSheetsUIPlugin } from "@univerjs/sheets-ui";
import { UniverUIPlugin, IContextMenuService, ComponentManager } from "@univerjs/ui";
import { UniverSheetsCrosshairHighlightPlugin } from "@univerjs/sheets-crosshair-highlight";
import { UniverThreadCommentUIPlugin } from "@univerjs/thread-comment-ui";
import { UniverThreadCommentPlugin } from "@univerjs/thread-comment";
import { UniverSheetsThreadCommentBasePlugin } from "@univerjs/sheets-thread-comment-base";
import { UniverSheetsThreadCommentPlugin } from "@univerjs/sheets-thread-comment";
import { FUniver, FWorkbook, FWorksheet } from "@univerjs/facade";
/**
 * 【从虚拟模块导入语言包】以及【自动导入样式】是由 Univer Plugins 提供的能力，详情参考：https://univer.ai/zh-CN/guides/sheet/advanced/univer-plugins
 * 如果在使用该插件的时候出现了问题，或者无法理解如何使用，请禁用 Univer Plugins，并手动导入语言包和样式
 */
import { zhCN, enUS } from "univer:locales";
// 重绘行号
import { rowHeaderCustomExtension } from "./plugin/rowHeaderCustomExtension";
// 自定义菜单插件
import { CustomMenuPlugin } from "./plugin/customMenuPlugin";
import { useUniverUtil } from "./hooks/useUniverUtil";
import popupMessage from "./components/popupMessage";

const props = defineProps({
  data: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  },
  config: {
    type: Object as PropType<Record<string, any>>,
    default: () => {}
  }
});
const { data, config } = props;
const { hexToRgb, convertMenu, getHideMenuList } = useUniverUtil({});
const univerRef = ref<Univer | undefined>();
const univerAPI = ref<FUniver | undefined>();
const workbook = ref<Workbook | undefined>();
const container = ref<HTMLElement | undefined>();
let defaultReadonlyRanges: Record<string, number>[] = [];
let readonlyRanges: Record<string, number>[] = [];

onMounted(() => init());
onBeforeUnmount(() => {
  destroyUniver();
});
/**
 * @description: 初始化
 * @return
 */
const init = () => {
  // 如果设置了标题行，标题默认只读
  if (config.headerRowCount) {
    defaultReadonlyRanges = [
      { startRow: 0, startColumn: 0, endRow: config.headerRowCount - 1, endColumn: config.maxColumnCount - 1, canMoveRow: 0 }
    ];
  }
  // univer对象实例化
  const univer = new Univer({
    theme: defaultTheme,
    locale: LocaleType.ZH_CN,
    locales: {
      [LocaleType.ZH_CN]: zhCN,
      [LocaleType.EN_US]: enUS
    },
    logLevel: LogLevel.ERROR
  });
  /**
   * 注意其他地方使用univerRef.value时必须使用toRaw获取原始对象
   */
  univerRef.value = univer;
  univerAPI.value = FUniver.newAPI(univer);
  // 注册插件
  registerPlugin(toRaw(univerRef.value)!);
  // 组件渲染后的生命周期
  univerAPI.value?.getHooks().onRendered(() => {
    const book = univerAPI.value?.getActiveWorkbook()!;
    const sheet = book.getActiveSheet();
    // 更改行号渲染
    const headerFontColor = hexToRgb(config.headerFontColor || "#000000");
    const headerBackgroundColor = hexToRgb(config.headerBackgroundColor || "#ffffff");
    univerAPI.value?.registerSheetRowHeaderExtension(
      book.getId()!,
      new rowHeaderCustomExtension(
        config.headerRowCount,
        config.showRowIndexEnd || sheet.getMaxColumns(),
        headerFontColor,
        headerBackgroundColor
      )
    );
    // 注册自定义组件
    const componentManager = toRaw(univerRef.value)?.__getInjector().get(ComponentManager);
    componentManager?.register("popupMessage", popupMessage, { framework: "vue3" });
    // 清空默认选区
    clearSelections(book, sheet);
    // 命令执行前拦截
    commandInterception(book, sheet);
    // 命令执行后触发
    commandTrigger(book, sheet);
  });
  // 创建workbook实例
  workbook.value = univer.createUnit<IWorkbookData, Workbook>(UniverInstanceType.UNIVER_SHEET, data);
};
/**
 * @description: 注册univer插件
 * @param univer
 * @return
 */
const registerPlugin = (univer: Univer) => {
  univer.registerPlugin(UniverRenderEnginePlugin);
  univer.registerPlugin(UniverFormulaEnginePlugin);
  univer.registerPlugin(UniverSheetsFormulaPlugin);
  univer.registerPlugin(UniverUIPlugin, {
    container: container.value,
    header: config.showHeader,
    toolbar: config.showToolbar,
    footer: config.showFooter
  });

  // doc plugins
  univer.registerPlugin(UniverDocsPlugin, { hasScroll: false });
  univer.registerPlugin(UniverDocsUIPlugin);

  // sheet plugins
  univer.registerPlugin(UniverSheetsPlugin);
  // 根据参数获取隐藏菜单
  const hiddenMenuList = getHideMenuList(config.showMenuList);
  univer.registerPlugin(UniverSheetsUIPlugin, {
    menu: hiddenMenuList,
    formulaBar: config.showFormula
  });
  univer.registerPlugin(UniverSheetsCrosshairHighlightPlugin);
  univer.registerPlugin(UniverThreadCommentPlugin);
  univer.registerPlugin(UniverThreadCommentUIPlugin);
  univer.registerPlugin(UniverSheetsThreadCommentBasePlugin);
  univer.registerPlugin(UniverSheetsThreadCommentPlugin);

  // 注册自定义菜单
  const { commands, menus } = convertMenu(config.customMenuData, univerAPI.value!);
  univer.registerPlugin(CustomMenuPlugin, { commands, menus, menuIcons: config.customMenuIcons });
};

/**
 * @description: 清空选区
 * @param sheet
 * @param book
 * @return
 */
const clearSelections = (book: FWorkbook, sheet: FWorksheet) => {
  univerAPI.value?.executeCommand(SetSelectionsOperation.id, {
    selections: [],
    subUnitId: sheet.getSheetId(),
    unitId: book.getId(),
    type: 2
  });
};

/**
 * @description: 在执行命令前进行拦截
 * @param sheet
 * @param book
 */
const commandInterception = (book: FWorkbook, sheet: FWorksheet) => {
  // 监听单元格数据发生改变前
  sheet.onBeforeCellDataChange((cell: any) => {
    // // 撤销和恢复改变数据不拦截
    // if (undoOrRedoFlag) {
    //   return;
    // }
    if (Object.keys(cell.getMatrix()).length) {
      const checkResult = config.onBeforeCellDataChange?.(cell.getDataRange(), cell.getMatrix());
      if (checkResult !== undefined && !checkResult) {
        throw new Error("业务检核未通过");
      } else if (checkResult) {
        return;
      }
      const { startRow, startColumn, endRow, endColumn } = cell.getDataRange();
      for (let rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
        for (let columnIndex = startColumn; columnIndex <= endColumn; columnIndex++) {
          const isReadonly = readonlyRanges.find((readonlyRange) => {
            return (
              rowIndex >= readonlyRange.startRow &&
              rowIndex <= readonlyRange.endRow &&
              columnIndex >= readonlyRange.startColumn &&
              columnIndex <= readonlyRange.endColumn
            );
          });
          if (isReadonly) {
            // 拦截事件
            throw new Error("只读区域不允许操作");
          }
        }
      }
    }
  });
  univerAPI.value?.onBeforeCommandExecute((command: any) => {
    const contextMenuService = univerRef.value!.__getInjector().get(IContextMenuService);
    const range = sheet.getActiveRange()?.getRange()!;
    // 先将右键菜单打开
    contextMenuService.enable();
    // 临时屏蔽 剪切、复制、粘贴、撤销、恢复 命令
    if (
      [
        "univer.command.copy",
        "univer.command.cut",
        "univer.command.paste",
        "sheet.command.paste-bu-short-key",
        "univer.command.undo",
        "univer.command.redo"
      ].includes(command.id)
    ) {
      // 拦截事件
      throw new Error("暂时禁止复制、粘贴、剪切");
    }
    // 监听撤销、恢复
    // if (["univer.command.undo", "univer.command.redo"].includes(command.id)) {
    //   undoOrRedoFlag = true;
    // }
    // 单元格获取焦点
    if (command.id === "sheet.operation.set-activate-cell-edit") {
      if (readonlyRanges.length) {
        const isReadonly = readonlyRanges.find((readonlyRange) => {
          // range.rangeType=1时为整行选择
          return (
            (range.rangeType !== 1 || !readonlyRange.canMoveRow) &&
            range.startRow >= readonlyRange.startRow &&
            range.startRow <= readonlyRange.endRow &&
            range.endRow >= readonlyRange.startRow &&
            range.endRow <= readonlyRange.endRow &&
            range.startColumn >= readonlyRange.startColumn &&
            range.startColumn <= readonlyRange.endColumn &&
            range.endColumn >= readonlyRange.startColumn &&
            range.endColumn <= readonlyRange.endColumn
          );
        });
        if (isReadonly) {
          // 禁止右键菜单
          contextMenuService.hideContextMenu();
          contextMenuService.disable();
          // 清空选区
          clearSelections(book, sheet);
          // 拦截事件
          throw new Error("只读区域禁止任何操作");
        }
      }
    }
    // 禁止双击显示编辑框
    if (["sheet.operation.set-cell-edit-visible"].includes(command.id)) {
      throw new Error("禁止双击");
    }
    // 监听键盘录入
    if (["doc.command.ime-input", "doc.command.insert-text"].includes(command.id)) {
      throw new Error("禁止使用键盘录入");
    }
    // 禁止调整行高
    if (command.id === "sheet.command.delta-row-height") {
      throw new Error("禁止调整行高");
    }
    // 禁止异动单元格
    if (command.id === "sheet.command.move-range") {
      throw new Error("禁止移动单元格");
    }
    // 监听缩放
    if (["sheet.command.set-zoom-ratio", "sheet.operation.set-zoom-ratio"].includes(command.id)) {
      throw new Error("禁止缩放");
    }
    // 监听新增列
    if (command.id === "sheet.mutation.insert-col") {
      if (sheet?.getMaxColumns() === config.maxColumnCount) {
        showMessage("error", `新增列失败，已达到最大列数：${config.maxColumnCount}`);
        throw new Error("超出设置的最大列数，禁止新增列");
      }
    }
  });
};

/**
 * @description: 命令执行后触发
 * @param sheet
 * @param book
 */
const commandTrigger = (book: FWorkbook, sheet: FWorksheet) => {
  univerAPI.value?.getSheetHooks().onCellPointerMove((cell: any) => {
    // 拿到当前鼠标指向的单元格
    if (cell?.location) {
      const { row: rowIndex, col: columnIndex } = cell?.location;
      config.onCellHover?.(rowIndex, columnIndex);
    }
  });
  // 监听单元格数据发送改变
  sheet.onCellDataChange((cell: any) => {
    if (Object.keys(cell.getMatrix()).length) {
      config.onCellDataChange?.(cell.getDataRange(), cell.getMatrix());
    }
  });

  // 监听各种命令
  univerAPI.value?.onCommandExecuted((command: any) => {
    nextTick(() => {
      // 监听删除和插入列
      if (["sheet.mutation.remove-col", "sheet.mutation.insert-col", "sheet.command.move-cols"].includes(command.id)) {
        if (command.id == "sheet.mutation.insert-col") {
          const range = command.params.range;
          const insertRange = sheet.getRange(config.headerRowCount, range.startColumn, range.endRow + 1 + config.headerRowCount, 1);
          insertRange.setValue({ s: { bg: { rgb: "#ffffff" } }, v: "" });
        }
        config.onColumnChange?.();
      }
      // 监听插入行
      if (command.id === "sheet.mutation.insert-row") {
        const range = command.params.range;
        const insertRange = sheet.getRange(range.startRow, range.startColumn, 1, range.endColumn + 1);
        insertRange.setValue({ s: { bg: { rgb: "#ffffff" } }, v: "" });
      }
      // 监听清除选取内容
      if (command.id === "sheet.command.clear-selection-content") {
        config.onClearCell?.(sheet);
      }
      // 行顺序调整改变
      if (command.id === "sheet.command.move-rows") {
        // 回传新行号和旧的开始行号和结束行号
        config.onMoveRows?.(command.params.toRange.startRow, command.params.fromRange.startRow, command.params.fromRange.endRow);
      }
    });
  });
};

/**
 * @description: 设置只读区域
 * @param ranges
 * @param clearFlag
 * @return
 */
const setReadonlyRanges = async (ranges: Record<string, number>[], clearFlag?: boolean) => {
  if (clearFlag) {
    readonlyRanges = defaultReadonlyRanges;
  }
  // 合并去重
  const set = new Set([...readonlyRanges, ...ranges].map((item) => JSON.stringify(item)));
  readonlyRanges = Array.from(set).map((item) => JSON.parse(item));
};
/**
 * @description:  修复滚动条遮挡数据
 * @return
 */
const fixScrollBarPressData = async () => {
  const sheet = univerAPI.value?.getActiveWorkbook()?.getActiveSheet()!;
  // 这将在第一行位置后插入一行
  await sheet.insertRowAfter(sheet.getMaxRows());
  const startRow = sheet.getMaxRows() - 1;
  await sheet.setRowHeightsForced(startRow, 1, 20);
  const newRow = {
    startRow: startRow,
    startColumn: 0,
    endRow: startRow,
    endColumn: config.maxColumnCount - 1,
    canMoveRow: 0
  };
  // 设置只读
  setReadonlyRanges([newRow]);
};

/**
 * @description: 设置保护区域
 * @param ranges 指定区域
 * @param flag true保护，false取消保护
 * @return
 */
const setPermissionRanges = async (ranges: IRange[], flag: boolean) => {
  const book = univerAPI.value?.getActiveWorkbook();
  const sheet = book?.getActiveSheet();
  const permission = univerAPI.value?.getPermission();
  const res = await permission?.addRangeBaseProtection(book?.getId()!, sheet?.getSheetId()!, ranges);
  const { permissionId, ruleId } = res!;
  if (flag) {
    permission?.setRangeProtectionPermissionPoint(
      book?.getId()!,
      sheet?.getSheetId()!,
      permissionId,
      RangeProtectionPermissionEditPoint,
      false
    );
    // 隐藏保护提示弹窗
    permission?.setPermissionDialogVisible(false);
  } else {
    permission?.removeRangeProtection(book?.getId()!, sheet?.getSheetId()!, [ruleId]);
  }
};

/**
 * @description: 获取数据
 */
const getData = (): IWorkbookData => {
  if (!workbook.value) {
    throw new Error("表格尚未初始化");
  }
  return workbook.value.save();
};
/**
 * @description: 获取单元格数据集合
 * @param sheetName 工作表名称，不传则返回第一个工作表的单元格数据
 * @return
 */
const getSheetData = (sheetName?: string) => {
  const tableData = getData();
  let sheet: Partial<IWorksheetData> = {};
  if (sheetName) {
    sheet = tableData.sheets[sheetName];
  } else {
    sheet = Object.values(tableData.sheets)[0];
  }
  if (!sheet) {
    return undefined;
  }
  return { cellDatas: sheet.cellData, rowCount: sheet.rowCount, columnCount: sheet.columnCount };
};
let popupDisposable: any = undefined;
let popupCell: string | undefined = undefined;
/**
 * @description: 显示提示信息
 * @param rowIndex
 * @param columnIndex
 * @param message
 * @return
 */
const showPopupMessage = (rowIndex: number, columnIndex: number, message: string) => {
  const key = `row${rowIndex}-column${columnIndex}`;
  // 如果key相同 且已经显示 直接返回，防止提示框画面闪烁
  if (popupDisposable && popupCell === key) {
    return;
  }
  popupCell = key;
  const sheet = univerAPI.value?.getActiveWorkbook()?.getActiveSheet();
  // 先销毁在创建
  hiddenPopupMessage();
  popupDisposable = sheet?.getRange(rowIndex, columnIndex, 1, 1).attachPopup({
    // componentKey 必须是一个组件或已注册组件的键
    componentKey: "popupMessage",
    //这是组件的数据，可以传递给组件
    extraProps: {
      message
    }
  });
};
/**
 * @description: 隐藏提示信息
 */
const hiddenPopupMessage = () => {
  popupDisposable && popupDisposable.dispose();
  popupDisposable = undefined;
};
/**
 * @description: 设置十字高亮
 * @param color 高亮颜色，不传则关闭十字高亮
 * @return
 */
const setCrosshairHighlightColor = (color?: string, opacity?: number) => {
  if (color) {
    let rgbColor: string = color;
    // 如果传入的颜色是16进制，需要转换
    if (!color.includes("rgba")) {
      const rgbArr = hexToRgb(color);
      if (rgbArr?.length === 3) {
        rgbColor = `rgba(${rgbArr.join(",")},${opacity || 1})`;
      }
    }
    univerAPI.value?.setCrosshairHighlightEnabled(true);
    univerAPI.value?.setCrosshairHighlightColor(rgbColor);
  } else {
    univerAPI.value?.setCrosshairHighlightEnabled(false);
  }
};
/**
 * @description: 销毁Univer对象
 */
const destroyUniver = () => {
  toRaw(univerRef.value)?.dispose();
  univerRef.value = undefined;
  workbook.value = undefined;
};
/**
 * @description: 向父组件暴漏方法
 */
defineExpose({
  univerAPI,
  setReadonlyRanges,
  fixScrollBarPressData,
  setPermissionRanges,
  getData,
  getSheetData,
  showPopupMessage,
  setCrosshairHighlightColor,
  hiddenPopupMessage,
  destroyUniver
});
</script>

<style lang="scss">
.univer-sheet {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // 隐藏更多选项
  &.hidden-menu-more {
    .univer-toolbar-btn .univerjs-icon-more-function-single {
      display: none;
    }
  }
  // 顶部工具栏背景
  .univer-toolbar {
    background-color: #f5f5f5;
    .menu-icon {
      margin: 5px -5px 0 0;
    }
  }
  .univer-toolbar-container {
    width: 100%;
    margin: 0 40px;
    justify-content: left;
  }
  // 隐藏 拖动后显示的图标
  .univer-btn-container {
    display: none;
  }
}
:global(.univer-menubar) {
  display: none;
}
// 降低el-dialog组件的z-index，保证univer组件的浮窗可以正常显示
.el-overlay {
  z-index: 1000 !important;
}
.univer-theme {
  .univer-thread-comment {
    padding: 12px;
    margin-top: -40px;
    background-color: black;
    * {
      color: #f5f5f5;
    }
    .univer-thread-comment-title {
      display: none;
    }
    .univer-thread-comment-content .univer-thread-comment-item {
      padding: 0 6px;
      margin-bottom: 0;
      .univer-thread-comment-item-head,
      .univer-thread-comment-item-title,
      .univer-thread-comment-item-time {
        display: none;
      }
      .univer-thread-comment-item-title {
        position: absolute;
        right: 5px;
        .univer-thread-comment-username {
          display: none;
        }
        .univer-thread-comment-icon {
          font-size: 16px;
          &:first-child {
            display: none;
          }
        }
      }
      .univer-thread-comment-item-content {
        font-size: 14px;
        letter-spacing: 2px;
      }
    }
    .univer-mentions {
      display: none;
    }
  }
  .univer-menu.univer-menu-root.univer-menu-vertical {
    max-height: 400px !important;
  }
  .univer-menu-submenu .univer-menu-sub {
    max-height: 360px !important;
    overflow-y: auto !important;
  }
  .univer-menu {
    .univer-menu-item-content {
      gap: 0;
      // TODO: 临时隐藏 因有图标 二级菜单滑动时 菜单会消失
      svg {
        display: none;
      }
    }
    .univer-menu-item-activated,
    .univer-menu-item-active,
    .univer-menu-submenu-active {
      background-color: #fbe8bd !important;
    }
  }
}
</style>
