{"extends": "@tsconfig/node18/tsconfig.json", "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "playwright.config.*"], "compilerOptions": {"composite": true, "moduleResolution": "node", "module": "ESNext", "types": ["node"], "lib": ["es5", "es6", "es2015", "es7", "es2016", "es2017", "es2020", "dom", "dom.iterable", "webworker", "scripthost", "es2015.core", "es2015.collection", "es2015.generator", "es2015.iterable", "es2015.promise", "es2015.proxy", "es2015.reflect", "es2015.symbol", "es2015.symbol.wellknown", "es2016.array.include", "es2017.object", "es2017.sharedmemory", "es2017.string"]}}