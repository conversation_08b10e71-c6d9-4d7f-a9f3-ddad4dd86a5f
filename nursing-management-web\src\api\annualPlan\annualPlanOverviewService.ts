/*
 * FilePath     : /src/api/annualPlan/annualPlanOverviewService.ts
 * Author       : 马超
 * Date         : 2024-02-12 16:34
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 16:04
 * Description  : 年度计划查询-查询与命令
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { previewAnnualPlan } from "@/views/annualPlan/quarterPlan/types/annualPlanPreview";
export class annualPlanOverviewService {
  private static exportAnnualPlanApi: string = "/AnnualPlanOverview/ExportAnnualPlan";
  private static getAnnualPlanPreviewApi: string = "/AnnualPlanOverview/GetAnnualPlanPreview";
  private static exportQuarterPlanApi: string = "/AnnualPlanOverview/ExportQuarterPlan";
  private static exportMonthlyPlanApi: string = "/AnnualPlanOverview/ExportMonthlyPlan";

  /**
   * @description: 获取年度计划文档下载
   * @param params
   * @return
   */
  public static exportAnnualPlan = (params?: any) =>
    http.get(this.exportAnnualPlanApi, params, { loadingText: Loading.LOAD }) as Promise<string>;
  /**
   * @description: 获取年度计划浏览数据列表
   * @param params
   * @return
   */
  public static getAnnualPlanPreview = (params?: any) =>
    http.get(this.getAnnualPlanPreviewApi, params, { loadingText: Loading.LOAD }) as Promise<previewAnnualPlan>;
  /**
   * @description: 获取季度计划文档下载
   * @param params
   * @return
   */
  public static exportQuarterPlan = (params?: any) =>
    http.get(this.exportQuarterPlanApi, params, { loadingText: Loading.LOAD }) as Promise<string>;
  /**
   * @description: 获取月度计划文档下载
   * @param params
   * @return
   */
  public static exportMonthlyPlan = (params?: any) =>
    http.get(this.exportMonthlyPlanApi, params, { loadingText: Loading.LOAD }) as Promise<string>;
}
