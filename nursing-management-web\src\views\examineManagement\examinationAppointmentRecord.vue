<!--
 * FilePath     : \src\views\examineManagement\examinationAppointmentRecord.vue
 * Author       : 苏军志
 * Date         : 2025-04-03 14:51
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-13 08:53
 * Description  : 实操类考核预约记录
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="examination-appointment-record">
    <template #header>
      <label>监考日期：</label>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        class="header-date"
        @change="getAppointmentList()"
      />
      <label class="header-label">考核计划：</label>
      <el-select
        class="examination-record-select"
        placeholder="请选择考核计划"
        v-model="examinationRecordID"
        clearable
        @change="filterAppointmentList()"
      >
        <el-option
          v-for="item in examinationRecordList"
          :key="item.examinationRecordID"
          :label="item.examinationName"
          :value="item.examinationRecordID"
        />
      </el-select>
      <employee-selector
        label="监考人"
        v-model="examinerID"
        clearable
        filterable
        :list="examinersOptions"
        @change="filterAppointmentList()"
      />
      <employee-selector
        label="预约人"
        v-model="employeeID"
        clearable
        filterable
        :list="employeeOptions"
        @change="filterAppointmentList()"
      />
      <!-- <el-button class="add-button" v-permission:B="1" @click="sendNotification">发送通知</el-button> -->
    </template>
    <el-table :data="showAppointmentList" border stripe height="100%" :span-method="tableRowSpanMethod">
      <el-table-column :min-width="convertPX(160)" label="监考日期" align="center" prop="scheduleDate"> </el-table-column>
      <el-table-column :min-width="convertPX(160)" label="监考时段" align="center" prop="scheduleTimeRange"> </el-table-column>
      <el-table-column :min-width="convertPX(150)" label="监考人" prop="examinerName" align="center" />
      <el-table-column :min-width="convertPX(160)" label="部门" prop="departmentName" />
      <el-table-column :min-width="convertPX(120)" label="预约人" prop="employeeName" align="center" />
      <el-table-column :min-width="convertPX(200)" label="预约考核" prop="examinationName" />
      <el-table-column :min-width="convertPX(160)" label="预约时间" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.appointmentDate, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(160)" label="状态" align="center">
        <template v-slot="{ row }">
          <el-tag :type="getExaminationAppointmentStatusTag(row.statusCode)">{{ row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(150)" label="撤销原因" prop="cancelReason" />
      <el-table-column :min-width="convertPX(160)" label="撤销时间" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.cancelDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" :width="convertPX(90)" align="center">
        <template #default="{ row }">
          <el-tooltip content="发送通知">
            <i class="iconfont icon-edit" v-permission:B="3" @click="sendNotification(row)" />
          </el-tooltip>
        </template>
      </el-table-column> -->
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
const dateRange = ref<[string, string]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const examinationRecordID = ref<string>();
const examinerID = ref<string>();
const employeeID = ref<string>();
const appointmentList = ref<Record<string, any>[]>([]);
const showAppointmentList = ref<Record<string, any>[]>([]);
const examinationRecordList = ref<Record<string, any>[]>([]);
const examinersOptions = ref<Record<string, any>[]>([]);
const employeeOptions = ref<Record<string, any>[]>([]);
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
const { getExaminationAppointmentStatusTag } = useStatusTag();

onMounted(() => {
  getAppointmentList();
});

/**
 * @description: 获取预约列表
 */
const getAppointmentList = () => {
  let params: Record<string, any> = {
    startDate: dateRange.value[0],
    endDate: dateRange.value[1]
  };
  examinationAppointmentService.getAppointmentList(params).then((res: any) => {
    appointmentList.value = res ?? [];
    appointmentList.value.forEach((item: any) => {
      item.scheduleDate = datetimeUtil.formatDate(item.scheduleDate, "yyyy-MM-dd");
      examinationRecordList.value.push({
        examinationRecordID: item.examinationRecordID,
        examinationName: item.examinationName
      });
      examinersOptions.value = [...examinersOptions.value, ...item.examiners];
      employeeOptions.value.push({
        value: item.employeeID,
        label: item.employeeName
      });
    });
    // 去重
    examinationRecordList.value = [...new Set(examinationRecordList.value.map((item) => JSON.stringify(item)))].map((item) =>
      JSON.parse(item)
    );
    examinersOptions.value = [...new Set(examinersOptions.value.map((item) => JSON.stringify(item)))].map((item) => JSON.parse(item));
    employeeOptions.value = [...new Set(employeeOptions.value.map((item) => JSON.stringify(item)))].map((item) => JSON.parse(item));
    filterAppointmentList();
  });
};
/**
 * @description: 过滤数据
 */
const filterAppointmentList = () => {
  // 根据examinationRecordID,examinerID,employeeID三个字段过滤数据
  showAppointmentList.value = appointmentList.value.filter((item: any) => {
    // 检查考核计划ID是否匹配
    const matchRecordID = !examinationRecordID.value || item.examinationRecordID === examinationRecordID.value;
    // 检查监考人ID是否匹配
    const matchExaminerID =
      !examinerID.value || item.examiners.findIndex((examiner: Record<string, string>) => examiner.value === examinerID.value) !== -1;
    // 检查预约人ID是否匹配
    const matchEmployeeID = !employeeID.value || item.employeeID === employeeID.value;
    // 只有当所有选中的过滤条件都匹配时才保留该项
    return matchRecordID && matchExaminerID && matchEmployeeID;
  });
  // 设置合并列
  setTableRowSpanArr(showAppointmentList.value, [
    "scheduleDate",
    "scheduleTimeRange,examinerScheduleID",
    "examinerName,examinerScheduleID",
    "departmentName,examinerScheduleID"
  ]);
};
// /**
//  * @description: 给预约人员发送通知
//  */
// const sendNotification = () => {};
</script>

<style lang="scss">
.examination-appointment-record {
  .header-label {
    margin-left: 20px;
  }
  .header-date {
    width: 320px !important;
  }
  .examination-record-select {
    width: 300px;
  }
  .employee-selector {
    margin-left: 20px;
  }
}
</style>
