/*
 * FilePath     : \src\views\mainLayout\hooks\useBreadcrumb.ts
 * Author       : 苏军志
 * Date         : 2023-09-06 21:44
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-06 09:34
 * Description  : 获取面包屑数据的hooks
 * CodeIterationRecord:
 */

const { sessionStore } = useStore();

/**
 * 获取面包屑数据的hooks
 */
export function useBreadcrumb() {
  /**
   * description: 根据菜单递归循环组装面包屑项目
   * param {*} path 路由
   * param {*} menuListID 菜单序号
   * param {*} menus 菜单集合
   * return {*} 菜单
   */
  const createBreadcrumbByMenuList = (path: string, menuListID: number, menus: any): Record<string, any> => {
    let menu = undefined;
    for (let i = 0; i < menus.length; i++) {
      if (menus[i].menuID === menuListID) {
        menu = menus[i];
        if (sessionStore.breadcrumb.findIndex((value: any) => value.name === menus[i].menuName) === -1) {
          sessionStore.breadcrumb.unshift({
            name: menus[i].menuName
          });
        }
        break;
      }
      if (menus[i].router === path) {
        menu = menus[i];
        sessionStore.breadcrumb.unshift({
          name: menus[i].menuName
        });
        break;
      }
      if (menus[i].children) {
        menu = createBreadcrumbByMenuList(path, menuListID, menus[i].children);
        if (menu) {
          break;
        }
      }
    }
    // 如果有父菜单，则继续查找
    if (menu && menu.parentID) {
      createBreadcrumbByMenuList("", menu.parentID, menus);
    }
    return menu;
  };

  return {
    /**
     * description: 根据路由和菜单组装面包屑项目
     * param {*} path 路由
     * param {*} menuList 菜单集合
     * return {*}
     */
    createBreadcrumb(path: string, menuList: Array<Record<string, any>>) {
      let parentMenuListID = 0;
      sessionStore.breadcrumb = [];
      // 先找到路由对应的菜单
      // 先从顶部菜单找
      for (let key in sessionStore.pageTopMenus) {
        let topMenu = sessionStore.pageTopMenus[key].find((menu: any) => {
          return menu.routerPath === path;
        });
        if (topMenu) {
          sessionStore.breadcrumb.push({
            name: topMenu.menuName
          });
          parentMenuListID = topMenu.parentMenuListID;
          break;
        }
      }
      // 从左侧菜单找
      createBreadcrumbByMenuList(path, parentMenuListID, menuList);
      // 插入首页
      sessionStore.breadcrumb.unshift({
        name: "首页",
        path: "/home"
      });
    }
  };
}
