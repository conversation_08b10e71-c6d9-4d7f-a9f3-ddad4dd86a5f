<!--
 * FilePath     : \src\components\selector\roleSelector.vue
 * Author       : 张现忠
 * Date         : 2024-03-07 14:54
 * LastEditors  : 张现忠
 * LastEditTime : 2024-03-08 10:30
 * Description  : 人员角色选择器
 * CodeIterationRecord:
 -->
<template>
  <div class="role-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="roleIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      @change="change"
    >
      <el-option v-for="(item, index) in roleOptions" :key="index" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "角色"
  },
  modelValue: {
    type: [String, Array<string>]
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 240
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let roleIDs = useVModel(props, "modelValue", emits);
let roleOptions = ref<Array<Record<string, any>>>([]);
onMounted(async () => {
  roleOptions.value = await useDictionaryData().getAuthorityRoles();
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, roleOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.role-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
