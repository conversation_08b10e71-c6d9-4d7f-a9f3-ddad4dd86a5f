<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\personalInformation\index.vue
 * Author       : 来江禹
 * Date         : 2023-08-07 09:30
 * LastEditors  : 马超
 * LastEditTime : 2025-06-28 15:51
 * Description  : 人员个人信息查看
 * CodeIterationRecord:  3671-作为护理管理人员，我需要护士个人信息档案，以便查看护士相关档案信息
-->
<template>
  <base-layout class="personal-information" :drawerOptions="drawerOptions" :showHeader="false">
    <el-collapse>
      <el-collapse-item v-for="(item, index) in personalCollapseList" :key="index" :title="item.title">
        <template #title>
          <span> {{ item["title"] }}</span>
          <el-tooltip content="编辑" v-if="item['editFlag'] && !isReadonly">
            <i v-permission:B="3" class="iconfont icon-edit" @click.stop="edit(item)"></i>
          </el-tooltip>
        </template>
        <personal-table v-if="item.class == 'Table'" :tableData="item.data" :headerName="item.name" :onDelete="deleteRow"></personal-table>
        <personal-descriptions v-if="item.class == 'Collapse'" :descriptionsData="item.data" :dataName="item.name"></personal-descriptions>
      </el-collapse-item>
    </el-collapse>

    <!-- 新增和修改 -->
    <template #drawerContent>
      <el-form
        v-if="drawerFormat.name === 'clothingArchives'"
        ref="submitRefs"
        label-width="120px"
        label-position="right"
        :model="drawerFormat.data"
        class="personal-information-drawer"
      >
        <template v-for="prop in drawerDatas" :key="prop.label">
          <el-form-item :label="prop.label" :prop="prop.prop" :required="prop.required">
            <template #error>
              <span class="errorMessage">{{ `${prop.label}为必选项` }}</span>
            </template>
            <el-select
              v-if="prop.prop == 'clothesSize'"
              v-model="drawerFormat.data['clothesSize']"
              placeholder="请选择尺码"
              class="personal-information-drawer-select"
            >
              <el-option v-for="item in clothingSizeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-input-number
              v-else-if="prop.prop == 'shoeSize'"
              class="personal-information-drawer-input-number"
              v-focus
              :step="0.5"
              :min="0"
              :max="50"
              :controls="false"
              :step-strictly="true"
              v-model="drawerFormat.data[prop.prop]"
            />
            <el-input v-else type="text" v-model="drawerFormat.data[prop.prop]" class="personal-information-drawer-input"></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div v-if="drawerFormat.name === 'strongPointHeader'">
        <div class="add-button-container">
          <el-button v-permission:B="3" class="add-button" @click="addRecord">新增</el-button>
        </div>
        <el-table :data="drawerFormat.data" class="personal-information-drawer">
          <el-table-column prop="strengthName" label="个人特长">
            <template v-slot="scope">
              <el-select v-model="scope.row.strengthName" placeholder="请选择个人特长" filterable allow-create>
                <el-option
                  v-for="item in personalStrengthSettings"
                  :key="item.key"
                  :label="item.value"
                  :value="item.value"
                  :disabled="isOptionDisabled(item.value)"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="honor" label="荣誉">
            <template v-slot="scope">
              <el-input v-model="scope.row.honor" placeholder="对应特长所获荣誉" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import personalDescriptions from "./components/descriptions/personalDescriptions.vue";
import personalTable from "./components/table/personalTable.vue";
import type { collapse } from "./types/collapseView";
import type { itemProp } from "./types/descriptionsView";
import descriptionsProp from "./components/descriptions/descriptionsData";
const route = useRoute();
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
// 员工信息
let personalCollapseList = ref<Array<collapse>>([]);
// 弹窗表单内容
let drawerDatas = ref({} as itemProp[]);
// 弹窗表单员工信息(某一项)
let drawerFormat = ref<Record<string, any>>({});
// 表单响应式变量声明
const submitRefs = ref<any>({});
// 弹窗输入框宽度
const contentWidth = computed(() => `${convertPX(386)}px`);

/**
 * 检查是否处于只读模式
 */
const isReadonly = computed(() => {
  return route.query.readonly === "true";
});

// 弹窗参数
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    if (drawerFormat.value.name === "clothingArchives") {
      save(toRef(submitRefs.value));
    } else if (drawerFormat.value.name === "strongPointHeader") {
      saveStrongPoint(drawerFormat.value.data);
    }
  }
});
// 员工服饰尺码信息，添加配置后获取配置信息
const clothingSizeOptions = ref<Array<any>>([
  { label: "S", value: "S" },
  { label: "M", value: "M" },
  { label: "L", value: "L" },
  { label: "XL", value: "XL" },
  { label: "XXL", value: "XXL" },
  { label: "XXXL", value: "XXXL" }
]);

onMounted(async () => {
  await getPersonalData();
  getPersonalStrengthSettings();
});
/**
 * @description: 获取员工个人信息数据
 * @return
 */
const getPersonalData = async () => {
  const param = {
    employeeID: route?.query?.employeeID ?? userStore.employeeID
  };
  await employeeService.getEmployeePersonalData(param).then((res: any) => {
    if (res) {
      personalCollapseList.value = res;
    } else {
      showMessage("error", `${userStore.userName}无个人信息数据`);
    }
  });
};

/**
 * @description: 编辑
 * @return {*}
 * @param {*} data:显示的数据
 * @param {*} editFormat:JobFormat 格式
 */
const edit = async (editFormat: Record<string, any>) => {
  drawerDatas.value = common.clone(descriptionsProp[editFormat.name]) as itemProp[];
  drawerFormat.value = common.clone(editFormat);
  drawerOptions.value.drawerTitle = `编辑 -${editFormat["title"]}`;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 保存
 * @return {*}
 */
const save = async (refs: any) => {
  let { validateRule } = useForm();
  if (!(await validateRule(refs))) {
    return;
  }
  let params = {
    employeeID: drawerFormat.value.data.employeeID,
    height: drawerFormat.value.data.height,
    weight: drawerFormat.value.data.weight,
    clothesSize: drawerFormat.value.data.clothesSize,
    shoeSize: drawerFormat.value.data.shoeSize,
    pantsSize: drawerFormat.value.data.pantsSize
  };
  // 校验 height 和 weight 是否为有效数字
  if (!validateSizes(params.height, params.weight)) {
    showMessage("error", "身高和体重必须为有效数字");
    return;
  }
  // 保存
  await employeeService.saveEmployeeClothingSizes(params).then(() => {
    showMessage("success", "保存成功");
    drawerOptions.value.showDrawer = false;
    getPersonalData();
  });
};
// 保存前检验
const validateSizes = (height: string, weight: string): boolean => {
  const isHeightValid = !isNaN(parseFloat(height)) && isFinite(Number(height));
  const isWeightValid = !isNaN(parseFloat(weight)) && isFinite(Number(weight));
  return isHeightValid && isWeightValid;
};
//#region 个人特长相关逻辑
const personalStrengthSettings = ref<Record<string, string>[]>();
/**
 * @description: 获取访视类型
 */
const getPersonalStrengthSettings = () => {
  let params = {
    settingTypeCode: "EmployeeStrengthSetting",
    settingTypeValues: ["EmployeeStrength"]
  };
  useDictionaryData()
    .getSettingDictionaryByCodeValue(params)
    .then((datas) => {
      personalStrengthSettings.value = datas["EmployeeStrength"];
    });
};
/**
 * @description: 新增记录
 * @return {*}
 */
const addRecord = () => {
  drawerFormat.value.data.push({});
};
/**
 * @description: 保存个人特长
 * @return {*}
 */
const saveStrongPoint = async (params: Record<string, any>) => {
  if (!params.every((item: any) => item.strengthName)) {
    showMessage("warning", "个人特长为必填项");
    return;
  }
  params.forEach((item: any) => {
    item.strengthID = personalStrengthSettings.value!.find((setting) => setting.value === item.strengthName)?.key;
  });
  // 保存
  await employeeService.saveEmployeeStrengths(params).then(() => {
    showMessage("success", "保存成功");
    drawerOptions.value.showDrawer = false;
    getPersonalData();
  });
};
const isOptionDisabled = (value: string) => {
  return drawerFormat.value.data.some((row: any) => row.strengthName === value);
};
const deleteRow = (row: any) => {
  deleteConfirm(`确定要删除特长${row.strengthName}么？`, async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let params = {
      employeeStrengthID: row.employeeStrengthID
    };
    employeeService.deleteEmployeeStrengths(params).then((result) => {
      if (result) {
        showMessage("success", "删除成功");
        getPersonalData();
      } else {
        showMessage("error", "删除失败");
      }
      drawerOptions.value.showDrawer = false;
    });
  });
};
//#endregion
</script>
<style lang="scss">
.personal-information {
  height: 100%;
  .personal-information-drawer {
    .personal-information-drawer-select {
      width: v-bind(contentWidth);
    }
    .personal-information-drawer-input-number {
      width: v-bind(contentWidth);
      .el-input__inner {
        text-align: left;
      }
    }
    .personal-information-drawer-input {
      width: v-bind(contentWidth);
    }
    .errorMessage {
      color: var(--el-color-danger);
      line-height: 1;
      position: absolute;
      top: 100%;
      left: 0;
    }
  }
  .add-button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
    margin-left: 100%;
  }
}
</style>
