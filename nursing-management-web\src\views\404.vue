/* * FilePath : \src\views\404.vue * Author : 苏军志 * Date : 2023-09-06 17:12 * LastEditors : 苏军志 * LastEditTime : 2023-09-20 14:52 *
Description : * CodeIterationRecord: */
<!--
 * FilePath     : \src\views\404.vue
 * Author       : 苏军志
 * Date         : 2023-08-31 11:37
 * LastEditors  : 苏军志
 * LastEditTime : 2024-01-16 10:31
 * Description  : 404
 * CodeIterationRecord:
-->
<template>
  <div class="error-404">
    <div class="error-wrap">
      <div class="text-404">404</div>
      <div class="text">抱歉，您访问的页面丢失了！</div>
      <div class="suggest-wrap">
        建议您
        <el-button size="large" type="primary" @click="goPage('home')" class="page-button">返回首页</el-button>
        <el-button size="large" type="success" @click="goPage()" class="page-button">返回上一页</el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const router = useRouter();
const goPage = (routerName?: string) => {
  if (routerName) {
    router.replace({ name: "home" });
  } else {
    router.go(-1);
  }
};
</script>
<style lang="scss">
.error-404 {
  height: 100%;
  width: 100%;
  background-color: #ffffff;
  overflow: hidden;
  background: url("/static/images/404_back.jpg");
  background-size: 100% 100%;
  height: 100%;
  .error-wrap {
    position: relative;
    width: 660px;
    height: 40%;
    padding: 30px 20px;
    top: 28%;
    left: 64%;
    text-align: center;
    .text-404 {
      font-size: 120px;
      letter-spacing: 30px;
    }
    .text {
      font-size: 40px;
    }
    .suggest-wrap {
      width: 100%;
      font-size: 30px;
      color: #ff0000;
      text-align: left;
      padding-left: 80px;
      .page-button {
        margin-left: 40px;
      }
    }
  }
}
</style>
