/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanDictionaryStore.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-23 10:32
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-08 10:39
 * Description  : 年度计划字典状态与行为
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";
import type { annualIndicatorList } from "../../types/annualPlanDictionary";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";

/**
 * @description: 年度计划字典聚合行为
 */
export const useAnnualPlanDictionaryStore = defineStore("annualPlanDictionary", {
  state: () => {
    return {
      indicatorList: [] as annualIndicatorList[]
    };
  },
  getters: {
    refIndicatorIDs(): Set<number> {
      if (!useAnnualPlanMaintainStore().indicatorsByGroupID) {
        return new Set<number>();
      }
      return new Set<number>(Object.values(useAnnualPlanMaintainStore().indicatorsByGroupID).flat().map((planIndicator) => planIndicator.annualIndicatorID));
    },
    noRefIndicatorList(state): annualIndicatorList[] {
      return state.indicatorList.filter((indicator) => !this.refIndicatorIDs.has(indicator.annualIndicatorID));
    }
  },
  actions: {
    /**
     * @description: 获取指标字典
     * @return
     */
    async setIndicatorList() {
      const params = {
        departmentID: usePlanManagementStore().departmentID,
        showUpperIndicator: true
      };
      this.indicatorList = await annualPlanSettingService.getAnnualIndicatorList(params);
    }
  }
});
