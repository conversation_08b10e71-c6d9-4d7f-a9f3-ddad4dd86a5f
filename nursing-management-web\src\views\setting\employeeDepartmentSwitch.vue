<!--
 * FilePath     : \src\views\setting\employeeDepartmentSwitch.vue
 * Author       : 张现忠
 * Date         : 2024-03-18 10:30
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-07 15:32
 * Description  : 人员多部门权限维护页面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="employee-department-switch" :drawerOptions="drawerOptions">
    <template #header>
      <department-selector v-model="switchDepartmentID" :onlySelectEndNode="false" clearable v-permission:B="25" />
      <el-button class="add-button" @click="addOrModify()" v-permission:B="1">新增</el-button>
    </template>
    <el-table :data="employeeDepartmentSwitchList" border stripe height="100%">
      <el-table-column prop="employeeName" label="姓名" align="center" :width="convertPX(200)" />
      <el-table-column label="部门" align="left" :min-width="convertPX(130)">
        <template #default="scope">
          <el-tag class="depart-tag" effect="dark" v-for="(deptName, index) in scope.row.departmentNames" :key="index">
            {{ deptName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" label="维护人" align="center" :width="convertPX(160)" />
      <el-table-column prop="modifyDateTime" label="维护时间" align="center" :width="convertPX(160)">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(80)" align="center">
        <template #default="scope">
          <el-tooltip content="修改">
            <i @click="addOrModify(scope.row)" class="iconfont icon-edit" v-permission:B="3"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i @click="deleteRow(scope.row)" class="iconfont icon-delete" v-permission:B="4"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="submitRefs" class="form-style" label-width="auto" :model="currRow" :rules="rules" :validate-on-rule-change="false">
        <el-form-item label="姓名：" prop="employeeID">
          <employee-selector
            label=""
            :departmentID="switchDepartmentID"
            v-model="currRow.employeeID"
            :disabled="disabled"
            filterable
            :width="386"
            showAll
          />
        </el-form-item>
        <el-form-item label="部门" prop="departmentIDs">
          <department-selector
            label=""
            v-model="currRow.departmentIDs"
            :props="{ expandTrigger: 'hover', multiple: true }"
            clearable
            :width="386"
          />
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import type { employeeDepartmentSwitchView } from "./types/employeeDepartmentSwitchView";
const rules = ref({
  employeeID: [{ required: true, message: "请选择人员", trigger: "blur" }],
  departmentIDs: [{ required: true, message: "请选择部门", trigger: "blur" }]
});
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
const disabled = ref(false);
const submitRefs = shallowRef();
const switchDepartmentID = ref(userStore.departmentID);
const employeeDepartmentSwitchList = ref<employeeDepartmentSwitchView[]>([]);
const currRow = ref<employeeDepartmentSwitchView>({} as employeeDepartmentSwitchView);
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "50%",
  cancel: () => (drawerOptions.value.showDrawer = false),
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveEmployeeDepartmentSwitch(currRow.value);
  }
});
/**
 *  监听部门变化，重新获取员工部门权限列表
 */
watch(switchDepartmentID, () => {
  getEmployeeDepartmentSwitchList();
});
onMounted(() => {
  getEmployeeDepartmentSwitchList();
});
/**
 * @description: 新增和修改按钮点击业务处理逻辑
 * @param row
 * @return
 */
const addOrModify = (row?: employeeDepartmentSwitchView) => {
  disabled.value = Boolean(row);
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerTitle = !row ? "新增人员部门权限" : "修改人员部门权限";
  currRow.value = common.clone(row) || ({} as employeeDepartmentSwitchView);
};
/**
 * @description: 保存人员多部门权限
 * @param row
 * @return
 */
const saveEmployeeDepartmentSwitch = (row: any) => {
  drawerOptions.value.showDrawer = false;
  let params = {
    employeeID: row.employeeID,
    departmentIDs: row.departmentIDs
  };
  employeeDepartmentSwitchService.SaveEmployeeDepartmentSwitch(params).then((respBool: any) => {
    if (respBool) {
      showMessage("success", "保存成功");
      getEmployeeDepartmentSwitchList();
      return;
    }
    showMessage("error", "保存失败");
  });
};
/**
 * @description: 获取员工多部门权限列表
 * @return
 */
const getEmployeeDepartmentSwitchList = () => {
  let params = {
    departmentID: switchDepartmentID.value
  };
  employeeDepartmentSwitchService.GetEmployeeDepartmentSwitchList(params).then((respData: any) => {
    if (respData) {
      employeeDepartmentSwitchList.value = respData;
      return;
    }
    showMessage("error", "获取列表失败");
  });
};
/**
 * @description: 删除行
 * @param row
 * @return
 */
const deleteRow = (row: employeeDepartmentSwitchView) => {
  deleteConfirm("确定要删除么？", (flag: Boolean) => {
    if (!flag) {
      return;
    }
    deleteEmployeeDepartmentSwitch(row);
  });
};
/**
 * @description: 删除员工多部门权限
 * @param row
 * @return
 */
const deleteEmployeeDepartmentSwitch = (row: employeeDepartmentSwitchView) => {
  let params = {
    employeeID: row.employeeID
  };
  employeeDepartmentSwitchService.DeleteEmployeeDepartmentSwitch(params).then((respBool: any) => {
    if (!respBool) {
      showMessage("error", "删除失败");
      return;
    }
    showMessage("success", "删除成功");
    getEmployeeDepartmentSwitchList();
  });
};
</script>

<style lang="scss">
.employee-department-switch {
  .depart-tag {
    margin-right: 5px;
  }
}
</style>
