/*
 * FilePath     : \src\api\userLoginService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-26 09:42
 * Description  : 用户登录相关Api接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";
export class userLoginService {
  private static loginApi: string = "/userLogin/Login";
  private static getServerDateTimeApi: string = "/userLogin/GetServerDateTime";
  private static getSessionByTokenApi: string = "/userLogin/GetSessionByToken";
  private static bindWechatApi: string = "/userLogin/BindWechat";
  private static logOutApi: string = "/userLogin/LogOut";
  private static updateDepartmentIDOfSessionApi: string = "/userLogin/UpdateDepartmentIDOfSession";
  private static changePasswordApi: string = "/userLogin/ChangePassword";
  private static transformDepartmentIDApi: string = "/userLogin/TransformDepartmentID";
  private static ssoUserCheckApi: string = "/userLogin/SSOUserCheck";

  // 获取系统时间
  public static getServerDateTime() {
    return http.get(this.getServerDateTimeApi);
  }
  // 登陆系统
  public static login(params: any) {
    return http.post(this.loginApi, params, { loadingText: Loading.LOGIN });
  }
  // 根据Token获取Session
  public static async getSessionByToken() {
    return await http.get(this.getSessionByTokenApi);
  }
  // 绑定微信
  public static bindWechat() {
    return http.post(this.bindWechatApi);
  }
  //  退出登录
  public static logOut() {
    return http.post(this.logOutApi, undefined, { loadingText: Loading.EXIT });
  }
  /**
   * @description 更新Session的部门ID
   * @param params
   * @returns
   */
  public static updateDepartmentIDOfSession(params: any) {
    return http.post(this.updateDepartmentIDOfSessionApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
  /**
   * @description 修改密码
   * @param params
   * @returns
   */
  public static changePassword(params: any) {
    return http.post(this.changePasswordApi, params);
  }
  /**
   * @description 转换部门ID （从其他系统跳转过来的departmentID转换 ，例如CCC系统跳转到NursingManagement系统）
   * @param departmentID 部门ID
   * @param sourceSystem 来源系统
   * @returns
   */
  public static transformDepartmentID(params: any) {
    return http.get(this.transformDepartmentIDApi, params);
  }
  /**
   * @description: 单点登录验证用户
   * @param params
   * @return
   */
  public static ssoUserCheck(params: any) {
    return http.post(this.ssoUserCheckApi, qs.stringify(params), { loadingText: Loading.LOGIN });
  }
}
