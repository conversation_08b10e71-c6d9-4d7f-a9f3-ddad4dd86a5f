/*
 * FilePath     : \src\types\drawerOptions.ts
 * Author       : 苏军志
 * Date         : 2023-09-11 19:34
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-17 19:46
 * Description  : 抽屉参数
 * CodeIterationRecord:
 */

/**
 * 抽屉参数
 */
declare interface DrawerOptions {
  /**
   * 插槽唯一标识
   */
  drawerName?: String;
  /**
   * 抽屉标题
   */
  drawerTitle: String;
  /**
   * 是否显示抽屉
   */
  showDrawer: Boolean;
  /**
   * 抽屉打开方向，默认rtl
   */
  drawerDirection?: "rtl" | "ltr" | "ttb" | "btt";
  /**
   * Drawer 窗体的大小,
   * 当使用 number 类型时, 以像素为单位,
   * 当使用 string 类型时, 请传入 'x%', 否则便会以 number 类型解释
   * 默认80%
   */
  drawerSize?: String | number;
  /**
   * 是否显示取消按钮，默认显示
   */
  showCancel?: Boolean;
  /**
   * 是否显示确认按钮，默认显示
   */
  showConfirm?: Boolean;
  /**
   * 取消按钮显示文本，默认取消
   */
  cancelText?: String;
  /**
   * 确认按钮显示文本，默认保存
   */
  confirmText?: String;
  /**
   * 关闭抽屉前回调函数，用法参考el-drawer官网的before-close属性说明
   */
  beforeClose?: Function;
  /**
   * 关闭抽屉时回调函数
   */
  cancel?: Function;
  /**
   * 抽屉中确定/保存按钮点击回调函数
   */
  confirm?: Function;
  /**
   * 自定义class名称
   */
  className?: string;
  /**
   * 抽屉打开的回调函数
   */
  opened?:Function;
}
