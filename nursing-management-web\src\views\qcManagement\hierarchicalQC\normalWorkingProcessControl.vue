<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\normalWorkingProcessControl.vue
 * Author       : 郭鹏超
 * Date         : 2023-11-07 11:36
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-03 14:33
 * Description  : 常态工作控制
 * CodeIterationRecord:
-->
<template>
  <base-layout class="normal-working-process-control" :drawerOptions="drawerOptions" headerHeight="auto">
    <template #header>
      <div class="left">
        <label>年月:</label>
        <el-date-picker
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          v-model="searchView.yearMonth"
          :clearable="false"
          class="header-date"
        >
        </el-date-picker>
        <label>组织:</label>
        <department-switch-cascader
          @select="pageOptions.changeFormType(searchView)"
          :disabled="pageOption.formDisabled"
          v-model="searchView.departmentID"
        >
        </department-switch-cascader>
        <span class="header-label" v-permission:S="12">
          <span>显示申诉成功记录：</span>
          <el-switch v-model="showAppealSuccessfulSwitch" @change="filterTableData(true)" />
        </span>
        <span class="header-label" v-permission:S="12">
          <span>显示全部：</span>
          <el-switch v-model="showAllSwitch" @change="filterTableData(true)" />
        </span>
      </div>

      <div class="header-button">
        <el-button class="add-button" v-permission:B="1" @click="openQcDrawer('add', undefined)">新增 </el-button>
        <el-button class="add-button" v-permission:B="18" @click="openQcDrawer('subjectMaintenance', undefined)">主题维护 </el-button>
      </div>
    </template>
    <el-table ref="tableRef" height="100%" :data="showTableData" stripe border @filter-change="handleFilterChange">
      <el-table-column prop="subjectName" label="主题" :min-width="convertPX(150)" align="left"></el-table-column>
      <el-table-column label="质控问题" :min-width="convertPX(300)" align="left">
        <template #default="scope">
          <div v-html="scope.row.contents"></div>
        </template>
      </el-table-column>
      <el-table-column label="发生日期" :width="convertPX(150)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.examineDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column
        label="问题发生人"
        :width="convertPX(160)"
        align="center"
        column-key="qcEmployeeName"
        :filters="qcEmployeeNameFilters"
        :filter-multiple="true"
      >
        <template #default="{ row, column }">
          <span v-if="getButtonAuthority([row.examineEmployeeID, ...row.qcEmployeeID], 40) || setFilterRow(row, column)">
            {{ row.qcEmployeeName }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="记录人"
        :width="convertPX(100)"
        align="center"
        column-key="examineEmployee"
        :filters="examineEmployeeFilters"
        :filter-multiple="true"
      >
        <template #default="{ row, column }">
          <span v-if="getButtonAuthority(row.examineEmployeeID, 40) || setFilterRow(row, column)">{{ row.examineEmployee }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="整改人"
        :width="convertPX(100)"
        align="center"
        column-key="rectifierEmployeeName"
        :filters="rectifierFilters"
        :filter-multiple="true"
      >
        <template #default="{ row, column }">
          <span v-if="row.problemRectificationView || setFilterRow(row, column)">{{ row.problemRectificationView.employeeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="整改日期" :width="convertPX(150)" align="center">
        <template #default="{ row }">
          <span
            v-if="row.problemRectificationView"
            v-formatTime="{ value: row.problemRectificationView.rectificationDateTime, type: 'date' }"
          ></span>
        </template>
      </el-table-column>
      <el-table-column label="整改备注" :width="convertPX(200)" align="center">
        <template #default="{ row }">
          <span v-if="row.problemRectificationView">{{ row.problemRectificationView.rectificationRemarks }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        :width="convertPX(150)"
        align="center"
        column-key="submitStatus"
        :filters="statusFilters"
        :filter-multiple="true"
      >
        <template #default="{ row }">
          <el-tag :type="getAuditStatusTag(row.auditStatus)">{{ row.submitStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(250)">
        <template #default="{ row }">
          <el-tooltip :content="row.examineEmployeeID == userStore.employeeID && row.auditStatus !== '6' ? '修改' : '查看'">
            <i
              v-permission:B="3"
              :class="['iconfont', `icon-${row.examineEmployeeID == userStore.employeeID && row.auditStatus !== '6' ? 'edit' : 'search'}`]"
              @click.stop="openQcDrawer('add', row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="图片预览">
            <i class="iconfont icon-img-preview" @click="openImgPreview(row)"> </i>
          </el-tooltip>
          <template v-if="row.auditStatus !== '6'">
            <el-tooltip content="整改">
              <i
                v-visibilityHidden="row.auditStatus === '0'"
                class="iconfont icon-rectification"
                @click.stop="addOrUpdateRectification(row)"
              ></i>
            </el-tooltip>
            <el-tooltip content="确认整改">
              <i
                v-visibilityHidden="row.auditStatus === '5' && isHeadNurse()"
                class="iconfont icon-rectification"
                @click.stop="confirmRectification(row.hierarchicalQCMainID)"
              ></i>
            </el-tooltip>
            <el-tooltip content="申诉" v-if="['0','4'].includes(row.auditStatus!)">
              <i class="iconfont icon-appeal" v-visibilityHidden="getButtonAuthority(row.qcEmployeeID)" @click.stop="startAppeal(row)"></i>
            </el-tooltip>
            <el-tooltip content="取消申诉" v-if="row.auditStatus == '1'">
              <i class="iconfont icon-stop" v-visibilityHidden="getButtonAuthority(row.qcEmployeeID)" @click.stop="stopSubmit(row)"></i>
            </el-tooltip>
            <el-tooltip content="撤销">
              <i
                class="iconfont icon-revoke"
                v-permission:B="26"
                v-if="showRevokeFlag"
                v-visibilityHidden="
                  getButtonAuthority(row.qcEmployeeID) && showRevokeButton(row.auditStatus, row.qcEmployeeID) && row.auditStatus <= 4
                "
                @click="startRevoke(row)"
              ></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i
                v-permission:B="4"
                v-visibilityHidden="getButtonAuthority(row.examineEmployeeID) && hiddenButtonByRevoke(row.auditStatus)"
                class="iconfont icon-delete"
                @click.stop="deleteRecord(row)"
              ></i>
            </el-tooltip>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <base-layout v-if="drawerOptions.drawerName == 'add'" headerHeight="50px">
        <template #header>
          <label>年月：</label>
          <el-date-picker
            type="month"
            format="YYYY-MM"
            value-format="YYYY-MM"
            v-model="drawerSearchView.yearMonth"
            :disabled="!!currentRecord"
            class="header-date"
          >
          </el-date-picker>
          <label>主题：</label>
          <qcSubjectSelector
            :defaultFirstFlag="true"
            @change="drawerSubjectChange"
            v-model="drawerSearchView.hierarchicalQCSubjectID"
            :params="drawerSearchView"
            :clearable="false"
          ></qcSubjectSelector>
          <label>记录日期：</label>
          <el-date-picker
            :clearable="false"
            class="qc-date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="saveView.qcDate"
            type="date"
            placeholder=""
            :disabled-date="disabledDate"
          ></el-date-picker>
        </template>
        <zhy-form-renderer
          v-if="formData"
          ref="qcFormDom"
          :formData="formData"
          :uploadOptions="uploadOptions"
          :disabled="currentRecord?.auditStatus === '6'"
          watching
          @change="getDetails"
        />
      </base-layout>
      <!-- 申诉弹窗 -->
      <el-form ref="appealForm" :model="appealRecord" :rules="formRules" label-width="auto" v-if="drawerOptions.drawerName === 'appeal'">
        <el-form-item label="申诉主题：" prop="subjectName">
          <span>{{ appealRecord.subjectName }}</span>
        </el-form-item>
        <el-form-item label="申诉原因：" prop="appealReason">
          <el-input type="textarea" v-model="appealRecord.appealReason" />
        </el-form-item>
      </el-form>
      <!-- 撤销弹窗 -->
      <el-form ref="revokeForm" :model="revokeRecord" :rules="revokeRules" label-width="auto" v-if="drawerOptions.drawerName === 'revoke'">
        <el-form-item label="撤销主题：" prop="subjectName">
          <span>{{ revokeRecord.subjectName }}</span>
        </el-form-item>
        <el-form-item label="撤销原因：" prop="revokeReason">
          <el-input type="textarea" v-model="revokeRecord.revokeReason" />
        </el-form-item>
      </el-form>
      <!-- 整改弹窗 -->
      <el-form
        class="rectification-form"
        ref="rectificationForm"
        :model="rectificationRecord"
        :rules="formRules"
        label-width="auto"
        v-if="drawerOptions.drawerName === 'rectification'"
      >
        <el-form-item label="整改人：" prop="employeeID">
          <employee-selector label="" v-model="rectificationRecord.employeeID" :departmentID="userStore.departmentID" :showAll="true" />
        </el-form-item>
        <el-form-item label="整改时间：" prop="rectificationDateTime">
          <el-date-picker
            class="rectification-date"
            v-model="rectificationRecord.rectificationDateTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="整改备注：" prop="rectificationRemarks">
          <el-input type="textarea" :rows="5" v-model="rectificationRecord.rectificationRemarks" />
        </el-form-item>
      </el-form>
      <hierarchicalQCSubject
        v-if="drawerOptions.drawerName == 'subjectMaintenance'"
        :subjectMaintenanceView="skipSubjectData"
      ></hierarchicalQCSubject>
      <img-preview v-if="drawerOptions.drawerName == 'imgPreview'" :imgPreviewData="imgList"></img-preview>
    </template>
    <template #drawerOtherFooter v-if="showDrawerOtherFooter">
      <div>
        <el-button
          v-permission:B="2"
          v-visibilityHidden="!currentRecord || getButtonAuthority(currentRecord.examineEmployeeID)"
          class="print-button"
          @click="saveMainAndDetails(false)"
          >暂存</el-button
        >
        <el-button
          v-permission:B="2"
          v-visibilityHidden="!currentRecord || getButtonAuthority(currentRecord.examineEmployeeID)"
          type="primary"
          @click="saveMainAndDetails(true)"
          >保存</el-button
        >
      </div>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const { isHeadNurse } = useUtils();
const { userStore, sessionStore } = useStore() as any;
import type { uploadOption } from "zhytech-ui";
import hierarchicalQCSubject from "./hierarchicalQCSubject.vue";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
import type { skipSubjectView } from "./types/subjectMaintenanceView";
const { getButtonAuthority } = useQcCommonMethod();
// 动态表单动的上传文件参数
const uploadOptions: uploadOption = {
  serverApi: `${common.session("serverUrl")}/file/uploadFile`,
  headers: {
    "Management-Token": sessionStore.token
  },
  autoUpload: false
};
const showAppealSuccessfulSwitch = ref<Boolean>(false);
const showAllSwitch = ref<Boolean>(true);
const tableRef = shallowRef<any>();

/**
 * @description: 初始化
 */
onMounted(async () => {
  await getTableData();
});
import qcOptions from "./setting/index";
import normalWorkingProcessControl from "./setting/normalWorkingProcessControl";
//  差异配置初始化
const pageOptions = ref<qcOptions>(
  new qcOptions("normalWorkingProcessControl", {
    qcLevel: "1",
    formType: "6",
    qcType: "normalWorkingFormType",
    departmentID: userStore.departmentID
  })
);
const pageOption = ref<normalWorkingProcessControl>(pageOptions.value.pageOption as unknown as normalWorkingProcessControl);
import type { subjectSearchView } from "./types/subjectSearchView";
// 筛选条件初始化
const searchView = ref<subjectSearchView>({
  yearMonth: datetimeUtil.getNowDate("yyyy-MM"),
  qcType: pageOptions.value.qcType,
  qcLevel: pageOption.value.qcLevel,
  departmentID: pageOption.value.departmentID,
  formType: pageOption.value.formType
});
watch(searchView.value, () => getTableData());
const showTableData = ref<Record<string, any>[]>([]);
const tableData = ref<Record<string, any>[]>([]);
/**
 * @description: 获取常态工作控制记录
 */
const getTableData = async () => {
  // 处理CCC跳转过来mounted一次，触发refresh后又mounted一次，导致第二次请求被拦截,导致数据为空
  let params = {
    ...searchView.value,
    startYearMonth: searchView.value.yearMonth,
    endYearMonth: searchView.value.yearMonth
  };
  await hierarchicalQCService.getNormalWorkingTableData(params).then((res: any) => {
    tableData.value = res;
    showTableData.value = res;
    // 清除表格筛选器，重新应用所有筛选条件
    filterTableData(true);
  });
};
const skipSubjectData = ref<skipSubjectView>({
  qcLevel: pageOption.value.qcLevel,
  formType: pageOption.value.formType!,
  qcType: "normalWorkingFormType",
  departmentID: pageOption.value.departmentID
});
const currentRecord = ref<Record<string, any>>();
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false,
  drawerName: "",
  cancel: () => {
    restoreDrawerOption();
  },
  confirm: async () => {
    if (drawerOptions.value.drawerName === "appeal") {
      await formConfirm(appealForm, async () => submitForApproval(appealRecord.value));
    }
    if (drawerOptions.value.drawerName === "rectification") {
      await formConfirm(rectificationForm, async () => saveProblemRectification());
    }
    if (drawerOptions.value.drawerName === "revoke") {
      const validateResult = await useForm().validateRule(revokeForm);
      if (!validateResult) {
        return;
      }
      restoreDrawerOption();
      await revokeApproval(appealRecord.value.hierarchicalQCRecordID, revokeRecord.value.revokeReason);
    }
  }
});
/**
 * @description:弹窗内容提交
 * @param form 表单
 * @param submitAction 提交方法
 * @return
 */
const formConfirm = async (form: any, submitAction: () => Promise<void>) => {
  const validateResult = await useForm().validateRule(form);
  if (!validateResult) {
    return;
  }
  restoreDrawerOption();
  await submitAction();
  await getTableData();
};
const drawerSearchView = ref<subjectSearchView>({
  yearMonth: datetimeUtil.getNowDate("yyyy-MM"),
  departmentID: userStore.departmentID,
  formType: pageOption.value.formType,
  qcLevel: pageOption.value.qcLevel,
  qcType: pageOptions.value.qcType,
  hierarchicalQCSubjectID: undefined
});
/**
 * @description: 常态工作控制弹窗初始化
 * @param drawerName
 * @param item
 */
const openQcDrawer = async (drawerName: string, item?: Record<string, any>) => {
  formData.value = undefined;
  saveView.details = [];
  currentRecord.value = item;
  drawerOptions.value.drawerTitle = "常态工作过程质量控制";
  drawerSearchView.value.hierarchicalQCSubjectID = item?.hierarchicalQCSubjectID ?? undefined;
  drawerSearchView.value.yearMonth = searchView.value.yearMonth;
  drawerSearchView.value.departmentID = item?.departmentID ?? searchView.value.departmentID;
  saveView.qcDate = item?.examineDate ?? datetimeUtil.getNowDate("yyyy-MM-dd");
  saveView.hierarchicalQCRecordID = item?.hierarchicalQCRecordID ?? undefined;
  saveView.hierarchicalQCMainID = item?.hierarchicalQCMainID ?? undefined;
  templateCode.value = item?.templateCode ?? undefined;
  drawerOptions.value.showDrawer = true;
  if (drawerName) {
    drawerOptions.value.drawerName = drawerName;
  }
  if (drawerName === "subjectMaintenance") {
    drawerOptions.value.drawerTitle = "主题维护";
  }
};

const templateCode = ref<string>("");
/**
 * @description: 评估主题变更
 * @param option
 */
const drawerSubjectChange = async (option: any) => {
  templateCode.value = option?.templateCode;
  saveView.hierarchicalQCSubjectID = drawerSearchView.value.hierarchicalQCSubjectID ?? "";
  disabledRecordDateStart.value = option?.startDate ?? "";
  disabledRecordDateEnd.value = option?.endDate ?? "";
  await getFormTemplate();
};

import { saveClass } from "./types/hierarchicalSaveView";
const saveView = reactive(new saveClass());
const { saveQcMainAndDetails, setSaveDetails } = useQcCommonMethod();
const qcFormDom = ref<any>();
/**
 * @description:质控 暂存保存
 * @param saveFlag
 * @return
 */
const saveMainAndDetails = async (saveFlag: boolean) => {
  setSaveDetails(saveView);
  await saveQcMainAndDetails(saveView, qcFormDom.value, saveFlag, isSaveMethod);
};
/**
 * @description: 保存后调用
 */
const isSaveMethod = async () => {
  drawerOptions.value.showDrawer = false;
  getTableData();
  showMessage("success", "保存成功");
};

/**
 * @description: 删除主记录
 * @param row
 * @return
 */
const deleteRecord = async (row: Record<string, any>) => {
  if (!row?.hierarchicalQCRecordID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  confirmBox("是否删除当前考核结果维护记录？", "删除考核结果维护记录", (flag: Boolean) => {
    if (flag) {
      let params = {
        recordID: row.hierarchicalQCRecordID
      };
      hierarchicalQCService.deleteHierarchicalQCRecord(params).then((res: any) => {
        if (res) {
          getTableData();
          showMessage("success", "删除成功");
        } else {
          showMessage("error", "删除失败");
        }
      });
    }
  });
};

import type { dynamicFormData, formAttribute } from "zhytech-ui";
import { useNormalWorkingAppeal } from "./hooks/useNormalWorkingAppeal";
const formData = ref<dynamicFormData<formAttribute>>();
/**
 * @description: 获取质控评估内容
 * @return
 */
const getFormTemplate = async () => {
  formData.value = undefined;
  if (!templateCode.value) {
    return;
  }
  let params = {
    templateCode: templateCode.value,
    careMainID: currentRecord?.value?.hierarchicalQCMainID,
    index: Math.random()
  };
  await hierarchicalQCService.getQCAssessView(params).then((res: any) => {
    if (res) {
      formData.value = res;
      params.careMainID && showEmployee(params.careMainID);
    }
  });
};
/**
 * @description: 获取质控内容点选数据
 * @param data
 * @return
 */
const getDetails = (data: Record<string, any>[], fileList: Record<string, any>[]) => {
  saveView.templateDetails = data;
  saveView.templateFileList = fileList;
};

const { submitForApproval, stopApproval } = useNormalWorkingAppeal();
const { revokeApproval, showRevokeFlag, hiddenButtonByRevoke } = useApproval(true);
const { showRevokeButton } = useButton();
const { getAuditStatusTag } = useStatusTag();
const appealForm = shallowRef<any>();
const revokeForm = shallowRef<any>();
const appealRecord = ref({} as any);
const rectificationForm = shallowRef<any>();
const rectificationRecord = ref({} as any);
// 表单验证规则
const formRules = {
  appealReason: [{ required: true, message: "申诉原因不能为空", trigger: "blur" }],
  employeeID: [{ required: true, message: "请选择整改人", trigger: "blur" }],
  rectificationDateTime: [{ required: true, message: "请选择整改时间", trigger: "blur" }]
};
const revokeRules = {
  reason: [{ required: true, message: "撤销原因不能为空", trigger: "blur" }]
};
const showDrawerOtherFooter = computed(() => {
  return (
    drawerOptions.value.drawerName !== "subjectMaintenance" &&
    drawerOptions.value.drawerName !== "appeal" &&
    drawerOptions.value.drawerName !== "revoke" &&
    drawerOptions.value.drawerName !== "rectification" &&
    drawerOptions.value.drawerName !== "imgPreview" &&
    currentRecord.value?.auditStatus !== "6"
  );
});
/**
 * @description: 开始申诉，打开申诉框
 * @param row 维护记录
 */
const startAppeal = (row: any) => {
  appealRecord.value = { ...row, appealReason: "" };
  drawerOptions.value.drawerSize = "50%";
  drawerOptions.value.drawerName = "appeal";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
};
import { useUtils } from "../../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 取消申诉审批
 * @param row 维护记录
 * @returns
 */
const stopSubmit = async (row: any) => {
  if (!(await showAlert("warning", "确定取消申诉吗？"))) {
    return;
  }
  await stopApproval(row);
  getTableData();
};
const revokeRecord = ref<Record<string, any>>({});
/**
 * @description: 开始撤销，打开撤销框
 * @param row 维护记录
 * @returns
 */
const startRevoke = (row: any) => {
  revokeRecord.value = { ...row, appealReason: "" };
  drawerOptions.value.drawerSize = "50%";
  drawerOptions.value.drawerName = "revoke";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
};
const componentKey = ref<number>(0);
/**
 * @description :还原原本抽屉选项
 */
const restoreDrawerOption = () => {
  drawerOptions.value.showDrawer = false;
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showCancel = false;
  drawerOptions.value.showConfirm = false;
  // 主题维护之后 重新初始化顶部筛选组件
  drawerOptions.value.drawerName === "subjectMaintenance" && componentKey.value++;
};
/**
 * @description: 控制人员是否显示
 * @param careMainID 记录ID
 * @return
 */
const showEmployee = (careMainID: string) => {
  let hierarchicalQCMainData = showTableData.value.find((hierarchicalQCMain) => {
    return hierarchicalQCMain.hierarchicalQCMainID === careMainID;
  });
  if (!hierarchicalQCMainData) {
    return;
  }
  let showEmployeeLabelFlag = getButtonAuthority([hierarchicalQCMainData.examineEmployeeID, ...hierarchicalQCMainData.qcEmployeeID], 40);
  if (showEmployeeLabelFlag || !formData.value) {
    return;
  }
  formData.value.components.some((element) => {
    // 修改人员的显示标记
    if (element.itemID === 973) {
      element.props.showFlag = showEmployeeLabelFlag;
      return true;
    }
    return false;
  });
};
/**
 * @description: 新增或修改问题整改记录
 * @param row
 * @return
 */
const addOrUpdateRectification = (row: any) => {
  rectificationRecord.value = { ...row.problemRectificationView };
  if (!rectificationRecord.value.employeeID) {
    rectificationRecord.value.employeeID = userStore.employeeID;
    rectificationRecord.value.rectificationDateTime = datetimeUtil.getNowDate();
    rectificationRecord.value.hierarchicalQCMainID = row.hierarchicalQCMainID;
  }
  drawerOptions.value.drawerSize = "40%";
  drawerOptions.value.drawerName = "rectification";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.drawerTitle = "问题整改";
};
/**
 * @description: 保存问题整改记录
 */
const saveProblemRectification = () => {
  hierarchicalQCService.saveProblemRectificationData(rectificationRecord.value).then((result) => {
    if (result) {
      showMessage("success", "保存成功");
      getTableData();
      return;
    }
  });
};
/**
 * @description: 确认整改
 */
const confirmRectification = (hierarchicalQCMainID: string) => {
  const params = {
    hierarchicalQCMainID: hierarchicalQCMainID
  };
  hierarchicalQCService.confirmRectification(params).then((res: any) => {
    if (res) {
      showMessage("success", "确认整改成功");
      getTableData();
    }
  });
};
const imgList = ref<any>();
/**
 * @description: 打开图片预览
 * @param row
 */
const openImgPreview = (row: any) => {
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerName = "imgPreview";
  drawerOptions.value.drawerTitle = "图片预览";
  drawerOptions.value.showCancel = false;
  drawerOptions.value.showConfirm = false;
  let params = {
    templateCode: row.templateCode,
    careMainID: row.hierarchicalQCMainID,
    index: Math.random()
  };
  hierarchicalQCService.getPreviewImage(params).then((res: any) => {
    imgList.value = res;
  });
};

const disabledRecordDateStart = ref<string>("");
const disabledRecordDateEnd = ref<string>("");
/**
 * @description: 日期选择器禁用
 * @param date 选择日期
 */
const disabledDate = (date: Date) => {
  if (!pageOption.value.restrictExamineDateFlag) {
    return false;
  }
  return (
    datetimeUtil.formatDate(date, "yyyy-MM-dd") > disabledRecordDateEnd.value ||
    datetimeUtil.formatDate(date, "yyyy-MM-dd") < disabledRecordDateStart.value
  );
};

// #region 表格筛选器
// 存储激活的筛选项
const activeFilters = ref<Record<string, string[]>>({});
/**
 * @description: 统一筛选
 * @param key
 * @param nestedKey
 */
const getColumnFilters = (key: string, nestedKey?: string) => {
  return computed(() => {
    if (!tableData.value || tableData.value.length === 0) {
      return [];
    }
    const values = new Set<string>();
    tableData.value.forEach((item: any) => {
      if (!item || (item.isFilterRow && item.isFilterRow[key] === -1)) {
        return;
      }
      const value = nestedKey ? item[key]?.[nestedKey] : item[key];
      values.add(value || "");
    });
    let retArr = Array.from(values).map((val) => ({ text: val, value: val }));
    sortByKeys(retArr, ["text"], undefined, (a: any, b: any) => a.localeCompare(b, "zh-Hans-CN"));
    return retArr;
  });
};
// 列筛选list
const qcEmployeeNameFilters = getColumnFilters("qcEmployeeName");
const examineEmployeeFilters = getColumnFilters("examineEmployee");
const rectifierFilters = getColumnFilters("problemRectificationView", "employeeName");
const statusFilters = getColumnFilters("submitStatus");
// table 过滤筛选的列 key-value配置
const filterConfig: Record<string, (item: any) => any> = {
  qcEmployeeName: (item) => item.qcEmployeeName,
  examineEmployee: (item) => item.examineEmployee,
  rectifierEmployeeName: (item) => item.problemRectificationView?.employeeName,
  submitStatus: (item) => item.submitStatus
};
/**
 * @description: 筛选事件
 * @param filters  - filters = { ${column-key}:[keyword1,keyword2]}
 */
const handleFilterChange = (filters: Record<string, string[]>) => {
  // 更新筛选条件：如果某个列的筛选值为空数组，则移除该列的筛选条件
  Object.keys(filters).forEach((key) => {
    // 如果筛选值为空，则移除该列的筛选条件
    if (filters[key].length === 0) {
      delete activeFilters.value[key];
    } else {
      activeFilters.value[key] = filters[key];
    }
  });
  filterTableData();
};
/**
 * @description: 过滤数据
 * @param clearTableFilter 是否清除表格筛选
 * @returns
 */
const filterTableData = (clearTableFilter: boolean = false) => {
  let dataToFilter = tableData.value;
  // 清除表格筛选
  if (clearTableFilter) {
    tableRef.value?.clearFilter();
    // 同时清除筛选条件状态
    activeFilters.value = {};
  } else {
    // 如果存在表格筛选，则按照筛选条件过滤数据
    const hasActiveFilters = Object.values(activeFilters.value).some((f) => f.length > 0);
    if (hasActiveFilters) {
      dataToFilter = dataToFilter.filter((item) => {
        return Object.keys(activeFilters.value).every((key) => {
          const filterValues = activeFilters.value[key];
          if (filterValues.length === 0) {
            return true;
          }
          const itemValue = filterConfig[key](item);
          if (Array.isArray(itemValue)) {
            return itemValue.some((value) => filterValues.includes(value));
          }
          // 特殊处理 问题发生人为字符串 多个名字使用、拼接,解决 筛选'XX'条件时，不能将'XX、YY'筛选出来的情况
          if (itemValue && itemValue.indexOf("、") > -1) {
            return filterValues.includes(itemValue) || itemValue.split("、").some((val: any) => filterValues.includes(val));
          }
          return filterValues.includes(itemValue || "");
        });
      });
    }
  }
  // 最后按照header中的条件开关过滤数据
  showTableData.value = dataToFilter.filter((item: any) => {
    let showAll = showAllSwitch.value
      ? true
      : item.examineEmployeeID === userStore.employeeID || item.qcEmployeeID.includes(userStore.employeeID);
    let showAppealSuccessful = showAppealSuccessfulSwitch.value ? item.auditStatus === "2" : true;
    return showAll && showAppealSuccessful;
  });
};
/**
 * @description: 根据单元格内容显示情况 设置过滤行 (单元格内容不显示时，设置不作为 对应过滤列表内容)
 * @param row 行数据
 * @param column 列字段信息
 * @returns false 不显示列数据
 */
const setFilterRow = (row: any, column: any) => {
  const colKey = `${column.columnKey}`;
  if (!row.isFilterRow) {
    row.isFilterRow = {};
  }
  row.isFilterRow[colKey] = -1;
  return false;
};
// #endregion
</script>

<style lang="scss">
.normal-working-process-control {
  & > .base-header {
    padding: 0 10px;
    @include flex-aline(row, space-between);
    .left {
      @include flex-aline();
      .header-date {
        width: 150px;
      }
    }
    .add-button {
      margin-top: 0;
    }
  }
  .el-drawer {
    .base-header {
      padding: 0 10px;
      & > label {
        margin-left: 10px;
      }
      .header-date {
        width: 150px;
      }
      .qc-date {
        width: 180px;
      }
    }
  }
  .drawer-search-form {
    vertical-align: -webkit-baseline-middle;
  }
  .rectification-form {
    .rectification-date {
      width: 200px;
    }
  }
}
</style>
