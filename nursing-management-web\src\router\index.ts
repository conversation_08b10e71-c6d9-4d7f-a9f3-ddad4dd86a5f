/*
 * FilePath     : \src\router\index.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 08:42
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-01-12 15:53
 * Description  : 路由
 * CodeIterationRecord:
 */

import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      redirect: "/login"
    },
    {
      path: "/login",
      name: "login",
      component: () => import("@/views/login.vue")
    },
    {
      path: "/mainLayout",
      name: "mainLayout",
      component: () => import("@/views/mainLayout/index.vue"),
      children: [
        {
          path: "/home",
          name: "home",
          component: () => import("@/views/home/<USER>"),
          meta: { auth: true }
        },
        /* 未开发的画面 */
        {
          path: "/:pathMatch(.*)*",
          name: "developing",
          component: () => import("@/views/developing.vue")
        }
      ]
    },
    /* 找不到路由跳转到404页面 */
    {
      path: "/:pathMatch(.*)*",
      name: "404",
      component: () => import("@/views/404.vue")
    },
    /* 组件Demo页面 */
    {
      path: "/componentsDemo",
      name: "componentsDemo",
      component: () => import("@/views/componentsDemo.vue")
    },
    /* 组件Demo页面 */
    {
      path: "/univerDemo",
      name: "univerDemo",
      component: () => import("@/views/univerDemo.vue")
    },
    /* 跳转登录中转页面 */
    {
      path: "/externalTransfer",
      name: "externalTransfer",
      component: () => import("@/views/externalTransfer.vue")
    },
    {
      path: "/hierarchicalQCResultForm",
      name: "hierarchicalQCResultForm",
      component: () => import("@/views/qcManagement/hierarchicalQC/hierarchicalQCResultForm.vue")
    }
  ]
});
// 解决发布新版本后，点击菜单时报错
router.onError((error) => {
  const fetchResourcesErrors = ["Failed to fetch dynamically imported module", "Importing a module script failed"];
  if (fetchResourcesErrors.some((item) => error.message.includes(item))) {
    window.location.reload();
  }
});
export default router;
