<!--
 * FilePath     : \src\views\employeeManagement\employeeDepartmentChangeRequest\index.vue
 * Author       : 胡长攀
 * Date         : 2023-12-20 10:03
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-01-08 17:18
 * Description  : 人员部门维护
 -->

<template>
  <base-layout class="employee-department-maintenance" :drawerOptions="drawerOptions">
    <template #header>
      <span>申请日期：</span>
      <el-date-picker
        class="employee-department-date-picker"
        v-model="requestStartDate"
        type="date"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :disabled-date="(val:any)=>pickerOptions(requestEndDate,val)"
        placeholder="请选择日期"
        @change="getTableView"
      />
      <span>--</span>
      <el-date-picker
        class="employee-department-date-picker"
        v-model="requestEndDate"
        type="date"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :disabled-date="(val:any)=>pickerOptions(val,requestStartDate)"
        placeholder="请选择日期"
        @change="getTableView"
      />
      <el-button v-permission:B="1" class="add-button" @click="addEmployeeToDepartment">新增</el-button>
    </template>
    <el-table :data="employeeDepartmentChangeRequestView" border stripe height="100%">
      <el-table-column prop="employeeName" label="人员" align="center" :min-width="convertPX(120)" />
      <el-table-column prop="originalDepartmentName" label="原部门" align="center" :min-width="convertPX(130)" />
      <el-table-column prop="departmentName" label="新部门" align="center" :min-width="convertPX(120)" />
      <el-table-column label="状态" align="center" :width="convertPX(120)">
        <template v-slot="scope">
          <el-tag :type="getApproveStatusTag(scope.row.statusCode)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" align="center" :min-width="convertPX(160)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(80)">
        <template #default="scope">
          <el-tooltip content="删除" v-if="scope.row.statusCode < 1">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteRow(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <div class="employee-department-maintenance-drawer">
        <div class="left-view">
          <div class="header">
            <department-selector v-model="originalDepartmentID" label="原部门" @change="getEmployee(originalDepartmentID, true)" />
            <label class="label">人员：</label>
            <el-input
              class="input"
              v-model="searchOriginalEmployeeInputData"
              @keyup.enter="searchAssignEmployee(true)"
              placeholder="请输入姓名或简拼"
            >
              <template #append>
                <i @click="searchAssignEmployee(true)" class="iconfont icon-search" />
              </template>
            </el-input>
            <label class="employee-total-count">总人数：{{ originalEmployeeCopyListViews.length }}人</label>
          </div>
          <div class="content">
            <drag
              :draggableOption="{ itemKey: 'employeeID', groupName: 'originalEmployeeListGroup', put: true, pull: true }"
              :multiFlag="true"
              v-if="originalEmployeeListViews.length"
              v-model="originalEmployeeListViews"
            >
              <template #content="{ element }">
                <tag :name="element.employeeName" :title="'人员名称：' + element.employeeName + ' ，人员ID：' + element.employeeID"> </tag>
              </template>
            </drag>
          </div>
        </div>
        <div class="right-view">
          <div class="header">
            <department-selector
              v-model="departmentID"
              label="新部门"
              :disableOptions="disableOptions"
              @change="getEmployee(departmentID, false)"
            />
            <label class="label">人员：</label>
            <el-input
              class="input"
              v-model="searchEmployeeInputData"
              @keyup.enter="searchAssignEmployee(false)"
              placeholder="请输入姓名或简拼"
            >
              <template #append>
                <i @click="searchAssignEmployee(false)" class="iconfont icon-search" />
              </template>
            </el-input>
            <label class="employee-total-count">总人数：{{ employeeCopyListViews.length }}人</label>
          </div>
          <div class="content">
            <div class="show-content">
              <drag
                v-model="employeeListViews"
                :draggableOption="{ itemKey: 'employeeID', groupName: 'showEmployeeListGroup', put: false, pull: false }"
                :multiFlag="true"
              >
                <template #content="{ element }">
                  <tag
                    :closeable="element.departmentID === originalDepartmentID"
                    :name="element.employeeName"
                    :title="'人员名称：' + element.employeeName + ' ，人员ID：' + element.employeeID"
                  ></tag>
                </template>
              </drag>
            </div>
            <drag
              v-model="addEmployeeDepartmentChangeRequest"
              :draggableOption="{ itemKey: 'employeeID', groupName: 'employeeListGroup', put: true, pull: true }"
              :multiFlag="true"
              showTipText
            >
              <template #content="{ element, index }">
                <tag
                  :closeable="element?.departmentID === originalDepartmentID"
                  :name="element?.employeeName"
                  :title="'人员名称：' + element?.employeeName + ' ，人员ID：' + element?.employeeID"
                  @remove="deleteEmployee(index)"
                ></tag>
              </template>
            </drag>
          </div>
        </div>
      </div>
    </template>
    <template #drawerOtherFooter>
      <label class="employee-total-count" v-if="addEmployeeDepartmentChangeRequest.length"
        >调整人数：{{ addEmployeeDepartmentChangeRequest.length }}人</label
      >
    </template>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
let { userStore } = useStore();
const { getApproveStatusTag } = useStatusTag();
// 筛选时间
const requestStartDate = ref(datetimeUtil.getNowDate("yyyy-MM-dd"));
const requestEndDate = ref(datetimeUtil.getNowDate("yyyy-MM-dd"));
// 新部门ID
const departmentID = ref<number>(0);
// 原始部门ID
const originalDepartmentID = ref<number>(userStore.departmentID);
// 表格数据
const employeeDepartmentChangeRequestView = ref<Record<string, any>[]>([]);
// 原部门可申请人员列表
let originalEmployeeListViews = ref<Record<string, any>[]>([]);
let originalEmployeeCopyListViews = ref<Record<string, any>[]>([]);
// 新部门人员信息
let employeeListViews = ref<Record<string, any>[]>([]);
let employeeCopyListViews = ref<Record<string, any>[]>([]);
// 搜索框查找（原部门）
let searchOriginalEmployeeInputData = ref<string>("");
// 搜索框查找（新部门）
let searchEmployeeInputData = ref<string>("");
// 添加人员集合
let addEmployeeDepartmentChangeRequest = ref<Record<string, any>[]>([]);
// 禁用部门集合
let disableOptions = ref<number[]>([originalDepartmentID.value]);
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "新增人员部门调动申请",
  showDrawer: false,
  drawerSize: "100%",
  cancel: () => closeDrawer(),
  confirm: () => saveEmployeeDepartmentChangeRequest()
});

onMounted(() => {
  // 获取人员部门变更申请记录
  getTableView();
});
/**
 * @description: 新增部门人员调动申请
 */
const addEmployeeToDepartment = async () => {
  drawerOptions.value.showDrawer = true;
  getEmployee(originalDepartmentID.value, true);
};

/**
 * description: 获取人员清单
 * return {*}
 */
const getEmployee = (departmentID: number, isOriginalDepartmentID: boolean) => {
  let params = {
    departmentID: departmentID
  };
  employeeService.getDepartmentChangeRequestEmployeeByDepartmentID(params).then((data: any) => {
    if (data) {
      if (isOriginalDepartmentID) {
        originalEmployeeListViews.value = data;
        originalEmployeeCopyListViews.value = data;
        addEmployeeDepartmentChangeRequest.value = [];
        disableOptions.value = [originalDepartmentID.value];
      } else {
        employeeListViews.value = data;
        employeeCopyListViews.value = data;
      }
    }
  });
};

/**
 * @description: 移除人员部门调整
 * @param index
 * @return
 */
const deleteEmployee = (index: any) => {
  originalEmployeeListViews.value.push(addEmployeeDepartmentChangeRequest.value[index]);
  addEmployeeDepartmentChangeRequest.value.splice(index, 1);
};
/**
 * @description: 保存员工部门申请变更
 */
const saveEmployeeDepartmentChangeRequest = () => {
  if (addEmployeeDepartmentChangeRequest.value.length === 0) {
    showMessage("warning", "请选择人员");
    return;
  }
  if (departmentID.value === 0) {
    showMessage("warning", "请选择部门");
    return;
  }
  let params = {
    employeeListViews: addEmployeeDepartmentChangeRequest.value,
    departmentID: departmentID.value
  };
  employeeService.saveEmployeeDepartmentChangeRequest(params).then((data: any) => {
    if (data) {
      showMessage("success", "保存成功");
      closeDrawer();
      getTableView();
    } else {
      showMessage("error", "保存失败");
    }
  });
};
/**
 * @description: 获取表格数据
 * @return
 */
const getTableView = () => {
  let params = {
    requestStartDate: requestStartDate.value,
    requestEndDate: requestEndDate.value
  };
  employeeService.getEmployeeDepartmentChangeRequestView(params).then((data: any) => {
    if (data) {
      employeeDepartmentChangeRequestView.value = data;
    }
  });
};
/**
 * @description: 删除员工部门变更申请
 */
const deleteRow = (row: any) => {
  confirmBox("确定要删除此条记录？", "申请记录删除", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    // 删除员工部门变更申请
    employeeService.deleteEmployeeDepartmentChangeRequest(row).then((data: any) => {
      if (data) {
        showMessage("success", "删除成功");
        getTableView();
      } else {
        showMessage("error", "删除失败");
      }
    });
  });
};

/**
 * @description: 过滤方法
 */
const match = (item: Record<string, any>, matchData: string) =>
  item.employeeName.includes(matchData) || item.employeeID.includes(matchData) || item.namePinyin.includes(matchData);
/**
 * @description: 查找人员
 * @param {*} isOriginal 是否是原病区
 */
const searchAssignEmployee = (isOriginal: boolean) => {
  if (isOriginal && originalEmployeeCopyListViews.value) {
    originalEmployeeListViews.value = originalEmployeeCopyListViews.value.filter((item) =>
      match(item, searchOriginalEmployeeInputData.value)
    );
    return;
  }
  if (!isOriginal && employeeCopyListViews.value) {
    employeeListViews.value = employeeCopyListViews.value.filter((item) => match(item, searchEmployeeInputData.value));
  }
};

/**
 * @description: 关闭弹窗
 */
const closeDrawer = () => {
  drawerOptions.value.showDrawer = false;
  departmentID.value = 0;
  originalDepartmentID.value = userStore.departmentID;
  employeeCopyListViews.value = [];
  employeeListViews.value = [];
  addEmployeeDepartmentChangeRequest.value = [];
  searchOriginalEmployeeInputData.value = "";
  searchEmployeeInputData.value = "";
};
/**
 * @description: 开始时间不可大于结束时间
 */
const pickerOptions = (date1: any, date2: any) => {
  if (!date1 || !date2) {
    return;
  }
  let dateTime1 = datetimeUtil.formatDate(date1, "yyyy-MM-dd");
  let dateTime2 = datetimeUtil.formatDate(date2, "yyyy-MM-dd");
  return dateTime1 <= dateTime2;
};
</script>
<style lang="scss">
.employee-department-maintenance {
  .employee-department-date-picker {
    width: 200px;
  }
  .employee-department-maintenance-drawer {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 100%;

    & > div {
      flex: 1;
      height: 100%;
      max-width: 49%;
    }
    .left-view {
      display: flex;
      flex-direction: column;
      height: 100%;
      & > div {
        border: 1px solid $border-color;
      }
      .header {
        padding: 5px 20px;
        .input {
          width: 250px;
        }
      }
      .content {
        flex: auto;
        overflow-y: auto;
        border-top: none;
        .draggable-view {
          width: 48%;
          height: 50px;
        }
      }
    }
    .right-view {
      display: flex;
      flex-direction: column;
      height: 100%;
      .employee-total-count {
        padding-right: 5px;
      }
      & > div {
        border: 1px solid $border-color;
      }
      .header {
        padding: 5px 20px;
        .input {
          width: 300px;
        }
      }
      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-y: auto;
        border-top: none;
        & > div {
          flex: 1;
          width: 100%;
          max-height: 50%;
        }
        .show-content {
          border-bottom: 1px solid $border-color;
        }
        .draggable-view {
          width: 48%;
          height: 50px;
        }
      }
    }
  }
  .employee-total-count {
    color: red;
    margin: 0 10px;
  }
}
</style>
