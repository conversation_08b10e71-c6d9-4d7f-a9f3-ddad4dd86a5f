<!--
 * FilePath     : \src\views\externalTransfer.vue
 * Author       : 张现忠
 * Date         : 2024-06-22 09:40
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-04 08:49
 * Description  : 中转页面，从CCC系统跳转过来
 * CodeIterationRecord: 4683-作为IT人员，我需要CCC系统可以对接护理管理三级质控，以利于CCC系统操作三级质控执行（21）昭永
 -->
<!--
参数说明:
  hospitalID: 医院ID，用于标识用户所属的医院。
  language: 语言类型，默认值为1。
  clientType: 客户端类型，默认值为"1"。
  departmentID: 部门ID，用于标识用户所属的部门。
  loginType: 登录类型，默认值为"his"。
  userId: 用户ID，用于用户认证。
  password: 用户密码，用于用户认证。
  menuID: 菜单ID，用于标识用户所访问的页面。
  sourceSystem: 来源系统，默认值为"CCC"。
  access_token: 单点登录token。
  宏力单点登录链接：http://localhost:6066/externalTransfer?access_token=123&hospitalID=1&language=1&clientType=1&loginType=his
-->
<template><div></div></template>
<script setup lang="ts">
const { sessionStore, userStore, commonStore } = useStore();
const { proxy } = getCurrentInstance() as any;
const route = useRoute();
const router = useRouter() as any;
const routeQuery = route.query as Record<string, any>;
const hospitalID = route.query.hospitalID || "";
const language = route.query.language || 1;
const clientType = route.query.clientType || "1";
const departmentID = ref<number>(routeQuery.departmentID ? parseInt(routeQuery.departmentID) : 0);
const loginType = route.query.loginType || "his";
const sourceSystem = routeQuery.sourceSystem || "CCC";
const accessToken = routeQuery.access_token;
sessionStore.loginParams = routeQuery;
onMounted(async () => {
  await setLanguage();
  await setHospital();
  await init();
});
/**
 * @description: 页面初始化(登录)
 */
const init = async () => {
  // 宏力集成平台单点登录
  if (accessToken && hospitalID === "1") {
    sessionStore.externalTransfer = false;
    // 验证token是否有效
    let params = {
      token: accessToken,
      hospitalID: hospitalID,
      language: language,
      clientType: clientType,
      loginType: loginType
    };
    await userLoginService.ssoUserCheck(params).then(async (session: any) => {
      if (session) {
        await updateStore(session, params);
        initMenuAndRouter();
      }
    });
    return;
  }
  sessionStore.externalTransfer = true;
  if (!routeQuery.userId || !routeQuery.password) {
    showMessage("warning", "缺少参数：userId、password");
    return;
  }
  if (sessionStore.isLogin && sessionStore.token && userStore.employeeID === routeQuery.userId) {
    await userLoginService.getSessionByToken().then(async (session: any) => {
      if (!session) {
        return;
      }
      // 将session中的科室使用传过来的科室（实际是stationID）赋值，然后在updateStore中做转换处理
      session.departmentID = departmentID.value;
      await updateStore(session);
      initMenuAndRouter();
    });
    return;
  }
  // 清除缓存后登录
  sessionStore.$reset();
  userStore.$reset();
  await userLogin(routeQuery.userId, routeQuery.password);
};
/**
 * @description: 跳转页面
 * @returns
 */
const jumpPage = () => {
  // 跳转到相应的路由
  if (routeQuery.router) {
    router.replace({ path: routeQuery.router, query: { ...routeQuery } });
    return;
  }
  if (routeQuery.menuID) {
    let routerPath = findRoutePathById(menuList.value, routeQuery.menuID)?.router;
    if (routerPath) {
      router.replace({ path: routerPath });
    }
  } else {
    router.replace({ path: "/home" });
  }
};

/**
 * @description: 根据菜单ID，找到对应的路由路径
 * @param menuItems 菜单数据
 * @param menuID 菜单ID
 */
const findRoutePathById = (menuItems: any, menuID: number): any => {
  for (let item of menuItems) {
    if (Number(item.menuID) === Number(menuID)) {
      // 找到匹配的菜单项
      return item;
    }
    if (item.children && item.children.length > 0) {
      let found = findRoutePathById(item.children, menuID); // 递归查找子菜单
      if (found) {
        return found;
      }
    }
  }
  return null; // 没有找到匹配的菜单项，返回 null
};
/**
 * @description: 设置医院信息
 * @returns
 */
const setHospital = async () => {
  if (!hospitalID) {
    showMessage("error", "缺少参数：hospitalID");
    return;
  }
  await dictionaryService.getHospitalByHospitalID({ hospitalID: hospitalID }).then((result: any) => {
    if (!result) {
      showMessage("error", "获取医院信息失败！");
      return;
    }
    commonStore.hospital = result;
  });
};
/**
 * @description: 设置语言类别
 * @returns
 */
const setLanguage = async () => {
  await settingDictionaryService.getLanguageList().then((result: any) => {
    commonStore.languageList = result ?? [];
    let localLanguageList = result ?? [];
    let languageObject = localLanguageList.find((languageItem: any) => Number(language) === languageItem.language);
    if (languageObject) {
      commonStore.language = languageObject;
      proxy.$i18n.locale = languageObject.languageCode;
    }
  });
};
/**
 * @description: 账号登录
 * @param userID 用于登录的账号
 * @param password 用于登录的密码
 */
const userLogin = async (userID: string, password: string) => {
  let params = {
    loginType: loginType,
    userID: userID.trim(),
    password: password.trim(),
    hospitalID: hospitalID,
    language: language,
    clientType: clientType
  };
  await userLoginService.login(params).then(async (session: any) => {
    if (session) {
      await updateStore(session, params);
      initMenuAndRouter();
    }
  });
};
/**
 * @description: 转换部门ID(转换成当前登录系统的部门ID OrganizationType =1)
 * @param departmentID 跳转系统的部门ID
 * @param sourceSystem 来源系统
 * @returns { any }转换后的部门ID
 */
const transformDepartmentID = async (departmentID: number, sourceSystem: string): Promise<any> => {
  let transformedDeptID;
  let params = {
    departmentID: departmentID,
    sourceSystem: sourceSystem
  };
  await userLoginService.transformDepartmentID(params).then((result: any) => {
    transformedDeptID = result || departmentID;
  });
  return transformedDeptID;
};
let menuList = ref([] as any);
/**
 * @description: 初始化菜单和路由,然后跳转页面
 * @returns {void}
 */
const initMenuAndRouter = () => {
  // 使用 Promise.all() 等待两个异步操作完成
  Promise.all([routerService.getRouterAndComponentList(), menuService.getMenuList({ menuType: "Main" })]).then(([routerData, menuData]) => {
    // 处理获取到的路由数据
    if (routerData) {
      sessionStore.routerList = (routerData as any).routerList;
      sessionStore.componentList = (routerData as any).componentList;
    }
    // 处理获取到的菜单数据
    if (menuData) {
      let { mainMenuList, topMenuList } = menuData as any;
      menuList.value = mainMenuList;
      sessionStore.pageTopMenus = topMenuList;
    }
    // 在所有异步操作完成后执行 jumpPage()
    jumpPage();
  });
};
/**
 * @description: 更新Store中的数据
 * @param session 返回的session信息
 * @param loginParams 登录参数
 */
const updateStore = async (session: Record<string, any>, loginParams?: Record<string, any>) => {
  sessionStore.isLogin = true;
  sessionStore.token = session.token;
  userStore.employeeID = session.employeeID;
  userStore.oaUserID = session.oaUserID;
  userStore.hisUserID = session.hisUserID;
  userStore.userName = session.userName;
  userStore.roles = session.roles;
  userStore.departmentID = session.departmentID;

  // 组装登录参数，为系统更新后自动重新登录准备
  if (loginParams) {
    sessionStore.loginParams = loginParams;
  }
  // 如果没有传入departmentID，直接返回
  if (departmentID.value === 0) {
    return;
  }
  // 将传入的departmentID转换为护理管理的departmentID
  let transformedDeptID = await transformDepartmentID(departmentID.value, sourceSystem);
  if (transformedDeptID === session.departmentID) {
    return;
  }
  // 更新当前登录用户的部门ID
  userStore.departmentID = transformedDeptID;
  await userLoginService.updateDepartmentIDOfSession({ departmentID: transformedDeptID });
};
</script>
