/*
 * FilePath     : \src\api\annualPlanSettingService.ts
 * Author       : 谢明亮
 * Date         : 2023-09-22 11:15
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-07 21:03
 * Description  : 年度计划字典维护
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { annualIndicatorList } from "@/views/annualPlan/types/annualPlanDictionary";
import qs from "qs";
export class annualPlanSettingService {
  // 分类
  private static getAnnualPlanTypeListApi: string = "/AnnualPlanSetting/GetAnnualPlanTypeList";
  private static saveAnnualPlanTypeListApi: string = "/AnnualPlanSetting/SaveAnnualPlanTypeList";
  private static deleteAnnualPlanTypeApi: string = "/AnnualPlanSetting/DeleteAnnualPlanType";
  private static getAnnualPlanTypeListByDepartmentApi: string = "/AnnualPlanSetting/GetAnnualPlanTypeListByDepartment";
  private static checkAnnualPlanTypeListApi: string = "/AnnualPlanSetting/CheckAnnualPlanTypeList";
  private static getTypeListApi: string = "/AnnualPlanSetting/GetTypeList";
  // 指标
  private static getAnnualIndicatorListApi: string = "/AnnualPlanSetting/GetAnnualIndicatorList";
  private static saveAnnualIndicatorListApi: string = "/AnnualPlanSetting/SaveAnnualIndicatorList";
  private static deleteAnnualIndicatorListApi: string = "/AnnualPlanSetting/DeleteAnnualIndicatorList";
  private static getGoalListApi: string = "/AnnualPlanSetting/GetGoalList";
  // 执行项目
  private static getAnnualInterventionListApi: string = "/AnnualPlanSetting/GetAnnualInterventionList";
  private static saveAnnualInterventionListApi: string = "/AnnualPlanSetting/SaveAnnualInterventionList";

  // 目标
  private static updateGoalContentApi: string = "/AnnualPlanSetting/UpdateGoalContent";
  private static saveAnnualPlanMainGoalApi: string = "/AnnualPlanSetting/SaveAnnualPlanMainGoal";
  private static deleteAnnualPlanMainGoalApi: string = "/AnnualPlanSetting/DeleteAnnualPlanMainGoal";
  private static checkAnnualPlanMainGoalApi: string = "/AnnualPlanSetting/CheckAnnualPlanMainGoal";
  /**
   * @description: 获取指标字典
   * @param params.showUpperIndicator 是否显示上级指标
   * @return
   */
  public static getAnnualIndicatorList = (params?: any) => http.get(this.getAnnualIndicatorListApi, params, { loadingText: Loading.LOAD }) as Promise<annualIndicatorList[]>;
  /**
   * @description: 保存指标字典
   * @param params
   * @return
   */
  public static saveAnnualIndicatorList = (params: any) =>
    http.post(this.saveAnnualIndicatorListApi, params, { loadingText: Loading.SAVE });
  /**
   * @description: 删除指标字典
   * @param params
   * @return
   */
  public static deleteAnnualIndicatorList = (params: any) =>
    http.post(this.deleteAnnualIndicatorListApi, params, { loadingText: Loading.DELETE });
  /**
   * @description: 获取执行项目字典
   * @param params
   * @return
   */
  public static getAnnualInterventionList = (params?: any) =>
    http.get(this.getAnnualInterventionListApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 保存执行项目字典
   * @param params
   * @return
   */
  public static saveAnnualInterventionList = (params: any) =>
    http.post(this.saveAnnualInterventionListApi, params, { loadingText: Loading.SAVE });
  /**
   * @description: 更新目标字典
   * @param params
   * @return
   */
  public static updateGoalContent = (params?: any) => http.get(this.updateGoalContentApi, params);
  /**
   * @description: 获取分类字典
   * @param params
   * @return
   */
  public static getTypeList = (params?: any) => http.post(this.getTypeListApi, qs.stringify(params), { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;

  /**
   * @description: 获取年度计划分类（本部门）
   * @param params
   * @return
   */
  public static getAnnualPlanTypeList = (params?: any) => http.get(this.getAnnualPlanTypeListApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 保存年度计划分类
   * @param params
   * @return
   */
  public static saveAnnualPlanTypeList = (params: any) => http.post(this.saveAnnualPlanTypeListApi, params, { loadingText: Loading.SAVE });
  /**
   * @description: 删除年度计划分类
   * @param params
   * @return
   */
  public static deleteAnnualPlanType = (params: any) => http.post(this.deleteAnnualPlanTypeApi, params, { loadingText: Loading.DELETE });

  /**
   * @description: 获取年度计划分类（本部门及上级、间接上级部门分类）
   * @param params
   * @return
   */
  public static getAnnualPlanTypeListByDepartment = (params: any) =>
    http.get(this.getAnnualPlanTypeListByDepartmentApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 保存年度计划分类目标
   * @param params
   * @return
   */
  public static saveAnnualPlanMainGoal = (params: any) => http.post(this.saveAnnualPlanMainGoalApi, params, { loadingText: Loading.SAVE });
  /**
   * @description: 删除年度计划分类目标
   * @param params
   * @return
   */
  public static deleteAnnualPlanMainGoal = (params: any) =>
    http.post(this.deleteAnnualPlanMainGoalApi, params, { loadingText: Loading.DELETE });
  /**
   * @description: 检核年度计划目标
   */
  public static checkAnnualPlanMainGoal = (params: any) =>
    http.post(this.checkAnnualPlanMainGoalApi, params, { loadingText: Loading.LOAD });

  /**
   * @description: 获取目标字典
   * @param params
   * @returns 
   */
  public static getGoalList = (params: any) => http.post(this.getGoalListApi, qs.stringify(params), { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 检核年度计划分类
   */
  public static checkAnnualPlanTypeList = (params: any) =>
    http.post(this.checkAnnualPlanTypeListApi, params, { loadingText: Loading.LOAD });
}
