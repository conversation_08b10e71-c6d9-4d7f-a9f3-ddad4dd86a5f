<!--
 * FilePath     : \src\views\employeeManagement\index.vue
 * Author       : 孟昭永
 * Date         : 2023-07-27 16:28
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 19:17
 * Description  : 人事档案清单
 * CodeIterationRecord:
-->
<template>
  <base-layout class="employee-list" headerHeight="auto">
    <template #header>
      <div v-if="refreshFlag">
        <department-selector
          v-permission:DL="13"
          v-model="departmentID"
          :props="{ expandTrigger: 'hover', multiple: true }"
          :clearable="true"
          :width="245"
          @change="getEmployee"
        ></department-selector>
        <title-selector @change="employeeSearch" v-model="positionIDs" multiple></title-selector>
        <educational-selector @change="employeeSearch" v-model="educationalIDs" multiple></educational-selector>
        <entry-date-selector @change="employeeSearch" v-model="entryDate"></entry-date-selector>
        <label>姓名：</label>
        <el-input clearable v-model="employeeName" class="name-search-input" :placeholder="`请输入姓名`" @change="employeeSearch">
          <template #append>
            <i @click="employeeSearch" class="iconfont icon-search" />
          </template>
        </el-input>
      </div>
      <keep-alive v-else>
        <div>
          <department-selector
            :props="{ expandTrigger: 'hover', multiple: true }"
            :clearable="true"
            v-model="departmentID"
            :width="240"
            @change="getEmployee"
          ></department-selector>
          <title-selector @change="employeeSearch" v-model="positionIDs" multiple></title-selector>
          <educational-selector @change="employeeSearch" v-model="educationalIDs" multiple></educational-selector>
          <entry-date-selector @change="employeeSearch" v-model="entryDate"></entry-date-selector>
          <label>姓名：</label>
          <el-input clearable v-model="employeeName" class="name-search-input" :placeholder="`请输入姓名`" @change="employeeSearch">
            <template #append>
              <i @click="employeeSearch" class="iconfont icon-search" />
            </template>
          </el-input>
        </div>
      </keep-alive>
      <!-- <el-button type="primary" @click="drawerScreen = true">高级筛选</el-button> -->
      <!-- 统计 -->
      <div class="employee-statistic">
        <div
          v-for="(employeeStatisticView, index) in employeeStatisticViews"
          :key="index"
          @click="cardClick(index)"
          :class="['statistic-card', { select: employeeStatisticView.selectFlag }]"
        >
          <div class="label">{{ employeeStatisticView.statisticTitle }}</div>
          <div class="value">
            {{ employeeStatisticView.statisticValue + "人" }}
          </div>
        </div>
      </div>
    </template>
    <!-- 筛选抽屉 -->
    <!-- <el-drawer v-model="drawerScreen" direction="rtl">
      <template #header>
        <h4>筛选</h4>
      </template>
      <template #default> </template>
      <template #footer>
        <div style="flex: auto">
          <el-button>取消</el-button>
          <el-button type="primary">确认</el-button>
        </div>
      </template>
    </el-drawer> -->

    <!-- 明细 -->
    <el-table
      v-el-table-infinite-scroll="tableDataLoad"
      @row-dblclick="showEmployeeDetail"
      :infinite-scroll-disabled="scrollDisabled"
      stripe
      highlight-current-row
      :data="employeeListViews"
      border
      height="100%"
    >
      <el-table-column fixed="left" prop="departmentName" label="部门" :width="convertPX(200)" />
      <el-table-column fixed="left" prop="employeeName" label="姓名" :width="convertPX(100)" />
      <el-table-column fixed="left" prop="hrpEmployeeID" label="HRP编码" :width="convertPX(115)" />
      <el-table-column prop="gender" label="性别" :width="convertPX(65)" align="center" />
      <el-table-column prop="nation" label="民族" :width="convertPX(65)" />
      <el-table-column prop="birthdate" label="出生日期" :width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.birthdate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="age" label="年龄" :width="convertPX(65)" align="right" />
      <el-table-column prop="entryDate" label="入职日期" :width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.entryDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="entryAge" label="工龄" :width="convertPX(65)" align="right" />
      <el-table-column prop="capabilityLevel" label="层级" :width="convertPX(80)" align="center" />
      <el-table-column prop="promotionDate" label="晋级日期" :width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.promotionDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="professionalLevel" label="职称" :width="convertPX(100)" />
      <el-table-column prop="obtainingDate" label="获取日期" :width="convertPX(150)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.obtainingDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="firstDegree" label="第一学历" :width="convertPX(110)" align="center" />
      <el-table-column prop="highestDegree" label="最高学历" :width="convertPX(110)" align="center" />
      <el-table-column prop="homeAddress" label="家庭地址" :width="convertPX(500)" />
      <el-table-column prop="actualAddress" label="实际住址" :width="convertPX(500)" />
      <el-table-column prop="nativePlace" label="籍贯" :width="convertPX(500)" />
      <el-table-column prop="fileID" label="档案编号" :width="convertPX(140)" />
      <el-table-column prop="jobCategory" label="在职形式" :width="convertPX(110)" align="center" />
    </el-table>
  </base-layout>
</template>

<script setup lang="ts">
let { userStore } = useStore();
const convertPX: any = inject("convertPX");
const route = useRoute();
const scrollDisabled = ref(false);
// 从路由中获取是否需要刷新
let refreshFlag = computed(() => route.meta.refreshFlag);
/**
 * keepAlive包裹页面的钩子函数
 */
onActivated(async () => {
  if (refreshFlag.value) {
    positionIDs.value = [];
    educationalIDs.value = [];
    entryDate.value = [];
    employeeName.value = "";
    employeeListViews.value = [];
    // 初始化
    await getEmployee();
  }
});
let employeeListViews = ref<Record<string, any>[]>([]);
let employeeCopyListViews = ref();
let employeeStatisticViews = ref();
// 病区选择器
let departmentID = ref<number | number[]>(userStore.departmentID);
// 层级选择器
let capabilityIDs = ref<String[]>([]);
// 职称选择器
let positionIDs = ref<string[]>([]);
// 学历选择器
let educationalIDs = ref<String[]>([]);
// 入职日期选择器
let entryDate = ref<String[]>([]);
// 姓名
let employeeName = ref<String>("");
let preResignationEmployee = ref<number>();
// 住院病区人员标签被选中状态
let inHospitalStationEmployee = ref<boolean>(false);
// 非住院病区人员标签被选中状态
let unHospitalStationEmployee = ref<boolean>(false);
/**
 * description: 获取人员清单
 * return {*}
 */
const getEmployee = async () => {
  employeeCopyListViews.value = [];
  employeeStatisticViews.value = [];
  employeeListViews.value = [];
  let params = {
    departmentIDs: typeof departmentID.value === "number" ? [departmentID.value] : departmentID.value,
    capabilityIDs: capabilityIDs.value,
    positionIDs: positionIDs.value,
    educationalIDs: educationalIDs.value,
    entryDate: entryDate.value,
    employeeName: employeeName.value,
    // 只取护士
    nurseFlag: true
  };
  await employeeService.getEmployeeList(params).then((data: any) => {
    if (data) {
      employeeCopyListViews.value = data.employeeListViews;
      employeeStatisticViews.value = data.employeeStatisticViews;
      scrollDisabled.value = false;
      employeeSearch();
    }
  });
};
/**
 * description: 顶部搜索逻辑
 * return {*}
 */
const employeeSearch = () => {
  employeeListViews.value = [];
  let searchData = common.clone(employeeCopyListViews.value);
  if (typeof departmentID.value === "number") {
    departmentID.value && (searchData = searchData.filter((item: any) => departmentID.value === item.departmentID));
  } else {
    departmentID.value && (searchData = searchData.filter((item: any) => (departmentID.value as number[]).includes(item.departmentID)));
  }
  positionIDs.value.length && (searchData = searchData.filter((item: any) => positionIDs.value.includes(item.professionalCode)));
  educationalIDs.value.length && (searchData = searchData.filter((item: any) => educationalIDs.value.includes(item.highestDegreeCode)));
  entryDate.value &&
    entryDate.value.length &&
    (searchData = searchData.filter((item: any) => item.entryDate >= entryDate.value[0] && item.entryDate <= entryDate.value[1]));
  employeeName.value && (searchData = searchData.filter((item: any) => item.employeeName.includes(employeeName.value)));
  capabilityIDs.value.length &&
    (searchData = searchData.filter((item: any) => capabilityIDs.value.includes(String(item.capabilityLevelID))));
  preResignationEmployee.value && (searchData = searchData.filter((item: any) => item.statusCode === preResignationEmployee.value));
  // 住院病区人员条件筛选
  inHospitalStationEmployee.value && (searchData = searchData.filter((item: any) => item.isInHospitalStationEmployee));
  // 非住院病区人员条件筛选
  unHospitalStationEmployee.value && (searchData = searchData.filter((item: any) => !item.isInHospitalStationEmployee));
  if (searchData.length !== employeeCopyListViews.value.length) {
    scrollDisabled.value = true;
    employeeListViews.value = searchData;
    cardSearch();
  } else {
    scrollDisabled.value = false;
    cardSearch(employeeCopyListViews.value);
    tableDataLoad();
  }
};
/**
 * description:卡片人数计算
 * param {*} tableData
 * return {*}
 */
const cardSearch = (tableData: any = employeeListViews.value) => {
  employeeStatisticViews.value.forEach((item: any) => {
    // 在职人数默认为总人数，后端已做筛选
    if (item.statisticCode === "GetStaffEmployee") {
      item.statisticValue = tableData.length;
    } else if (item.statisticCode === "GetPreresignationEmployee") {
      item.statisticValue = tableData.filter((tableItem: any) => tableItem.statusCode === "2").length;
    } else if (item.statisticCode === "GetUnHospitalStationEmployee") {
      item.statisticValue = tableData.filter((tableItem: any) => !tableItem.isInHospitalStationEmployee).length;
    } else if (item.statisticCode === "GetInHospitalStationEmployee") {
      item.statisticValue = tableData.filter((tableItem: any) => tableItem.isInHospitalStationEmployee).length;
    } else {
      item.statisticValue = tableData.filter((tableItem: any) => String(tableItem.capabilityLevelID) === item.statisticCode).length;
    }
  });
};
/**
 * description: 卡片筛选人数
 * param {*} index
 * return {*}
 */
const cardClick = (index: Number) => {
  capabilityIDs.value = [];
  preResignationEmployee.value = undefined;
  inHospitalStationEmployee.value = false;
  unHospitalStationEmployee.value = false;
  employeeStatisticViews.value.forEach((view: any, viewIndex: Number) => {
    index === viewIndex && (view.selectFlag = !view.selectFlag);
    // 目前只有在职人数和层级筛选可以使用,后续需要迭代
    view.selectFlag && view.statisticFunction === "GetCapabilityLevel" && capabilityIDs.value.push(view.statisticCode);
    view.selectFlag && view.statisticFunction === "GetPreresignationEmployee" && (preResignationEmployee.value = 2);
    view.selectFlag && view.statisticFunction === "GetInHospitalStationEmployee" && (inHospitalStationEmployee.value = true);
    view.selectFlag && view.statisticFunction === "GetUnHospitalStationEmployee" && (unHospitalStationEmployee.value = true);
  });
  employeeSearch();
};
/**
 * description: 表格按需加载
 * param {*} tableData
 * return {*}
 */
const tableDataLoad = (tableData: any = employeeCopyListViews.value) => {
  if (!tableData) {
    return;
  }
  let loadData = tableData.slice(employeeListViews.value.length, employeeListViews.value.length + 50);
  employeeListViews.value = [...employeeListViews.value, ...loadData];
  if (employeeListViews.value.length >= employeeCopyListViews.value.length) {
    scrollDisabled.value = true;
  }
};
/**
 * description: 双击跳转到详情页面
 * return {*}
 */
const router = useRouter();
const showEmployeeDetail = (item: any) => {
  router.push({
    path: "/personalInformation",
    query: {
      employeeID: item.employeeID
    }
  });
};
</script>
<style lang="scss">
.employee-list {
  width: 100%;
  background-color: #ffffff;
  .base-header {
    .name-search-input {
      width: 200px;
    }
  }
  .employee-statistic {
    display: flex;
    .statistic-card {
      flex: auto;
      margin: 5px 5px 0 5px;
      padding: 5px;
      border-radius: 6px;
      cursor: pointer;
      border: 1px solid $border-color;
      &.select {
        box-shadow: 2px 5px 4px 1px rgba(0, 0, 0, 0.3);
        @include select-style();
      }
      & > div {
        height: 20px;
        line-height: 20px;
        text-align: center;
      }
      background-color: $base-color;
      font-weight: bold;
      color: #ffffff;
    }
  }
  .employee-table {
    overflow-anchor: none;
  }
  .employee-list-pagination {
    margin: 3px 0px 0px 0px;
    display: flex;
    justify-content: center;
  }
}
</style>
