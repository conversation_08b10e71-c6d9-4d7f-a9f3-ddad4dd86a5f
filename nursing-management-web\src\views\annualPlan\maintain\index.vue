<!--
 * FilePath     : /src/views/annualPlan/maintain/index.vue
 * Author       : 杨欣欣
 * Date         : 2023-08-24 10:10
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 20:48
 * Description  : 年度计划维护页面
 * CodeIterationRecord:
-->
<template>
  <base-layout class="annual-plan-main" headerHeight="auto" :drawerOptions="drawerOptions">
    <template #header>
      <annual-plan-header :year="year" />
      <div>
        <!-- <el-button v-permission:B="3" class="add-button" @click="uploadFile">上传附件</el-button> -->
        <el-button v-permission:B="27" :disabled="annualPlanStore.annualPlanPublished" @click="publishAnnualPlan">{{ annualPlanStore.annualPlanPublished ? "已" : "" }}发布</el-button>
      </div>
    </template>
    <div class="main-wrap" v-if="annualPlanMainID">
      <div class="left-wrap">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>目录</span>
            </div>
          </template>
          <div
            class="type-list"
            v-drag-sort="{
              el: '',
              handleClass: '.icon-sort',
              ghostClass: 'type-drag-ghost',
              disabled: readOnly,
              callBack: maintainStore.resetPlanTypesSort
            }"
          >
            <div class="type-item" v-for="(planType, index) in planTypes" :key="planType.typeID" @click="goToType(planType.typeID)">
              <div>
                <i :class="{ 'iconfont icon-sort': !readOnly }"></i>
                {{ `${index + 1}、${planType.typeContent}${sessionStore.debugMode ? `#${planType.typeID}` : ""}` }}
              </div>
            </div>
          </div>
        </el-card>
        <ref-indicators class="indicator-list" />
      </div>
      <div class="right-wrap">
        <template v-for="(planType, index) in planTypes" :key="planType.typeID">
          <div :class="['type-content', `type${planType.typeID}`]">
            {{ index + 1 + "、" + planType.typeContent }}
          </div>
          <!-- 计划分类的策略目标 -->
          <annual-plan-goals
            v-model="goalsByTypeID[planType.typeID]"
            :ref="(el:any) => common.setComponentRefs(planGoalRefs, planType.typeID, el)"
            :typeID="planType.typeID"
          />
        </template>
      </div>
    </div>
    <template #drawerContent>
      附件：
      <Upload-File :fileAssociations="fileAssociations" :fileInfo="attachments" :header="fileHeader"></Upload-File>
    </template>
  </base-layout>
  <Teleport to="body">
    <component :is="activeComponent" />
  </Teleport>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
import { useAnnualPlanMaintainStore } from "./hooks/useAnnualPlanMaintainStore";
import common from "@/utils/common";
import type { planIndicator, planProject } from "../types/annualPlanMain";
const { sessionStore } = useStore();
const win = window as any;

const year = usePlanTime().getPlanAnnual();
const annualPlanStore = usePlanManagementStore();
const { annualPlanMainID, departmentID, readOnly } = storeToRefs(annualPlanStore);
const maintainStore = useAnnualPlanMaintainStore();
const { planTypes, goalsByTypeID, planGroups, indicatorsByGroupID, projectsByGroupID } = storeToRefs(maintainStore);

//#region 数据加载
onMounted(async () => {
  await loadAnnualPlanData();
});
watch(departmentID, async () => {
  await loadAnnualPlanData(true);
});
/**
 * @description: 初始化年度计划数据
 */
const initAnnualPlan = async () => {
  const params = {
    mainID: annualPlanMainID.value
  };
  const result = await annualPlanMainService.getAnnualPlan(params);
  maintainStore.$patch(() => {
    ({
      planTypes: planTypes.value,
      goalsByTypeID: goalsByTypeID.value,
      planGroups: planGroups.value,
      indicatorsByGroupID: indicatorsByGroupID.value,
      projectsByGroupID: projectsByGroupID.value
    } = result);
  });
};
/**
 * @description: 加载年度计划数据
 */
const loadAnnualPlanData = async (reset: boolean = false) => {
  await annualPlanStore.setAnnualPlanMainID(reset);
  maintainStore.expandPlanGoalIDs = [];
  maintainStore.expandedPlanGoalIDs.clear();
  await initAnnualPlan();
  annualPlanStore.setReadOnly(annualPlanStore.annualPlanPublished);
};
//#endregion

const planGoalRefs = ref<Map<number, Record<string, any>>>(new Map());
/**
 * @description: 跳到指定计划分类
 * @param typeID 分类字典ID
 * @return
 */
const goToType = async (typeID: number) => {
  maintainStore.expandPlanGoalIDs = goalsByTypeID.value[typeID].map((planGoal) => planGoal.mainGoalID);
  maintainStore.addUnExpandedPlanGoalIDs(maintainStore.expandPlanGoalIDs);
  const typeContent = document.querySelector(`.type${typeID}`) as HTMLElement;
  typeContent?.scrollIntoView();
};

//#region 发布年度计划
/**
 * @description: 发布年度计划
 * @return
 */
const publishAnnualPlan = async () => {
  const params = {
    mainID: annualPlanMainID.value
  };
  annualPlanMainService.publishAnnualPlan(params).then((res: any) => {
    if (!res) {
      showMessage("error", "发布失败");
      return;
    }
    showMessage("success", "发布成功");
    annualPlanStore.annualPlanPublished = true;
    annualPlanStore.setReadOnly(true);
  });
};
//#endregion

//#region 弹窗
const isNewItem = computed(() => maintainStore.editingDetail?.detailID?.startsWith("temp_") ?? true);
const editingDetail = ref<Partial<planProject | planIndicator>>({});
const activeComponent = computed(() => {
  if (maintainStore.editingDetailType === "") {
    return undefined;
  }
  // 懒加载组件，只有在需要时才导入
  if (maintainStore.editingDetailType === "indicator") {
    return defineAsyncComponent(() => import("./components/indicatorEditDialog.vue"));
  }
  return defineAsyncComponent(() => import("./components/projectEditDialog.vue"));
});
maintainStore.$onAction(({ name }) => {
  if (name === "openDialog" && maintainStore.editingDetail) {
    editingDetail.value = isNewItem.value ? maintainStore.editingDetail : common.clone(maintainStore.editingDetail);
  }
});
//#endregion

//#region 上传文件
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "上传附件",
  drawerSize: "50%",
  showDrawer: false,
  confirm: () => (drawerOptions.value.showDrawer = false),
  beforeClose: () => {
    if (win.apiRequestList.length) {
      showMessage("warning", "正在保存，请稍后再关闭");
      return;
    }
    drawerOptions.value.showDrawer = false;
  }
});
// eslint-disable-next-line @typescript-eslint/naming-convention
const fileHeader = ref<Object>({ "Management-Token": sessionStore.token });
const attachments = ref<Array<Object>>([]);
const fileAssociations = computed(() => {
  return {
    sourceID: annualPlanMainID.value,
    fileClass: FileClass.AnnualPlanFile
  };
});
// /**
//  * @description: 上传附件
//  * @return
//  */
// const uploadFile = async () => {
//   fileAssociations.value.sourceID = annualPlanMainID.value;
//   attachments.value = await annualPlanMainService.getAnnualPlanAttachments({
//     annualPlanMainID: annualPlanMainID.value
//   });
//   drawerOptions.value.showDrawer = true;
// };
//#endregion

// 卸载组件后，清空展开的目标ID集合
onUnmounted(() => {
  maintainStore.expandPlanGoalIDs = [];
  maintainStore.expandedPlanGoalIDs.clear();
  annualPlanStore.setReadOnly(false);
});
</script>
<style lang="scss">
.annual-plan-main {
  .base-header {
    display: flex;
    justify-content: space-between;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    align-items: center;
    .annual-plan-header {
      padding-top: 8px;
    }
  }

  .year-picker {
    width: 140px;
    margin-right: 20px;
  }
  .header-label {
    margin-left: 10px;
  }
  .main-wrap {
    display: flex;
    height: 100%;
    .left-wrap {
      height: 100%;
      width: 300px;
      margin-right: 20px;
      display: flex;
      flex-direction: column;
      .el-card {
        height: 100%;
        .el-card__header {
          padding: 4px 16px;
        }
        .el-card__body {
          padding: 0 0 0 4px;
          height: 100%;
          overflow-y: auto;
          .type-drag-ghost {
            opacity: 0;
          }
          &::-webkit-scrollbar {
            width: 0;
          }
          &:hover::-webkit-scrollbar {
            width: 6px;
          }
        }
      }
      .card-header {
        font-size: 20px;
        font-weight: bold;
      }
      .type-list {
        margin-bottom: 10px;
        height: 100%;
        .type-item {
          font-size: 18px;
          cursor: pointer;
          padding: 5px;
          margin: 5px 0;
          border: 1px solid transparent;
          .icon-sort {
            margin-right: 10px;
            cursor: grab;
          }
          &:hover {
            border-color: $border-color;
          }
        }
      }
      .indicator-list {
        height: calc(100% - 310px);
      }
    }
    .right-wrap {
      flex: auto;
      // 面板区域滚动，不影响左侧区域
      overflow-y: auto;
      .type-content {
        margin: 20px 0 5px 0;
        font-size: 24px;
        font-weight: bold;
        color: red;
        &:first-child {
          margin-top: 10px;
        }
      }
    }
  }
  // 指标字典列表占据剩余部分
  .ref-indicators {
    flex: 1;
  }
}
</style>
