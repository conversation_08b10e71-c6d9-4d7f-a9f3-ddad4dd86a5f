/*
 * FilePath     : \src\views\trainingManagement\types\trainingClass.ts
 * Author       : 孟昭永
 * Date         : 2024-07-10 17:15
 * LastEditors  : 孟昭永
 * LastEditTime : 2024-07-12 16:54
 * Description  :
 * CodeIterationRecord:
 */
/**
 * @description: 培训群组
 */
export interface trainingClass {
  /**
   * 培训群组主表ID
   */
  trainingClassMainID: string;
  /*
   * 培训群组名称
   */
  trainingClassName: string;
  /*
   * 开班日期
   */
  startDate: string;
  /*
   * 闭班日期
   */
  endDate: string;
  /*
   * 培训时长
   */
  trainingDuration: number;
  /**
   * 结业日期
   */
  completeDate: string;
  /*
   * 新增人员
   */
  addEmployeeName: string;
  /*
   * 添加时间
   */
  addDateTime: string;
  /*
   * 修改时间
   */
  modifyEmployeeName: string;
  /*
   * 修改时间
   */
  modifyDateTime: string;
  /**
   * 课程ID组
   */
  courseSettingIDArr: Array<string>;
}
/**
 * @description: 培训群组的课程
 */
export interface trainingClassCourse {
  /**
   * 课程唯一ID
   */
  courseSettingID: number;
  /*
   * 课程名称
   */
  courseName: string;
  /*
   * 课程简介
   */
  courseIntroduction: string;
  /**
   * 课程制定年份
   */
  year: number;
  /*
   * 课程分类ID
   */
  courseTypeID: string;
  /*
   * 课程分类
   */
  courseTypeName: string;
  /*
   * 子课程列表
   */
  childCourses?: trainingClassCourse[];
}
