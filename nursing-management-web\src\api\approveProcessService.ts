/*
 * FilePath     : \src\api\approveProcessService.ts
 * Author       : 杨欣欣
 * Date         : 2023-09-17 15:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-18 17:06
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";

export class approveProcessService {
  private static getApproveProcessesApi: string = "/ApproveProcess/GetApproveProcesses";
  private static getApproveProcessNodesApi: string = "/ApproveProcess/GetApproveProcessNodes";
  private static getEnableDepartmentIDsByProveCategoryApi: string = "/ApproveProcess/GetEnableDepartmentIDsByProveCategory";
  private static addApproveProcessApi: string = "/ApproveProcess/AddApproveProcess";
  private static updateApproveProcessApi: string = "/ApproveProcess/UpdateApproveProcess";
  private static deleteApproveProcessApi: string = "/ApproveProcess/DeleteApproveProcess";
  private static enableApproveProcessApi: string = "/ApproveProcess/EnableApproveProcess";
  private static disableApproveProcessApi: string = "/ApproveProcess/DisableApproveProcess";

  /**
   * 获取制订的审批流程列表
   * @param params
   * @param params.employeeID HR工号
   * @returns
   */
  public static getApproveProcesses = (params?: any) => http.get(this.getApproveProcessesApi, params, { loadingText: Loading.LOAD });
  /**
   * 获取审批流程对应的节点数据
   * @param params
   * @param params.approveProcessID 审批流程ID
   * @returns
   */
  public static getApproveProcessNodes = (params?: any) => http.get(this.getApproveProcessNodesApi, params, { loadingText: Loading.LOAD });
  /**
   * 获取分类已启用科室集合
   * @param params
   * @param params.typeCode 分类码
   * @returns
   */
  public static getEnableDepartmentIDsByProveCategory = (params?: any) => http.get(this.getEnableDepartmentIDsByProveCategoryApi, params);
  /**
   * @description: 新增审批流程
   * @param params 保存对象
   * @return
   */
  public static addApproveProcess = (params?: any) => http.post(this.addApproveProcessApi, params, { loadingText: Loading.LOAD });
  /**
   * @description: 修改审批流程
   * @param params 保存对象
   * @return
   */
  public static updateApproveProcess = (params?: any) => http.post(this.updateApproveProcessApi, params, { loadingText: Loading.LOAD });
  /**
   * 删除审批流程
   * @param params
   * @param params.approveProcessID 审批流程ID
   * @returns
   */
  public static deleteApproveProcess = (params?: any) => http.post(this.deleteApproveProcessApi, params, { loadingText: Loading.LOAD });
  /**
   * 启用审批流程
   * @param params
   * @param params.approveProcessID 审批流程ID
   * @returns
   */
  public static enableApproveProcess = (params?: any) => http.post(this.enableApproveProcessApi, params, { loadingText: Loading.LOAD });
  /**
   * 停用审批流程
   * @param params
   * @param params.approveProcessID 审批流程ID
   * @returns
   */
  public static disableApproveProcess = (params?: any) => http.post(this.disableApproveProcessApi, params, { loadingText: Loading.LOAD });
}
