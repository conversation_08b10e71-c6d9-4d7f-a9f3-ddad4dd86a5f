/*
 * FilePath     : \src\views\annualPlan\types\importWorkDto.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-30 15:11
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 19:38
 * Description  : 导入工作视图对象
 * CodeIterationRecord: 
 */
import type { annualPrincipal } from "./common";

/**
 * 导入计划工作 - 工作项
 */
export interface importWorkDto {
  /**
   * 所属分类ID
   */
  typeID: number;
  /**
   * 参考的字典ID
   */
  apInterventionID?: number | undefined;
  /**
   * 序号
   */
  sort?: number | undefined;
  /**
   * 工作内容
   */
  workContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 是否是临时性工作
   */
  isTemp: boolean;
  /**
   * 计划执行月份，仅季度计划制定时有用
   */
  planMonths: number[];
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人集合
   */
  principals: annualPrincipal[];
}
