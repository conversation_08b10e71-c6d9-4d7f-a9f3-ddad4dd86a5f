<!--
 * FilePath     : \src\views\scheduling\index.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:32
 * LastEditors  : 苏军志
 * LastEditTime : 2023-12-18 18:50
 * Description  : 排班相关
 * CodeIterationRecord:
-->
<template>
  <top-menu ref="childPage" :menuList="tabMenus"></top-menu>
</template>

<script setup lang="ts">
const { sessionStore } = useStore();
const route = useRoute();
let childPage = ref<any>();
let parentRouterName = route.matched[route.matched.length - 2]?.name as string;
const tabMenus = ref(sessionStore.pageTopMenus[parentRouterName]);
// #region  解决 路由带参数时，切换路由不刷新问题
let params = route.params;
watch(
  () => route.params,
  (val) => {
    if (Reflect.ownKeys(val).length && params !== val) {
      params = val;
      refreshData();
    }
  },
  { immediate: true }
);
//#endregion

/**
 * description: 系统顶部刷新按钮触发
 */
const refreshData = () => {
  nextTick(() => {
    if (childPage?.value) {
      // 刷新前取消未完成的请求
      http.cancelPageRequest();
      childPage?.value.refreshData();
    }
  });
};
// 暴漏给父路由
defineExpose({
  refreshData
});
</script>
