/*
 * FilePath     : \src\api\employeeRoleService.ts
 * Author       : LX
 * Date         : 2023-08-14 10:03
 * LastEditors  : 马超
 * LastEditTime : 2025-03-26 09:56
 * Description  : 人员角色相关API
 */
import http from "@/utils/http";
import qs from "qs";
export class employeeRoleService {
  private static getEmployeeRoleListApi: string = "EmployeeRole/GetEmployeeRoleList";
  private static saveEmployeeRoleApi: string = "EmployeeRole/SaveEmployeeRole";
  private static deleteEmployeeRoleApi: string = "EmployeeRole/DeleteEmployeeRole";
  /**
   * 获取人员角色列表
   * @param params
   * @returns array
   */
  public static getEmployeeRoleList(params?: any) {
    return http.get(this.getEmployeeRoleListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 保存人员角色
   * @param params
   * @returns bool
   */
  public static SaveEmployeeRole(params: any) {
    return http.post(this.saveEmployeeRoleApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * 删除人员角色
   * @param params
   * @returns bool
   */
  public static DeleteEmployeeRole(params: any) {
    return http.post(this.deleteEmployeeRoleApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
}
