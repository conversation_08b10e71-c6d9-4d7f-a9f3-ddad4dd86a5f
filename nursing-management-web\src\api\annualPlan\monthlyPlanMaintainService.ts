/*
 * FilePath     : \src\api\annualPlan\monthlyPlanMaintainService.ts
 * Author       : 杨欣欣
 * Date         : 2025-03-18 16:41
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 10:36
 * Description  : 月度计划制定-查询与命令
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { planWorkImportVo } from "@/views/annualPlan/types/planWorkImportVo";
import type { monthlyPlanPreview } from "@/views/annualPlan/types/monthlyPlanPreview";
import type {
  saveMonthlyWorksCommand,
  importMonthlyWorksCommand,
  updateMonthlyPlanWorkCommand,
  deleteMonthlyWorkQuery,
  getMonthlyPlanMainIDQuery,
  publishMonthlyPlanCommand,
  getMonthlyWorksQuery,
  getMonthlyPlanStatusQuery,
  resetMonthlyWorksSortCommand,
  getCanImportMpWorksQuery,
  getMonthlyPlanQuickReferenceVoQuery,
  getMonthlyPlanPreviewQuery,
  getBrowseMPViewsQuery
} from "@/views/annualPlan/types/monthlyPlanTypes";
import type { planAndWorksVo } from "@/views/annualPlan/types/planAndWorksVo";

export class monthlyPlanMaintainService {
  //#region 月计划表增删改查
  private static saveMonthlyWorksApi: string = "/MonthlyPlanMaintain/SaveMonthlyWorks";
  private static deleteMonthlyWorkApi: string = "/MonthlyPlanMaintain/DeleteMonthlyWork";
  private static updateMonthlyWorkApi: string = "/MonthlyPlanMaintain/UpdateMonthlyWork";
  private static getMonthlyPlanMainIDApi: string = "/MonthlyPlanMaintain/GetMonthlyPlanMainID";
  private static publishMonthlyPlanApi: string = "/MonthlyPlanMaintain/PublishMonthlyPlan";
  private static getMonthlyWorksApi: string = "/MonthlyPlanMaintain/GetMonthlyWorks";
  private static getMonthlyPlanStatusApi: string = "/MonthlyPlanMaintain/GetMonthlyPlanStatus";
  private static resetMonthlyWorksSortApi: string = "/MonthlyPlanMaintain/ResetMonthlyWorksSort";
  private static getMonthlyPlanPreviewApi: string = "/MonthlyPlanMaintain/GetMonthlyPlanPreview";
  private static getBrowseMPViewsApi: string = "/MonthlyPlanMaintain/GetBrowseMPViews";
  //#endregion

  //#region 参考、导入其它部门数据
  private static getCanImportMpWorksGroupByPlanThenTypeApi: string = "/MonthlyPlanMaintain/GetCanImportMpWorksGroupByPlanThenType";
  private static getMonthlyPlanQuickReferenceVosApi: string = "/MonthlyPlanMaintain/GetMonthlyPlanQuickReferenceVos";
  private static importMonthlyWorksApi: string = "/MonthlyPlanMaintain/ImportMonthlyWorks";
  //#endregion

  //#region 月计划表增删改查
  public static saveMonthlyWorks = (params: saveMonthlyWorksCommand) =>
    http.post(this.saveMonthlyWorksApi, params, { loadingText: Loading.SAVE }) as Promise<boolean>;

  public static deleteMonthlyWork = (params: deleteMonthlyWorkQuery) =>
    http.get(this.deleteMonthlyWorkApi, params, { loadingText: Loading.DELETE }) as Promise<boolean>;

  public static updateMonthlyWork = (params: updateMonthlyPlanWorkCommand) =>
    http.post(this.updateMonthlyWorkApi, params) as Promise<boolean>;

  public static getMonthlyPlanMainID = (params: getMonthlyPlanMainIDQuery) =>
    http.get(this.getMonthlyPlanMainIDApi, params) as Promise<string>;

  public static publishMonthlyPlan = (params: publishMonthlyPlanCommand) =>
    http.post(this.publishMonthlyPlanApi, undefined, {
      params,
      loadingText: Loading.SAVE
    }) as Promise<boolean>;

  public static getMonthlyWorks = (params: getMonthlyWorksQuery) =>
    http.get(this.getMonthlyWorksApi, params, { loadingText: Loading.LOAD }) as Promise<planAndWorksVo[]>;

  public static getMonthlyPlanStatus = (params: getMonthlyPlanStatusQuery) =>
    http.get(this.getMonthlyPlanStatusApi, params, { loadingText: Loading.LOAD }) as Promise<boolean>;
  public static resetMonthlyWorksSort = (params: resetMonthlyWorksSortCommand) => http.post(this.resetMonthlyWorksSortApi, params);
  //#endregion

  //#region 参考、导入其它部门数据
  public static getCanImportMpWorksGroupByPlanThenType = (params: getCanImportMpWorksQuery) =>
    http.get(this.getCanImportMpWorksGroupByPlanThenTypeApi, params, { loadingText: Loading.LOAD }) as Promise<planWorkImportVo[]>;

  public static getMonthlyPlanQuickReferenceVos = (params: getMonthlyPlanQuickReferenceVoQuery) =>
    http.get(this.getMonthlyPlanQuickReferenceVosApi, params, { loadingText: Loading.LOAD }) as Promise<planWorkImportVo[]>;

  public static importMonthlyWorks = (params: importMonthlyWorksCommand) =>
    http.post(this.importMonthlyWorksApi, params, { loadingText: Loading.SAVE }) as Promise<string>;

  //#endregion

  //#region 月度计划预览
  public static getMonthlyPlanPreview = (params: getMonthlyPlanPreviewQuery) =>
    http.get(this.getMonthlyPlanPreviewApi, params, { loadingText: Loading.LOAD }) as Promise<monthlyPlanPreview>;
  //#endregion

  //#region 月度计划浏览
  /**
   * @description: 获取浏览月度计划视图
   * @param params
   * @return
   */
  public static getBrowseMPViews = (params: getBrowseMPViewsQuery) =>
    http.get(this.getBrowseMPViewsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  //#endregion
}
