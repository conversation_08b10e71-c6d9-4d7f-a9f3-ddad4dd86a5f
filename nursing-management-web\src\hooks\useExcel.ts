/*
 * relative     : \nursing-management-web\src\hooks\useExcel.ts
 * Author       : 张现忠
 * Date         : 2025-04-04 10:01
 * LastEditors  : 张现忠
 * LastEditTime : 2025-04-04 10:45
 * Description  :excel文档操作工具
 * CodeIterationRecord:
 */
import * as XLSX from "xlsx";
import { read } from "xlsx";

export function useExcel() {
  // 返回当前时区与该日期所在时区之间的时间差（以毫秒为单位）
  const getTimezoneOffsetMS = (date: Date) => {
    const time = date.getTime();
    const utcTime = Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds()
    );
    return time - utcTime;
  };

  /**
   * @description: 计算当前时区与1899年12月30日时区之间的时间差（以毫秒为单位）
   * @param function
   * @return
   */
  const importBugHotfixDiff = (() => {
    const baseDate = new Date(1899, 11, 30, 0, 0, 0);
    const dnThreshAsIs = (new Date().getTimezoneOffset() - baseDate.getTimezoneOffset()) * 60000;
    const dnThreshToBe = getTimezoneOffsetMS(new Date()) - getTimezoneOffsetMS(baseDate);
    return dnThreshAsIs - dnThreshToBe;
  })();

  /**
   * @description: 修复日期的精度损失（需要注意传入的 Date 对象必须是已经转换为当前时区 Date 对象）
   * @param date
   * @return
   */
  const fixPrecisionLoss = (date: Date) => {
    // 如果年份是1899说明是时间类型，不存在时差，直接返回
    if (date.getFullYear() === 1899) {
      return datetimeUtil.formatDate(date, "hh:mm:ss");
    }
    // 如果是日期时间类型，会相差43秒，需要矫正时间
    let newDate = new Date(date.getTime() - importBugHotfixDiff);
    return datetimeUtil.formatDate(newDate, "yyyy-MM-dd hh:mm:ss");
  };
  /**
   * description: 读取excel文件
   * param {*} file
   * return {*}
   */
  const readExcelFile = (file: File): Promise<XLSX.WorkBook> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const workbook = read(e.target?.result, {
            type: "array",
            // 保证日期列可以正常导入
            cellDates: true
          });
          resolve(workbook);
        } catch (error) {
          reject(new Error("Excel文件解析失败，请检查文件格式是否正确"));
        }
      };
      reader.onerror = () => reject(new Error("文件读取失败"));
      reader.readAsArrayBuffer(file);
    });
  };
  return {
    fixPrecisionLoss,
    readExcelFile
  };
}
