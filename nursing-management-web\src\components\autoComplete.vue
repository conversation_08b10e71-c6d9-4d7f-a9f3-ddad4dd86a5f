<!--
 * FilePath     : \src\components\autoComplete.vue
 * Author       : 郭鹏超
 * Date         : 2023-11-14 10:38
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-05 19:16
 * Description  : 自动补全组件
 * CodeIterationRecord:
-->

<template>
  <div class="auto-complete">
    <span v-if="label">{{ label }}</span>
    <el-autocomplete
      ref="autoCompleteRef"
      v-model="modelContent"
      :select-when-unmatched="selectWhenUnmatched"
      :fetch-suggestions="getSuggestion"
      value-key="value"
      clearable
      :disabled="disabled"
      v-bind="$attrs"
      @blur="blurData"
    >
      <template #default="{ item }">
        <span>{{ item.value }}</span>
      </template>
    </el-autocomplete>
  </div>
</template>

<script setup lang="ts">
const autoCompleteRef = ref<HTMLElement | null>();
const props = defineProps({
  modelValue: {
    type: String,
    default: () => undefined
  },
  options: {
    type: Array<OptionView>,
    default: () => [],
    required: false
  },
  // 选择器标签
  label: {
    type: String,
    default: () => "备注："
  },
  selectWhenUnmatched: {
    type: Boolean,
    default: () => true
  },
  matchMode: {
    type: String as () => "start" | "random",
    default: "start"
  },
  width: {
    type: Number,
    default: 500
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
let { options, width } = toRefs(props);
const emits = defineEmits(["update:modelValue", "change"]);
// 选中元素的计算值，父子组件通信使用
const modelContent = useVModel(props, "modelValue", emits);
/**
 * @description: 备选框失焦后触发chang方法
 * @return
 */
const blurData = () => {
  emits("change", modelContent.value);
};
/**
 * @description: 根据已有输入获取提示信息
 * @return
 * @param inputValue 已有输入值
 * @param cb 回调 提示信息
 */
const getSuggestion = (inputValue: string, cb: Function) => {
  const results = inputValue ? useArrayFilter(options, createFilter(inputValue)) : options.value;
  cb(unref(results));
};
/**
 * @description: 过滤建议的方法
 * @return
 * @param queryString 已输入的字符串
 */
const createFilter = (queryString: string) => {
  const matchModes: Record<string, any> = {
    start: (filterData: Record<string, any>) => filterData.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0,
    random: (filterData: Record<string, any>) => filterData.value.indexOf(queryString) >= 0
  };
  return matchModes[props.matchMode];
};
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const completeWidth = computed(() => `${convertPX(width.value)}px`);
</script>

<style lang="scss">
.auto-complete {
  display: inline-flex;
  justify-content: center;
  span {
    font-size: 12px;
    flex-shrink: 0;
  }
  .el-autocomplete {
    width: v-bind(completeWidth);
  }
}
</style>
