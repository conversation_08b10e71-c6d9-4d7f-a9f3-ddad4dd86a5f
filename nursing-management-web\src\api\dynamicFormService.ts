/*
 * FilePath     : \src\api\dynamicFormService.ts
 * Author       : 苏军志
 * Date         : 2024-03-23 18:17
 * LastEditors  : 苏军志
 * LastEditTime : 2024-03-27 16:54
 * Description  : 动态表单相关接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";

export class dynamicFormService {
  private static saveFormTemplateApi: string = "/dynamicForm/SaveFormTemplate";
  private static getFormTemplateByRecordIDApi: string = "/dynamicForm/GetFormTemplateByRecordID";

  // 保存表单模板
  public static saveFormTemplate(params: any) {
    return http.post(this.saveFormTemplateApi, params, { loadingText: Loading.SAVE });
  }
  // 根据表单ID获取表单模板
  public static getFormTemplateByRecordID(params: any) {
    return http.get(this.getFormTemplateByRecordIDApi, params, { loadingText: Loading.LOAD });
  }
}
