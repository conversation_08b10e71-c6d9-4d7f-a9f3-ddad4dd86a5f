{"version": 3, "sources": ["../../element-plus/dist/locale/en.js"], "sourcesContent": ["/*! Element Plus v2.6.1 */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleEn = factory());\n})(this, (function () { 'use strict';\n\n  var en = {\n    name: \"en\",\n    el: {\n      colorpicker: {\n        confirm: \"OK\",\n        clear: \"Clear\",\n        defaultLabel: \"color picker\",\n        description: \"current color is {color}. press enter to select a new color.\"\n      },\n      datepicker: {\n        now: \"Now\",\n        today: \"Today\",\n        cancel: \"Cancel\",\n        clear: \"Clear\",\n        confirm: \"OK\",\n        dateTablePrompt: \"Use the arrow keys and enter to select the day of the month\",\n        monthTablePrompt: \"Use the arrow keys and enter to select the month\",\n        yearTablePrompt: \"Use the arrow keys and enter to select the year\",\n        selectedDate: \"Selected date\",\n        selectDate: \"Select date\",\n        selectTime: \"Select time\",\n        startDate: \"Start Date\",\n        startTime: \"Start Time\",\n        endDate: \"End Date\",\n        endTime: \"End Time\",\n        prevYear: \"Previous Year\",\n        nextYear: \"Next Year\",\n        prevMonth: \"Previous Month\",\n        nextMonth: \"Next Month\",\n        year: \"\",\n        month1: \"January\",\n        month2: \"February\",\n        month3: \"March\",\n        month4: \"April\",\n        month5: \"May\",\n        month6: \"June\",\n        month7: \"July\",\n        month8: \"August\",\n        month9: \"September\",\n        month10: \"October\",\n        month11: \"November\",\n        month12: \"December\",\n        week: \"week\",\n        weeks: {\n          sun: \"Sun\",\n          mon: \"Mon\",\n          tue: \"Tue\",\n          wed: \"Wed\",\n          thu: \"Thu\",\n          fri: \"Fri\",\n          sat: \"Sat\"\n        },\n        weeksFull: {\n          sun: \"Sunday\",\n          mon: \"Monday\",\n          tue: \"Tuesday\",\n          wed: \"Wednesday\",\n          thu: \"Thursday\",\n          fri: \"Friday\",\n          sat: \"Saturday\"\n        },\n        months: {\n          jan: \"Jan\",\n          feb: \"Feb\",\n          mar: \"Mar\",\n          apr: \"Apr\",\n          may: \"May\",\n          jun: \"Jun\",\n          jul: \"Jul\",\n          aug: \"Aug\",\n          sep: \"Sep\",\n          oct: \"Oct\",\n          nov: \"Nov\",\n          dec: \"Dec\"\n        }\n      },\n      inputNumber: {\n        decrease: \"decrease number\",\n        increase: \"increase number\"\n      },\n      select: {\n        loading: \"Loading\",\n        noMatch: \"No matching data\",\n        noData: \"No data\",\n        placeholder: \"Select\"\n      },\n      dropdown: {\n        toggleDropdown: \"Toggle Dropdown\"\n      },\n      cascader: {\n        noMatch: \"No matching data\",\n        loading: \"Loading\",\n        placeholder: \"Select\",\n        noData: \"No data\"\n      },\n      pagination: {\n        goto: \"Go to\",\n        pagesize: \"/page\",\n        total: \"Total {total}\",\n        pageClassifier: \"\",\n        page: \"Page\",\n        prev: \"Go to previous page\",\n        next: \"Go to next page\",\n        currentPage: \"page {pager}\",\n        prevPages: \"Previous {pager} pages\",\n        nextPages: \"Next {pager} pages\",\n        deprecationWarning: \"Deprecated usages detected, please refer to the el-pagination documentation for more details\"\n      },\n      dialog: {\n        close: \"Close this dialog\"\n      },\n      drawer: {\n        close: \"Close this dialog\"\n      },\n      messagebox: {\n        title: \"Message\",\n        confirm: \"OK\",\n        cancel: \"Cancel\",\n        error: \"Illegal input\",\n        close: \"Close this dialog\"\n      },\n      upload: {\n        deleteTip: \"press delete to remove\",\n        delete: \"Delete\",\n        preview: \"Preview\",\n        continue: \"Continue\"\n      },\n      slider: {\n        defaultLabel: \"slider between {min} and {max}\",\n        defaultRangeStartLabel: \"pick start value\",\n        defaultRangeEndLabel: \"pick end value\"\n      },\n      table: {\n        emptyText: \"No Data\",\n        confirmFilter: \"Confirm\",\n        resetFilter: \"Reset\",\n        clearFilter: \"All\",\n        sumText: \"Sum\"\n      },\n      tour: {\n        next: \"Next\",\n        previous: \"Previous\",\n        finish: \"Finish\"\n      },\n      tree: {\n        emptyText: \"No Data\"\n      },\n      transfer: {\n        noMatch: \"No matching data\",\n        noData: \"No data\",\n        titles: [\"List 1\", \"List 2\"],\n        filterPlaceholder: \"Enter keyword\",\n        noCheckedFormat: \"{total} items\",\n        hasCheckedFormat: \"{checked}/{total} checked\"\n      },\n      image: {\n        error: \"FAILED\"\n      },\n      pageHeader: {\n        title: \"Back\"\n      },\n      popconfirm: {\n        confirmButtonText: \"Yes\",\n        cancelButtonText: \"No\"\n      },\n      carousel: {\n        leftArrow: \"Carousel arrow left\",\n        rightArrow: \"Carousel arrow right\",\n        indicator: \"Carousel switch to index {index}\"\n      }\n    }\n  };\n\n  return en;\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,KAAC,SAAU,QAAQ,SAAS;AAC1B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,OAAO,UAAU,QAAQ,IACxF,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,OAAO,KAC1D,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,OAAO,sBAAsB,QAAQ;AAAA,IAClH,GAAG,SAAO,WAAY;AAAE;AAEtB,UAAI,KAAK;AAAA,QACP,MAAM;AAAA,QACN,IAAI;AAAA,UACF,aAAa;AAAA,YACX,SAAS;AAAA,YACT,OAAO;AAAA,YACP,cAAc;AAAA,YACd,aAAa;AAAA,UACf;AAAA,UACA,YAAY;AAAA,YACV,KAAK;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,SAAS;AAAA,YACT,iBAAiB;AAAA,YACjB,kBAAkB;AAAA,YAClB,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,SAAS;AAAA,YACT,UAAU;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,YACX,WAAW;AAAA,YACX,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,MAAM;AAAA,YACN,OAAO;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,YACA,WAAW;AAAA,cACT,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,YACA,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,aAAa;AAAA,UACf;AAAA,UACA,UAAU;AAAA,YACR,gBAAgB;AAAA,UAClB;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,SAAS;AAAA,YACT,aAAa;AAAA,YACb,QAAQ;AAAA,UACV;AAAA,UACA,YAAY;AAAA,YACV,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,YACX,oBAAoB;AAAA,UACtB;AAAA,UACA,QAAQ;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,UAAU;AAAA,UACZ;AAAA,UACA,QAAQ;AAAA,YACN,cAAc;AAAA,YACd,wBAAwB;AAAA,YACxB,sBAAsB;AAAA,UACxB;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,YACX,eAAe;AAAA,YACf,aAAa;AAAA,YACb,aAAa;AAAA,YACb,SAAS;AAAA,UACX;AAAA,UACA,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,UACA,MAAM;AAAA,YACJ,WAAW;AAAA,UACb;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ,CAAC,UAAU,QAAQ;AAAA,YAC3B,mBAAmB;AAAA,YACnB,iBAAiB;AAAA,YACjB,kBAAkB;AAAA,UACpB;AAAA,UACA,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,mBAAmB;AAAA,YACnB,kBAAkB;AAAA,UACpB;AAAA,UACA,UAAU;AAAA,YACR,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IAET,CAAE;AAAA;AAAA;", "names": []}