/*
 * FilePath     : \src\stores\modules\common.ts
 * Author       : 苏军志
 * Date         : 2023-06-05 10:52
 * LastEditors  : 苏军志
 * LastEditTime : 2023-11-14 18:45
 * Description  : 系统公共状态管理
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";

const useCommonStore = defineStore({
  id: "common",
  state: () => {
    return {
      language: { languageCode: "zh", language: 1, languageName: "简体中文" } as Record<string, any>,
      languageList: [] as Record<string, any>[],
      hospital: {} as Record<string, any>
    };
  },
  // 数据持久化
  persist: {
    enabled: true,
    // 指定字段存储，并且指定存储方式：
    strategies: [{ storage: localStorage }]
  }
});

export default useCommonStore;
