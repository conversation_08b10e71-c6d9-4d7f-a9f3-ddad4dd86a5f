<!--
 * FilePath     : \src\views\home\index.vue
 * Author       : 苏军志
 * Date         : 2023-06-17 17:03
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-26 09:48
 * Description  : 系统首页
 * CodeIterationRecord:
-->
<template>
  <div class="home">
    <div class="scheduling-wrap">
      <div class="header">
        <span class="show-date"> {{ showDate }}</span>
        <span> {{ shiftSchedulingData["remark"] }}</span>
      </div>
      <el-calendar v-model="currentDate">
        <template #date-cell="{ data }">
          <div v-set-parent-color="dateAndBgColor.get(data.day)">
            {{ data.day.split("-").slice(1).join("-") }}
          </div>
          <div v-if="shiftSchedulingData[data.day]" class="scheduling-data" v-html="dateAndContent.get(data.day)"></div>
        </template>
      </el-calendar>
    </div>
    <div class="list-wrap">
      <el-card class="message-list">
        <template #header>
          <div class="card-header">
            <span>最新消息</span>
            <el-tooltip content="查看更多" placement="top">
              <i class="iconfont icon-show-more" @click="goMorePage(1)"></i>
            </el-tooltip>
          </div>
        </template>
        <div class="message-item-list" v-if="topMessageList?.length > 0">
          <div class="message-item-wrap" v-for="(message, index) in topMessageList" :key="index">
            <el-tooltip v-if="message.messageContent" :content="message.messageContent" raw-content>
              <div class="message-item" @click="showMessageContent(message)">
                <span :class="index < 3 ? `index-top${index + 1}` : ''">{{ index + 1 }}、</span>
                <span :class="[{ 'is-top': message.isTop }]">
                  <span>{{ message.messageTitle }}</span>
                  (<span class="time-suffix" v-formatTime="{ value: message.publishTime, type: 'date', format: 'yyyy-MM-dd' }" />)
                </span>
                <i v-if="message.isNew" class="badge-new">NEW</i>
              </div>
            </el-tooltip>
            <div v-else class="message-item" @click="showMessageContent(message)">
              <span :class="index < 3 ? `index-top${index + 1}` : ''">{{ index + 1 }}、</span>
              <span :class="[{ 'is-top': message.isTop }]">
                <span>{{ message.messageTitle }}</span>
                (<span class="time-suffix" v-formatTime="{ value: message.publishTime, type: 'date', format: 'yyyy-MM-dd' }" />)
              </span>
              <i v-if="message.isNew" class="badge-new">NEW</i>
            </div>
          </div>
        </div>
        <el-empty v-else description="暂无消息" />
      </el-card>
      <el-card class="train-list">
        <template #header>
          <div class="card-header">
            <span>我的课程</span>
            <el-tooltip content="查看更多" placement="top">
              <i class="iconfont icon-show-more" @click="goMorePage(2)"></i>
            </el-tooltip>
          </div>
        </template>
        <el-empty description="暂无课程" />
      </el-card>
      <el-card class="to-do-list">
        <template #header>
          <div class="card-header">
            <span>我的待办</span>
          </div>
        </template>
        <el-empty description="暂无待办" v-if="!toDoList || toDoList.length <= 0" />
        <el-scrollbar v-else>
          <ul v-for="(todoItem, ulIndex) in toDoList" :key="ulIndex">
            <li class="list-item one-level">
              <a href="javascript:;" @click="jumpToPage(todoItem.routerPath, undefined)">
                <span class="list-item-index">{{ ulIndex + 1 }}</span
                >{{ todoItem.toDoContent }}
                <i class="tip">{{ "(" + todoItem.counts + ")" }}</i>
              </a>
            </li>
            <ul class="child-list" v-if="todoItem.toDoList?.length > 0">
              <li v-for="(todo, index) in todoItem.toDoList" :key="index">
                <a href="javascript:;" @click="jumpToPage(todoItem.routerPath, todo)">
                  <span class="list-item-index">{{ index + 1 }}</span>
                  <span>{{ (todo as any).title }}</span>
                  <!-- 待审批明细不显示时间 -->
                  <span
                    v-if="todo.interventionID"
                    class="tip"
                    v-formatTime="{ value: todo.scheduleDateTime, type: 'date', format: 'yyyy-MM-dd' }"
                  />
                </a>
              </li>
            </ul>
          </ul>
        </el-scrollbar>
      </el-card>
      <el-card class="favorites-list">
        <template #header>
          <div class="card-header">
            <span>我的计划</span>
            <span class="card-header-tip" v-if="planList.toDoList?.length > 0">共{{ planList.toDoList?.length }}条</span>
            <el-tooltip content="查看更多" placement="top">
              <i class="iconfont icon-show-more" @click="goMorePage(4)"></i>
            </el-tooltip>
          </div>
        </template>
        <el-empty description="暂无计划" v-if="planList.toDoList?.length <= 0" />
        <el-scrollbar v-else>
          <li v-for="(plan, index) in planList.toDoList" :key="index">
            <a href="javascript:;" @click="jumpToPage(planList.routerPath, plan)">
              <span class="list-item-index">{{ index + 1 }}</span>
              <span>{{ plan.title }}</span>
              <span class="tip" v-formatTime="{ value: plan.scheduleDateTime, type: 'date', format: 'yyyy-MM-dd' }" />
            </a>
          </li>
        </el-scrollbar>
      </el-card>
    </div>
    <message-preview
      v-model="showMessageFlag"
      :params="systemMessageNotice"
      :isPendingConfirmMessage="isPendingConfirmMessage"
    ></message-preview>
  </div>
</template>

<script setup lang="ts">
const { userStore } = useStore();
const router = useRouter();
import { useMessageConfirmationHandler } from "@/views/messageManagement/hooks/useMessageConfirmationHandler";
const { systemMessageNotice, isPendingConfirmMessage, showMessageFlag, getPendingConfirmationMessage, showMessageContent } =
  useMessageConfirmationHandler();
const restPostType = "4";
const currentDate = ref<Date>(new Date());
const shiftSchedulingData = ref<Record<string, any>>({});
const showDate = computed(() => datetimeUtil.formatDate(currentDate.value, "yyyy年MM月排班表"));
let noonList: Record<string, string>[] = [];
let startDate = computed(() => datetimeUtil.getMonthFirstDay(`${currentDate.value}-01`));
let endDate = computed(() => datetimeUtil.getMonthLastDay(`${currentDate.value}-01`));
const dateAndContent = ref(new Map<string, string>());
const dateAndBgColor = new Map<string, string>();
const planList = ref<{ routerPath: string; toDoList: any[] }>({} as any);
const toDoList = ref<any>([]);
onBeforeMount(() => {
  getNoonList();
  // 获取个人排班数据
  getShiftSchedulingByEmployeeID();
  // 获取计划列表
  getPlanScheduleList();
  // 获取待办列表
  getToDoList();
  // 获取待确认的系统通知
  getPendingConfirmationMessage();
  if (systemMessageNotice.value) {
    isPendingConfirmMessage.value = true;
  }
  // 获取首页消息列表
  getHomeMessageList();
});
/*
 * @description: 设置父元素背景色
 */
const vSetParentColor = {
  updated(el: any, binding: any) {
    binding.value && el.parentNode.style.setProperty("background-color", binding.value);
  }
};
/**
 * @description: 获取日历单元格显示内容和背景色
 * @return 显示内容
 */
const createDateContentAndBgColor = () => {
  Object.entries(shiftSchedulingData.value).forEach(([date, data]) => {
    let content = "";
    if (!data.noonPost) {
      return;
    }
    noonList.forEach((noon) => {
      const post = data.noonPost[noon.value];
      if (post) {
        let postName = `<span style="color:${post.color ? post.color : "#000000"}"">${post.departmentPostShortName}</span>`;
        if (post.postType === restPostType) {
          postName = `<span style="color:${post.schedulingRequestFlag ? "#ff0000" : "#ff00ff"}">${post.departmentPostShortName}</span>`;
        }
        if (content !== postName) {
          if (content) {
            content += `${content === "/" ? "" : "/"}${postName}`;
          } else {
            content = postName;
          }
        }
      } else {
        content += "/";
      }
    });
    // 岗位背景色：上午岗位岗位背景色有配置，且不是默认背景色，使用上午岗位背景色配置，否则使用下午岗位背景色配置或者默认背景色
    let postBackGroundColor = data.noonPost?.["1"]?.backGroundColor;
    if (!postBackGroundColor || postBackGroundColor === "#FFFFFF") {
      postBackGroundColor = data.noonPost?.["2"]?.backGroundColor || "#FFFFFF";
    }
    dateAndBgColor.set(date, postBackGroundColor);
    // 循环添加标记
    if (data.markList?.length) {
      // 按照sort升序排序
      sortByKeys(data.markList, ["sort"]);
      dateAndBgColor.set(date, data.markList[0].backGroundColor);
      let markContent = "";
      data.markList.forEach((mark: Record<string, any>) => {
        if (mark.icon) {
          markContent += `<span style="color:${mark.color || "#ff0000"}">${mark.icon}</span>`;
        }
      });
      if (markContent) {
        content += markContent;
      }
    }
    dateAndContent.value.set(date, content);
  });
};
/**
 * @description: 获取个人排班数据
 */
const getShiftSchedulingByEmployeeID = () => {
  const params = {
    departmentID: userStore.departmentID,
    startDate: startDate.value,
    endDate: endDate.value,
    employeeID: userStore.employeeID
  };
  schedulingService.getShiftSchedulingByEmployeeID(params).then((data: any) => {
    if (data) {
      shiftSchedulingData.value = data;
      createDateContentAndBgColor();
    }
  });
};
/**
 * @description: 获取午别字典
 */
const getNoonList = () => {
  // 通过hooks从数据库获取数据
  const params: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "JobPositions",
    settingTypeValue: "NoonType",
    index: Math.random()
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    noonList = datas;
  });
};
/**
 * @description: 获取计划列表
 * @returns
 */
const getPlanScheduleList = () => {
  const params = {
    departmentID: userStore.departmentID,
    scheduleMonth: parseInt(datetimeUtil.getNow("MM")),
    preOrNextFlag: false
  };
  homeService.getUnExecAnnualSchedule(params).then((data: any) => {
    let tempData = data || [];
    planList.value = tempData;
  });
};
/**
 * @description: 获取待办列表
 * @returns
 */
const getToDoList = () => {
  homeService.getToDoList().then((data: any) => {
    toDoList.value = data || [];
  });
};
/**
 * @description:跳转到其他功能页面
 * @param routerPath 跳转路径
 * @param item 点击的通知信息对象
 * @returns
 */
const jumpToPage = (routerPath: string, item: any) => {
  // 设置路由跳转参数
  if (!routerPath) {
    showMessage("warning", "无法跳转到该功能页面");
    return;
  }
  // 跳转带参数的动态路由页面
  let splitPath = routerPath.split("?");
  let path = splitPath[0];
  if (splitPath.length > 1 && splitPath[1].split("=").length > 1) {
    path += `/${splitPath[1].split("=")[1]}`;
  }
  router.push({
    path: path,
    query: {
      month: item?.scheduleDateTime?.split("-")?.[1],
      proveCategory: item?.proveCategory
    }
  });
};
const topMessageList = ref<Record<string, any>[]>([]);
/**
 * @description: 获取首页消息列表
 */
const getHomeMessageList = () => {
  homeService.getHomeMessageList().then((data: any) => {
    topMessageList.value = data || [];
  });
};
// 路由map
const routerMap: Record<number, string> = {
  1: "/messageManagement",
  2: "/trainingLearner",
  4: "/annualSchedule"
};
/**
 * @description: 跳转更过页面
 * @param index
 * @return
 */
const goMorePage = (index: number) => {
  router.push({
    path: routerMap[index]
  });
};
</script>

<style lang="scss">
.home {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  .scheduling-wrap {
    margin: 10px;
    .header {
      text-align: center;
      font-size: 24px;
      color: #ff0000;
      font-weight: bold;
      .show-date {
        margin: 0 50px 0 -50px;
      }
    }
    .el-calendar__header {
      display: none;
    }
    .el-calendar__body {
      padding: 0;
      .el-calendar-table {
        td {
          pointer-events: none;
        }
      }
      .is-today {
        .el-calendar-day {
          font-weight: bold;
          @include select-style();
        }
      }
      .el-calendar-day {
        text-align: center;
        &:hover {
          color: #000000;
        }
        .date {
          margin-bottom: 10px;
        }
        .scheduling-data {
          font-weight: bold;
        }
      }
    }
  }
  .list-wrap {
    height: 100%;
    flex: auto;
    display: flex;
    align-items: center;
    overflow-y: hidden;
    .el-card {
      height: 95%;
      flex: 1;
      margin: 10px;
      .el-card__header {
        padding: 10px;
        background-color: $base-color;
        color: #ffffff;
        font-weight: bold;
        .icon-show-more {
          float: right;
          color: #ffffff;
          cursor: pointer;
        }
      }
      .el-card__body {
        box-sizing: content-box;
        padding: 2px 5px;
        // 1.不设置高度无法显示滚动条
        // 2.100%中包含了header中的高度，导致内容显示不全，
        height: calc(90% - 10px);
        ul,
        li {
          list-style: none;
          padding-left: 0;
          margin-top: 5px;
          margin-bottom: 5px;
          position: relative;
        }
        li {
          a {
            color: #0e0e0e;
            text-decoration: none;
            &:not(.list-item-index):hover {
              text-decoration: underline;
            }

            .tip {
              color: #ff0000;
              margin: 0 5px;
              font-size: 80%;
              font-style: normal;
              white-space: nowrap;
            }
          }
        }
        .list-item-index {
          display: inline-block;
          position: relative;
          margin-right: 4px;
          text-align: center;
          height: 100%;
          &:after {
            content: "、";
          }
        }
      }
    }
    .one-level {
      background: #e4e5ea;
      font-weight: bold;
      padding: 4px 0;
    }
    .card-header-tip {
      margin-left: 10px;
      color: #ff0000;
      font-weight: normal;
    }
    .child-list {
      margin-left: 20px;
    }
    .message-list {
      height: 95%;
      .message-item-list {
        overflow-x: hidden;
        .message-item-wrap {
          display: flex;
          .message-item {
            margin-bottom: 5px;
            cursor: pointer;
            &:hover {
              text-decoration: underline;
            }
            .index-top1 {
              color: #fe2d46;
              font-weight: bold;
            }
            .index-top2 {
              color: #f60;
              font-weight: bold;
            }
            .index-top3 {
              color: #faa90e;
              font-weight: bold;
            }
            .is-top {
              font-weight: bold;
            }
            .time-suffix {
              font-size: 80%;
              padding-left: 5px;
              // 保证不换行 解决flex下空间不足时被换行
              white-space: nowrap;
            }
            .badge-new {
              display: inline-block;
              margin-top: -2px;
              margin-bottom: 2px;
              padding: 1px 8px 1px 4px;
              font-weight: bold;
              font-size: 80%;
              border-radius: 6px;
              background-color: #ff0000;
              color: #ffffff;
              scale: 0.8;
            }
          }
        }
      }
    }
  }
}
</style>
