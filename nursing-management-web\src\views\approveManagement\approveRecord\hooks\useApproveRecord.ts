/*
 * FilePath     : \nursing-management-web\src\views\approveManagement\approveRecord\hooks\useApproveRecord.ts
 * Author       : 张现忠
 * Date         : 2023-10-23 17:10
 * LastEditors  : 张现忠
 * LastEditTime : 2024-11-02 09:30
 * Description  :  业务审批与后端交互的相关api请求
 * CodeIterationRecord:
 */

export function useApproveRecord() {
  return {
    /**
     * @description: 获取审核信息-页面呈现使用
     * @param recordID:审批主记录
     * @return
     */
    async getApproveDetailView(recordID: string) {
      // 发起网络请求获取后端节点数据
      return await approveRecordService
        .getApproveDetailView({
          approveMainRecordID: recordID
        })
        .then((result: any) => {
          return result;
        });
    },
    /**
     * @description: 获取待审批记录信息
     * @param proveCategory: 审批类别码
     * @param startDate: 开始日期
     * @param endDate: 结束日期
     * @return
     */
    async getApproveRecordView(proveCategory: string, startDate?: string, endDate?: string) {
      return await approveRecordService
        .getApproveRecordView({ proveCategory: proveCategory, startDate: startDate, endDate: endDate })
        .then((result: any) => {
          return result;
        });
    },
    /**
     * @description: 已审批完成的记录信息
     * @param proveCategory: 审批类别码
     * @param startDate: 开始日期
     * @param endDate: 结束日期
     * @return {} 历史审批数据
     */
    async getHistoryApproveRecordView(proveCategory: string, startDate: string, endDate: string) {
      return await approveRecordService
        .getHistoryApproveRecordView({ proveCategory: proveCategory, startDate: startDate, endDate: endDate })
        .then((result: any) => {
          return result;
        });
    },
    /**
     * @description: 保存审批
     * @return
     */
    async saveApproval(param: any) {
      let params = toRaw(param);
      return await approveRecordService.saveApproval(params).then((result: any) => {
        return result;
      });
    },
    /**
     * @description: 获取审批类型及对应数量
     * @param isCompleteFlag: 是否已完成审批
     * @param startDate: 开始日期
     * @param endDate: 结束日期
     * @return
     */
    async getApproveCategoryAndCount(isCompleteFlag: boolean, startDate?: string, endDate?: string) {
      // 查询待审批记录数量的时候，没有时间范围。获取全部待审批的数量
      let params = !isCompleteFlag
        ? { isCompleteFlag: isCompleteFlag }
        : { isCompleteFlag: isCompleteFlag, startDate: startDate, endDate: endDate };
      return await approveRecordService.getApproveCategoryAndCount(params).then((result: any) => {
        return result;
      });
    }
  };
}
