/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\onJobInformation\hooks\useFormatData.ts
 * Author       : 张现忠
 * Date         : 2023-09-20 19:19
 * LastEditors  : 马超
 * LastEditTime : 2025-01-14 19:23
 * Description  :
 * CodeIterationRecord:
 */
import type { jobFormat } from "../types/jobFormat";

export function useFormatData() {
  const jobFormatData: jobFormat[] = [
    {
      title: "在职记录",
      key: "employeeStaffData",
      tabsFlag: false,
      props: [],
      prop: [
        {
          label: "档案编号",
          field: "fileID",
          type: "T",
          required: true
        },
        {
          label: "HRP编号",
          field: "hrpEmployeeID",
          type: "T",
          required: true
        },
        {
          label: "工号",
          field: "employeeID",
          type: "T",
          required: true
        },
        {
          label: "HIS编号",
          field: "hisEmployeeID",
          type: "T",
          required: true
        },
        {
          label: "工作性质",
          field: "jobCategoryCode",
          type: undefined,
          required: true,
          alias: "jobCategory"
        },
        {
          label: "入职日期",
          field: "entryDate",
          type: "D",
          required: true
        },
        {
          label: "试用期(月)",
          field: "probation",
          type: "T",
          required: false
        },
        {
          label: "转正日期",
          field: "turnRegularDate",
          type: "D",
          required: true
        },
        {
          label: "部门",
          field: "departmentID",
          type: "dept",
          required: true,
          alias: "departmentName"
        },
        {
          label: "岗位",
          field: "post",
          type: "Post",
          required: true,
          alias: "postType"
        },
        {
          label: "职务",
          field: "title",
          type: "Title",
          required: true,
          alias: "title"
        },
        {
          label: "级别",
          field: "level",
          alias: "employeeLevelName",
          type: "capLevel",
          required: true
        }
      ]
    },
    // 任职记录
    {
      title: "任职记录",
      key: "employeeEmploymentRecord",
      tabsFlag: true,
      props: [
        {
          title: "正式任职",
          key: "formalPosition",
          tabsFlag: false,
          props: [],
          prop: [
            {
              label: "任职开始日期",
              field: "startDate",
              type: "SD",
              required: true
            },
            {
              label: "任职结束日期",
              field: "endDate",
              type: "ED",
              required: true
            },
            {
              label: "任职部门",
              field: "departmentID",
              type: "dept",
              required: true,
              alias: "departmentName"
            },
            {
              label: "任职职务",
              field: "title",
              type: "titleSelector",
              required: true,
              alias: "titleName"
            },
            {
              label: "任职岗位",
              field: "post",
              type: "Post",
              required: true,
              alias: "postName"
            }
          ]
        },
        {
          title: "兼职任职",
          key: "partTimePosition",
          tabsFlag: false,
          props: [],
          prop: [
            {
              label: "任职开始日期",
              field: "startDate",
              type: "SD",
              required: true
            },
            {
              label: "任职结束日期",
              field: "endDate",
              type: "ED",
              required: true
            },
            {
              label: "任职岗位",
              field: "post",
              type: "Post",
              required: true,
              alias: "postName"
            },
            {
              label: "任职职务",
              field: "title",
              type: "partTimeSelector",
              required: true,
              alias: "titleName"
            }
          ]
        },
        {
          title: "社会任职",
          key: "socialPosition",
          tabsFlag: false,
          props: [],
          prop: [
            {
              label: "任职开始日期",
              field: "startDate",
              type: "SD",
              required: true
            },
            {
              label: "任职结束日期",
              field: "endDate",
              type: "ED",
              required: true
            },
            {
              label: "任职职务",
              field: "title",
              type: "text",
              required: true,
              alias: "titleName"
            }
          ]
        }
      ],
      prop: []
    },
    // 奖惩记录表
    {
      title: "奖惩记录",
      key: "employeeReward",
      tabsFlag: false,
      props: [],
      prop: [
        {
          label: "奖励/惩罚",
          field: "rewardLevelCode",
          type: "rewardLevel",
          required: true,
          alias: "rewardLevelName"
        },
        {
          label: "奖惩类别",
          field: "rewardTypeCode",
          type: "rewardType",
          required: true,
          alias: "rewardTypeName"
        },
        {
          label: "奖惩日期",
          field: "rewardDate",
          type: "D",
          required: true
        },
        {
          label: "奖惩内容",
          field: "rewardContent",
          type: "T",
          required: true
        }
      ]
    },
    {
      title: "岗位层级",
      key: "employeeCapabilityLevel",
      tabsFlag: false,
      props: [],
      prop: [
        {
          label: "护理层级",
          field: "capabilityLevelID",
          type: "capLevel",
          required: true,
          alias: "capabilityLevel"
        },
        {
          label: "晋级时间",
          field: "promotionDate",
          type: "D",
          required: true
        }
      ]
    }
  ];
  return {
    /**
     * 表格格式内容
     */
    jobFormatData
  };
}
