/*
 * FilePath     : \src\types\settingDictionaryParams.ts
 * Author       : 来江禹
 * Date         : 2024-07-16 09:50
 * LastEditors  : 来江禹
 * LastEditTime : 2024-07-16 17:43
 * Description  :获取SettingDictionary字典参数
 * CodeIterationRecord:
 */

/* eslint-disable */
/**
 * 字典参数
 */
declare interface SettingDictionaryParams {
  /**
   *  类别值(一阶)
   */
  settingType: String;
  /**
   * 类别值(二阶)
   */
  settingTypeCode?: String;
  /**
   * 类别值(三阶)
   */
  settingTypeValue?: String;
  /**
   * settingValue值
   */
  settingValue?: String;
  /**
   * 排除类别值(三阶)
   */
  filterSettingTypeValue?: String;
  /**
   * 随机数，防止 请求被拦截
   */
  index?: number;
}
