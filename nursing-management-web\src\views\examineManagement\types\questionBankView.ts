/*
 * FilePath     : \nursing-management-web\src\views\examineManagement\types\questionBankView.ts
 * Author       : 来江禹
 * Date         : 2024-04-23 16:15
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-04 17:29
 * Description  : 考核题库数据结构定义
 * CodeIterationRecord:
 */
export interface questionBankView {
  /**
   * 考核题库ID
   */
  questionBankID: string;
  /**
   * 考核题库名称
   */
  content: string;
  /**
   * 分类名称
   */
  questionBankTypeName: string;
  /**
   * 分类
   */
  questionBankType: string;
  /**
   * 来源类别
   */
  sourceType?: string;
  /**
   * 来源ID
   */
  sourceID?: string;
  /**
   * 培训课程
   */
  trainingCourse?: string;
  /**
   * 年份(版本号)
   */
  year?: number;
  /**
   * 实操类考核标记
   */
  isPractical: boolean;
  /**
   * 修改人工号
   */
  modifyEmployeeID?: string;
  /**
   * 修改人
   */
  modifyEmployee?: string;
  /**
   * 修改时间
   */
  modifyDateTime?: Date;
  /**
   * 医院序号
   */
  hospitalID: string;
  /**
   * 题库层级
   */
  organizationalDepartmentCode: string;
  /**
   * 组织部门名称
   */
  organizationalDepartment?: string;
  /**
   * 父级组织部门ID
   */
  parentID?: string;
  /**
   * 组织部门ID
   */
  departmentID?: number;
  /**
   * 子题库
   */
  children?: questionBankView[];
  /**
   * 排序
   */
  sort: number;
}
