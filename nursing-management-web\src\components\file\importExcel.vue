<!--
 * FilePath     : \src\components\file\importExcel.vue
 * Author       : 郭鹏超
 * Date         : 2023-10-17 14:56
 * LastEditors  : 张现忠
 * LastEditTime : 2025-04-04 15:22
 * Description  : excel文件数据导入table
 * CodeIterationRecord:
-->

<template>
  <el-upload
    class="import-excel"
    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    :auto-upload="false"
    :show-file-list="false"
    :on-change="uploadChange"
  >
    <el-button type="primary" class="import-button">{{ importExcelOption?.buttonName ?? "Excel文件数据导入" }}</el-button>
  </el-upload>
</template>

<script lang="ts" setup>
import * as XLSX from "xlsx";
import { useExcel } from "@/hooks/useExcel";
const { fixPrecisionLoss, readExcelFile } = useExcel();
const emits = defineEmits(["getExcelData"]);
const props = defineProps({
  importExcelOption: {
    type: Object as () => ImportExcelView,
    default: () => {
      return {};
    }
  }
});
/**
 * description: 上传文件解析
 * param {*} file
 * return {*}
 */
const uploadChange = async (file: any) => {
  if (!Object.keys(props.importExcelOption.columnData)?.length) {
    showMessage("error", "导入Excel数据失败！");
    return;
  }
  const files = file.raw;
  if (files.length <= 0) {
    return false;
  } else if (!/\.(xls|xlsx)$/.test(files.name.toLowerCase())) {
    showMessage("error", "上传文件只能是 .xlsx 或 .xls 格式!");
    return false;
  }
  if (props.importExcelOption.parseExcelFunc) {
    if (!props.importExcelOption?.sheetName || !props.importExcelOption?.firstFixedColumnName) {
      throw new Error("parseExcelFunc必须包含sheetName和firstFixedColumnName参数");
    }
    const result = await props.importExcelOption.parseExcelFunc(
      files,
      props.importExcelOption.sheetName,
      props.importExcelOption.firstFixedColumnName
    );
    emits("getExcelData", result);
    return;
  }
  // 读取表格
  let workbook = await readExcelFile(files);
  // 获取需要的sheet
  const wsName = props.importExcelOption.sheetName ?? workbook.SheetNames[0];
  const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsName]);
  emits("getExcelData", dealExcel(ws));
};
/**
 * description: 表格数据返回数组对象
 * param {*} ws
 * return {*}
 */
const dealExcel = (ws: any) => {
  let arr: { [key: string]: any }[] = [];
  const keyValues = Object.entries(props.importExcelOption.columnData);
  ws.forEach((sourceObj: { [key: string]: any }) => {
    const keys = Object.keys(sourceObj);
    const item: { [key: string]: any } = {};
    keyValues.forEach((arr) => {
      if (keys.includes(arr[1])) {
        let value = sourceObj[arr[1]];
        // 导入的数据只有日期时间类型的才是object，特殊处理
        if (typeof value == "object") {
          value = fixPrecisionLoss(value);
        }
        item[arr[0]] = value;
      }
    });
    arr = [...arr, item];
  });
  return arr;
};
</script>

<style>
.import-excel {
  display: inline-block;
}
</style>
