{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "externalVariable.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts", "src/components/univerSheet/template/*"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "moduleResolution": "node", "resolveJsonModule": true, "noImplicitAny": true, "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}