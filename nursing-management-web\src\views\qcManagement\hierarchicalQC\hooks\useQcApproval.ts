/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useQcApproval.ts
 * Author       : 张现忠
 * Date         : 2023-12-10 09:34
 * LastEditors  : 马超
 * LastEditTime : 2025-06-23 10:04
 * Description  : 质控审批相关api
 * CodeIterationRecord:
 */

import type { mainRow, recordRow } from "../types/hierarchicalQCResultView";
import { useUtils } from "../../../../hooks/useUtils";
const { showAlert } = useUtils();
export const useQcApproval = () => {
  const { userStore } = useStore();
  const getContent = (row: mainRow, record: recordRow) => {
    return `被考核人${row.examineEmployee} 于${datetimeUtil.formatDate(row.examineDate, "yyyy-MM-dd")}完成${
      record.formName
    } 项目考核，得分${row.point}`;
  };
  return {
    /**
     * @description 提交审批
     * @param row 质控维护记录
     */
    async submitForApproval(row: mainRow, record: recordRow) {
      if (!record.verifierEmployeeID) {
        showMessage("warning", "质控没有指定审核人员");
        return;
      }
      let param = {
        sourceID: row.hierarchicalQCMainID,
        departmentID: userStore.departmentID,
        addEmployeeID: userStore.employeeID,
        // 质控审批 查看配置：select * from AdministrationDictionary where SettingTypeCode='ProveCategory'
        proveCategory: "MG-073",
        content: getContent(row, record),
        selfSelectedApprover: record.verifierEmployeeID.split(",")
      };
      await hierarchicalQCService.submitForApproval(param).then((res: any) => {
        if (!res.recordSaveFlag) {
          return;
        }
        if (!res.approveSaveFlag) {
          showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
        }
      });
    },
    /**
     * @description 停止未开始的审批
     * @param row 质控维护记录
     */
    async stopApproval(row: mainRow) {
      let param = {
        hierarchicalMainID: row.hierarchicalQCMainID
      };
      await hierarchicalQCService.stopHierarchicalQCApproval(param).then((respData: any) => {
        // 提交审批之后返回结果
        if (respData) {
          showMessage("success", "停止审批成功");
        }
      });
    }
  };
};
