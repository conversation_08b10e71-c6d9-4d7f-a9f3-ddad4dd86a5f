<!--
 * FilePath     : \src\views\annualPlan\maintain\components\refIndicators.vue
 * Author       : 杨欣欣
 * Date         : 2023-10-08 14:28
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 15:31
 * Description  : 年度计划维护抽屉，指标字典区域，可拖拽到明细
 * CodeIterationRecord:
 -->
<template>
  <div class="ref-indicators">
    <div class="header">
      <div class="search">
        <label class="label">关键字：</label>
        <el-input clearable v-model.lazy="indicatorMatchValue">
          <template #append>
            <el-icon class="iconfont icon-search" />
          </template>
        </el-input>
      </div>
      <div class="filter">
        <el-radio-group v-model="level">
          <el-radio-button value="all">全部指标</el-radio-button>
          <el-radio-button value="upper">上级指标</el-radio-button>
          <el-radio-button value="current">本级指标</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <drag
      class="body"
      :list="showIndicators"
      :sortable="false"
      :draggableOption="getDraggableOption()"
      draggableViewClass="drag-item-view"
      :disabled="readOnly"
    >
      <template #content="{ element }">
        <tag contentAlign="left" :color="element.isNewIndicator ? '#f00f00' : '#000000'" :name="element.indicatorContent" />
      </template>
    </drag>
  </div>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanDictionaryStore } from "../hooks/useAnnualPlanDictionaryStore";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";

const { departmentID, readOnly } = storeToRefs(usePlanManagementStore());
const dictionaryStore = useAnnualPlanDictionaryStore();
const maintainStore = useAnnualPlanMaintainStore();
watch(
  departmentID,
  async () => {
    await dictionaryStore.setIndicatorList();
  },
  { immediate: true }
);

// 关注指标新增，若参数中指标字典ID为0，说明是会新增指标字典，在方法完成后需要补充到indicatorList
maintainStore.$onAction(({ name, after, args }) => {
  if (name === "addNewPlanIndicatorToPlanGroup") {
    const newIndicator = args[0];
    if (newIndicator.annualIndicatorID) {
      return;
    }
    after(() => {
      dictionaryStore.indicatorList.push({
        // 方法完成后，指标的字典ID已被补充
        annualIndicatorID: newIndicator.annualIndicatorID,
        indicatorContent: newIndicator.localShowName,
        level: "current",
        isNewIndicator: true
      });
    });
  }
});
//#region 筛选
const level = ref("all");
const indicatorMatchValue = ref<string>();
const showIndicators = computed(() => {
  let searchedIndicators = common.clone(dictionaryStore.noRefIndicatorList);
  if (level.value !== "all") {
    searchedIndicators = searchedIndicators.filter((indicator) => indicator.level === level.value);
  }
  // 关键字过滤，如果没有关键字，提前返回
  if (!indicatorMatchValue.value) {
    return searchedIndicators;
  }
  return searchedIndicators.filter((indicator) => indicator.indicatorContent.includes(indicatorMatchValue.value!));
});
//#endregion

const getDraggableOption = () => {
  return {
    itemKey: "index",
    groupName: "indicator",
    put: false,
    pull: "indicatorGroup",
    componentData: {
      "data-group-name": "indicatorList"
    }
  };
};
</script>
<style lang="scss">
.ref-indicators {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  // 筛选区
  .header {
    .search {
      margin: 20px 0 10px 5px;
      display: flex;
      align-items: center;
      .label {
        white-space: nowrap;
      }
    }
  }
  .body {
    margin-top: 16px;
    background-color: #f3f3f3;
    display: flex;
    height: 100%;
    flex-direction: column;
    overflow-y: auto;
  }
  .drag-item-view {
    .tag {
      cursor: move;
      caret-color: red;
      .el-tag__content:hover {
        font-weight: bold;
        color: $base-color;
      }
    }
  }
}
</style>
