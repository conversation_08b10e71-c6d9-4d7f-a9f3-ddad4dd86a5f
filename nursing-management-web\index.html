<!--
 * FilePath     : \index.html
 * Author       : 苏军志
 * Date         : 2023-06-04 08:42
 * LastEditors  : 苏军志
 * LastEditTime : 2024-01-29 16:43
 * Description  : 
 * CodeIterationRecord: 
-->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/static/images/logo.png" />
    <!-- 设置页面不缓存 -->
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <!-- 强制Chromium内核，作用于360浏览器、QQ浏览器等国产双核浏览器 -->
    <meta name="renderer" content="webkit" />
    <!-- 强制Chromium内核，作用于其他双核浏览器 -->
    <meta name="force-rendering" content="webkit" />
    <!-- 如果有安装 Google Chrome Frame 插件则强制为Chromium内核，否则强制本机支持的最高版本IE内核，作用于IE浏览器 -->
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="referrer" content="no-referrer" />
    <style>
      html,
      body,
      #app {
        height: 100%;
        width: 100%;
        margin: 0;
        padding: 0;
        user-select: none;
        overflow-y: hidden;
        overflow-x: auto;
        color: #000000;
        background-color: #f3f3f3;
        /* 设置字体 */
        font-family: "Microsoft Yahei", "Avenir", Helvetica, Arial, sans-serif, "Times New Roman";
        /* 设置文字抗锯齿 */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
    </style>
  </head>
  <body>
    <script type="module" src="/src/main.ts"></script>
    <script>
      this.globalThis || (this.globalThis = this);
    </script>
    <div id="app"></div>
  </body>
</html>
