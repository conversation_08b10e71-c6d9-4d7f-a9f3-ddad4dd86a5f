<!--
 * FilePath     : \src\views\transferExternal\transferStatistics.vue
 * Author       : 郭鹏超
 * Date         : 2024-03-07 08:22
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:10
 * Description  :统计
 * CodeIterationRecord:
-->
<template>
  <div class="transfer-statistics">
    <iframe v-if="url" :src="url" :key="iframeKey" scrolling="no" frameborder="0" width="100%" height="99%"></iframe>
  </div>
</template>

<script setup lang="ts">
const { userStore } = useStore();
const route = useRoute();
let url = ref<string>();
let typeCode = ref<string>();
let path = ref<string>();
let params = route.params;
onMounted(() => {
  defaultUrl();
});

import { isEqual } from "lodash-es";
const iframeKey = ref<number>(0);
watch(
  () => route.params,
  (val) => {
    if (Reflect.ownKeys(val).length && !isEqual(params, val)) {
      params = val;
      defaultUrl();

      iframeKey.value++;
    }
  },
  { immediate: true, deep: true }
);
/**
 * @description: 组装默认URL
 */
const defaultUrl = () => {
  if (!common.session("statisticsWebUrl")) {
    url.value = undefined;
    return;
  }
  typeCode.value = (params?.typeCode ?? "") as string;
  path.value = (params?.path ?? "") as string;
  if (!typeCode.value || !path.value) {
    return;
  }
  url.value = common.session("statisticsWebUrl") + path.value;
  url.value += packageUrl();
  url.value += "&system=NursingManagement";
};
/**
 * @description: 组装各页面URL
 */
const packageUrl = () => {
  switch (typeCode.value) {
    case "normalWorking":
      return getNormalWorkingProcessControlUrl();
    case "nodeQualityControl":
      return getNodeQualityControlUrl();
    default:
      return "";
  }
};
/**
 * description: 依据权限 获取统计查看数据维度
 * return {*}
 */
const getNormalWorkingProcessControlUrl = () => {
  const roles = (userStore.roles ?? []) as number[];
  let dimensionTypeApiID = 0;
  // 无权限 或者 护士权限给到本科室权限
  if (roles.some((role) => role < 60)) {
    dimensionTypeApiID = userStore.departmentID;
  }
  //  护理部 和管理员给到给到全院权限
  if (roles.some((role) => [70, 99].includes(role))) {
    dimensionTypeApiID = 0;
  }
  return "?typeCode=" + params?.typeCode + "&dimensionTypeApiID=" + dimensionTypeApiID;
};
/**
 * @description: 获取节点式督导统计URL
 */
const getNodeQualityControlUrl = () => {
  const qcFormLevel = (params?.qcFormLevel ?? "") as string;
  let url = "?typeCode=" + params?.typeCode + "&qcFormLevel=" + qcFormLevel;
  if (qcFormLevel === "1") {
    url += "&deparmentID=" + userStore.departmentID;
  }
  return url;
};
</script>

<style lang="scss">
.transfer-statistics {
  height: 100%;
}
</style>
