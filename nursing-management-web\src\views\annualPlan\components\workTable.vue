<!--
 * FilePath     : \src\views\annualPlan\components\workTable.vue
 * Author       : 杨欣欣
 * Date         : 2024-12-28 16:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 21:19
 * Description  : 季度工作呈现表格。纯呈现面，不作数据修改与后端请求
 * CodeIterationRecord:
 -->
<template>
  <div class="plan-work-table">
    <div class="qp-work-table-header">
      <div :id="`type-${typeAndWorks.typeID}`" class="type-name">{{ typeAndWorks.typeName }}</div>
      <el-button v-if="!annualPlanStore.readOnly" type="primary" class="add-button" @click="selectRow(addWork())">新增</el-button>
    </div>
    <div>
      <el-table
        class="work-table"
        ref="tableRef"
        :row-key="(row: planWork) => (row.monthlyPlanDetailID || row.quarterPlanDetailID) +'-' + row.sort"
        border
        stripe
        :data="typeAndWorks.children.filter((work) => showRoutineWorks || work.workType === workType.Key)"
        header-cell-class-name="header-cell"
        :row-class-name="rowClassName"
        @cell-click="handleCellClick"
        v-dragSort="{
          el: '.el-table__body-wrapper tbody',
          callBack: resetWorksSort,
          filter: '.routine-work',
          ghostClass: 'row-drag-ghost'
        }"
      >
        <el-table-column type="selection" :width="convertPX(32)" align="center" reserve-selection />
        <el-table-column v-if="sessionStore.debugMode" prop="sort" label="序号" :width="convertPX(55)" align="center" />
        <el-table-column v-if="sessionStore.debugMode" label="ID" :width="convertPX(55)" align="center">
          <template #default="{ row }">
            <div class="data-key">{{ isQuarter ? row.quarterPlanDetailID : row.monthlyPlanDetailID }}</div>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(240)">
          <template #header>
            参考分解目标任务
            <el-tooltip placement="top">
              <template #content>
                <div class="reference-intervention-help">
                  <p>来自年度计划制定</p>
                </div>
              </template>
              <i class="iconfont icon-info"></i>
            </el-tooltip>
          </template>
          <template #default="{ row }">
            {{ row.apInterventionLocalShowName }}
          </template>
        </el-table-column>
        <el-table-column label="工作重点">
          <template #default="{ row }">
            <el-input
              v-focus
              v-if="getEditState(row).isWorkContentEdit"
              type="textarea"
              v-model="row.workContent"
              @blur="handleInputBlur(row, () => (getEditState(row).isWorkContentEdit = false))"
            />
            <span v-if="!getEditState(row).isWorkContentEdit" data-prop="workContent">{{ row.workContent }}</span>
          </template>
        </el-table-column>
        <el-table-column label="完成要求">
          <template #default="{ row }">
            <el-input
              v-focus
              v-if="getEditState(row).isRequirementEdit"
              type="textarea"
              v-model="row.requirement"
              @blur="handleInputBlur(row, () => (getEditState(row).isRequirementEdit = false))"
            />
            <span v-if="!getEditState(row).isRequirementEdit" data-prop="requirement">{{ row.requirement }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工作类型" align="center" :width="convertPX(96)">
          <template #default="{ row }">
            <span v-if="annualPlanStore.readOnly">{{ row.workType === workType.Key ? "重点" : "常规" }}</span>
            <el-switch
              v-else
              v-model="row.workType"
              style="--el-switch-on-color: #ff7400"
              inline-prompt
              :active-value="1"
              active-text="重点"
              :inactive-value="2"
              inactive-text="常规"
              :disabled="isQuarter ? !row.quarterPlanDetailID : !row.monthlyPlanDetailID"
              @change="updateWork(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="负责人" align="center" :width="convertPX(160)">
          <template #default="{ row }">
            <el-link data-prop="principal" class="principal-name">{{ row.principalGroupName || row.principalName }}</el-link>
          </template>
        </el-table-column>
        <el-table-column v-if="!planStatus" label="操作" align="center" :width="convertPX(128)">
          <template #default="{ row }">
            <div v-if="!annualPlanStore.readOnly" class="operation-wrapper">
              <el-tooltip content="快捷引用" placement="top">
                <i class="iconfont icon-auto-fill" @click.stop="handleWorkQuoteEdit(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <i class="iconfont icon-delete" v-visibilityHidden="row.isTemp" @click.stop="deleteWork(row)" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
import type { planAndWorksVo } from "@/views/annualPlan/types/planAndWorksVo";
import { ElTable } from "element-plus";
import { workType } from "../types/common";

const { period } = defineProps<{
  period: "quarter" | "monthly";
  planStatus: boolean;
  showRoutineWorks: boolean;
}>();
const { sessionStore } = useStore();
const annualPlanStore = usePlanManagementStore();
const typeAndWorks = defineModel<planAndWorksVo>({ required: true });
const convertPX: any = inject<any>("convertPX");
type planWork = planAndWorksVo["children"][number];
interface emits {
  (e: "update", work: planWork): void;
  (e: "delete", key: string): void;
  (e: "editPrincipal", work: planWork, selectRow: (row: planWork) => Promise<void>): void;
  (e: "editWorkReference", work: planWork, selectRow: (row: planWork) => Promise<void>): void;
  (e: "resetSort", typeID: number, workIDAndSort: Record<string, number>, resolve: (success: boolean) => void): void;
}
const emit = defineEmits<emits>();
const isQuarter = computed(() => period === "quarter");

//#region 表格数据操作
/**
 * @description: 新增工作到列表
 */
const addWork = () => {
  const newWork: planWork = {
    quarterPlanDetailID: undefined,
    monthlyPlanDetailID: undefined,
    typeID: typeAndWorks.value.typeID,
    apInterventionLocalShowName: "",
    workType: workType.Key,
    isTemp: true,
    workContent: "",
    requirement: "",
    principals: [],
    principalName: "",
    planMonths: [],
    sort: typeAndWorks.value.children.filter((work) => work.workType === workType.Key).length + 1
  };
  typeAndWorks.value.children.unshift(newWork);
  return newWork;
};
/**
 * @description: 删除工作
 * @param row 当前行
 * @return
 */
const deleteWork = (row: planWork) => {
  if (!row.isTemp) {
    showMessage("warning", "非临时性工作不可删除！");
    return;
  }
  deleteConfirm("确定要删除工作吗？", async (flag: boolean) => {
    if (!flag) {
      return;
    }
    // 发起删除请求
    emit("delete", isQuarter.value ? row.quarterPlanDetailID! : row.monthlyPlanDetailID!);
    // 前端删除行 - 使用类型安全的方法
    let workIndex: number = -1;
    workIndex = typeAndWorks.value.children.indexOf(row);
    workIndex !== -1 && typeAndWorks.value.children.splice(workIndex, 1);
    // 常规工作无sort，无需更新后续work的sort
    if (!row.workType) {
      return;
    }
    // 修改其后的sort - 使用类型安全的方法
    typeAndWorks.value.children.forEach((work) => {
      if (work.workType === workType.Key && work.sort && work.sort > row.sort!) {
        work.sort -= 1;
      }
    });
  });
};
/**
 * @description: 工作类型变更，更新工作
 * @param row 当前行
 * @return
 */
const updateWork = (row: planWork) => {
  if (row.typeID === workType.Key) {
    // 变更为了重点工作
    row.sort = typeAndWorks.value.children.filter((work) => work.workType === workType.Key).length + 1;
  } else {
    // 变更为了常规工作
    typeAndWorks.value.children.forEach((work) => {
      if (work.sort && work.sort > row.sort!) {
        work.sort -= 1;
      }
    });
    row.sort = undefined;
  }
  emit("update", row);
};
//#endregion

//#region 排序
/**
 * @description: 预排序方法，计算排序后的数据但不直接修改原始数据
 * @param newIndex 新下标
 * @param oldIndex 旧下标
 * @return 返回排序后的children数组和workIDAndSort对象
 */
const preSort = (newIndex: number, oldIndex: number) => {
  // 深度克隆children数组，确保不修改原始数据
  const cloneChildren = typeAndWorks.value.children.map((work) => ({ ...work }));
  // 执行排序操作
  const row = cloneChildren[oldIndex];
  cloneChildren.splice(oldIndex, 1);
  cloneChildren.splice(newIndex, 0, row);
  // 更新重点工作的sort值
  cloneChildren.forEach((work, index) => {
    if (work.workType === workType.Key) {
      work.sort = index + 1;
    }
  });
  const key = isQuarter.value ? "quarterPlanDetailID" : "monthlyPlanDetailID";
  // 生成workIDAndSort映射对象
  const workIDAndSort = cloneChildren.reduce((acc, work) => {
    if (!work[key] || work.workType === workType.Routine || !work.sort) {
      return acc;
    }
    acc[work[key]!] = work.sort;
    return acc;
  }, {} as Record<string, number>);

  return {
    sortedChildren: cloneChildren,
    workIDAndSort
  };
};

/**
 * @description: 工作重排序
 * @param newIndex 新下标
 * @param oldIndex 旧下标
 * @return
 */
const resetWorksSort = async (newIndex: number, oldIndex: number) => {
  if (newIndex === oldIndex) {
    return;
  }
  // 使用预排序方法获取排序后的数据
  const { sortedChildren, workIDAndSort } = preSort(newIndex, oldIndex);
  // 通知父组件-请求重排序
  const successSortFlag = await new Promise((resolve) => {
    emit("resetSort", typeAndWorks.value.typeID, workIDAndSort, resolve);
  });
  if (successSortFlag) {
    // 只有在提交成功后才更新typeAndWorks对象，完成表格重绘
    typeAndWorks.value.children = sortedChildren;
    showMessage("success", "排序成功！");
  } else {
    showMessage("error", "排序失败！");
  }
};
//#endregion

//#region 编辑
const rowClassName = ({ row }: { row: planWork }) => (row.workType === workType.Routine ? "routine-work" : "");
/**
 * @description: 单元格点击，抛出cellClick事件
 * @param row 当前行
 * @param column 当前列
 * @param cell 当前单元格
 * @return
 */
const handleCellClick = (row: planWork, column: any, cell: HTMLTableCellElement) => {
  if (annualPlanStore.readOnly) {
    return;
  }
  const propertyName = common.getDataSet(cell, "data-prop", true);
  if (!propertyName) {
    return;
  }
  propertyName === "principal" && emit("editPrincipal", row, selectRow);
  propertyName === "workContent" && (getEditState(row).isWorkContentEdit = true);
  propertyName === "requirement" && (getEditState(row).isRequirementEdit = true);
};
/**
 * @description: 打开上级部门季度计划工作内容选择弹窗
 * @param row 当前行
 * @return
 */
const handleWorkQuoteEdit = (row: planWork) => {
  if (row.isTemp || !row.apInterventionID) {
    showMessage("warning", "没有可供参考的工作");
    return;
  }
  emit("editWorkReference", row, selectRow);
};
type elTableType = InstanceType<typeof ElTable>;
const tableRef = useTemplateRef<elTableType>("tableRef");

// 独立管理编辑状态，避免污染原始数据
const editingStates = reactive<Record<string, { isWorkContentEdit: boolean; isRequirementEdit: boolean }>>({});

/**
 * @description: 获取编辑状态
 * @param row 当前行
 * @return
 */
const getEditState = (row: planWork) => {
  const key = (row.monthlyPlanDetailID || row.quarterPlanDetailID) + "-" + row.sort;
  if (!editingStates[key]) {
    editingStates[key] = { isWorkContentEdit: false, isRequirementEdit: false };
  }
  return editingStates[key];
};

watch(
  () => typeAndWorks.value.children,
  (newChildren) => {
    const currentKeys = new Set(newChildren.map((row) => (row.monthlyPlanDetailID || row.quarterPlanDetailID) + "-" + row.sort));
    Object.keys(editingStates).forEach((key) => {
      if (!currentKeys.has(key)) {
        delete editingStates[key];
      }
    });
  },
  { deep: true }
);
/**
 * @description: 勾选当前行（保持多选状态）
 * @param row 当前行
 * @return
 */
const selectRow = async (row: planWork) => {
  await nextTick(() => {
    tableRef.value?.toggleRowSelection(toValue(row), true);
  });
};
/**
 * @description: 输入框失焦，还原为文本，并勾选当前行
 * @param row 当前行
 * @param func 失焦后执行的函数
 * @return
 */
const handleInputBlur = (row: planWork, func: () => void) => {
  func();
  nextTick(() => {
    tableRef.value?.toggleRowSelection(row, true);
  });
};
//#endregion
defineExpose({
  /**
   * @description: 清空选择
   */
  clearSelection: () => tableRef.value?.clearSelection(),
  /**
   * @description: 获取要保存的工作
   */
  getToSaveWorks: () => {
    const toSaveWorks = (tableRef.value?.getSelectionRows() as planWork[]) ?? [];
    if (!toSaveWorks.length) {
      return [];
    }
    const noWorkContent = (work: planWork) => !work.workContent;
    const noRequirement = (work: planWork) => work.workType === workType.Key && !work.requirement;
    if (toSaveWorks.some((work) => noWorkContent(work) || noRequirement(work))) {
      showMessage("error", `请完善分类${typeAndWorks.value.typeName}下的工作内容和重点工作的完成要求！`);
      throw new Error("业务异常，必填项未完善");
    }
    const keyWorks = typeAndWorks.value.children.filter((work) => work.workType === workType.Key);
    // 计算新工作的sort值
    typeAndWorks.value.children.forEach((work) => {
      if (work.workType === 1 && !work.sort) {
        work.sort = keyWorks.indexOf(work) + 1;
      }
    });
    return toSaveWorks;
  }
});
</script>
<style scoped lang="scss">
.reference-intervention-help {
  font-size: 16px;
  line-height: 0.8;
}
.plan-work-table {
  .qp-work-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .type-name {
      font-size: 32px;
      margin: 8px 0;
      font-weight: 600;
    }
  }
  .work-table {
    margin-top: 8px;
    .data-key {
      white-space: nowrap;
    }
    .el-switch :deep(.is-text) {
      letter-spacing: 2.5px;
    }
    .iconfont {
      margin-right: 8px;
    }
    .principal-name {
      font-size: 18px;
    }
    ::v-deep(.row-drag-ghost) {
      opacity: 0;
    }
  }
}
</style>
