<!--
 * FilePath     : \src\views\examineManagement\paperMaintain.vue
 * Author       : 来江禹
 * Date         : 2024-08-07 15:34
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-02 08:53
 * Description  : 试卷维护页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout :drawerOptions="drawerOptions" class="paper-maintain">
    <template #header>
      <el-date-picker
        type="daterange"
        range-separator="至"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        v-model="filterDate"
        class="header-date"
        @change="getPaperMaintainList()"
      >
      </el-date-picker>
      <el-radio-group v-model="paperType" class="paper-type" @change="getPaperMaintainList">
        <el-radio-button v-for="item in paperTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-radio-group>
      <department-switch-selector
        :employeeID="userStore.employeeID"
        v-model="filterDepartmentID"
        clearable
        @select="getPaperMaintainList"
      ></department-switch-selector>
      <el-button class="add-button" v-permission:B="1" @click="addRecord(paperType)">新增</el-button>
    </template>
    <el-table :data="paperMainList" border stripe height="100%" class="paper-main-table">
      <el-table-column :min-width="convertPX(200)" label="试卷名称" prop="paperTitle"></el-table-column>
      <el-table-column :width="convertPX(150)" label="部门" prop="departmentName" align="center"></el-table-column>
      <el-table-column :width="convertPX(110)" label="题目数量" prop="questionCount" align="center"></el-table-column>
      <el-table-column
        v-if="paperType === '1'"
        :width="convertPX(110)"
        label="难度等级"
        prop="difficultyLevel"
        align="center"
      ></el-table-column>
      <el-table-column v-if="paperType === '1'" :width="convertPX(90)" label="总分" prop="totalPoints" align="center"></el-table-column>
      <el-table-column
        :width="convertPX(110)"
        :label="paperType === '1' ? '及格分数' : '达标分数'"
        prop="passingScore"
        align="center"
      ></el-table-column>
      <el-table-column :width="convertPX(140)" label="新增人" prop="addEmployeeName" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="新增时间" prop="addDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(140)" label="修改人" prop="modifyEmployeeName" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="修改时间" prop="modifyDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(200)" align="center">
        <template #default="scope">
          <el-tooltip content="编辑">
            <i
              class="iconfont icon-edit"
              v-permission:B="3"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click="addRecord(paperType, scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="预览">
            <i class="iconfont icon-search" v-permission:B="7" @click="previewPaper(scope.row.examinationPaperMainID)"></i>
          </el-tooltip>
          <el-tooltip content="复制">
            <i class="iconfont icon-copy" v-permission:B="3" @click="copyPaper(scope.row.examinationPaperMainID)"></i>
          </el-tooltip>
          <el-tooltip content="考核发布">
            <i
              class="iconfont icon-publish"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click="maintainExaminationRecord(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              class="iconfont icon-delete"
              v-permission:B="4"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click="deleteData(scope.row.examinationPaperMainID)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName == 'AddOrModifyRecord'"
        :model="paperMainData"
        ref="submitRefs"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item v-if="paperMainData.isPractical" label="实操题库：" prop="questionBankID">
          <question-bank-selector
            v-model="paperMainData.questionBankID"
            label=""
            :showDefaultValues="false"
            :width="400"
            :isPractical="true"
            @select="selectQuestionBank"
          ></question-bank-selector>
        </el-form-item>
        <el-form-item label="试卷名称：" prop="paperTitle">
          <el-input v-model="paperMainData.paperTitle" placeholder="请输入试卷名称"></el-input>
        </el-form-item>
        <el-form-item label="部门：" prop="departmentID">
          <department-switch-selector
            label=""
            v-model="paperMainData.departmentID"
            :employeeID="userStore.employeeID"
            clearable
          ></department-switch-selector>
        </el-form-item>
        <template v-if="!paperMainData.isPractical">
          <el-form-item label="组卷规则：" prop="examinationConditionRecordID">
            <el-button type="primary" @click="showExaminationCondition()">选择组卷规则</el-button>
            <div class="condition-name" v-if="paperMainData.conditionName">
              <span>{{ paperMainData.conditionName }}</span>
            </div>
          </el-form-item>
          <el-form-item label="试卷总分：" prop="totalPoints">
            <el-input-number v-model="paperMainData.totalPoints" :disabled="true" />
          </el-form-item>
        </template>
        <el-form-item :label="paperMainData.isPractical ? '达标分数：' : '及格分数：'" prop="passingScore">
          <el-input-number v-model="paperMainData.passingScore" :min="1" :max="paperMainData.totalPoints" />
        </el-form-item>
        <el-form-item v-if="!paperMainData.isPractical" label="组卷模式：" prop="paperQuestionMode">
          <el-radio-group v-model="paperMainData.paperQuestionMode" class="mode-radio-layout">
            <el-radio v-for="{ label, value } in paperQuestionModeOptions" :value="value" :key="value">{{ label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-drawer
        class="condition-drawer"
        :title="innerDrawerOptions.drawerTitle"
        v-model="innerDrawerOptions.showDrawer"
        :size="innerDrawerOptions.drawerSize"
      >
        <examination-condition-form
          v-model="paperMainData.examinationConditionRecordID"
          :conditionName="paperMainData.paperTitle"
          ref="conditionFormRef"
        ></examination-condition-form>
        <template #footer>
          <el-button v-if="innerDrawerOptions.showCancel" @click="() => innerDrawerOptions.cancel?.()"> 取消</el-button>
          <el-button v-if="innerDrawerOptions.showConfirm" v-permission:B="2" type="primary" @click="innerDrawerOptions.confirm?.()">
            保存
          </el-button>
        </template>
      </el-drawer>
      <paper-preview v-if="drawerOptions.drawerName == 'PreviewPaper'" :formData="paperFormData"></paper-preview>
      <examination-record-form
        v-if="drawerOptions.drawerName == 'MaintainExamineRecord'"
        ref="examinationRecordFormRef"
        v-model="examinationRecordFormData"
        :routeType="'Examination'"
        :examinationType="paperType"
        :publishExaminePaperMainID="publishExaminePaperMainID"
      ></examination-record-form>
    </template>
    <template #drawerButtonAfter v-if="drawerOptions.drawerName == 'MaintainExamineRecord'">
      <el-button class="edit-button" v-permission:B="2" @click="saveExaminationRecord(true)">发布</el-button>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
// #region 引入
import { examineService } from "@/api/examineService";
import type { dynamicFormData } from "zhytech-ui";
import examinationConditionForm from "./components/examinationConditionForm.vue";
import examinationRecordForm from "./components/examinationRecordForm.vue";
import { usePaper } from "./hooks/usePaper";
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
// #endregion

// #region 变量定义
const examinationRecordFormData = ref<Record<string, any>>({});
const paperMainData = ref<Record<string, any>>({});
const paperMainList = ref<Record<string, any>[]>([]);
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const paperFormData = ref<dynamicFormData<Record<string, any>>>({
  datas: {},
  components: [],
  props: {}
});
const paperType = ref<string>("1");
const paperTypeOptions = ref<Record<string, any>[]>([]);
const submitRefs = ref<any>();
const examinationRecordFormRef = shallowRef();
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    if (drawerOptions.value.drawerName === "MaintainExamineRecord") {
      saveExaminationRecord(false);
      return;
    }
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    if (await saveRecord()) {
      getPaperMaintainList();
    }
  }
});
const filterDepartmentID = ref<number | number[] | undefined>(userStore.departmentID);
const { addRecord, saveRecord } = usePaper(paperMainData, drawerOptions, userStore);
const publishExaminePaperMainID = ref<string>();
// 不定题选项
const paperQuestionModeOptions = ref<Record<string, { label: string; value: number; type: string }>[]>([]);
// 规则验证rule
const rules = reactive({
  examinationConditionRecordID: [
    {
      required: true,
      message: "请选择组卷规则",
      trigger: "change"
    }
  ],
  questionBankID: [
    {
      required: true,
      message: "请选择题库",
      trigger: "select"
    }
  ],
  paperTitle: [
    {
      required: true,
      message: "请输入试卷名称",
      trigger: "change"
    }
  ],
  totalPoints: [
    {
      required: true,
      message: "请输入总分",
      trigger: "change"
    }
  ],
  departmentID: [
    {
      required: true,
      message: "请选择部门",
      trigger: "change"
    }
  ]
});
// #endregion

//#region 初始化
onMounted(() => {
  getPaperMaintainList();
  getPaperTypeSetting();
  getPaperQuestionModeSetting();
});
// #endregion

//#region 业务逻辑
/**
 * @description: 获取试卷记录
 */
const getPaperMaintainList = () => {
  let params = {
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    paperType: paperType.value,
    departmentID:
      filterDepartmentID.value && Array.isArray(filterDepartmentID.value) && filterDepartmentID.value.length > 0
        ? filterDepartmentID.value[0]
        : filterDepartmentID.value
  };
  examineService.getExaminationPaperMainList(params).then((result: any) => {
    if (result) {
      paperMainList.value = result;
    }
  });
};
/**
 * @description: 保存考核记录方法
 * @param isPublish 是否发布
 */
const saveExaminationRecord = (isPublish: boolean) => {
  examinationRecordFormRef.value.saveRecord(isPublish).then((result: any) => {
    if (result) {
      showMessage("success", "发布考核成功！");
      drawerOptions.value.showDrawer = false;
    } else {
      showMessage("error", "发布考核失败！");
    }
  });
};

/**
 * @description: 删除数据
 * @param mainID
 */
const deleteData = (mainID: string) => {
  if (!mainID) {
    return;
  }
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let param = {
      mainID: mainID
    };
    examineService.deleteExaminationPaperMainData(param).then((result: any) => {
      if (result) {
        showMessage("success", "删除成功！");
        getPaperMaintainList();
      }
    });
  });
};
/**
 * @description: 预览试卷
 * @param examinationPaperMainID
 */
const previewPaper = async (examinationPaperMainID: string) => {
  paperFormData.value = {
    datas: {},
    components: [],
    props: {}
  };
  drawerOptions.value.drawerName = "PreviewPaper";
  drawerOptions.value.drawerTitle = "预览试卷";
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.showCancel = false;
  drawerOptions.value.showDrawer = true;
  const params = {
    mainID: examinationPaperMainID,
    isPreview: true
  };
  paperFormData.value = (await examineService.getPaperFormTemplate(params).then((res: any) => {
    return res.formTemplateView;
  })) as dynamicFormData<Record<string, any>>;
};

/**
 * @description: 获取试卷类型设置
 */
const getPaperTypeSetting = () => {
  let params: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationPaperMain",
    settingTypeValue: "PaperType",
    index: Math.random()
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    paperTypeOptions.value = datas;
  });
};
// #endregion

/**
 * @description: 选择题库
 * @param questionBank
 * @return
 */
const selectQuestionBank = (questionBank: Record<string, any>) => {
  paperMainData.value.paperTitle = questionBank.label;
};

// #region 组卷规则弹窗相关逻辑
const conditionFormRef = shallowRef();
const innerDrawerOptions = ref<DrawerOptions>({
  drawerTitle: "组卷规则",
  showDrawer: false,
  drawerSize: "90%",
  showConfirm: true,
  showCancel: true,
  confirm: async () => {
    let [savedConditionMainID, totalScore] = await conditionFormRef.value.save();
    if (!savedConditionMainID) {
      return;
    }
    paperMainData.value.examinationConditionRecordID = savedConditionMainID;
    paperMainData.value.totalPoints = totalScore;
    innerDrawerOptions.value.showDrawer = false;
  }
});

/**
 * @description: 显示组卷规则弹窗
 */
const showExaminationCondition = () => {
  innerDrawerOptions.value.showDrawer = true;
};

// #endregion
/**
 * @description: 跳转到考核发布
 * @param row 试卷记录
 * @returns
 */
const maintainExaminationRecord = (row: Record<string, any>) => {
  if (!row) {
    return;
  }
  drawerOptions.value.drawerName = "MaintainExamineRecord";
  drawerOptions.value.drawerTitle = "考核发布";
  drawerOptions.value.drawerSize = "50%";
  drawerOptions.value.showDrawer = true;
  publishExaminePaperMainID.value = row.examinationPaperMainID;
};
/**
 * @description: 复制试卷
 * @param examinationPaperMainID 试卷MainID
 */
const copyPaper = (examinationPaperMainID: string) => {
  let param = { examinationPaperMainID: examinationPaperMainID };
  examineService.copyExamPaperMain(param).then((res: any) => {
    if (res) {
      showMessage("success", "复制成功！");
      getPaperMaintainList();
    }
  });
};

/**
 * @description: 获取不定题设置
 */
const getPaperQuestionModeSetting = () => {
  let params: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationPaperMain",
    settingTypeValue: "PaperQuestionMode",
    index: Math.random()
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    paperQuestionModeOptions.value = datas || [];
  });
};
</script>
<style lang="scss">
.paper-maintain {
  .paper-type {
    margin: 0 20px;
  }
  .header-date {
    width: 300px;
  }
  .condition-name {
    width: 100%;
    color: $base-color;
    // 字体间隔
    letter-spacing: 0.5px;
  }
  .paper-main-table {
    .icon-publish {
      color: $base-color;
    }
  }
  .mode-radio-layout {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .el-radio {
      margin: 3px 0; // 调整每个选项的上下间距
    }
  }
}
</style>
