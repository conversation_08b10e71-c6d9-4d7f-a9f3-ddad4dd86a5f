/*
 * FilePath     : \src\views\messageManagement\hooks\useMessageConfirmationHandler.ts
 * Author       : 张现忠
 * Date         : 2024-12-11 16:11
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:31
 * Description  :
 * CodeIterationRecord:
 */
// 护士长角色ID
let rolesOfMustRead: number[] = [];
export function useMessageConfirmationHandler() {
  const systemMessageNotice = ref<Record<string, any>>();
  const showMessageFlag = ref<boolean>(false);
  // 判断是否为待确认的系统通知 | 需要确认，用于判断是否需要设置确认前等待10秒
  const isPendingConfirmMessage = ref<boolean>(false);
  /**
   * @description: 获取必须阅读系统弹窗消息的用户
   */
  const getMustReadMessageRole = () => {
    let params = {
      settingType: "Common",
      settingTypeCode: "MustReadMessageRole"
    };
    settingDictionaryService.getSettingDictionaryDict(params).then((responseData: any) => {
      if (responseData?.length) {
        rolesOfMustRead = responseData.map((item: Record<string, any>) => item.value) || [];
      }
    });
  };
  getMustReadMessageRole();
  /**
   * @description: 获取待确认消息
   */
  const getPendingConfirmationMessage = () => {
    messageConfirmationService.getPendingConfirmationMessage().then((responseData: any) => {
      if (responseData) {
        isPendingConfirmMessage.value = true;
        systemMessageNotice.value = {
          messageRecordID: responseData.messageRecordID,
          messageContent: responseData.messageContent,
          title: responseData.messageTitle
        };
        // 弹窗显示系统通知
        if (responseData.messageContent) {
          showMessageFlag.value = true;
        }
      }
    });
  };

  /**
   * @description: 确认已读系统通知
   * @param confirm 是否确认
   */
  const completeRead = async (confirm: boolean) => {
    if (checkRole(confirm)) {
      return false;
    }
    if (confirm && systemMessageNotice.value) {
      return await confirmMessage(systemMessageNotice.value.messageRecordID);
    }
    return false;
  };
  /**
   * @description: 检查角色是否必须阅读完系统通知
   * @param hasRead 已经阅读
   * @returns true:必须阅读 false:不需要阅读
   */
  const checkRole = (hasRead: boolean) => {
    const roles = useStore().userStore.roles as number[];
    if (!hasRead && Array.isArray(roles) && roles.some((userRole: number) => rolesOfMustRead.includes(userRole))) {
      showMessage("warning", "您需要阅读完系统通知");
      return true;
    }
    return false;
  };
  /**
   * @description: 确认消息
   * @param messageRecordID 消息记录ID
   */
  const confirmMessage = async (messageRecordID: string) => {
    if (!messageRecordID) {
      return;
    }
    const params = {
      messageRecordID: messageRecordID
    };
    let result = true;
    await messageConfirmationService.confirmMessage(params).then((responseData: any) => {
      if (!responseData) {
        showMessage("error", "保存失败");
        result = false;
      }
    });
    return result;
  };
  /**
   * @description: 显示消息正文到弹窗中
   * @param message 消息
   */
  const showMessageContent = (message: Record<string, any>) => {
    if (message.messageContent) {
      systemMessageNotice.value = {
        messageRecordID: message.messageRecordID,
        title: message.messageTitle,
        messageContent: message.messageContent
      };
      isPendingConfirmMessage.value = false;
      showMessageFlag.value = true;
      return;
    }
    messageManagementService.getMessageDetail({ messageRecordID: message.messageRecordID }).then((data: any) => {
      systemMessageNotice.value = { messageRecordID: message.messageRecordID, title: message.messageTitle, messageContent: data };
      isPendingConfirmMessage.value = false;
      showMessageFlag.value = true;
    });
  };
  return {
    // 待阅读的系统通知
    systemMessageNotice,
    // 系统通知弹窗是否显示
    showMessageFlag,
    // 打开确认 计时
    isPendingConfirmMessage,
    /**
     * @description: 获取待确认消息
     */
    getPendingConfirmationMessage,
    /**
     * @description: 确认已读系统通知
     * @param confirm 是否确认
     */
    completeRead,
    /**
     * @description: 检查角色是否必须阅读完系统通知
     * @param role 角色
     * @returns true:必须阅读 false:不需要阅读
     */
    checkRole,
    /**
     * @description: 确认消息
     * @param messageRecordID 消息记录ID
     */
    showMessageContent
  };
}
