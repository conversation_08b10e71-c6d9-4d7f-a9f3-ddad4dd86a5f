.el-dialog {
  --el-dialog-width: 80% !important;
  height: 80%;
  padding: 0 !important;
  margin-top: 10vh !important;
  &.is-fullscreen {
    --el-dialog-width: 100% !important;
    margin-top: 0 !important;
  }
  .el-dialog__header {
    @include l-gradient-bg(right, darken($base-color, 10%), lighten($base-color, 20%));
    height: 32px;
    line-height: 34px;
    padding: 5px 0;
    margin: 0;
    .el-dialog__title {
      color: #ffffff;
      padding-left: 16px;
      font-size: 22px;
      letter-spacing: 3px;
    }
    .el-dialog__close {
      color: #ff0000 !important;
      font-size: 28px;
      margin-bottom: 8px;
    }
  }

  .el-dialog__body {
    padding: 10px !important;
    box-sizing: border-box;
    overflow: auto;
    height: calc(100% - 85px);
  }

  &.no-footer .el-dialog__body {
    height: calc(100% - 45px);
  }
  .el-dialog__footer {
    height: 40px;
    padding: 0 15px;
    box-sizing: border-box;
  }
}
