<!--
 * FilePath     : \src\components\settingDictionaryMaintain\index.vue
 * Author       : 来江禹
 * Date         : 2024-07-11 08:31
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-26 09:25
 * Description  :  考核、培训分类字典维护
                  参数 settingTypeCode SettingDictionary表settingTypeCode
                      settingType： 考核：ExaminationManagement；培训：TrainingManagement
                      treeTableFlag: 是否是树形结构 true；false
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="setting-dictionary" headerHeight="auto">
    <template #header>
      <el-button class="add-button" @click="addSettingDictionary('')">新增</el-button>
      <label
        >分类名称：
        <el-input clearable v-model="localShowName" class="name-search-input" placeholder="请输入分类名称" @change="nameSearch">
          <template #append>
            <i @click="nameSearch" class="iconfont icon-search" />
          </template>
        </el-input>
      </label>
    </template>
    <el-table
      :data="classifiedData"
      ref="classifiedTable"
      height="100%"
      :row-key="treePropTableFlag ? 'settingValue' : ''"
      :tree-props="getTreeProps()"
      @row-click="rowClick"
      :row-style="rowStyle"
      border
      stripe
    >
      <el-table-column label="分类名称" prop="localShowName" :min-width="convertPX(400)">
        <template #default="scope">
          <span v-if="!scope.row.editFlag">{{ scope.row.localShowName }}</span>
          <el-input v-else v-model="scope.row.localShowName"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="修改人" prop="modifyEmployee" :width="convertPX(300)">
        <template #default="scope">
          <span v-if="!scope.row.editFlag">{{ scope.row.modifyEmployee }}</span>
          <employee-selector v-else v-model="scope.row.modifyEmployeeID" :showAll="true" label="" filterable :disabled="true" />
        </template>
      </el-table-column>
      <el-table-column label="修改时间" :width="convertPX(300)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(140)" align="center">
        <template #default="scope">
          <el-tooltip v-if="scope.row.settingValue && treePropTableFlag" content="新增">
            <i class="iconfont icon-add" @click="addSettingDictionary(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="编辑">
            <i
              class="iconfont icon-edit"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click.stop="editSettingDictionaryRow(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="保存">
            <i
              class="iconfont icon-save"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click.stop="saveSettingDictionaryData(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              class="iconfont icon-delete"
              v-visibilityHidden="userStore.employeeID === scope.row.modifyEmployeeID"
              @click.stop="deleteSettingDictionary(scope.row)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
import type { settingDictionaryView } from "./types/settingDictionaryView";
const { userStore } = useStore();
const props = defineProps({
  settingTypeCode: {
    type: String,
    default: () => null
  },
  settingType: {
    type: String,
    default: () => null
  },
  treeTableFlag: {
    type: Boolean,
    default: true
  }
});
const route = useRoute();
const querySettingType = route.query.settingType as string;
const querySettingTypeCode = route.query.settingTypeCode as string;
const treePropTableFlag = ref<Boolean>(false);
const classifiedData = ref<Array<settingDictionaryView>>([]);
const copyClassifiedData = ref<Array<settingDictionaryView>>([]);
const expandedRows = ref<Array<settingDictionaryView>>([]);
let classifiedTable = ref<any>();
const localShowName = ref<string>("");
//#region 初始化
onMounted(() => {
  if ("treeTableFlag" in route.query) {
    treePropTableFlag.value = Boolean(route.query?.treeTableFlag);
  } else {
    treePropTableFlag.value = props.treeTableFlag;
  }
  getSettingDictionaryData();
});
//#endregion

//#region
/**
 * @description: 编辑数据
 * @param row
 * @return
 */
const editSettingDictionaryRow = (row: settingDictionaryView) => {
  row.editFlag = true;
  // 修改时，调整修改时间为当前时间 -因为时间不让用户修改
  row.modifyDateTime = new Date();
};
/**
 * @description: 新增数据
 * @param row
 * @return
 */
const addSettingDictionary = (row: Record<string, any> | string) => {
  const insertData = getInsertData();
  if (!row) {
    insertData.editFlag = true;
    classifiedData.value.push(insertData);
  } else {
    findDataNode(classifiedData.value, row as Record<string, any>);
  }
};
/**
 * @description: 递归找寻插入数据节点
 * @param data 递归查找数据
 * @param row 操作行
 * @param deleteFlag 是否删除
 * @return
 */
const findDataNode = (data: Array<settingDictionaryView>, row: Record<string, any>, deleteFlag: boolean = false) => {
  data.forEach((element, index) => {
    if (element.settingValue === row.settingValue) {
      if (deleteFlag) {
        data.splice(index, 1);
        return;
      }
      const insertData = getInsertData();
      insertData.editFlag = true;
      insertData.parentID = element.settingValue;
      insertData.level = element.level + 1;
      element.children.push(insertData);
    } else {
      findDataNode(element.children, row, deleteFlag);
    }
  });
};
/**
 * @description: 删除数据
 * @param row
 * @return
 */
const deleteSettingDictionary = (row: settingDictionaryView) => {
  confirmBox("是否删除分类记录？", "删除分类记录", (flag: Boolean) => {
    if (flag) {
      if (row.settingValue) {
        let params = {
          settingValue: row.settingValue,
          settingTypeCode: props.settingTypeCode ?? querySettingTypeCode
        };
        settingDictionaryService.deleteSettingDictionaryMaintain(params).then((res) => {
          if (res) {
            showMessage("success", "删除成功");
            getSettingDictionaryData();
          } else {
            showMessage("error", "删除失败");
          }
        });
      } else {
        findDataNode(classifiedData.value, row, true);
      }
    }
  });
};
/**
 * @description: 保存数据
 * @param row
 * @return
 */
const saveSettingDictionaryData = (row: settingDictionaryView) => {
  if (!row.localShowName) {
    showMessage("warning", "分类名称不能为空");
    return;
  }
  let params = {
    settingType: props.settingType ?? querySettingType,
    localShowName: row.localShowName,
    settingValue: row.settingValue,
    parentID: row.parentID,
    level: row.level,
    settingTypeCode: row.settingTypeCode,
    children: row.children,
    modifyDateTime: row.modifyDateTime
  };
  settingDictionaryService.saveSettingDictionaryMaintain(params).then((res) => {
    if (res) {
      showMessage("success", "保存成功");
      getSettingDictionaryData();
    }
  });
};
/**
 * @description: 获取数据
 */
const getSettingDictionaryData = () => {
  let params = {
    settingType: props.settingType ?? querySettingType,
    settingTypeCode: props.settingTypeCode ?? querySettingTypeCode
  };
  settingDictionaryService.getSettingDictionaryMaintain(params).then((res: any) => {
    if (res) {
      classifiedData.value = res;
      copyClassifiedData.value = res;
    }
  });
};
/**
 * @description: 行点击事件
 * @param row
 * @return
 */
const rowClick = (row: settingDictionaryView) => {
  if (!treePropTableFlag.value) {
    return;
  }
  // 防置点击行的数据
  expandedRows.value = [];
  const index = expandedRows.value.indexOf(row);
  if (index === -1) {
    if (row.children && row.children.length > 0) {
      expandedRows.value.push(...row.children);
    }
  }
  classifiedTable.value.toggleRowExpansion(row);
};
/**
 * @description: 构造插入数据
 */
const getInsertData = (): settingDictionaryView => {
  return {
    settingType: "",
    localShowName: "",
    settingValue: "",
    parentID: "",
    level: 1,
    modifyEmployee: "",
    modifyEmployeeID: userStore.employeeID,
    employeeID: "",
    hospitalID: "",
    modifyDateTime: new Date(),
    language: 1,
    settingTypeCode: props.settingTypeCode ?? querySettingTypeCode,
    children: []
  } as settingDictionaryView;
};
/**
 * @description: 获取树形结构子节点名称
 */
const getTreeProps = () => {
  if (treePropTableFlag.value) {
    return { children: "children" };
  }
};
/**
 * @description: 按照分类名称模糊查询
 */
const nameSearch = () => {
  if (!localShowName.value) {
    classifiedData.value = [];
    classifiedData.value = copyClassifiedData.value;
    return;
  }
  classifiedData.value = [];
  copyClassifiedData.value.forEach((element) => {
    if (element.localShowName.includes(localShowName.value)) {
      classifiedData.value.push(element);
    }
    if (searchChildren(element.children)) {
      classifiedData.value.push(element);
    }
  });
};
/**
 * @description: 查找子数据
 * @param data
 * @return
 */
const searchChildren = (data: Array<settingDictionaryView>): Boolean => {
  let flag = false;
  data.forEach((element) => {
    if (element.localShowName.includes(localShowName.value)) {
      flag = true;
    }
    if (searchChildren(element.children)) {
      flag = true;
    }
  });
  return flag;
};
/**
 * @description: 更改展开行子行背景颜色
 * @param data
 * @return
 */
const rowStyle = (data: any) => {
  if (expandedRows.value.find((row) => row.settingValue === data.row.settingValue)) {
    return {
      backgroundColor: "#ffef99"
    };
  }
};
//#endregion
</script>
<style lang="scss">
.setting-dictionary {
  .base-header {
    .name-search-input {
      width: 200px;
    }
  }

  .table-date {
    width: 260px;
  }
}
</style>
