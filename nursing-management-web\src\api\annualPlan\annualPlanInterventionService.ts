/*
 * FilePath     : \src\api\annualPlan\annualPlanInterventionService.ts
 * Author       : 杨欣欣
 * Date         : 2023-08-10 17:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-11 16:20
 * Description  : 年度计划-分解目标任务查询与命令
 * CodeIterationRecord:
 */
import http from "@/utils/http";

export class annualPlanInterventionService {
  private static getAnnualInterventionsApi: string = "/AnnualPlanIntervention/GetAnnualInterventions";
  private static getUsedInterventionIDsApi: string = "/AnnualPlanIntervention/GetUsedInterventionIDs";
  private static getProjectDetailsGroupedByMainGoalApi: string = "/AnnualPlanIntervention/GetProjectDetailsGroupedByMainGoal";
  private static getRecPrincipalOptionsApi: string = "/AnnualPlanIntervention/GetRecPrincipalOptions";
  private static saveAnnualInterventionsApi: string = "/AnnualPlanIntervention/SaveAnnualInterventions";
  private static deleteAnnualInterventionApi: string = "/AnnualPlanIntervention/DeleteAnnualIntervention";
  private static getAnnualPlanGeneralViewApi: string = "/AnnualPlanIntervention/GetAnnualPlanGeneralView";

  /**
   * @description: 获取分解目标任务集合
   * @param params
   * @return
   */
  public static getAnnualInterventions = (params?: any) => http.get(this.getAnnualInterventionsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 获取已使用的分解目标任务字典ID集合
   * @param params
   * @return
   */
  public static getUsedInterventionIDs = (params?: any) => http.get(this.getUsedInterventionIDsApi, params) as Promise<number[]>;
  /**
   * @description: 获取按策略目标分组的目标任务
   * @param params
   * @return
   */
  public static getProjectDetailsGroupedByMainGoal = (params?: any) => http.get(this.getProjectDetailsGroupedByMainGoalApi, params);
  /**
   * @description: 获取责任人选项
   * @param params
   * @return
   */
  public static getRecPrincipalOptions = (params?: any) => http.get(this.getRecPrincipalOptionsApi, params);
  /**
   * @description: 保存分解目标任务集合
   * @param params
   * @return
   */
  public static saveAnnualInterventions = (params?: any) =>
    http.post(this.saveAnnualInterventionsApi, params, { loadingText: Loading.SAVE }) as Promise<boolean>;
  /**
   * @description: 删除分解目标任务
   * @param params
   * @return
   */
  public static deleteAnnualIntervention = (params?: any) =>
    http.get(this.deleteAnnualInterventionApi, params, { loadingText: Loading.DELETE });
  /**
   * @description: 获取分解目标任务总览
   * @param params
   * @return
   */
  public static getAnnualPlanGeneralView = (params?: any) =>
    http.get(this.getAnnualPlanGeneralViewApi, params, { loadingText: Loading.LOAD });
}
