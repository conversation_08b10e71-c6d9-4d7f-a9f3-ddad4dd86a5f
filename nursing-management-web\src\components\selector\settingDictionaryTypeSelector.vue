<!--
 * FilePath     : \src\components\selector\settingDictionaryTypeSelector.vue
 * Author       : 张现忠
 * Date         : 2024-04-09 14:19
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:43
 * Description  : settingDictionary分类选择器
 * CodeIterationRecord:
-->
<template>
  <div class="setting-dictionary-type-selector">
    <span v-if="label">{{ label }} : </span>
    <el-cascader
      class="selector-component"
      v-model="innerClassifyID"
      :options="courseTypeOptions"
      :show-all-levels="showAllLevels"
      :props="propsData"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
    </el-cascader>
  </div>
</template>

<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange } = useExposeSelectorEvent();
const { getCascaderFullValue } = useCascaderFullValue();
const props = defineProps({
  label: {
    type: String,
    default: "分类"
  },
  modelValue: {
    type: String,
    default: ""
  },
  type: {
    type: String,
    default: ""
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 180
  },
  settingTypeCode: {
    type: String,
    default: () => undefined
  },
  settingType: {
    type: String,
    default: () => undefined
  },
  showAllLevels: {
    type: Boolean,
    default: false
  }
});
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
const propsData = ref({
  value: "settingValue",
  label: "localShowName",
  children: "children",
  expandTrigger: "hover"
});
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let classifyID = useVModel(props, "modelValue", emits);
let innerClassifyID = ref([] as string[]);
/**
 *根据value凭借出级联父级数据
 * @param value
 */
const ignoreUpdates = (value: string) => {
  innerClassifyID.value = [];
  courseTypeOptions.value.forEach((item) => {
    if (item.settingValue === value) {
      innerClassifyID.value.push(item.settingValue);
    }
    if (item.settingValue !== value && item.children.length) {
      getChildrenChooseValue(item.children, value, item.settingValue);
    }
  });
};
/**
 * 递归找出子节点数据
 * @param childrenList
 * @param value
 * @param parentValue
 */
const getChildrenChooseValue = (childrenList: Array<Record<any, any>>, value: string, parentValue: string) => {
  childrenList.forEach((item) => {
    if (item.settingValue === value) {
      innerClassifyID.value.push(parentValue);
      innerClassifyID.value.push(item.settingValue);
    }
    if (item.settingValue !== value && item.children.length) {
      let innerClassifyIDLength = innerClassifyID.value.length;
      getChildrenChooseValue(item.children, value, item.settingValue);
      if (innerClassifyID.value.length !== innerClassifyIDLength) {
        innerClassifyID.value.unshift(parentValue);
      }
    }
  });
};
let courseTypeOptions = ref<Array<Record<any, any>>>([]);
onMounted(async () => {
  let params = {
    settingTypeCode: props.settingTypeCode,
    settingType: props.settingType
  };
  await settingDictionaryService.getSettingDictionaryMaintain(params).then((res: any) => {
    if (res) {
      courseTypeOptions.value = res;
    }
  });
  ignoreUpdates(classifyID.value);
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  ignoreUpdates((classifyID.value = Array.isArray(value) ? value[value.length - 1] : value));
  exposeChange(Array.isArray(value) ? value[value.length - 1] : value, emits);
  const [, selectItems] = getCascaderFullValue(
    Array.isArray(value) ? value[value.length - 1] : value,
    courseTypeOptions.value,
    "settingValue",
    "children",
    false
  );
  emits("select", selectItems);
};
</script>

<style lang="scss">
.setting-dictionary-type-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
