<!--
 * FilePath     : \src\components\selector\postSelector.vue
 * Author       : 张现忠
 * Date         : 2023-08-01 09:59
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-11-19 10:29
 * Description  : 岗位下拉选择器组件
 * CodeIterationRecord:
-->

<template>
  <div class="post-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="postIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option
        v-for="(item, index) in postOptions"
        :disabled="optionDisableFunc(item)"
        :key="index"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "岗位"
  },
  modelValue: {
    type: [Number, Array<Number>]
  },
  postType: {
    type: String,
    default: undefined
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 240
  },
  optionDisableFunc: {
    type: Function,
    default: (item: Record<any, any>) => false
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let postIDs = useVModel(props, "modelValue", emits);

let postOptions = ref<Array<Record<any, any>>>([]);
// 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
let { list } = toRefs(props);
if (list?.value) {
  postOptions.value = list.value;
} else {
  let { getPostData } = useDictionaryData();
  getPostData(props.postType).then((datas) => {
    postOptions.value = datas;
  });
}
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, postOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.post-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
