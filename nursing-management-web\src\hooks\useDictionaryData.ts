/*
 * FilePath     : /src/hooks/useDictionaryData.ts
 * Author       : 苏军志
 * Date         : 2023-09-16 11:44
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 19:03
 * Description  : 获取字典表数据的hooks
 * CodeIterationRecord:
 */

/**
 * 获取字典表数据的hooks
 */
export function useDictionaryData() {
  return {
    /**
     * 获取字典数据
     * @param settingTypeCode
     * @param settingValue
     * @returns
     */
    async getAdministrationDictionaryData(administrationParams: AdministrationParams) {
      let data: Record<any, any>[] = [];
      await dictionaryService.getAdministrationDict(administrationParams).then((result: any) => {
        data = result ?? [];
      });
      return data;
    },

    /**
     * 获取岗位字典
     * @param postTypeID
     * @returns
     */
    async getPostData(postTypeID?: string, index?: number) {
      let datas: Record<any, any>[] = [];
      const params = { postTypeID, index: index ?? Math.random() };
      await dictionaryService.getPostDict(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },
    /**
     * @description: 获取部门岗位字典
     * @param departmentID
     * @param showAll
     * @param postTypeID
     * @param currentDate
     * @param index
     * @return
     */
    async getDepartmentPostData(departmentID: number, showAll: boolean, postTypeID?: string, currentDate?: Date | string, index?: number) {
      let datas: Record<any, any>[] = [];
      const params = { departmentID, showAll, postTypeID, currentDate, index };
      await dictionaryService.getDepartmentPostDict(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },
    /**
     * 获取能级字典
     * @returns
     */
    async getCapabilityLevelData(type?: string, index?: number) {
      let datas: Record<any, any>[] = [];
      const params = { type, index };
      await dictionaryService.getCapabilityLevelDict(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },

    /**
     * 获取部门人员
     * @param departmentID
     * @returns
     */
    async getEmployeeData(showAll: boolean, departmentID?: number, index?: number) {
      let datas: Array<Record<any, any>> = [];
      const params = {
        showAll,
        departmentID,
        index
      };
      await dictionaryService.getEmployeeDict(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },

    /**
     * @description: 获取部门级联选择器
     * @param organizationType
     * @param disableDepartmentIDs
     * @param index
     * @return
     */
    async getDepartmentCascaderData(organizationType?: string, disableDepartmentIDs?: number[], index?: number) {
      let datas: CascaderList<number>[] = [];
      let disableIDs =
        Array.isArray(disableDepartmentIDs) && disableDepartmentIDs.length > 0
          ? disableDepartmentIDs.filter((id: any) => id !== undefined) // 过滤掉 undefined 的值
          : [];
      const params = { organizationType, disableDepartmentIDs: JSON.stringify(disableIDs), index };
      await dictionaryService.getDepartmentCascaderList(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },
    /**
     * @description: 获取注记图示
     * @param moduleType 标识类型
     * @return
     */
    async getIconsByModuleType(moduleType: string) {
      let datas: AdministrationIcon[] = [];
      const params = { moduleType };
      await dictionaryService.getIconsByModuleType(params).then((result: any) => {
        datas = result ?? [];
      });
      return datas;
    },
    /**
     * 获取审批分类级联选择器
     * @param index
     * @returns
     */
    async getProveCategoryCascaderData(index?: number) {
      let collection: CascaderList<string>[] = [];
      const params = { index };
      await settingDictionaryService.getProveCategoryCascaderList(params).then((result: any) => {
        collection = result ?? [];
      });
      return collection;
    },
    /**
     * 获取职务级联选择器
     * @param index
     * @param departmentID 部门ID
     * @returns
     */
    async getDepartmentToJobs(departmentID?: number, index?: number) {
      let collection: Record<string, any>[] = [];
      const params = { index, departmentID };
      await dictionaryService.getDepartmentToJobs(params).then((result: any) => {
        collection = result ?? [];
      });
      return collection;
    },
    /**
     * @description: 模糊查询人员信息
     * @param name 姓名
     * @return
     */
    async getEmployeeDataByName(name: string) {
      let data: Array<Record<string, any>> = [];
      const params = {
        employeeName: name
      };
      await dictionaryService.getEmployeeDataByName(params).then((result: any) => (data = result));
      return data;
    },
    /**
     * @description: 查询一名员工信息
     * @param employeeIDs 工号
     * @return
     */
    async getEmployeeDataByIDs(employeeIDs: string[] | string | undefined) {
      let data: Array<Record<string, any>> = [];
      if (!employeeIDs|| !employeeIDs.length) {
        return data;
      }
      const employeeIDsStr = JSON.stringify(typeof employeeIDs === "string" ? [employeeIDs] : employeeIDs);
      data = await dictionaryService
        .getEmployeeDataByIDs({
          employeeIDs: employeeIDsStr
        });
      return data;
    },
    /**
     * @description: 获取部门名称
     * @param departmentID 部门ID
     * @return
     */
    async getDepartmentName(departmentID: number) {
      let data: string = "";
      await dictionaryService.getDepartmentName({ departmentID }).then((result: any) => {
        data = result;
      });
      return data;
    },
    /**
     * @description: 获取人员角色清单
     * @return
     */
    async getAuthorityRoles() {
      let data: Array<Record<string, any>> = [];
      await dictionaryService.GetAuthorityRoles().then((result: any) => {
        data = result;
      });
      return data;
    },
    /**
     * @description: 获取人员多部门IDs
     * @param employeeID 工号
     * @param organizationType 组织类型
     * @return
     */
    async getEmployeeDepartment(employeeID: string, organizationType: string) {
      return await dictionaryService
        .getEmployeeDepartment({ employeeID: employeeID, organizationType: organizationType, index: Math.random() })
        .then((result: any) => {
          return result || [];
        });
    },
    /**
     * @description: 根据部门ID获取部门岗位
     * @param departmentID 工号
     * @return
     */
    async getPostDictByDepartmentID(departmentID: number, index?: number) {
      return await dictionaryService.getPostDictByDepartmentID({ departmentID, index }).then((result: any) => {
        return result || [];
      });
    },
    /**
     * @description:获取审批流程跳转设置
     * @returns
     */
    async getApprovalJumpSetting() {
      return await settingDictionaryService.getApprovalJumpSetting().then((result: any) => {
        return result || {};
      });
    },
    /**
     * @description: 根据SettingTypeCode和SettingTypeValue获取配置键值对配置
     * @param params
     * @returns
     */
    async getSettingDictionaryByCodeValue(params: any) {
      if (Array.isArray(params.settingTypeValues)) {
        params.settingTypeValues = JSON.stringify(params.settingTypeValues);
      } else {
        params.settingTypeValues = JSON.stringify([params.settingTypeValues]);
      }
      return await settingDictionaryService.getSettingDictionaryByCodeValue(params).then((result: any) => {
        return result;
      });
    },
    /**
     * @description: 根据SettingTypeCode和SettingTypeValue获取配置级联数据
     * @param params
     * @returns
     */
    async getCascaderSettingDictionary(params: any) {
      return await settingDictionaryService.getCascaderSettingDictionary(params).then((result: any) => {
        return result;
      });
    }
  };
}
