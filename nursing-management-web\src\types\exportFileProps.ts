/*
 * FilePath     : \src\types\exportFileProps.ts
 * Author       : 来江禹
 * Date         : 2023-09-17 11:34
 * LastEditors  : 张现忠
 * LastEditTime : 2025-04-04 10:58
 * Description  : 文件导出组件Model
 * CodeIterationRecord:
 */
/* eslint-disable */
/**
 * 文件导入PropsView
 */
type FileType = keyof typeof FileType;
declare interface FilePropsView {
  /**
   * 组件类型  option：excel导入:importExcel , excel导出：exportExcel,word导出：exportWord ,PDF导出：exportPdf
   */
  typeArr: FileType[];
  /**
   * Excel导人PropsView
   */
  importExcelOption?: ImportExcelView;
  /**
   * Excel导出PropsView
   */
  exportExcelOption?: ExportExcelView[];
  /**
   * Word导出PropsView
   */
  exportWordOption?: ExportWordView;
  /**
   * PDF导出PropsView
   */
  exportPdfOption?: ExportPdfView;
}
/**
 * Excel导入PropsView
 */
declare interface ImportExcelView {
  /**
   * 按钮名称
   */
  buttonName: string;
  /**
   *表格对应关系
   */
  columnData: object;
  /**
   * 需要解析的表格名称
   */
  sheetName?: string;
  /**
   * 第一个固定列的名称（导入的excel中从左到右一部分列是层级列，不固定列数，之后的是固定列，这个字段用来判断所有的固定列）
   */
  firstFixedColumnName?: string;
  /**
   * 解析excel的函数
   * @param file 导入的文件
   * @param sheetName 表格名称
   * @param firstFixedColumnName 第一个固定列的名称
   * @returns
   */
  parseExcelFunc?: (file: File, sheetName: string, firstFixedColumnName: string) => Promise<any>;
}

/**
 * Excel导出PropsView
 */
declare interface ExportExcelView {
  /**
   * 按钮名称
   */
  buttonName?: string;
  /**
   * 标题
   */
  fileName: string;
  /**
   * value值
   */
  sheetName: string;
  /**
   * 表头数据
   */
  columnData: Object;
  /**
   * 表格数据
   */
  tableData: any[];
}
/**
 * PDF导入出PropsView
 */
declare interface ExportPdfView {
  /**
   * 按钮名称
   */
  buttonName: string;
  /**
   * 标题
   */
  fileName: string;
  /**
   * dom元素
   */
  element: any;
}
/**
 * Word导入出PropsView
 */
declare interface ExportWordView {
  /**
   * 按钮名称
   */
  buttonName: string;
  /**
   * 标题
   */
  fileName: string;
  /**
   * word文档替换元素
   */
  wordData: any;
  /**
   * word模板文档地址
   */
  templateWordUrl: string;
}
