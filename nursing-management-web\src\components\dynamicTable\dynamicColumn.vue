<!--
 * FilePath     : \src\components\dynamicTable\dynamicColumn.vue
 * Author       : 郭鹏超
 * Date         : 2021-09-22 10:53
 * LastEditors  : 张现忠
 * LastEditTime : 2024-05-25 08:48
 * Description  : 表头组件
-->
<template>
  <div class="dynamic-column">
    <div v-if="item.columnStyle == 'Date'">
      <el-date-picker
        v-model="scopeRow[item.prop]"
        type="date"
        :clearable="false"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width: 100%"
      ></el-date-picker>
    </div>
    <!-- 时间 -->
    <div v-if="item.columnStyle == 'Time'">
      <el-time-picker v-model="scopeRow[item.prop]" value-format="HH:mm" format="HH:mm" style="width: 100%"></el-time-picker>
    </div>
    <!-- 日期时间文字 -->
    <div v-if="item.columnStyle.indexOf('date') != -1 || item.columnStyle.indexOf('time') !=-1|| item.columnStyle =='dateTime'">
      <div v-formatTime="{ value: scopeRow[item.prop], type: item.columnStyle  }"></div>
    </div>
    <!--文字 -->
    <div v-if="item.columnStyle == 'text'">
      <!-- <span>111 {{ scopeRow }}{{ item.prop }}</span> -->
      <div v-html="scopeRow[item.prop]"></div>
    </div>
    <div v-if="item.columnStyle == 'select'">
      <el-select style="width: 100%" v-model="scopeRow[item.prop]" placeholder="请选择">
        <el-option v-for="(option, index) in item.options" :key="index" :label="option.label" :value="option.value"></el-option>
      </el-select>
    </div>
    <div v-if="item.columnStyle == 'input'">
      <el-input style="width: 100%" v-model="scopeRow[item.prop]"></el-input>
    </div>
    <div v-if="item.columnStyle == 'check' && item.options">
      <el-checkbox-group v-model="scopeRow[item.prop]">
        <el-checkbox v-for="(option, index) in item.options" :key="index" :label="option.value">
          {{ option.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => {}
  },
  scope: {
    type: Object,
    default: () => {}
  }
});
const emits = defineEmits(["update:scopeRow"]);
const scopeRow = useVModel(props, "scope", emits);
</script>
