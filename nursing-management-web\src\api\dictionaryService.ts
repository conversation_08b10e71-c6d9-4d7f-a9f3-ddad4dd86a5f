/*
 * FilePath     : /src/api/dictionaryService.ts
 * Author       : LX
 * Date         : 2023-08-14 10:03
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 19:03
 * Description  : 字典相关API
 */
import http from "@/utils/http";
export class dictionaryService {
  private static getHospitalListApi: string = "dictionary/GetHospitalList";
  private static getHospitalByHospitalIDApi: string = "dictionary/GetHospitalByHospitalID";
  private static getHrmDepartmentDictApi: string = "dictionary/GetHrmDepartmentDict";
  private static getAdministrationDictApi: string = "dictionary/GetAdministrationDict";
  private static getPostDictApi: string = "dictionary/GetPostDict";
  private static getDepartmentPostDictApi: string = "dictionary/GetDepartmentPostDict";
  private static getCapabilityLevelDictApi: string = "dictionary/GetCapabilityLevelDict";
  private static getEmployeeDictApi: string = "dictionary/GetEmployeeDict";
  private static getDepartmentCascaderListApi: string = "dictionary/GetDepartmentCascaderList";
  private static getIconsByModuleTypeApi: string = "dictionary/GetIconsByModuleType";
  private static getDepartmentToJobsApi: string = "dictionary/GetDepartmentToJobs";
  private static getEmployeeDataByNameApi: string = "dictionary/GetEmployeeDataByName";
  private static getEmployeeDataByIDsApi: string = "dictionary/GetEmployeeDataByIDs";
  private static getDepartmentNameApi: string = "dictionary/GetDepartmentName";
  private static getAuthorityRolesApi: string = "dictionary/GetAuthorityRoles";
  private static getDepartmentViewsByOrganizationTypeApi: string = "dictionary/GetDepartmentViewsByOrganizationType";
  private static getEmployeeDepartmentApi: string = "dictionary/GetEmployeeDepartment";
  private static getPostDictByDepartmentIDApi: string = "dictionary/GetPostDictByDepartmentID";
  private static getComponentListByTypeApi: string = "dictionary/GetComponentListByType";
  private static getUpperDeptOptionsApi: string = "dictionary/GetUpperDeptOptions";
  private static getVisitsDeptOptionsApi: string = "dictionary/GetVisitsDeptOptions";
  /**
   * 获取医院列表
   * @param params
   * @returns
   */
  public static getHospitalList(params?: any) {
    return http.get(this.getHospitalListApi, params);
  }
  /**
   * 获取医院信息
   * @param params
   * @returns
   */
  public static getHospitalByHospitalID(params?: any) {
    return http.get(this.getHospitalByHospitalIDApi, params);
  }
  /**
   * 获取人事部门字典
   * @param params
   * @returns
   */
  public static getHrmDepartmentDict(params?: any) {
    return http.get(this.getHrmDepartmentDictApi, params);
  }
  /**
   * 获取国标字典
   * @param params
   * @returns
   */
  public static getAdministrationDict(params?: any) {
    return http.get(this.getAdministrationDictApi, params);
  }
  /**
   * 获取岗位字典
   * @param params
   * @returns
   */
  public static getPostDict(params?: any) {
    return http.get(this.getPostDictApi, params);
  }
  /**
   * 获取部门岗位字典
   * @param params
   * @returns
   */
  public static getDepartmentPostDict(params?: any) {
    return http.get(this.getDepartmentPostDictApi, params);
  }
  /**
   * 获取能级位字典
   * @param params
   * @returns
   */
  public static getCapabilityLevelDict(params?: any) {
    return http.get(this.getCapabilityLevelDictApi, params);
  }
  /**
   * 获取部门人员
   * @param params
   * @returns
   */
  public static getEmployeeDict(params?: any) {
    return http.get(this.getEmployeeDictApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 获取部门级联选择器数据
   * @param params
   * @returns
   */
  public static getDepartmentCascaderList(params?: any) {
    return http.get(this.getDepartmentCascaderListApi, params);
  }
  /**
   * 获取图标
   * @param params
   * @returns
   */
  public static getIconsByModuleType(params?: any) {
    return http.get(this.getIconsByModuleTypeApi, params);
  }
  /**
   * 获取职务选择器数据
   * @param params
   * @param params.departmentID 部门ID
   * @returns
   */
  public static getDepartmentToJobs(params?: any) {
    return http.get(this.getDepartmentToJobsApi, params);
  }
  /**
   * 模糊查询人员信息
   * @param params
   * @returns
   */
  public static getEmployeeDataByName(params?: any) {
    return http.get(this.getEmployeeDataByNameApi, params);
  }
  /**
   * 模糊查询人员信息
   * @param params
   * @returns
   */
  public static getEmployeeDataByIDs(params?: any) {
    return http.get(this.getEmployeeDataByIDsApi, params) as Promise<Record<string, any>[]>;
  }
  /**
   * 获取部门名称
   * @param params.departmentID 部门ID
   * @returns
   */
  public static getDepartmentName(params?: any) {
    return http.get(this.getDepartmentNameApi, params);
  }
  /**
   * 获取权限角色清单
   * @returns array
   */
  public static GetAuthorityRoles() {
    return http.get(this.getAuthorityRolesApi, undefined);
  }
  /**
   * 获取部门列表
   * @param params
   * @returns
   */
  public static getDepartmentViewsByOrganizationType(params?: any): Promise<Record<string, any>[]> {
    return http.get(this.getDepartmentViewsByOrganizationTypeApi, params) as Promise<Record<string, any>[]>;
  }
  /**
   * 根据工号获取所拥有权限的科室
   * @param params
   * @returns
   */
  public static getEmployeeDepartment(params?: any) {
    return http.get(this.getEmployeeDepartmentApi, params);
  }
  /**
   * 根据部门ID获取部门岗位
   * @param params
   * @returns
   */
  public static getPostDictByDepartmentID(params?: any) {
    return http.get(this.getPostDictByDepartmentIDApi, params);
  }
  // 获取组件类型列表
  public static getComponentListByType(params: any) {
    return http.get(this.getComponentListByTypeApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取上级片区选项
   * @param params
   * @return
   */
  public static getUpperDeptOptions(params?: any) {
    return http.get(this.getUpperDeptOptionsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取就诊科室选项
   * @param params
   * @return
   */
  public static getVisitsDeptOptions(params?: any) {
    return http.get(this.getVisitsDeptOptionsApi, params, { loadingText: Loading.LOAD });
  }
}
