<!--
 * FilePath     : \src\views\examineManagement\components\examinationLevelSelector.vue
 * Author       : 苏军志
 * Date         : 2025-05-07 10:16
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-07 10:22
 * Description  : 考核级别选择器
 * CodeIterationRecord:
 -->

<template>
  <div class="examination-level-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="levelID"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "@/components/selector/hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "考核级别"
  },
  modelValue: {
    type: String
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 140
  },
  list: {
    type: Array as PropType<Array<Record<any, any>>>,
    default: () => undefined
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let levelID = useVModel(props, "modelValue", emits);

let levelOptions = ref<Array<Record<any, any>>>([]);
if (props.list?.length) {
  levelOptions.value = props.list;
} else {
  const params: SettingDictionaryParams = {
    settingType: "ExaminationManagement",
    settingTypeCode: "ExaminationRecord",
    settingTypeValue: "ExaminationLevel",
    index: Math.random()
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    levelOptions.value = datas;
  });
}
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, levelOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.examination-level-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
