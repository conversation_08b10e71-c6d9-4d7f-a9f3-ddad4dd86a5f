<!--
 * FilePath     : /src/components/selector/advancedEmployeeSelector.vue
 * Author       : 杨欣欣
 * Date         : 2025-06-18 16:59
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 16:11
 * Description  : 高级用户选择器
 * CodeIterationRecord:
 -->
<template>
  <div class="advanced-employee-selector">
    <div class="source-panel">
      <el-tabs v-model="activeTab" class="custom-tabs">
        <el-tab-pane label="用户组" name="groups">
          <div class="tab-content">
            <div class="tab-search">
              <el-input v-model="groupSearchKeyword" placeholder="搜索用户组" clearable></el-input>
            </div>
            <div class="list-container">
              <div v-for="group in displayEmployeeGroups" :key="group.id" class="list-item" @click="selectGroup(group)">
                <div class="item-content">
                  <i class="iconfont icon-employees"></i>
                  <span class="item-name">{{ group.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="用户" name="employees">
          <div class="tab-search">
            <el-input v-model="employeeSearchKeyWord" placeholder="搜索姓名、简拼" clearable></el-input>
          </div>
          <div class="list-container">
            <div
              v-for="employee in displayEmployees"
              :key="employee.id"
              class="list-item"
              @click="selectEmployee(employee)"
            >
              <div class="item-content">
                <i class="iconfont icon-login-user"></i>
                <span class="item-name">{{ employee.name }}</span>
              </div>
            </div>
            <div v-if="displayEmployees.length === 0" class="empty-tip">未找到匹配的用户</div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="selected-panel">
      <div class="panel-header">
        <span>已选列表</span>
      </div>
      <div class="panel-content">
        <el-tree :data="selectData" :props="treeProps">
          <template #default="{ node, data }">
            <div class="selected-node">
              <div class="node-content">
                <i class="iconfont" :class="[isEmployeeGroup(data) ? 'icon-employees' : 'icon-login-user']" />
                <span class="node-name">{{ data.name }}</span>
                <span v-if="isEmployeeGroup(data)" class="member-count"> ({{ data.members.length }}位成员) </span>
              </div>
              <el-tooltip content="移除" placement="right" v-if="node.level === 1">
                <i class="iconfont icon-del-img remove-btn" @click.stop="removeSelectedItem(data)"></i>
              </el-tooltip>
            </div>
          </template>
          <template #empty>
            <span>请从左侧选择用户组或用户</span>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import type { initialEmployee, initialEmployeeGroup, selectedEmployeeOrGroup } from "@/types/advancedEmployeeSelectorTypes";
// eslint-disable-next-line no-spaced-func
const { data = [] } = defineProps<{
  data?: (initialEmployee | initialEmployeeGroup)[];
}>();

onMounted(async () => {
  const [groups, employees] = await Promise.all([
    employeeGroupService.getEmployeeGroupsAsOptions(),
    employeeService.getEmployeesAsOptions()
  ]);
  employeeGroups.value = groups;
  Object.entries(employees).forEach(([key, value]) => employeeKeyPairs.value.set(key, value));
  setSelectedPanelData();
});
watch(
  () => data,
  () => {
    setSelectedPanelData();
  }
);

const isEmployeeGroup = (item: EmployeeGroupAsOption | EmployeeAsOption): item is EmployeeGroupAsOption => "members" in item;
//#region 待选区
type tabName = "groups" | "employees";
const activeTab = ref<tabName>("groups");
const employeeSearchKeyWord = ref<string>("");
const employeeKeyPairs = ref<Map<string, EmployeeAsOption>>(new Map());
const displayEmployees = computed(() => {
  const employees: EmployeeAsOption[] = [];
  const keyword = employeeSearchKeyWord.value.toLowerCase();
  const predicate = (employee: EmployeeAsOption) => {
    const notSelected = !allSelectedEmployeeIds.value.has(employee.id);
    const matchesKeyword = employeeSearchKeyWord.value
      ? employee.name.toLowerCase().includes(keyword) || employee.namePinyin?.toLowerCase().startsWith(keyword)
      : true;
    return notSelected && matchesKeyword;
  };
  employeeKeyPairs.value.forEach((employee) => predicate(employee) && employees.push(employee));
  return employees;
});
const groupSearchKeyword = ref<string>("");
const employeeGroups = ref<EmployeeGroupAsOption[]>([]);
const displayEmployeeGroups = computed(() => {
  const keyword = groupSearchKeyword.value.toLowerCase();
  const predicate = (group: EmployeeGroupAsOption) => {
    const notSelected = !allSelectedGroupIds.value.has(group.id);
    const matchesKeyword = groupSearchKeyword.value ? group.name.toLowerCase().includes(keyword) : true;
    return notSelected && matchesKeyword;
  };
  return employeeGroups.value.filter(predicate);
});
const emits = defineEmits(["change", "select"]);
/**
 * @description: 选择分组
 * @param group 被选择分组
 * @return
 */
const selectGroup = (group: EmployeeGroupAsOption) => {
  if (selectData.value.some((item) => isEmployeeGroup(item) && item.id === group.id)) {
    showMessage("error", "该用户组已被选择，无法重复选择");
    return;
  }
  const selectedGroup = common.clone(group);
  selectedGroup.members = selectedGroup.members.filter((member) => {
    if (allSelectedEmployeeIds.value.has(member.id)) {
      return false;
    }
    return true;
  });
  if (selectedGroup.members.length === 0) {
    showMessage("error", "该用户组所有成员均已被选择，无法选择此用户组");
    return;
  }
  if (selectedGroup.members.length !== group.members.length) {
    showMessage("warning", "部分成员已被选择，将不在此组中显示");
  }
  selectData.value.push(selectedGroup);
  emits("select", "group", readonly(selectedGroup));
  emits("change", readonly(selectData));
};
/**
 * @description: 选择成员
 * @param employee 被选择成员
 * @return
 */
const selectEmployee = (employee: EmployeeAsOption) => {
  if (allSelectedEmployeeIds.value.has(employee.id)) {
    showMessage("error", "该用户已被选择，不能重复选择");
    return;
  }
  selectData.value.push(common.clone(employee));
  emits("select", "employee", readonly(employee));
  emits("change", readonly(selectData));
};
//#endregion

//#region 已选区
const setSelectedPanelData = () => {
  selectData.value = data.map((item) => {
    if ("members" in item) {
      return {
        id: item.id,
        name: item.name,
        members: item.members.map((member) => ({
          id: member.id,
          name: employeeKeyPairs.value.has(member.id) ? employeeKeyPairs.value.get(member.id)!.name : ""
        }))
      };
    }
    return {
      id: item.id,
      name: employeeKeyPairs.value.has(item.id) ? employeeKeyPairs.value.get(item.id)!.name : ""
    };
  });
};
const treeProps = {
  label: "name",
  children: "members"
};
const allSelectedEmployeeIds = computed(() => {
  const employeeIds = new Set<string>();
  selectData.value.forEach((element) => {
    if (isEmployeeGroup(element)) {
      element.members.forEach((member) => employeeIds.add(member.id));
      return;
    }
    employeeIds.add(element.id);
  });
  return employeeIds;
});
const allSelectedGroupIds = computed(() => {
  return selectData.value.reduce((groupIds, current) => {
    if (isEmployeeGroup(current)) {
      groupIds.add(current.id);
    }
    return groupIds;
  }, new Set<number>());
});
const selectData = ref<(EmployeeGroupAsOption | EmployeeAsOption)[]>([]);
/**
 * @description: 移出成员
 * @param data 被移除的成员或用户组
 * @return
 */
const removeSelectedItem = (data: EmployeeGroupAsOption | EmployeeAsOption) => {
  const index = selectData.value.findIndex((item) => item === data);
  if (index === -1) {
    showMessage("error", "未找到要移除的成员");
    return;
  }
  selectData.value.splice(index, 1);
};
//#endregion

//#region 暴露状态
defineExpose({
  /**
   * @description: 获取已选择的用户和用户组
   */
  getSelectedData: () => {
    const selectedList: selectedEmployeeOrGroup[] = [];
    selectData.value.forEach((item) => {
      if (isEmployeeGroup(item)) {
        item.members.forEach((member) =>
          selectedList.push({
            groupID: item.id,
            groupName: item.name,
            employeeID: member.id,
            employeeName: member.name
          })
        );
        return;
      }
      selectedList.push({
        employeeID: item.id,
        employeeName: item.name
      });
    });
    return selectedList;
  },
  /**
   * @description: 清空选择
   */
  clearSelect() {
    selectData.value = [];
  }
});
//#endregion
</script>
<style scoped lang="scss">
.advanced-employee-selector {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 20px;
  background-color: #fff;
}
.source-panel {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  :deep(.custom-tabs) {
    height: 100%;
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__content {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
  .tab-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .tab-search {
    padding: 8px;
    border-bottom: 1px solid #e4e7ed;
  }
  .list-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }
  .list-item {
    padding: 4px 12px;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid #ebeef5;
    &:hover {
      background-color: lighten(#08c979, 40%);
    }
    .item-content {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
    }
    .iconfont {
      color: #06985b;
    }
    .item-name {
      flex-grow: 1;
      font-weight: 500;
    }
  }
  .empty-tip {
    text-align: center;
    color: #909399;
    font-size: 16px;
  }
}
.selected-panel {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  .panel-header {
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    :deep(.el-tree) .el-tree-node__content {
      height: 32px;
    }
    :deep(.el-tree-node__content:hover) {
      background-color: lighten(#08c979, 40%);
    }
  }
  .selected-node {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
  .node-name {
    margin-left: 8px;
  }
  .member-count {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  .node-content {
    display: flex;
    align-items: center;
    .iconfont {
      color: #40f7ab;
      margin-right: 8px;
    }
  }
  .remove-btn {
    opacity: 0;
  }
  .selected-node:hover .remove-btn {
    opacity: 1;
  }
}
</style>
