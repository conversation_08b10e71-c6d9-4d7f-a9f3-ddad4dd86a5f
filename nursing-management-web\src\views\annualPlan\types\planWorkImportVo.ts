/*
 * FilePath     : /src/views/annualPlan/types/planWorkImportVo.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-27 19:58
 * Description  : 月度计划工作导入视图类型定义
 * CodeIterationRecord: 
 */
import type { annualPrincipal, workType } from "./common";

/**
 * 计划工作导入 - 工作项
 */
export interface work {
  /**
   * 主键
   */
  key: string;
  /**
   * 参考执行项目ID
   */
  apInterventionID?: number | undefined;
  /**
   * 参考执行项目名称
   */
  apInterventionLocalShowName: string;
  /**
   * 分类字典ID
   */
  typeID: number;
  /**
   * 序号
   */
  sort?: number | undefined;
  /**
   * 月度工作内容
   */
  workContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作类型
   */
  workType: workType;
  /**
   * 临时性工作标记
   */
  isTemp: boolean;
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人详细信息
   */
  principals: annualPrincipal[];
  /**
   * 计划执行月份
   */
  planMonths: number[];
}

/**
 * 计划工作导入 - 类型分组
 */
export interface typeGroup {
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 分类名称
   */
  typeName: string;
  /**
   * 工作集合
   */
  children: work[];
}

/**
 * 计划工作导入视图对象
 */
export interface planWorkImportVo {
  /**
   * 部门ID
   */
  departmentID: number;
  /**
   * 是否为委员会
   */
  isCommittee: boolean;
  /**
   * 计划名称
   */
  planName: string;
  /**
   * 分类集合
   */
  children: typeGroup[];
}