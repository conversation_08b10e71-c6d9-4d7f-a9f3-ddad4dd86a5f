<!--
 * FilePath     : \src\views\scheduling\components\schedulingTemplateUniver.vue
  * Author       : 苏军志
  * Date         : 2024-09-28 15:13
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-22 16:59
  * Description  : 排班模板
  * CodeIterationRecord:
-->
<template>
  <div class="scheduling-template-univer" ref="schedulingTemplate">
    <univer-sheet ref="univerSheetRef" v-if="templateData" :data="templateData" :config="config"></univer-sheet>
  </div>
</template>

<script setup lang="ts">
// eslint-disable-next-line id-match
import type { IWorkbookData } from "@univerjs/core";
// eslint-disable-next-line id-match
import type { FWorksheet } from "@univerjs/facade";
import { useSchedulingUniverUtil } from "../hooks/useSchedulingUniverUtil";
import type { configType } from "@/components/univerSheet/types/configType";
import { menuEnum } from "@/components/univerSheet/types/menuEnum";
import { useUniverSheetTemplate } from "../hooks/useUniverSheetTemplate";
const props = defineProps({
  /**
   * 参数
   */
  params: {
    type: Object as PropType<Record<string, any>>,
    required: true
  }
});
const { selectionMode, schedulingTemplateRecordID, noonList, departmentPostList, shiftSchedulingMarkList } = props.params;
const convertPX: any = inject("convertPX");
const schedulingTemplateTable = ref<TableView>({ rows: [], columns: [] });
const headerRowCount: number = 1;
const initConfig: configType = {
  showFormula: false,
  showFooter: false,
  headerRowCount: headerRowCount,
  headerFontColor: "#000000",
  headerBackgroundColor: "#ffef99",
  showMenuList: [
    menuEnum.UNDO,
    menuEnum.REDO,
    // menuEnum.COPY,
    // menuEnum.PASTE,
    menuEnum.CLEAR_CONTENT,
    menuEnum.CELL_INSERT,
    menuEnum.MENU_DELETE,
    menuEnum.INSERT_ROW,
    menuEnum.REMOVE_ROW
  ],
  onColumnChange: () => columnChange(),
  onClearCell: (sheet: FWorksheet) => sheet?.getActiveRange()?.setBackgroundColor("#ffffff"),
  onBeforeCellDataChange: () => true
};
// 选择模式
if (selectionMode) {
  initConfig.showMenuList = [];
  initConfig.showHeader = false;
  initConfig.onColumnChange = undefined;
}
const { initUniverConfig, tableConvertCellData } = useSchedulingUniverUtil(initConfig, schedulingTemplateTable, true);
// univer配置
const config = ref<Record<string, any>>({});
const maxColumnCount: number = 31;
onMounted(async () => {
  const configParams = {
    maxColumnCount: maxColumnCount,
    noonList: noonList,
    departmentPostList: departmentPostList,
    shiftSchedulingMarkList: shiftSchedulingMarkList
  };
  // 初始化Univer表格配置参数
  config.value = initUniverConfig(configParams);
  nextTick(async () => await getTemplateData());
});
const schedulingTemplate = ref<any>();
const univerSheetRef = ref<any>();
const templateData = ref<IWorkbookData>();
/**
 * @description: 获取排班模板数据
 * @param schedulingTemplateRecordID
 * @return
 */
const getTemplateData = async () => {
  // univerSheet模板需要的参数
  let params: Record<string, any> = {
    rowCount: 3,
    columnCount: 7,
    maxColumnCount: maxColumnCount,
    headerRowCount: initConfig.headerRowCount,
    defaultRowHeight: convertPX(70),
    defaultColumnWidth: convertPX(75),
    headerBackgroundColor: initConfig.headerBackgroundColor,
    headerFontColor: initConfig.headerFontColor,
    cellData: undefined
  };
  // 选择模式
  if (selectionMode) {
    // params.hiddenRowHeader = 1;
    params.hiddenColumnHeader = 1;
  }
  // 新增取默认模板
  if (schedulingTemplateRecordID) {
    // 修改从数据库中获取模板
    await schedulingTemplateService.getTemplateData({ schedulingTemplateRecordID }).then((result: any) => {
      if (result) {
        params.rowCount = result.rowCount;
        params.columnCount = result.columnCount;
        schedulingTemplateTable.value = result.schedulingTemplateTable;
        params.cellData = tableConvertCellData(result.rowCount, result.columnCount, noonList);
      }
    });
  }
  const univerSheetWidth = schedulingTemplate.value.clientWidth;
  // 动态获取表格宽度/表格最大数量31，这里34是为了消除滚动条
  const columnWidth = univerSheetWidth / 34;
  params.defaultColumnWidth = columnWidth < 60 ? 60 : columnWidth;
  templateData.value = useUniverSheetTemplate().getSchedulingTemplateSheet(params);
};
/**
 * @description: 清空内容时把背景色也置为初始颜色
 * @param sheet 当前工作簿
 * @return
 */
const columnChange = () => {
  const sheet = univerSheetRef.value.univerAPI.getActiveWorkbook().getActiveSheet();
  const columnCount = sheet.getMaxColumns();
  const range = sheet.getRange(0, 0, headerRowCount, columnCount + 1);
  let values = [];
  for (let rowIndex = 0; rowIndex < headerRowCount; rowIndex++) {
    let rowValues = [];
    for (let columnIndex = 1; columnIndex <= columnCount; columnIndex++) {
      rowValues.push({ v: columnIndex.toString() });
    }
    values.push(rowValues);
  }
  range.setValues(values);
};
//#endregion
/**
 * @description: 向父组件暴漏方法
 */
defineExpose({
  getSheetData() {
    const { cellDatas, rowCount, columnCount } = univerSheetRef.value.getSheetData();
    // 非选择模式，返回全部数据
    if (!selectionMode) {
      return { cellDatas, rowCount, columnCount };
    }
    // 选择模式 返回选择的区域数据
    const range = univerSheetRef.value.univerAPI?.getActiveWorkbook().getActiveSheet().getActiveRange();
    let ranges: Record<string, number> = {
      startRow: headerRowCount,
      startColumn: 0,
      endRow: rowCount - headerRowCount,
      endColumn: columnCount - 1
    };
    if (range) {
      ranges = range.getRange();
    }
    let newCellDatas: Record<number, Record<number, any>> = {};
    for (let rowIndex = ranges.startRow; rowIndex <= ranges.endRow; rowIndex++) {
      let rowData: Record<number, any> = {};
      for (let columnIndex = ranges.startColumn; columnIndex <= ranges.endColumn; columnIndex++) {
        rowData[columnIndex - ranges.startColumn] = cellDatas[rowIndex][columnIndex];
      }
      newCellDatas[rowIndex - ranges.startRow] = rowData;
    }
    return newCellDatas;
  }
});
</script>
<style lang="scss">
.scheduling-template-univer {
  height: 100%;
  width: 100%;
}
</style>
