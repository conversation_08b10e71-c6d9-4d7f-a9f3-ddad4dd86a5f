<!--
 * FilePath     : \src\views\messageManagement\index.vue
 * Author       : 张现忠
 * Date         : 2024-10-19 11:25
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-29 19:18
 * Description  : 消息管理页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="message-record" :drawerOptions="drawerOptions">
    <template #header>
      <message-type-selector
        v-if="!isSystemNoticePage"
        v-model="messageType"
        :list="messageTypeList"
        @change="(type: string)=>{changeMessageType(type); getMessageList()}"
      />
      <department-selector
        v-if="!isSystemNoticePage"
        v-model="departmentIDs"
        :props="{ expandTrigger: 'hover', multiple: true }"
        :width="245"
        label="部门"
        :employeeID="userStore.employeeID"
        :disabled="messageType === focusOfWorkType"
        showWholeHospital
        @change="getMessageList"
      ></department-selector>
      <el-button class="add-button" @click="showAddDialog(isSystemNoticePage)" v-permission:B="1">新增</el-button>
    </template>
    <el-table :data="messageList" height="100%" highlight-current-row stripe border>
      <el-table-column v-if="!isSystemNoticePage" prop="messageTypeDescription" label="消息类型" :width="convertPX(120)" align="center" />
      <el-table-column prop="messageTitle" label="消息标题" :min-width="convertPX(200)" />
      <el-table-column prop="statusDescription" label="状态" :width="convertPX(100)" align="center" />
      <el-table-column prop="publishTime" label="发布时间" :width="convertPX(160)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.publishTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="isTop" label="是否置顶" :width="convertPX(110)" align="center">
        <template #default="{ row }">
          <span class="is-top"> {{ row.isTop ? "√" : "" }}</span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(110)" label="置顶天数" prop="topDays" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" label="新增人" prop="addEmployeeName" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="新增时间" prop="addDateTime" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(160)" label="最后修改时间" prop="modifyDateTime" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(190)" class="">
        <template #default="{ row }">
          <el-tooltip content="查看">
            <i class="iconfont icon-preview" @click="showEditDialog(row, 'preview')"></i>
          </el-tooltip>
          <!-- 非本人只能查看 -->
          <template v-if="row.addEmployeeID === userStore.employeeID">
            <!-- 系统公告不能在消息发布页面编辑 -->
            <el-tooltip content="编辑" v-if="!(!isSystemNoticePage && messageType === systemMessageType)">
              <i v-permission:B="3" class="iconfont icon-edit" @click="showEditDialog(row)"></i>
            </el-tooltip>
            <el-tooltip content="发送系统通知" v-if="isSystemNoticePage">
              <i
                v-permission:B="35"
                v-visibilityHidden="row.messageStatus !== '1'"
                class="iconfont icon-message"
                @click="showPreUpdateDialog(row.messageRecordID)"
              ></i>
            </el-tooltip>
            <el-tooltip
              :content="row.messageStatus === '1' ? '取消发布' : '发布'"
              v-if="!(!isSystemNoticePage && messageType === systemMessageType)"
            >
              <i
                v-if="row.messageStatus === '1'"
                v-permission:B="27"
                class="iconfont icon-stop"
                v-visibilityHidden="!isSystemNoticePage"
                @click="publishMessage(row.messageRecordID, '0')"
              ></i>
              <i v-else v-permission:B="27" class="iconfont icon-publish" @click="publishMessage(row.messageRecordID, '1')"></i>
            </el-tooltip>
            <el-tooltip content="删除" v-if="!(!isSystemNoticePage && messageType === systemMessageType)">
              <i v-permission:B="4" class="iconfont icon-delete" @click="deleteMessage(row.messageRecordID)"></i>
            </el-tooltip>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName == 'preUpdate'"
        ref="preUpdateRef"
        :model="preUpdateMessage"
        :rules="preUpdateRules"
        label-suffix="："
        label-width="auto"
      >
        <el-form-item label="更新日期" prop="updateDate">
          <el-date-picker
            class="update-time"
            v-model="preUpdateMessage.updateDate"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-time-picker class="update-time" v-model="preUpdateMessage.updateTime" format="hh:mm" value-format="hh:mm" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input class="version" v-model="preUpdateMessage.version" />
        </el-form-item>
        <el-form-item label="更新时长" prop="duration">
          <el-input-number v-model="preUpdateMessage.duration" :min="1" :max="120"> </el-input-number>
          <span class="duration-unit">分钟</span>
        </el-form-item>
        <el-form-item label="消息内容" prop="messageContent">
          <zhy-rich-text-editor v-model="preUpdateMessage.message" :option="{ readOnly: true }" />
        </el-form-item>
      </el-form>
      <el-form v-else :model="editMessage" :rules="rules" ref="submitRefs" label-suffix="：" label-width="auto">
        <el-form-item label="消息类型" prop="messageType">
          <message-type-selector
            label=""
            v-model="editMessage.messageType"
            :list="messageTypeList"
            @change="changeMessageType"
            :disabled="isSystemNoticePage"
            :optionDisableFunc="(item: Record<string, any>) => isSystemNoticePage || item.value === systemMessageType"
            :default="isSystemNoticePage"
          />
        </el-form-item>
        <el-form-item label="接收部门：">
          <department-selector
            v-model="editMessage.departmentIDs"
            :props="{ expandTrigger: 'hover', multiple: true }"
            :width="245"
            label=""
            :disabled="editMessage.departmentDisabled"
            :employeeID="userStore.employeeID"
            showWholeHospital
          ></department-selector>
        </el-form-item>
        <el-form-item label="消息标题" prop="messageTitle">
          <el-input v-model="editMessage.messageTitle"></el-input>
        </el-form-item>
        <el-form-item label="置顶" prop="isTop">
          <el-switch v-model="editMessage.isTop" @change="setDefaultTopDays"></el-switch>
        </el-form-item>
        <el-form-item label="置顶天数" prop="topDays" v-if="editMessage.isTop">
          <el-input-number type="number" :min="1" :max="365" v-model="editMessage.topDays" />
          <span class="end-top-date"> 在 {{ endTopDate }}结束置顶</span>
        </el-form-item>
        <el-form-item label="消息内容" prop="messageContent">
          <zhy-rich-text-editor class="message-content" v-if="drawerOptions.showDrawer" v-model="editMessage.messageContent" />
        </el-form-item>
      </el-form>
    </template>
    <template #drawerButtonAfter v-if="editMessage.messageStatus !== '1'">
      <el-button class="edit-button" v-permission:B="2" @click="drawerOptions.confirm?.('1')">发布</el-button>
    </template>
    <message-preview
      v-model="showMessagePreview"
      :params="{
        messageRecordID: editMessage.messageRecordID || '',
        messageContent: editMessage.messageContent,
        title: editMessage.messageTitle
      }"
    ></message-preview>
  </base-layout>
</template>

<script lang="ts" setup>
import { useMessageHandler } from "./hooks/useMessageHandler";
const route = useRoute();
const router = useRouter();
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
const {
  focusOfWorkType,
  messageList,
  messageType,
  departmentIDs,
  drawerOptions,
  submitRefs,
  editMessage,
  showMessagePreview,
  rules,
  messageTypeList,
  systemMessageType,
  preUpdateMessage,
  preUpdateRef,
  getMessageList,
  showAddDialog,
  showEditDialog,
  deleteMessage,
  publishMessage,
  changeMessageType,
  showPreUpdateMessageDialog
} = useMessageHandler();
// 用来判断是否为系统公告页面
const isSystemNoticePage = computed(() => route.name === "systemUpdateRecord");
messageType.value = isSystemNoticePage.value ? systemMessageType : "";
changeMessageType(messageType.value);
// 消息发布和系统发布页面共用同一个路由地址，因此需要监听路由变化，当两者的路由切换时，保证组件当前页面重新渲染
watch(
  () => route.name,
  (newValue, oldValue) => newValue && oldValue && router.go(0)
);
onMounted(() => {
  getMessageList();
});
/**
 * 设置置顶默认值一天
 */
const setDefaultTopDays = (isTop: boolean) => {
  editMessage.value.topDays = isTop ? 1 : undefined;
};
// 计算置顶结束时间
const endTopDate = computed(() => {
  const publishTime = editMessage.value.publishTime
    ? datetimeUtil.formatDate(editMessage.value.publishTime, "yyyy-MM-dd")
    : datetimeUtil.getNowDate();
  return datetimeUtil.addDate(publishTime + " 23:59", editMessage.value.topDays, "yyyy-MM-dd hh:mm");
});
const preUpdateRules = {
  updateDate: [{ required: true, message: "请选择日期", trigger: "blur" }],
  updateTime: [{ required: true, message: "请选择时间", trigger: "blur" }],
  version: [{ required: true, message: "请输入版本号", trigger: "blur" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }]
};
/**
 * @description: 显示发布更新信息弹窗
 * @param messageRecordID
 * @return
 */
const showPreUpdateDialog = async (messageRecordID: string) => {
  showPreUpdateMessageDialog(messageRecordID);
  preUpdateMessage.value.updateDate = datetimeUtil.getNowDate();
  preUpdateMessage.value.updateTime = datetimeUtil.getNowTime("hh:mm");
  preUpdateMessage.value.duration = 15;
};
watch(
  () => preUpdateMessage,
  () => {
    preUpdateMessage.value.message =
      `系统将于<span style="color: #ff0000"><b>${preUpdateMessage.value.updateDate} ${preUpdateMessage.value.updateTime}</b></span>开始更新<br />` +
      `更新版本号：<span style="color: #ff0000"><b>${preUpdateMessage.value.version ?? "V x.x.x"}</b></span><br />` +
      `预计用时<span style="color: #ff0000"><b>${preUpdateMessage.value.duration}</b></span>分钟<br /><b>更新期间请不要进行任何操作，更新完毕后请重新登入系统。</b>`;
  },
  { deep: true }
);
</script>

<style lang="scss">
.message-record {
  width: 100%;
  height: 100%;

  .message-content {
    min-height: 450px;
    max-height: 450px;
  }

  .icon-message {
    color: #ff0000;
  }

  .is-top {
    color: #ff0000;
    font-size: 22px;
  }

  .end-top-date {
    margin-left: 20px;
    color: $base-color;
  }

  .update-time,
  .version {
    width: 200px;
  }

  .duration-unit {
    margin-left: 10px;
  }
}
</style>
