<!--
 * FilePath     : \src\components\qrCode\index.vue
 * Author       : 胡长攀
 * Date         : 2024-11-18 16:37
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-12-20 16:40
 * Description  : 生成二维码
 * CodeIterationRecord:
 -->
<template>
  <div class="qr-code" ref="qrCodeContainer">
    <vue-qrcode :value="qrCodeText" :width="codeWidth" type="image/png" :color="props.color"></vue-qrcode>
    <span class="qr-code-description" ref="descriptionText">{{ props.codeDescription }}</span>
  </div>
</template>
<script setup lang="ts">
import vueQrcode from "vue-qrcode";
import { useTimer } from "../../hooks/useTimer";
const { startTimer, stopTimer } = useTimer();
const props = defineProps({
  // 二维码类型，扫描后访问对应API
  type: {
    type: String,
    default: ""
  },
  params: {
    type: Object as () => Record<string, any>,
    default: () => {}
  },
  // 二维码跳转页面
  url: {
    type: String,
    default: ""
  },
  color: {
    type: Array as any,
    default: () => []
  },
  width: {
    type: Number || undefined
  },
  codeDescription: {
    type: String,
    default: "请扫码进行签到"
  }
});
// 二维码容器
const qrCodeContainer = ref<HTMLDivElement>();
// 二维码底部文字描述
const descriptionText = ref<HTMLSpanElement>();
// 二维码宽度
const codeWidth = ref<number>(0);
// 二维码内容
const qrCodeText = ref<string>("");
// 获取服务器时间
const qrCodeTimeStamp = ref<number>(new Date().getTime());

onMounted(async () => {
  await createCodeAndUpdateSize();
  if (props.params.qrCodeRefreshTime && props.params.qrCodeRefreshTime > 0) {
    startTimer(props.params.qrCodeRefreshTime * 1000, await createCodeAndUpdateSize);
  }
});

onBeforeUnmount(() => {
  stopTimer();
});

// 监听二维码参数变化
watch([() => props.type, () => props.url, () => props.params], async () => {
  await createCodeAndUpdateSize();
});
/**
 * 创建二维码并更新宽度
 **/
const createCodeAndUpdateSize = async () => {
  await getServerDateTime();
  await createCode();
  nextTick(() => {
    updateCodeWidth();
    updateFontSize();
  });
};
/**
 * 传入高度使用传入的高度
 * 没有传入根据容器高度调整二维码大小
 * */
const updateCodeWidth = () => {
  if (props.width) {
    codeWidth.value = props.width;
    return;
  }
  if (qrCodeContainer.value) {
    codeWidth.value = qrCodeContainer.value.clientHeight * 0.8 * 1.1;
  }
};
/**
 * 根据容器高度调整字体大小
 * */
const updateFontSize = () => {
  if (qrCodeContainer.value && descriptionText.value) {
    descriptionText.value.style.fontSize = `${qrCodeContainer.value.clientHeight * 0.2 * 0.3}px`;
  }
};
/**
 * @description: 生成二维码
 */
const createCode = () => {
  let params: Record<string, any> = {
    type: props.type,
    timeStamp: qrCodeTimeStamp.value,
    url: props.url,
    params: props.params
  };
  qrCodeText.value = JSON.stringify(params);
};
/**
 * @description: 获取系统时间
 */
const getServerDateTime = async () => {
  await userLoginService.getServerDateTime().then((data: any) => {
    if (data) {
      qrCodeTimeStamp.value = new Date(data).getTime();
    }
  });
};
</script>
<style lang="scss">
.qr-code {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  .qr-code-description {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    color: red;
  }
}
</style>
