/*
 * FilePath     : \src\hooks\useTimer.ts
 * Author       : 苏军志
 * Date         : 2023-12-06 18:29
 * LastEditors  : 苏军志
 * LastEditTime : 2024-11-13 18:39
 * CodeIterationRecord: 定时器相关
 */

/**
 * 定时器相关
 */
export function useTimer() {
  let timer: any = undefined;
  const startTimer = (interval: number, callback: Function) => {
    timer = setInterval(() => {
      callback();
    }, interval);
  };
  const stopTimer = () => {
    if (timer) {
      clearInterval(timer);
    }
  };
  // 页面销毁时关闭定时器
  onUnmounted(() => {
    stopTimer();
  });
  return {
    startTimer,
    stopTimer
  };
}
