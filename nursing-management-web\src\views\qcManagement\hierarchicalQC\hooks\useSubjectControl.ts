/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useSubjectControl.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-08 11:02
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-10-31 17:11
 * Description  :主题维护逻辑处理
 * CodeIterationRecord:
 */
import { subjectClass } from "../types/hierarchicalQCSubjectView";
/**
 * description: 主题维护逻辑处理
 * param {any} 获取表格数据
 * param {subjectClass} 主题参数
 * param {Ref} subjectDrawerFlag
 * return {*}
 */
export const subjectControl = (getSubjectData: any, subjectView: subjectClass) => {
  /**
   * 选中字典主题
   */
  let currentQcForm: any = undefined;
  return {
    /**
     * description: 拼接主题名称 字典名称加月份
     * return {*}
     */
    getQcSubjectName(qcForm?: any) {
      qcForm && (currentQcForm = qcForm);
      if (subjectView.hierarchicalQCSubjectID && !subjectView.copyFlag) {
        return;
      }
      if (!subjectView.hierarchicalQCFormID) {
        subjectView.formName = undefined;
        return;
      }
      !subjectView.templateCode && (subjectView.templateCode = currentQcForm.templateCode);
      subjectView.formName = currentQcForm?.formName ?? "";
      if (subjectView.yearMonth && subjectView.yearMonth !== null) {
        subjectView.formName += subjectView.yearMonth ? "（" + subjectView.yearMonth + "）" : "";
      }
    },
    /**
     * description: 默认开始结束区间
     * return {*}
     */
    getFirstAndLastDayOfMonth() {
      const yearMonthArr = subjectView.yearMonth?.split("-");
      if (!yearMonthArr?.length) {
        return undefined;
      }
      const year = Number(yearMonthArr?.[0]);
      const month = Number(yearMonthArr?.[1]);
      subjectView.startDate = datetimeUtil.formatDate(new Date(year, month - 1, 1), "yyyy-MM-dd");
      subjectView.endDate = datetimeUtil.formatDate(new Date(year, month, 0), "yyyy-MM-dd");
    },
    /**
     * description: 主题保存
     * return {*}
     */
    async saveSubjectPlan() {
      await hierarchicalQCService.saveSubjectPlanSave(subjectView).then((res: any) => {
        if (res) {
          showMessage("success", "保存成功");
          return true;
        }
      });
    },

    /**
     * description: 主题删除
     * param {*} item
     * return {*}
     */
    deleteSubject(item: any) {
      confirmBox("确定要删除此主题么？", "主题删除", (flag: Boolean) => {
        if (flag) {
          hierarchicalQCService.deleteSubject({ hierarchicalQCSubjectID: item.hierarchicalQCSubjectID }).then((res: any) => {
            if (res) {
              showMessage("success", "删除成功");
              getSubjectData();
            }
          });
        }
      });
    },
    /**
     * description: 主题文件删除
     * param {*} item
     * return {*}
     */
    deleteQCFile(item: any) {
      confirmBox("确定要删除此报告么？", "报告删除", (flag: Boolean) => {
        if (flag) {
          hierarchicalQCService.deleteQCFile({ subjectID: item.hierarchicalQCSubjectID }).then((res: any) => {
            if (res) {
              showMessage("success", "删除成功");
              getSubjectData();
            }
          });
        }
      });
    }
  };
};
