/*
 * FilePath     : \src\utils\message.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 17:25
 * LastEditors  : 马超
 * LastEditTime : 2025-03-02 11:15
 * Description  : 各种消息弹窗封装
 * CodeIterationRecord:
 */
type MType = keyof typeof MessageType;

/**
 * @description: 提示信息
 * @param type 类型
 * @param message 消息内容
 * @return
 */
export const showMessage = (type: MType, message: string) => {
  ElMessage({
    type: type,
    showClose: true,
    dangerouslyUseHTMLString: true,
    message: message,
    // 合并内容相同的消息，不支持 VNode 类型的消息
    grouping: true,
    offset: 300,
    duration: 2000
  });
};
/**
 * @description: 弹窗
 * @param type 类型
 * @param message 消息内容
 * @param title 消息标题
 * @param btnName 按钮显示文字
 * @return
 */
// export const showAlert = (type: MType, message: string, title?: string, btnName?: string) => {
//   let confirmButtonText = btnName == "是" ? "是" : "确定";
//   let tipTitle = title || "系统提示";
//   return ElMessageBox.alert(message, tipTitle, {
//     type: type,
//     confirmButtonText: confirmButtonText,
//     dangerouslyUseHTMLString: true
//   });
// };
/**
 * @description: 确认弹窗
 * @param message 消息内容
 * @param title 消息标题
 * @param callback 回调函数
 * @param btnName 按钮显示文字
 * @return
 */
export const confirmBox = (message: string, title: string, callback?: Function, btnName?: string) => {
  let confirmButtonText = btnName === "是" ? "是" : "确定";
  let cancelButtonText = btnName === "是" ? "否" : "取消";
  let tipTitle = title || "系统提示";
  return ElMessageBox.confirm(message, tipTitle, {
    type: "warning",
    confirmButtonText: confirmButtonText,
    cancelButtonText: cancelButtonText,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    dangerouslyUseHTMLString: true
  })
    .then(() => {
      callback && callback(true);
    })
    .catch(() => {
      callback && callback(false);
    });
};
/**
 * @description: 删除确认框
 * @param message 消息内容
 * @param callback 回调韩式
 * @return
 */
export const deleteConfirm = (message: string, callback: (flag: boolean) => void) => {
  return ElMessageBox.confirm(message || "确定要删除此记录吗?", "系统提示", {
    type: "error",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    closeOnPressEscape: false,
    dangerouslyUseHTMLString: true
  })
    .then(() => {
      callback(true);
    })
    .catch(() => {
      callback(false);
    });
};
/**
 * @description: 浏览器右下角弹出消息,桌面消息
 * @param message 消息内容
 * @param title 消息标题
 * @param autoCloseTime 自动关闭时间，单位毫秒
 * @param callback 回调函数，单击消息时调用
 * @return
 */
export const showNotification = (message: string, title?: string, autoCloseTime?: number, callback?: Function) => {
  const { isSupported, notification, show, close, onClick, onShow, onError, onClose } = useWebNotification({
    title: title || "",
    body: message,
    dir: "auto",
    tag: "notification"
  });
  isSupported.value && show();
  onShow((event: Event) => {
    if (isSupported.value && autoCloseTime) {
      setTimeout(() => {
        close();
      }, autoCloseTime);
    }
  });
  onClick((event: Event) => {
    if (callback) {
      callback(event);
    }
    close();
  });
};
