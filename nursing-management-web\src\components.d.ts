/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AdvancedEmployeeSelector: typeof import('./components/selector/advancedEmployeeSelector.vue')['default']
    AnnualInterventionMaintain: typeof import('./views/annualPlan/formulate/components/annualInterventionMaintain.vue')['default']
    AnnualInterventionTable: typeof import('./views/annualPlan/components/annualInterventionTable.vue')['default']
    AnnualPlanGoals: typeof import('./views/annualPlan/maintain/components/annualPlanGoals.vue')['default']
    AnnualPlanGroups: typeof import('./views/annualPlan/maintain/components/annualPlanGroups.vue')['default']
    AnnualPlanHeader: typeof import('./views/annualPlan/components/annualPlanHeader.vue')['default']
    AnnualPlanIndicators: typeof import('./views/annualPlan/maintain/components/annualPlanIndicators.vue')['default']
    AnnualPlanPreview: typeof import('./views/annualPlan/components/annualPlanPreview.vue')['default']
    AnnualPlanProjects: typeof import('./views/annualPlan/maintain/components/annualPlanProjects.vue')['default']
    Approve: typeof import('./views/approveManagement/approveRecord/components/approve.vue')['default']
    ApproverSelector: typeof import('./views/approveManagement/approveProcess/components/approverSelector.vue')['default']
    AutoComplete: typeof import('./components/autoComplete.vue')['default']
    BaseLayout: typeof import('./components/baseLayout.vue')['default']
    CapabilityLevelSelector: typeof import('./components/selector/capabilityLevelSelector.vue')['default']
    ConditionInput: typeof import('./components/conditionInput.vue')['default']
    CourseSettingSelector: typeof import('./components/selector/courseSettingSelector.vue')['default']
    CourseTypeSelector: typeof import('./components/selector/courseTypeSelector.vue')['default']
    DepartmentCheckBox: typeof import('./views/qcManagement/hierarchicalQC/components/departmentCheckBox.vue')['default']
    DepartmentPostSelector: typeof import('./components/selector/departmentPostSelector.vue')['default']
    DepartmentSelector: typeof import('./components/selector/departmentSelector.vue')['default']
    DepartmentSwitchCascader: typeof import('./components/selector/departmentSwitchCascader.vue')['default']
    DepartmentSwitchSelector: typeof import('./components/selector/departmentSwitchSelector.vue')['default']
    Drag: typeof import('./components/drag/index.vue')['default']
    DynamicColumn: typeof import('./components/dynamicTable/dynamicColumn.vue')['default']
    DynamicTable: typeof import('./components/dynamicTable/index.vue')['default']
    EditApproveNodeDialog: typeof import('./views/approveManagement/approveProcess/components/editApproveNodeDialog.vue')['default']
    EducationalSelector: typeof import('./components/selector/educationalSelector.vue')['default']
    EmployeeGroupEditor: typeof import('./views/employeeManagement/employeeGroup/components/employeeGroupEditor.vue')['default']
    EmployeeSelector: typeof import('./components/selector/employeeSelector.vue')['default']
    EntryDateSelector: typeof import('./components/selector/entryDateSelector.vue')['default']
    EvaluationForm: typeof import('./views/trainingManagement/components/evaluationForm.vue')['default']
    ExaminationConditionForm: typeof import('./views/examineManagement/components/examinationConditionForm.vue')['default']
    ExaminationLevelSelector: typeof import('./views/examineManagement/components/examinationLevelSelector.vue')['default']
    ExaminationRecordForm: typeof import('./views/examineManagement/components/examinationRecordForm.vue')['default']
    ExaminationTypeRadio: typeof import('./views/examineManagement/components/examinationTypeRadio.vue')['default']
    ExaminationTypeSelector: typeof import('./views/examineManagement/components/examinationTypeSelector.vue')['default']
    ExamineCondition: typeof import('./views/examineManagement/components/examineCondition.vue')['default']
    ExamineStatusCodeSelector: typeof import('./views/examineManagement/components/examineStatusCodeSelector.vue')['default']
    ExportExcel: typeof import('./components/file/exportExcel.vue')['default']
    ExportPdf: typeof import('./components/file/exportPdf.vue')['default']
    ExportWord: typeof import('./components/file/exportWord.vue')['default']
    File: typeof import('./components/file/index.vue')['default']
    FlowDiagram: typeof import('./views/approveManagement/approveProcess/components/flowDiagram.vue')['default']
    FormTemplate: typeof import('./views/qcManagement/hierarchicalQC/components/formTemplate.vue')['default']
    ImgPreview: typeof import('./components/file/imgPreview.vue')['default']
    ImportExcel: typeof import('./components/file/importExcel.vue')['default']
    IndicatorEditDialog: typeof import('./views/annualPlan/maintain/components/indicatorEditDialog.vue')['default']
    ItemDetailList: typeof import('./components/itemDetailList.vue')['default']
    JobCategorySelector: typeof import('./components/selector/jobCategorySelector.vue')['default']
    JobSelector: typeof import('./components/selector/jobSelector.vue')['default']
    MainHeader: typeof import('./views/mainLayout/components/mainHeader.vue')['default']
    MessagePreview: typeof import('./views/messageManagement/components/messagePreview.vue')['default']
    MessageTypeSelector: typeof import('./components/selector/messageTypeSelector.vue')['default']
    MonthlyPlanPreview: typeof import('./views/annualPlan/monthlyPlan/components/monthlyPlanPreview.vue')['default']
    NavMenu: typeof import('./views/mainLayout/components/navMenu.vue')['default']
    NoonSelector: typeof import('./components/selector/noonSelector.vue')['default']
    OperationInstruction: typeof import('./views/scheduling/components/operationInstruction.vue')['default']
    PaperPreview: typeof import('./views/examineManagement/components/paperPreview.vue')['default']
    PartTimeSelector: typeof import('./components/selector/partTimeSelector.vue')['default']
    PostSelector: typeof import('./components/selector/postSelector.vue')['default']
    PrincipalSelector: typeof import('./components/selector/principalSelector.vue')['default']
    PrincipalSelectorDialog: typeof import('./views/annualPlan/components/principalSelectorDialog.vue')['default']
    ProjectEditDialog: typeof import('./views/annualPlan/maintain/components/projectEditDialog.vue')['default']
    ProveCategorySelector: typeof import('./components/selector/proveCategorySelector.vue')['default']
    QcForm: typeof import('./views/qcManagement/hierarchicalQC/components/qcForm.vue')['default']
    QcFormSelector: typeof import('./views/qcManagement/hierarchicalQC/components/qcFormSelector.vue')['default']
    QcFormTypeSelector: typeof import('./views/qcManagement/hierarchicalQC/components/qcFormTypeSelector.vue')['default']
    QcLevelSelector: typeof import('./views/qcManagement/hierarchicalQC/components/qcLevelSelector.vue')['default']
    QcSubjectSelector: typeof import('./views/qcManagement/hierarchicalQC/components/qcSubjectSelector.vue')['default']
    QrCode: typeof import('./components/qrCode/index.vue')['default']
    QuarterPlanPreview: typeof import('./views/annualPlan/quarterPlan/components/quarterPlanPreview.vue')['default']
    QuestionBankSelector: typeof import('./components/selector/questionBankSelector.vue')['default']
    RecordAndMainLayout: typeof import('./components/recordAndMainLayout.vue')['default']
    RefIndicators: typeof import('./views/annualPlan/maintain/components/refIndicators.vue')['default']
    RevokeApproval: typeof import('./views/approveManagement/approveRecord/components/revokeApproval.vue')['default']
    RoleSelector: typeof import('./components/selector/roleSelector.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SchedulingExplanationInfo: typeof import('./views/scheduling/components/schedulingExplanationInfo.vue')['default']
    SchedulingMonthlyStatistics: typeof import('./views/scheduling/components/schedulingMonthlyStatistics.vue')['default']
    SchedulingPrintTable: typeof import('./views/scheduling/components/schedulingPrintTable.vue')['default']
    SchedulingTable: typeof import('./views/scheduling/components/schedulingTable.vue')['default']
    SchedulingTemplateUniver: typeof import('./views/scheduling/components/schedulingTemplateUniver.vue')['default']
    SettingDictionaryMaintain: typeof import('./components/settingDictionaryMaintain/index.vue')['default']
    SettingDictionarySelector: typeof import('./components/selector/settingDictionarySelector.vue')['default']
    SettingDictionaryTypeSelector: typeof import('./components/selector/settingDictionaryTypeSelector.vue')['default']
    SubjectAssign: typeof import('./views/qcManagement/hierarchicalQC/components/subjectAssign.vue')['default']
    Tag: typeof import('./components/tag.vue')['default']
    TitleSelector: typeof import('./components/selector/titleSelector.vue')['default']
    TopMenu: typeof import('./components/topMenu.vue')['default']
    TrainingClassSelector: typeof import('./views/trainingManagement/components/trainingClassSelector.vue')['default']
    Transfer: typeof import('./components/transfer/index.vue')['default']
    TransferPanel: typeof import('./components/transfer/transferPanel.vue')['default']
    TruncatedToolTip: typeof import('./components/truncatedToolTip.vue')['default']
    UniverSheet: typeof import('./components/univerSheet/index.vue')['default']
    UploadFile: typeof import('./components/file/uploadFile.vue')['default']
    WorkBatchImport: typeof import('./views/annualPlan/components/workBatchImport.vue')['default']
    WorkBatchImportDialog: typeof import('./views/annualPlan/components/workBatchImportDialog.vue')['default']
    WorkCard: typeof import('./views/annualPlan/components/workCard.vue')['default']
    WorkQuoteDialog: typeof import('./views/annualPlan/components/workQuoteDialog.vue')['default']
    WorkSelector: typeof import('./views/annualPlan/components/workSelector.vue')['default']
    WorkTable: typeof import('./views/annualPlan/components/workTable.vue')['default']
  }
}
