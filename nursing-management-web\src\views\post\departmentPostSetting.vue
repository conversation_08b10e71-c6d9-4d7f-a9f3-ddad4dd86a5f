<!--
 * FilePath     : \src\views\post\departmentPostSetting.vue
 * Author       : 张现忠
 * Date         : 2023-08-16 08:21
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:35
 * Description  : 部门岗位设定表页面
 * CodeIterationRecord:
  2023-08-17 3653-作为IT人员，我需要开部门岗位/能级对照、部门岗位设定，以利护理管理系统推进（14）-zxz
-->
<template>
  <div class="department-post-setting">
    <base-layout class="department-post-setting" :drawerOptions="drawerOptions">
      <template #header>
        <File class="right-file" :fileOption="fileOption" @getExcelData="getExcelData"></File>
        <el-button @click="addRecord" class="add-button" v-permission:B="1">{{ i18nText.add }}</el-button>
      </template>
      <el-table :data="departmentPostSettings" stripe border height="100%" :span-method="tableRowSpanMethod">
        <el-table-column prop="postName" :label="i18nText.postName" :min-width="convertPX(150)" align="center"></el-table-column>
        <el-table-column prop="typeDesc" :label="i18nText.type" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="expectedCount" :label="i18nText.expectedCount" :min-width="convertPX(120)" align="center"></el-table-column>
        <el-table-column prop="publishDate" :label="i18nText.publishDate" :min-width="convertPX(140)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.publishDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="addPerson" :label="i18nText.addPerson" :min-width="convertPX(150)" align="center"></el-table-column>
        <el-table-column :label="i18nText.addDateTime" :min-width="convertPX(180)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="modifyPerson" :label="i18nText.modifyPerson" :min-width="convertPX(150)" align="center"></el-table-column>
        <el-table-column :label="i18nText.modifyDateTime" :min-width="convertPX(180)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
        <el-table-column :label="i18nText.operation" :width="convertPX(80)">
          <template #default="scope">
            <el-tooltip :content="i18nText.edit">
              <i @click="editRow(scope.row)" class="iconfont icon-edit" v-permission:B="3"></i>
            </el-tooltip>
            <el-tooltip :content="i18nText.delete">
              <i @click="deleteRow(scope.row)" class="iconfont icon-delete" v-permission:B="4"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <template #drawerContent>
        <el-form ref="submitRefs" :model="currRow" :label-width="convertPX(170)" class="form-style" :rules="rules">
          <el-form-item :label="i18nText.department" prop="departmentID">
            <department-selector v-model="currRow.departmentID" :disabled="disableType" label="" :width="386"></department-selector>
          </el-form-item>
          <el-form-item :label="i18nText.postName" prop="postID">
            <post-selector label="" v-model="currRow.postID" :disabled="disableType" :width="386"></post-selector>
          </el-form-item>
          <el-form-item :label="i18nText.type" prop="type">
            <el-select v-model="currRow.type" :disabled="disableType" class="type-select">
              <template v-for="(typeItem, index) in typeDict" :key="index">
                <el-option :label="typeItem.label" :value="typeItem.value"></el-option>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item :label="i18nText.expectedCount" prop="expectedCount">
            <el-input-number
              :min="1"
              :placeholder="i18nText.expectedCountPlaceholder"
              v-model.number="currRow.expectedCount"
              class="expected-count-input"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="i18nText.publishDate" prop="publishDate">
            <el-date-picker v-model="currRow.publishDate" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-form>
      </template>
    </base-layout>
  </div>
</template>
<script setup lang="ts">
const { userStore } = useStore() as any;
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
// #region 前端响应式变量
const submitRefs = ref<any>();
// 类型不可编辑
const disableType = ref<boolean>(false);
let typeDict = ref<Record<any, any>[]>([]);
let currRow = ref<Record<string, any>>({});
let departmentPostSettings = ref([{}] as any);
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    await saveRecord(currRow.value);
  }
});
let rules = reactive({
  departmentID: [{ required: true, message: "请选择部门科室", trigger: "change" }],
  postID: [{ required: true, message: "请选择岗位", trigger: "change" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  expectedCount: [{ required: true, message: "请输入期望岗位数量", trigger: "change" }],
  publishDate: [{ required: true, message: "请选择发布时间", trigger: "change" }]
});
let templateColumn = ref<{}>({
  postID: "岗位编码",
  postName: "岗位名称",
  typeID: "类型编码",
  typeDesc: "类型",
  expectedCount: "期望岗位数量",
  publishDate: "发布日期"
});
let exportExcelData = ref<any>([]);
let postDictionary = ref<any>([]);
let typeDictionary = ref<any>([]);

// 通过hooks从数据库获取数据
let { getPostData } = useDictionaryData();
// #endregion

// #region 前端事件触发数据处理
onMounted(() => {
  getDepartmentPostSetting(userStore.departmentID);
  getTypeDict();
});
/**
 * @description: 获取需要的字典数据
 */
const getTypeDict = () => {
  const param: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "DepartmentPostSetting",
    settingTypeValue: "Type"
  };
  settingDictionaryService.getSettingDictionaryDict(param).then((resultDatas: any) => {
    resultDatas.forEach((result: any) => {
      typeDictionary.value.push({
        typeID: result.value,
        typeName: result.label
      });
    });
    typeDict.value = resultDatas;
  });
};
/**
 * @description: 新增记录
 */
const addRecord = () => {
  currRow.value = { departmentID: userStore.departmentID };
  drawerOptions.value.drawerTitle = i18nText.value.add;
  drawerOptions.value.showDrawer = true;
  disableType.value = false;
};
/**
 * @description: 编辑当前行
 * @param row
 */
const editRow = (row: any) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  currRow.value = common.clone(row);
  currRow.value.departmentID = parseInt(currRow.value.departmentID) ?? currRow.value.departmentID;
  drawerOptions.value.drawerTitle = i18nText.value.edit;
  drawerOptions.value.showDrawer = true;
  disableType.value = true;
};

// #endregion

// #region 后端交互逻辑-新增、修改、删除、获取
/**
 * @description: 获取部门岗位设定记录
 * @param deptID 选择器选中的科室:区分响应式的departmentID
 */
const getDepartmentPostSetting = (deptID?: Number) => {
  let param = { departmentID: deptID };
  postService.getDepartmentPostSetting(param).then((respDatas: any) => {
    if (respDatas) {
      departmentPostSettings.value = respDatas;
      // 合并行
      setTableRowSpanArr(departmentPostSettings.value, ["postName"]);
    } else {
      departmentPostSettings.value = undefined;
    }
  });
};
/**
 * @description: 保存记录
 * @param params
 */
const saveRecord = async (params: any) => {
  await postService.saveDepartmentPostSetting(params).then((respFlag: any) => {
    if (respFlag) {
      getDepartmentPostSetting(params.departmentID);
    } else {
      showMessage("error", "保存部门岗位设定配置失败!");
    }
    drawerOptions.value.showDrawer = false;
  });
};
import { useUtils } from "../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 删除当前行
 * @param row
 */
const deleteRow = async (row: any) => {
  if (!row?.postID || !row?.departmentID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  let confirm = await showAlert("warning", i18nText.value.deleteConfirm, "删除部门岗位设定记录");
  if (!confirm) {
    return;
  }
  let params = {
    postID: row.postID,
    departmentID: row.departmentID,
    type: row.type
  };
  postService.deleteDepartmentPostSetting(params).then((respDatas: any) => {
    if (respDatas) {
      showMessage("success", "删除成功");
    }
    getDepartmentPostSetting(row.departmentID);
  });
};
/**
 * @description:  Excel 导入参数
 */
const importExcelOption = reactive<ImportExcelView>({
  columnData: templateColumn,
  buttonName: "导入数据"
});
/**
 * @description:  Excel 导出参数
 */
const exportExcelOption = reactive<ExportExcelView[]>([
  {
    buttonName: "导出模板",
    fileName: "部门岗位设定",
    sheetName: "部门岗位设定",
    columnData: templateColumn,
    tableData: exportExcelData.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位设定",
    sheetName: "岗位字典",
    columnData: {
      postID: "岗位编码",
      departmentPostName: "岗位名称"
    },
    tableData: postDictionary.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位设定",
    sheetName: "类型字典",
    columnData: {
      typeID: "类型编码",
      typeName: "类型名称"
    },
    tableData: typeDictionary.value
  }
]);
/**
 * @description: file组件参数
 */
const fileOption = reactive<FilePropsView>({
  typeArr: ["exportExcel", "importExcel"],
  exportExcelOption,
  importExcelOption
});
/**
 * @description: Excel导入数据处理
 */
const getExcelData = async (importData: any) => {
  if (importData.length === 0) {
    return;
  }
  let params: any[] = [];
  importData.forEach((importItem: any) => {
    let param = {
      departmentID: userStore.departmentID,
      expectedCount: importItem.expectedCount,
      postID: importItem.postID,
      publishDate: datetimeUtil.formatDate(importData[0].publishDate),
      type: importItem.typeID
    };
    params.push(param);
  });
  await postService.batchSaveDepartmentPostSetting(params).then(() => {
    getDepartmentPostSetting(userStore.departmentID);
  });
};
/**
 * @description: 获取字典数据
 */
getPostData().then((datas) => {
  datas.forEach((post) => {
    postDictionary.value.push({
      postID: post.value,
      departmentPostName: post.label
    });
  });
});
// #endregion

// #region 多语言处理
const i18nText = computed(() => {
  return {
    add: proxy.$t("button.add"),
    delete: proxy.$t("tip.delete"),
    edit: proxy.$t("tip.edit"),
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    department: proxy.$t("label.department"),
    post: proxy.$t("departmentPostSetting.post"),
    type: proxy.$t("departmentPostSetting.type"),
    expectedCount: proxy.$t("departmentPostSetting.expectedCount"),
    publishDate: proxy.$t("departmentPostSetting.publishDate"),
    postID: proxy.$t("departmentPostSetting.postID"),
    postName: proxy.$t("departmentPostSetting.postName"),
    addPerson: proxy.$t("departmentPostSetting.addPerson"),
    addDateTime: proxy.$t("departmentPostSetting.addDateTime"),
    modifyPerson: proxy.$t("departmentPostSetting.modifyPerson"),
    modifyDateTime: proxy.$t("departmentPostSetting.modifyDateTime"),
    expectedCountPlaceholder: proxy.$t("departmentPostSetting.expectedCountPlaceholder")
  };
});
// #endregion
// 用于调整时间选择器的宽度与其他选择器宽度相同
const datePickerWidth = computed(() => `${convertPX(386)}px`);
</script>
<style lang="scss">
.department-post-setting {
  width: 100%;
  height: 100%;
  .form-style {
    margin: 0;
    padding-top: 2%;
    .type-select,
    .expected-count-input {
      width: v-bind(datePickerWidth);
    }
    .el-date-editor {
      .el-input__wrapper {
        width: v-bind(datePickerWidth);
      }
    }
  }
  .right-file {
    display: block;
    float: right;
  }
}
</style>
