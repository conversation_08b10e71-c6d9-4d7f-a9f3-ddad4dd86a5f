/*
 * FilePath     : \src\views\signUpRecord\hooks\useColumns.ts
 * Author       : 张现忠
 * Date         : 2024-07-14 09:13
 * LastEditors  : 张现忠
 * LastEditTime : 2024-07-20 17:18
 * Description  :
 * CodeIterationRecord:
 */
export function useColumns() {
  const columns = [
    {
      prop: "employeeName",
      label: "姓名",
      width: 100,
      minWidth: 100,
      align: "center"
    },
    {
      prop: "signUpTypeDescription",
      label: "报名方式",
      width: 150,
      minWidth: 150,
      align: "center"
    },
    {
      prop: "statusDescription",
      label: "报名状态",
      width: 120,
      minWidth: 120,
      align: "center"
    },
    {
      prop: "sourceTypeDescription",
      label: "来源",
      width: 120,
      minWidth: 120,
      align: "center"
    },
    {
      prop: "addDateTime",
      label: "新增时间",
      width: 150,
      minWidth: 150,
      align: "center",
      slotType: "dateTime"
    }
  ];

  return { columns };
}
