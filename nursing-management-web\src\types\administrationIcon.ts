/*
 * FilePath     : \src\types\administrationIcon.ts
 * Author       : 杨欣欣
 * Date         : 2023-10-08 16:24
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-10-08 16:33
 * Description  : 注记图示字典数据结构
 * CodeIterationRecord:
 */
/**
 * 注记图示
 */
declare interface AdministrationIcon {
  /**
   * 主键
   */
  administrationIconID: number;
  /**
   * 医院代码
   */
  hospitalID: string;
  /**
   * 标识分类，来源字典或此表定义
   */
  sourceTable?: string;
  /**
   * 标识值，来源其他字典表，或次字典配置
   */
  identifyID?: number;
  /**
   * 标识类型（如：人员标识、年度计划、文档管理）
   */
  moduleType: string;
  /**
   * 群组号
   */
  groupID?: string;
  /**
   * 标识图标
   */
  icon: string;
  /**
   * 标识文本
   */
  text: string;
  /**
   * 标识说明
   */
  remark: string;
  /**
   * 标识颜色
   */
  color: string;
  /**
   * 背景颜色
   */
  backGroundColor?: string;
  /**
   * 排序
   */
  sort: number;
  /**
   * API名称
   */
  aPI?: string;
}
