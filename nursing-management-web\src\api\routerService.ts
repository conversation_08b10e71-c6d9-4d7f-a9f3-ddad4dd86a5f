/*
 * FilePath     : \src\api\routerService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-09-15 08:41
 * Description  : 路由相关Api接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";

export class routerService {
  private static getRouterAndComponentListApi: string = "/router/GetRouterAndComponentList";

  // 获取权限路由和按钮
  public static getRouterAndComponentList(params?: any) {
    return http.get(this.getRouterAndComponentListApi, params, { loadingText: Loading.LOAD });
  }
}
