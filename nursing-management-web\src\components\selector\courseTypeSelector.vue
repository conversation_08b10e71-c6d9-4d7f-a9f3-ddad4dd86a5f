<!--
 * FilePath     : \nursing-management-web\src\components\selector\courseTypeSelector.vue
 * Author       : 张现忠
 * Date         : 2024-04-09 14:19
 * LastEditors  : 张现忠
 * LastEditTime : 2024-11-23 11:56
 * Description  : 课程分类选择器
 * CodeIterationRecord:
-->
<template>
  <div class="course-type-selector">
    <span v-if="label">{{ label }} : </span>
    <el-cascader
      class="selector-component"
      v-model="innerClassifyID"
      :options="courseTypeOptions"
      :show-all-levels="false"
      :props="propsData"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
    </el-cascader>
  </div>
</template>

<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "课程分类"
  },
  modelValue: {
    type: [String],
    default: undefined
  },
  type: {
    type: String,
    default: undefined
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 180
  }
});
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
const propsData = ref({
  value: "settingValue",
  label: "localShowName",
  children: "children",
  expandTrigger: "hover"
  // checkStrictly:false
});
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let classifyID = useVModel(props, "modelValue", emits);
let innerClassifyID = ref([] as number[]);
const { ignoreUpdates } = watchIgnorable(
  () => classifyID,
  (value) => {
    courseTypeOptions.value.forEach((item) => {
      innerClassifyID.value = [];
      if (item.children) {
        let child = item.children.find((m: any) => m.settingValue === value);
        if (child) {
          innerClassifyID.value.push(item.settingValue);
          innerClassifyID.value.push(child.settingValue);
          return;
        }
      }
    });
  }
);
let courseTypeOptions = ref<Array<Record<any, any>>>([]);
onMounted(() => {
  let params = {
    settingType: "TrainingManagement",
    settingTypeCode: "TrainingClassification"
  };
  settingDictionaryService.getSettingDictionaryMaintain(params).then((res: any) => {
    if (res) {
      courseTypeOptions.value = res;
    }
  });
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  ignoreUpdates(() => (classifyID.value = Array.isArray(value) ? value[value.length - 1] : value));
  exposeChange(Array.isArray(value) ? value[value.length - 1] : value, emits);
  exposeSelect(value, courseTypeOptions.value, "settingValue", false, emits);
};
</script>

<style lang="scss">
.course-type-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
