/*
 * FilePath     : \src\i18n\index.ts
 * Author       : 苏军志
 * Date         : 2023-06-08 09:08
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:54
 * Description  : 语言国际化
 * CodeIterationRecord:
 */

// 引入common工具类
import common from "@/utils/common";
// 引入i18n模块
import { createI18n } from "vue-i18n";
// 引入element-ui英文语言包
// import elementEnLocale from "element-plus/es/locale/lang/en";
import elementEnLocale from "element-plus/dist/locale/en.js";
// 引入element-ui中文简体语言包
// import elementZhLocale from "element-plus/es/locale/lang/zh-cn";
import elementZhLocale from "element-plus/dist/locale/zh-cn.js";
// 引入本地英文语言包
import enLocal from "./lang/en";
// 引入本地中文简体语言包
import zhLocal from "./lang/zh";

// 引入语言包
const messages = {
  // 英文语言包
  en: {
    ...enLocal,
    ...elementEnLocale
  },
  // 中文简体语言包
  zh: {
    ...zhLocal,
    ...elementZhLocale
  }
};
// 创建国际化实例
const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  // 取localStorage中的语言，取不到默认中文简体
  locale: common.storage("common")?.language?.languageCode || "zh",
  messages
});
export default i18n;
