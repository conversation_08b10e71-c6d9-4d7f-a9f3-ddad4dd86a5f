/*
 * FilePath     : \nursing-management-web\src\api\DepartmentMaintenanceService.ts
 * Author       : 胡长攀
 * Date         : 2023-10-24 14:55
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-11-29 17:14
 * Description  :
 */

import http from "@/utils/http";

export class departmentMaintenanceService {
  private static getDepartmentVSDepartmentListViewApi: string = "/department/GetDepartmentVSDepartmentListView";
  private static saveDepartmentVSDepartmentListViewApi: string = "/department/SaveDepartmentVSDepartmentListView";
  private static saveDepartmentVSDepartmentListViewsApi: string = "/department/SaveDepartmentVSDepartmentListViews";
  private static enableOrDisableDepartmentApi: string = "/department/EnableOrDisableDepartment";

  // 获取HR部门与护理管理对照信息
  public static getDepartmentVSDepartmentListView(params?: any) {
    return http.get(this.getDepartmentVSDepartmentListViewApi, params, { loadingText: Loading.LOAD });
  }
  // 单个保存
  public static saveDepartmentVSDepartmentListViews(params: any) {
    return http.post(this.saveDepartmentVSDepartmentListViewsApi, params, { loadingText: Loading.LOAD });
  }
  // 批量保存
  public static saveDepartmentVSDepartmentListView(params: any) {
    return http.post(this.saveDepartmentVSDepartmentListViewApi, params, { loadingText: Loading.LOAD });
  }
  // 启用或停用该部门
  public static enableOrDisableDepartment(params: any) {
    return http.post(this.enableOrDisableDepartmentApi, params, { loadingText: Loading.LOAD });
  }
}
