/*
 * FilePath     : \src\stores\modules\user.ts
 * Author       : 苏军志
 * Date         : 2023-06-05 10:52
 * LastEditors  : 苏军志
 * LastEditTime : 2023-11-08 15:30
 * Description  : 用户相关状态管理
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";

const useUserStore = defineStore({
  id: "user",
  state: () => {
    return {
      employeeID: "",
      oaUserID: "",
      hisUserID: "",
      userName: "",
      roles: [],
      departmentID: -1
    };
  },
  // 数据持久化
  persist: {
    enabled: true,
    // 指定字段存储，并且指定存储方式：
    strategies: [
      { storage: sessionStorage }
      // { storage: localStorage, paths: ["userID", "userName"] }
    ]
  }
});

export default useUserStore;
