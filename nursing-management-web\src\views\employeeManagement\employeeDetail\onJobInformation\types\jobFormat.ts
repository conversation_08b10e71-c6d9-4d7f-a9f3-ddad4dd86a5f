/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\onJobInformation\types\jobFormat.ts
 * Author       : 张现忠
 * Date         : 2023-07-29 11:33
 * LastEditors  : 马超
 * LastEditTime : 2025-01-11 14:13
 * Description  : 在职信息呈现样式结构对象
 * CodeIterationRecord:
 * 2023-07-30 3661-作为护理管理人员，我需要护士在职信息档案，以便查看护士相关档案信息 -zxz
 */

export interface jobFormat {
  /**
   * Collapse标题名
   */
  title: string;
  /**
   * 每一个项目的key
   */
  key: string;
  /**
   * 字段属性
   */
  prop: jobProp[];
  /**
   * 是否多页签
   */
  tabsFlag: boolean;
  /**
   * 多页签字段属性
   */
  props: jobFormat[];
}

interface jobProp {
  /**
   * 标签名称
   */
  label?: string;
  /**
   * 标签字段
   */
  field: string;
  /**
   * 标签别名，用于显示文本 比如岗位显示名称而不是code
   */
  alias?: string;
  /**
   * 使用的组件类型 T D Dept 。。。
   */
  type?: string;
  /**
   * 参数是否必要：用于保存检核
   */
  required: boolean;
}
