/*
 * relative     : \nursing-management-web\src\api\examinerScheduleService.ts
 * Author       : 张现忠
 * Date         : 2025-03-10 16:52
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-05 16:40
 * Description  : 监考计划相关接口请求
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";

export class examinerScheduleService {
  // 管理监考计划相关接口的基本URL
  private static controllerBaseUrl: string = "/ExaminerSchedule";
  // 获取监考计划列表的API接口URL
  private static getExaminerScheduleListApi: string = this.controllerBaseUrl + "/GetExaminerScheduleList";
  // 保存监考计划的API接口URL
  private static saveExaminerScheduleApi: string = this.controllerBaseUrl + "/SaveExaminerSchedule";
  // 删除监考计划的API接口URL
  private static deleteExaminerScheduleApi: string = this.controllerBaseUrl + "/DeleteExaminerSchedule";

  /**
   * 获取监考计划列表
   * @param params 请求参数
   * @returns 返回监考计划列表数据
   */
  public static async getExaminerScheduleList(params: any): Promise<any> {
    return await http.get(this.getExaminerScheduleListApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * 保存监考计划
   * @param params 监考计划信息
   * @returns 返回保存结果
   */
  public static async saveExaminerSchedule(params: any): Promise<any> {
    return await http.post(this.saveExaminerScheduleApi, params, { loadingText: Loading.SAVE });
  }

  /**
   * 删除监考计划
   * @param params 待删除的监考计划ID
   * @returns 返回删除结果
   */
  public static async deleteExaminerSchedule(params: any): Promise<any> {
    return await http.post(this.deleteExaminerScheduleApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
}
