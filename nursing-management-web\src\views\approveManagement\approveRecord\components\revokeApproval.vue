<!--
 * FilePath     : \src\views\approveManagement\approveRecord\components\revokeApproval.vue
 * Author       : 张现忠
 * Date         : 2024-08-01 09:50
 * LastEditors  : 张现忠
 * LastEditTime : 2024-08-21 10:52
 * Description  : 审批撤销内容呈现组件
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="revoke-approval">
    <el-form ref="revokeForm" :model="revokeRecord" :rules="revokeRules" label-width="auto">
      <el-form-item v-for="(row, key) in revokeFormData" :key="key" :label="row.label + '：'">
        <span>{{ row.value }}</span>
      </el-form-item>
      <el-form-item label="撤销原因：" prop="revokeReason">
        <el-input type="textarea" v-model="revokeRecord.revokeReason" />
      </el-form-item>
    </el-form>
  </base-layout>
</template>

<script setup lang="ts">
const props = defineProps<{
  revokeFormData: Record<string, any>;
  id: string;
  isSource: boolean;
  drawerOptions: DrawerOptions;
}>();
const { revokeApproval } = useApproval(props.isSource);
const revokeForm = shallowRef();
const emits = defineEmits(["update:drawerOptions","refreshData"]);
const reactiveDrawerOptions = useVModel(props, "drawerOptions", emits);
// 存储外部默认弹窗配置
const storeDrawerOptions = ref<DrawerOptions>();
// 表单验证规则
const revokeRules = {
  revokeReason: [{ required: true, message: "撤销原因不能为空", trigger: "blur" }]
};
const revokeRecord = ref<Record<string, any>>({});
onMounted(async () => {
  revokeRecord.value = { ...props.revokeFormData, revokeReason: "" };
  storeDrawerOptions.value = { ...toRaw(reactiveDrawerOptions.value) };
  storeDrawerOptions.value.showDrawer = false;
  storeDrawerOptions.value.drawerName = undefined;
  // 调整外部弹窗属性
  reactiveDrawerOptions.value.showDrawer = true;
  reactiveDrawerOptions.value.drawerTitle = "撤销审批";
  reactiveDrawerOptions.value.drawerSize = "50%";
  reactiveDrawerOptions.value.showCancel = true;
  reactiveDrawerOptions.value.showConfirm = true;
  reactiveDrawerOptions.value.confirm = async () => {
    if (await saveRevoke()) {
      reactiveDrawerOptions.value = storeDrawerOptions.value!;
    }
  };
  // 取消按钮点击后，添加恢复默认弹窗配置
  reactiveDrawerOptions.value.cancel = () => {
    reactiveDrawerOptions.value = storeDrawerOptions.value!;
  };
});
/**
 * @description: 保存撤销质控
 * @returns {*}
 */
const saveRevoke = async () => {
  const { validateRule } = useForm();
  const validateResult = await validateRule(revokeForm.value);
  if (!validateResult) {
    return false;
  }
  await revokeApproval(props.id, revokeRecord.value.revokeReason);
  emits("refreshData");
  return true;
};

</script>
