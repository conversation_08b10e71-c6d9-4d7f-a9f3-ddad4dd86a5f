import {
  require_copy_constructor_properties,
  require_create_non_enumerable_property,
  require_create_property_descriptor,
  require_descriptors,
  require_export,
  require_fails,
  require_function_apply,
  require_function_uncurry_this,
  require_get_built_in,
  require_global_this,
  require_has_own_property,
  require_inherit_if_required,
  require_is_object,
  require_is_pure,
  require_object_define_property,
  require_object_is_prototype_of,
  require_object_set_prototype_of,
  require_to_string
} from "./chunk-CSILIYYZ.js";
import {
  ElInfiniteScroll
} from "./chunk-7MWVN5YD.js";
import "./chunk-LK7GAOJV.js";
import "./chunk-NPJQLO2R.js";
import "./chunk-7HW3APCP.js";
import "./chunk-7RXIZQGQ.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/core-js/internals/proxy-accessor.js
var require_proxy_accessor = __commonJS({
  "node_modules/core-js/internals/proxy-accessor.js"(exports, module) {
    "use strict";
    var defineProperty = require_object_define_property().f;
    module.exports = function(Target, Source, key) {
      key in Target || defineProperty(Target, key, {
        configurable: true,
        get: function() {
          return Source[key];
        },
        set: function(it) {
          Source[key] = it;
        }
      });
    };
  }
});

// node_modules/core-js/internals/normalize-string-argument.js
var require_normalize_string_argument = __commonJS({
  "node_modules/core-js/internals/normalize-string-argument.js"(exports, module) {
    "use strict";
    var toString = require_to_string();
    module.exports = function(argument, $default) {
      return argument === void 0 ? arguments.length < 2 ? "" : $default : toString(argument);
    };
  }
});

// node_modules/core-js/internals/install-error-cause.js
var require_install_error_cause = __commonJS({
  "node_modules/core-js/internals/install-error-cause.js"(exports, module) {
    "use strict";
    var isObject = require_is_object();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    module.exports = function(O, options) {
      if (isObject(options) && "cause" in options) {
        createNonEnumerableProperty(O, "cause", options.cause);
      }
    };
  }
});

// node_modules/core-js/internals/error-stack-clear.js
var require_error_stack_clear = __commonJS({
  "node_modules/core-js/internals/error-stack-clear.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var $Error = Error;
    var replace = uncurryThis("".replace);
    var TEST = function(arg) {
      return String(new $Error(arg).stack);
    }("zxcasd");
    var V8_OR_CHAKRA_STACK_ENTRY = /\n\s*at [^:]*:[^\n]*/;
    var IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);
    module.exports = function(stack, dropEntries) {
      if (IS_V8_OR_CHAKRA_STACK && typeof stack == "string" && !$Error.prepareStackTrace) {
        while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, "");
      }
      return stack;
    };
  }
});

// node_modules/core-js/internals/error-stack-installable.js
var require_error_stack_installable = __commonJS({
  "node_modules/core-js/internals/error-stack-installable.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    var createPropertyDescriptor = require_create_property_descriptor();
    module.exports = !fails(function() {
      var error = new Error("a");
      if (!("stack" in error)) return true;
      Object.defineProperty(error, "stack", createPropertyDescriptor(1, 7));
      return error.stack !== 7;
    });
  }
});

// node_modules/core-js/internals/error-stack-install.js
var require_error_stack_install = __commonJS({
  "node_modules/core-js/internals/error-stack-install.js"(exports, module) {
    "use strict";
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var clearErrorStack = require_error_stack_clear();
    var ERROR_STACK_INSTALLABLE = require_error_stack_installable();
    var captureStackTrace = Error.captureStackTrace;
    module.exports = function(error, C, stack, dropEntries) {
      if (ERROR_STACK_INSTALLABLE) {
        if (captureStackTrace) captureStackTrace(error, C);
        else createNonEnumerableProperty(error, "stack", clearErrorStack(stack, dropEntries));
      }
    };
  }
});

// node_modules/core-js/internals/wrap-error-constructor-with-cause.js
var require_wrap_error_constructor_with_cause = __commonJS({
  "node_modules/core-js/internals/wrap-error-constructor-with-cause.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var hasOwn = require_has_own_property();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var isPrototypeOf = require_object_is_prototype_of();
    var setPrototypeOf = require_object_set_prototype_of();
    var copyConstructorProperties = require_copy_constructor_properties();
    var proxyAccessor = require_proxy_accessor();
    var inheritIfRequired = require_inherit_if_required();
    var normalizeStringArgument = require_normalize_string_argument();
    var installErrorCause = require_install_error_cause();
    var installErrorStack = require_error_stack_install();
    var DESCRIPTORS = require_descriptors();
    var IS_PURE = require_is_pure();
    module.exports = function(FULL_NAME, wrapper, FORCED2, IS_AGGREGATE_ERROR) {
      var STACK_TRACE_LIMIT = "stackTraceLimit";
      var OPTIONS_POSITION = IS_AGGREGATE_ERROR ? 2 : 1;
      var path = FULL_NAME.split(".");
      var ERROR_NAME = path[path.length - 1];
      var OriginalError = getBuiltIn.apply(null, path);
      if (!OriginalError) return;
      var OriginalErrorPrototype = OriginalError.prototype;
      if (!IS_PURE && hasOwn(OriginalErrorPrototype, "cause")) delete OriginalErrorPrototype.cause;
      if (!FORCED2) return OriginalError;
      var BaseError = getBuiltIn("Error");
      var WrappedError = wrapper(function(a, b) {
        var message = normalizeStringArgument(IS_AGGREGATE_ERROR ? b : a, void 0);
        var result = IS_AGGREGATE_ERROR ? new OriginalError(a) : new OriginalError();
        if (message !== void 0) createNonEnumerableProperty(result, "message", message);
        installErrorStack(result, WrappedError, result.stack, 2);
        if (this && isPrototypeOf(OriginalErrorPrototype, this)) inheritIfRequired(result, this, WrappedError);
        if (arguments.length > OPTIONS_POSITION) installErrorCause(result, arguments[OPTIONS_POSITION]);
        return result;
      });
      WrappedError.prototype = OriginalErrorPrototype;
      if (ERROR_NAME !== "Error") {
        if (setPrototypeOf) setPrototypeOf(WrappedError, BaseError);
        else copyConstructorProperties(WrappedError, BaseError, { name: true });
      } else if (DESCRIPTORS && STACK_TRACE_LIMIT in OriginalError) {
        proxyAccessor(WrappedError, OriginalError, STACK_TRACE_LIMIT);
        proxyAccessor(WrappedError, OriginalError, "prepareStackTrace");
      }
      copyConstructorProperties(WrappedError, OriginalError);
      if (!IS_PURE) try {
        if (OriginalErrorPrototype.name !== ERROR_NAME) {
          createNonEnumerableProperty(OriginalErrorPrototype, "name", ERROR_NAME);
        }
        OriginalErrorPrototype.constructor = WrappedError;
      } catch (error) {
      }
      return WrappedError;
    };
  }
});

// node_modules/core-js/modules/es.error.cause.js
var $ = require_export();
var globalThis = require_global_this();
var apply = require_function_apply();
var wrapErrorConstructorWithCause = require_wrap_error_constructor_with_cause();
var WEB_ASSEMBLY = "WebAssembly";
var WebAssembly = globalThis[WEB_ASSEMBLY];
var FORCED = new Error("e", { cause: 7 }).cause !== 7;
var exportGlobalErrorCauseWrapper = function(ERROR_NAME, wrapper) {
  var O = {};
  O[ERROR_NAME] = wrapErrorConstructorWithCause(ERROR_NAME, wrapper, FORCED);
  $({ global: true, constructor: true, arity: 1, forced: FORCED }, O);
};
var exportWebAssemblyErrorCauseWrapper = function(ERROR_NAME, wrapper) {
  if (WebAssembly && WebAssembly[ERROR_NAME]) {
    var O = {};
    O[ERROR_NAME] = wrapErrorConstructorWithCause(WEB_ASSEMBLY + "." + ERROR_NAME, wrapper, FORCED);
    $({ target: WEB_ASSEMBLY, stat: true, constructor: true, arity: 1, forced: FORCED }, O);
  }
};
exportGlobalErrorCauseWrapper("Error", function(init) {
  return function Error2(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("EvalError", function(init) {
  return function EvalError(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("RangeError", function(init) {
  return function RangeError(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("ReferenceError", function(init) {
  return function ReferenceError(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("SyntaxError", function(init) {
  return function SyntaxError(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("TypeError", function(init) {
  return function TypeError(message) {
    return apply(init, this, arguments);
  };
});
exportGlobalErrorCauseWrapper("URIError", function(init) {
  return function URIError(message) {
    return apply(init, this, arguments);
  };
});
exportWebAssemblyErrorCauseWrapper("CompileError", function(init) {
  return function CompileError(message) {
    return apply(init, this, arguments);
  };
});
exportWebAssemblyErrorCauseWrapper("LinkError", function(init) {
  return function LinkError(message) {
    return apply(init, this, arguments);
  };
});
exportWebAssemblyErrorCauseWrapper("RuntimeError", function(init) {
  return function RuntimeError(message) {
    return apply(init, this, arguments);
  };
});

// node_modules/el-table-infinite-scroll/lib/es/utils.js
function syncAttrs(sourceElem, targetElem, attrsKeys) {
  var value;
  attrsKeys.forEach(function(name) {
    value = sourceElem.getAttribute(name);
    if (value !== null) {
      targetElem.setAttribute(name, value);
    } else {
      targetElem.removeAttribute(name);
    }
  });
}

// node_modules/el-table-infinite-scroll/lib/es/el-table-infinite-scroll.js
var msgTitle = "[el-table-infinite-scroll]: ";
var elTableScrollWrapperClass = ".el-scrollbar__wrap";
var ElTableInfiniteScroll = {
  mounted: function mounted(el, binding, VNode, oldVNode) {
    var scrollElem = el.querySelector(elTableScrollWrapperClass);
    if (!scrollElem) {
      throw new Error("".concat(msgTitle).concat(elTableScrollWrapperClass, " element not found."));
    }
    scrollElem.style.overflowY = "auto";
    setTimeout(function() {
      if (!el.style.height) {
        scrollElem.style.height = "400px";
        console.warn("".concat(msgTitle, "el-table height required, otherwise will set scrollbar default height: 400px"));
      }
      syncOptions(el, scrollElem);
      ElInfiniteScroll.mounted(scrollElem, binding, VNode, oldVNode);
    }, 0);
  },
  updated: function updated(el) {
    syncOptions(el, el.querySelector(elTableScrollWrapperClass));
  },
  unmounted: function unmounted(el) {
    var scrollElem = el.querySelector(elTableScrollWrapperClass);
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    ElInfiniteScroll.unmounted.apply(ElInfiniteScroll, [scrollElem].concat(args));
  }
};
function syncOptions(sourceElem, targetElem) {
  syncAttrs(sourceElem, targetElem, ["infinite-scroll-disabled", "infinite-scroll-delay", "infinite-scroll-immediate", "infinite-scroll-distance"]);
  var name = "infinite-scroll-distance";
  var value = +(sourceElem.getAttribute(name) || 0);
  targetElem.setAttribute(name, (value < 1 ? 1 : value) + "");
}

// node_modules/el-table-infinite-scroll/lib/es/index.js
var ElTableInfiniteScroll2 = Object.assign(ElTableInfiniteScroll, {
  install: function install(vue) {
    vue.directive("el-table-infinite-scroll", ElTableInfiniteScroll2);
  }
});
export {
  ElTableInfiniteScroll2 as default
};
/*! Bundled license information:

el-table-infinite-scroll/lib/es/utils.js:
  (*!
   * el-table-infinite-scroll v3.0.6
   * (c) 2019-2024 yujinpan
   *)

el-table-infinite-scroll/lib/es/el-table-infinite-scroll.js:
  (*!
   * el-table-infinite-scroll v3.0.6
   * (c) 2019-2024 yujinpan
   *)

el-table-infinite-scroll/lib/es/index.js:
  (*!
   * el-table-infinite-scroll v3.0.6
   * (c) 2019-2024 yujinpan
   *)
*/
//# sourceMappingURL=el-table-infinite-scroll.js.map
