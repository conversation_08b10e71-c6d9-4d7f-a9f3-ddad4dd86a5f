/*
 * FilePath     : \src\components\transfer\hooks\usePropsAlias.ts
 * Author       : 杨欣欣
 * Date         : 2024-04-03 10:51
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-04-08 11:22
 * Description  : 设置穿梭框组件对象成员别名
 * CodeIterationRecord:
 */
export const usePropsAlias = (props: { props: Record<string, any> }) => {
  const initProps: Record<string, any> = {
    label: "label",
    key: "key",
    disabled: "disabled"
  };

  return computed(() => ({
    ...initProps,
    ...props.props
  }));
};
