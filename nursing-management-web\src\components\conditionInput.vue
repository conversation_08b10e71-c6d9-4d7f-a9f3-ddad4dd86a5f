<!--
 * FilePath     : \src\components\conditionInput.vue
 * Author       : 来江禹
 * Date         : 2024-07-18 14:36
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-02 11:04
 * Description  : 新增培训清单选择人员条件组件
 * CodeIterationRecord:
 -->
<template>
  <div class="condition-input" @click="openConditionSetting">
    <div v-if="clearable && content" class="close" @click.stop="clear">x</div>
    <span v-if="!content" class="tip">单击设置条件</span>
    <span v-else v-html="content"></span>
  </div>
  <el-dialog class="filter-dialog" v-model="showConditionDialog" destroy-on-close :title="title">
    <zhy-dynamic-filter
      ref="dynamicFilterRef"
      showStyle
      :items="filterItems"
      :conditionTypes="filterConditions"
      :conditionProps="filterConditionProps"
      :defaultValue="transformedDefaultValue"
      :allowMultiLevel="allowMultiLevel"
    ></zhy-dynamic-filter>
    <template #footer>
      <el-button @click="showConditionDialog = false">取消</el-button>
      <el-button type="primary" @click="getFilterData">确定</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { conditionType } from "zhytech-ui";
const props = defineProps({
  /**
   * 标题
   */
  title: {
    type: String,
    default: "条件设置"
  },
  /**
   * 显示内容
   */
  content: {
    type: String,
    default: ""
  },
  /**
   * 是否显示样式
   */
  showStyle: {
    type: Boolean,
    default: false
  },
  /**
   * 是否可以清空
   */
  clearable: {
    type: Boolean,
    default: false
  },
  /**
   * 默认值
   */
  defaultValue: {
    type: Array<Record<string, any>>,
    default: () => []
  },
  selectComponent: {
    type: Object as PropType<Record<string, any>>,
    required: true
  },
  allowMultiLevel: {
    type: Boolean,
    default: true
  },
  width: {
    type: Number,
    default: 140
  }
});
const dynamicFilterRef = ref<any>();
const showConditionDialog = ref<boolean>(false);
const filterItems = ref<Record<string, any>[]>([]);
const filterConditions = ref<Record<string, conditionType[]>>({});
const filterConditionProps = ref<Record<string, any>>({});
const transformedDefaultValue = ref<Record<string, any>[]>([]);
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

/**
 * @description: 打开设定条件窗口
 */
const openConditionSetting = () => {
  filterItems.value = props.selectComponent.items ?? [];
  filterConditions.value = props.selectComponent.conditionTypes ?? {};
  filterConditionProps.value = props.selectComponent.conditionProps ?? {};
  transformedDefaultValue.value = transformValue(common.clone(props.defaultValue), false);
  showConditionDialog.value = true;
};

const emits = defineEmits(["result", "clear"]);
defineExpose({
  validator: (value?: string) => {
    if (!dynamicFilterRef.value) {
      return Boolean(value);
    }
    const { message } = dynamicFilterRef.value.getFilterData(false);
    if (message) {
      return false;
    }
    return true;
  }
});
/**
 * @description: 设置好的条件赋值给组件属性
 */
const getFilterData = () => {
  const filterData = dynamicFilterRef.value.getFilterData();
  if (filterData.message) {
    return;
  }
  transformValue(filterData.filterConditions, true);
  showConditionDialog.value = false;
  emits("result", filterData);
};
/**
 * @description: 按照条件转换value的值
 * @param conditions 选择的明细条件
 * @param stringify true:array 转 string / false:string 转 array
 * @return
 */
function transformValue(conditions: any, stringify: boolean) {
  for (const condition of conditions) {
    if (condition.value && ((typeof condition.value === "string" && condition.value.length) || Array.isArray(condition.value))) {
      // 更新顶层的 value
      condition.value = stringify ? JSON.stringify(condition.value) : JSON.parse(condition.value);
    }
    if (condition.children && condition.children.length > 0) {
      // 递归调用以处理子条件
      transformValue(condition.children, stringify);
    }
  }
  return conditions;
}
const clear = () => {
  emits("clear");
};
</script>
<style lang="scss">
.condition-input {
  position: relative;
  width: v-bind(selectorWidth);
  min-height: 36px;
  max-height: 90px;
  line-height: 32px;
  padding: 0 5px;
  box-sizing: border-box;
  border: 1px solid #999999;
  overflow: auto;
  cursor: pointer;
  &:hover .close {
    display: block;
  }
  .close {
    position: absolute;
    right: 3px;
    top: 3px;
    padding: 5px;
    width: 12px;
    height: 12px;
    line-height: 10px;
    text-align: center;
    border: 1px solid #999999;
    border-radius: 50px;
    cursor: pointer;
    color: #ff0000;
    display: none;
  }
  .tip {
    color: #a8abb2;
  }
}
.filter-dialog {
  .el-dialog__body {
    padding-top: 30px !important;
    box-sizing: border-box;
  }
}
</style>
