<!--
 * FilePath     : \src\views\scheduling\components\operationInstruction.vue
 * Author       : 苏军志
 * Date         : 2024-02-25 15:24
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-26 18:34
 * Description  : 操作说明
 * CodeIterationRecord:
 -->
<template>
  <div class="operating-instruction">
    <el-popover popper-class="instruction-popper" title="操作说明" :width="580" trigger="hover" effect="dark" placement="right-end">
      <template #reference>
        <i class="iconfont icon-question-mark"></i>
      </template>
      <template #default>
        <span class="operating-instruction-content" v-html="operatingInstruction"></span>
      </template>
    </el-popover>
  </div>
</template>
<script setup lang="ts">
// const operatingInstruction =
//   "1、上下拖动<b><font style='color:#ff7400'>姓名或层级列</font></b>可以调整人员顺序<br/>" +
//   "2、双击单元格弹出岗位列表，选择岗位即可排班（<b><font style='color:#ff7400'>第一阶的休假可以直接选择</font></b>）<br/>" +
//   "3、选择岗位后可以右键单元格调整上午或下午的岗位<br/>" +
//   "4、选择岗位后可以右键单元格选择特殊标记<br/>" +
//   "5、<b><font style='color:#ff7400'>鼠标停在休假处</font></b>可以显示当天是本月的第记几次休假<br/>" +
//   "6、单击选择一个单元格，拖动鼠标可以复制排班（<b><font style='color:#ff7400'>支持上下左右拖动复制</font></b>）<br/>" +
//   "7、按键盘上的<b><font style='color:#ff7400'>Ctrl + 鼠标点击</font></b>可以同时选中多个排班<br/>" +
//   "8、按键盘上的<b><font style='color:#ff7400'>Shift + 鼠标点击</font></b>可以快速选中连续的排班<br/>" +
//   "9、按键盘上的<b><font style='color:#ff7400'>Delete</font></b>键可以删除选中的排班<br/>" +
//   "10、按键盘上的<b><font style='color:#ff7400'>Ctrl + C</font></b>组合键可以复制选中的排班<br/>" +
//   "11、按键盘上的<b><font style='color:#ff7400'>Ctrl + V</font></b>组合键可以粘贴已复制的排班（<b><font style='color:#ff7400'>支持一次复制，多次粘贴</font></b>）<br/>" +
//   "12、按键盘上的<b><font style='color:#ff7400'>Ctrl + Z</font></b>组合键可以撤销排班<br/>" +
//   "13、按键盘上的<b><font style='color:#ff7400'>Ctrl + Y</font></b>组合键可以恢复排班<br/>" +
//   "14、单击画面上的<b><font style='color:#ff7400'>限制层级</font></b>开关可以切换排班时是否限制护士层级<br/>" +
//   "15、单击画面上的<b><font style='color:#ff7400'>显示日统计</font></b>开关可以切换是否显示排班表底部的日统计<br/>" +
//   "16、单击画面上的<b><font style='color:#ff7400'>排班月统计</font></b>按钮可以显示本月排班的统计<br/>" +
//   "17、单击画面上的<b><font style='color:#ff7400'>复制上月排班</font></b>按钮可以将上月的排班复制到本月（<b><font style='color:#ff7400'>只有未发布状态下才显示此按钮</font></b>）<br/>" +
//   "18、颜色说明：背景色为<b><font style='color:#000000;background-color:#d0eafb'> 浅蓝色 </font></b>表示当天去支援其他部门<br/>" +
//   "19、颜色说明：背景色为<b><font style='color:#000000;background-color:#fae8e6'> 浅粉色 </font></b>表示其他部门的人当天支援本部门<br/>" +
//   "20、颜色说明：背景色为<b><font style='color:#000000;background-color:#dddddd'> 浅灰色 </font></b>表示其他部门人员当天不支援（<b><font style='color:#ff7400'>禁止排班</font></b>）<br/>";
const operatingInstruction =
  "1、单击<b><font style='color:#ff7400'>序号</font></b>当鼠标变成<b><font style='color:#ff7400'>小手形状</font></b>时拖动可以调整人员顺序<br/>" +
  "2、在单元格上<b><font style='color:#ff7400'>右键</font></b>选择<b><font style='color:#ff7400'>岗位</font></b>进行排班<br/>" +
  "3、在单元格上<b><font style='color:#ff7400'>右键</font></b>选择<b><font style='color:#ff7400'>休假</font></b>进行排休<br/>" +
  "4、在单元格上<b><font style='color:#ff7400'>右键</font></b>选择<b><font style='color:#ff7400'>上午</font></b>或<b><font style='color:#ff7400'>下午</font></b>进行上午或下午的排班或排休<br/>" +
  "5、在单元格上<b><font style='color:#ff7400'>右键</font></b>选择<b><font style='color:#ff7400'>排班标记</font></b>进行添加特殊标记<br/>" +
  "6、在单元格上<b><font style='color:#ff7400'>右键</font></b>选择<b><font style='color:#ff7400'>排班模板</font></b>可以选择模板快速排班<br/>" +
  // "5、<b><font style='color:#ff7400'>鼠标停在休假处</font></b>可以显示当天是本月的第记几次休假<br/>" +
  "7、单击画面上的<b><font style='color:#ff7400'>排班月统计</font></b>按钮可以显示本月排班的统计<b><font style='color:#ff7400'>(此按钮在周排班时不显示)</font></b><br/>" +
  "8、单击画面上的<b><font style='color:#ff7400'>复制上月/周排班</font></b>按钮可以将上月/周的排班复制到本月/周<b><font style='color:#ff7400'>(排班未发布时下才显示)</font></b><br/>" +
  "9、颜色说明：背景色为<b><font style='color:#000000;background-color:#d0eafb'> 浅蓝色 </font></b>表示当天去支援其他部门<b><font style='color:#ff7400'>(禁止排班)</font></b><br/>" +
  "10、颜色说明：背景色为<b><font style='color:#000000;background-color:#fae8e6'> 浅粉色 </font></b>表示其他部门的人当天支援本部门<b><font style='color:#ff7400'>(可以排班)</font></b><br/>" +
  "11、颜色说明：背景色为<b><font style='color:#000000;background-color:#dddddd'> 浅灰色 </font></b>表示人员当天不在本部门<b><font style='color:#ff7400'>(禁止排班)</font></b><br/>" +
  "12、颜色说明：背景色为<b><font style='color:#000000;background-color:#fefdd8'> 浅黄色 </font></b>表示本月发生转科的人员<br/>";
</script>

<style lang="scss">
.operating-instruction {
  display: inline-block;
  .icon-question-mark {
    color: #ff0000;
    font-size: 28px;
  }
}
.instruction-popper {
  padding: 15px 0 15px 40px !important;
  .el-popover__title {
    font-weight: bold;
    color: #ff7400;
    font-size: 24px;
    border-bottom: 1px solid #ffffff;
    margin-left: -40px;
    padding: 0 20px 10px 30px;
    font-size: 26px;
  }
  .operating-instruction-content {
    color: #ffffff;
    line-height: 36px;
  }
}
</style>
