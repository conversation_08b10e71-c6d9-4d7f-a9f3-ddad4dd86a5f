/*
 * FilePath     : \src\views\examineManagement\hooks\usePaper.ts
 * Author       : 杨欣欣
 * Date         : 2024-09-04 17:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-01 16:21
 * Description  : 试卷相关操作
 * CodeIterationRecord:
 */
// eslint-disable-next-line id-match
import { type Ref } from "vue";

export function usePaper(drawerData: Ref<Record<string, any>>, drawerOptions: Ref<Record<string, any>>, userStore: Record<string, any>) {
  // 实操类型
  const practicalType = "2";
  /**
   * @description: 新增编辑数据
   * @param paperType 试卷类型
   * @param row
   */
  const addRecord = async (paperType: string, row?: Record<string, any>) => {
    drawerOptions.value.showConfirm = true;
    drawerOptions.value.drawerName = "AddOrModifyRecord";
    drawerOptions.value.drawerTitle = `${row ? "编辑" : "新增"}试卷`;
    drawerOptions.value.drawerSize = "40%";
    drawerOptions.value.showDrawer = true;
    drawerData.value.paperQuestionMode = "1";
    drawerData.value = { paperType, isPractical: paperType === practicalType, departmentID: userStore.departmentID };
    if (row) {
      drawerData.value.examinationPaperMainID = row.examinationPaperMainID;
      drawerData.value.paperTitle = row.paperTitle;
      drawerData.value.departmentID = row.departmentID;
      drawerData.value.paperQuestionMode = row.paperQuestionMode;
      drawerData.value.passingScore = row.passingScore;
      if (drawerData.value.isPractical) {
        drawerData.value.questionBankID = row.questionBankID;
      } else {
        drawerData.value.totalPoints = row.totalPoints;
        drawerData.value.conditionName = row.conditionName;
        drawerData.value.examinationConditionRecordID = row.examinationConditionRecordID;
      }
    }
  };
  /**
   * @description: 保存数据
   */
  const saveRecord = async () => {
    // 理论试卷，组卷模式不能为空（解决内网环境下，el-form 设置必选规则不生效的问题如下情况 <div v-if="!isPractical"><el-form-item></el-form-item></div>）
    if (!drawerData.value.isPractical && !drawerData.value.paperQuestionMode) {
      showMessage("warning", "请选择组卷模式！");
      return;
    }
    let ret = false;
    await examineService.saveExaminationPaper(drawerData.value).then((result) => {
      if (result) {
        ret = true;
        drawerOptions.value.showDrawer = false;
        showMessage("success", "保存成功！");
      }
    });
    return ret;
  };
  // 返回暴露的函数和状态
  return {
    /**
     * @description: 新增试卷
     */
    addRecord,
    /**
     * @description: 保存试卷
     */
    saveRecord
  };
}
