<!--
 * FilePath     : \src\components\selector\educationalSelector.vue
 * Author       : 孟昭永
 * Date         : 2023-08-13 11:58
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:43
 * Description  : 学历选择器
 * CodeIterationRecord:
-->
<template>
  <div class="educational-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="educationalIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="item in educationalOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "学历"
  },
  modelValue: {
    type: Array<String>
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 175
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let educationalIDs = useVModel(props, "modelValue", emits);

let educationalOptions = ref<Array<Record<any, any>>>([]);
// 通过hooks从数据库获取数据
const params: SettingDictionaryParams = {
  settingType: "EmployeeManagement",
  settingTypeCode: "EmployeeEducationalExperience",
  settingTypeValue: "EducationCode"
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  educationalOptions.value = datas;
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, educationalOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.educational-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
