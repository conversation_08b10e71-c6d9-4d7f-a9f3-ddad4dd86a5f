<!--
 * FilePath     : \src\components\selector\courseSettingSelector.vue
 * Author       : 来江禹
 * Date         : 2024-06-04 17:07
 * LastEditors  : 来江禹
 * LastEditTime : 2024-10-05 08:31
 * Description  : 培训课程下拉框
 * CodeIterationRecord:
 -->
<template>
  <div class="course-setting-selector">
    <span v-if="label">{{ label }} : </span>
    <el-cascader
      class="selector-component"
      v-model="settingIDs"
      :options="courseSettingOptions"
      :show-all-levels="false"
      :props="propsData"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      ref="cascaderRef"
      collapse-tags
      collapse-tags-tooltip
      @change="getCheckedNodes"
    >
    </el-cascader>
  </div>
</template>

<script setup lang="ts">
const { getCascaderFullValue } = useCascaderFullValue();
const { proxy } = getCurrentInstance() as any;
const props = defineProps({
  label: {
    type: String,
    default: ""
  },
  modelValue: {
    type: [String, Array<String>],
    default: ""
  },
  type: {
    type: String,
    default: undefined
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 180
  },
  checkStrictly: {
    type: Boolean,
    default: false
  },
  isMultiple: {
    type: Boolean,
    default: false
  }
});
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
const propsData = ref({
  expandTrigger: "hover",
  checkStrictly: props.checkStrictly,
  multiple: props.isMultiple
});
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select", "selectChooseNode"]);
let settingIDs = ref<String[] | Array<string[]>>([]);
let courseSettingOptions = ref<CascaderList<string>[]>([]);
let settingItems: CascaderList<number>[] | Array<CascaderList<number>[]>[];
onMounted(async () => {
  let param = {};
  await trainingService.getCourseSetting(param).then((res: any) => {
    if (res) {
      courseSettingOptions.value = res;
    }
  });
  initDefaultValue();
});
watch(
  () => props.modelValue,
  () => {
    initDefaultValue();
  }
);
/**
 * @description: 初始化默认值
 */
const initDefaultValue = () => {
  if (typeof props.modelValue === "string") {
    const defaultValue = getCascaderFullValue(
      props.modelValue,
      courseSettingOptions.value,
      "value",
      "children",
      typeof props.modelValue !== "string"
    ) as any;
    ignoreUpdates(
      () =>
        (settingIDs.value = props.isMultiple
          ? typeof props.modelValue !== "string"
            ? defaultValue[0]
            : [defaultValue[0]]
          : defaultValue[0])
    );
  } else if (Array.isArray(props.modelValue)) {
    let numericArray: string[] = props.modelValue as string[];
    const defaultValue = getCascaderFullValue(
      numericArray,
      courseSettingOptions.value,
      "value",
      "children",
      typeof props.modelValue !== "string"
    ) as any;
    ignoreUpdates(
      () =>
        (settingIDs.value = props.isMultiple
          ? typeof props.modelValue !== "string"
            ? defaultValue[0]
            : [defaultValue[0]]
          : defaultValue[0])
    );
  }
};
const { ignoreUpdates } = watchIgnorable(
  () => settingIDs.value,
  (newValue) => {
    if (!newValue?.length) {
      emits("update:modelValue", undefined);
      emits("change", undefined);
      emits("select", undefined);
      return;
    }
    let chooseValues: string | string[] = [];
    if (props.isMultiple) {
      // 如果是多选类型，返回每一个数组课程ID数据最后一个参数，选择的课程ID数组
      newValue.forEach((value: any) => (chooseValues as string[]).push(value[value.length - 1]));
    } else {
      // 如果是单选类型，返回课程ID数据最后一个参数，选择的课程ID
      chooseValues = newValue[newValue.length - 1] as string;
    }
    // 处理首次进入监听，组件默认值情况，传入的值newValue不是一个数组，是几个数值，在遍历时chooseValues数组均为undefine，此时取课程ID数据最后一个参数
    if (props.isMultiple && !(chooseValues as string[]).length && newValue) {
      chooseValues = newValue[newValue.length - 1] as string;
    }
    // 数据清空时，直接返回
    if (!(chooseValues as any)?.length) {
      return;
    }
    // 根据选择的值，获取到对象的位置及对象数据值
    const ret = getCascaderFullValue(
      chooseValues,
      courseSettingOptions.value,
      "value",
      "children",
      typeof chooseValues !== "number"
    ) as any;
    emits("update:modelValue", chooseValues);
    emits("change", chooseValues);
    settingItems = ret[1];
    emits("select", settingItems);
  },
  { deep: true }
);
/**
 * @description: 获取级联选择选中节点数据
 * @return
 */
const getCheckedNodes = () => {
  const chooseData = proxy.$refs?.cascaderRef.getCheckedNodes();
  const nodeDataList = ref<Array<Record<string, any>>>([]);
  if (!chooseData) {
    return;
  }
  chooseData.forEach((data: any) => {
    if (!data?.parent) {
      nodeDataList.value.push({
        value: data.value,
        level: data.level,
        label: data.label,
        parentValue: undefined
      });
    }
    // 只选择了子节点，把选择的子节点返回
    else {
      let chooseParentData = chooseData.filter((item: any) => item?.value === data.parent?.value);
      if (!chooseParentData?.length) {
        nodeDataList.value.push({
          value: data.value,
          level: data.data.level,
          label: data.label,
          parentValue: data.parent.value
        });
      }
    }
  });
  emits("selectChooseNode", nodeDataList.value);
};
</script>

<style lang="scss">
.course-setting-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
