<!--
 * FilePath     : \src\views\scheduling\shiftSchedulingRule.vue
 * Author       : 苏军志
 * Date         : 2024-07-07 11:20
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:00
 * Description  : 排班规则设定
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="shift-scheduling-rule" :showHeader="false">
    <el-table class="shift-scheduling-rule-table" :data="shiftSchedulingRules" border height="100%">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column label="排班规则" prop="description" align="center"> </el-table-column>
      <el-table-column label="规则值" align="center">
        <template v-slot="{ row }">
          <el-select v-if="row.componentType === 'Select'" v-model="row.ruleValue">
            <el-option v-for="(option, index) in getSelectOptions(row.ruleKey)" :key="index" :label="option.label" :value="option.value" />
          </el-select>
          <el-switch v-else-if="row.componentType === 'Switch'" v-model="row.ruleValue" active-value="true" inactive-value="false" />
          <el-input v-else v-model="row.ruleValue" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" align="center">
        <template v-slot="{ row }">
          <el-tooltip content="保存">
            <i v-permission:B="2" class="iconfont icon-save" @click="saveRule(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
const { userStore } = useStore();
const shiftSchedulingRules = ref<Record<string, any>[]>([]);
const ruleSelectOption = ref<Record<string, any>[]>([]);
onMounted(() => init());
/**
 * @description: 初始化
 */
const init = () => {
  getSchedulingRuleSelectOption();
  getShiftSchedulingRules();
};
/**
 * @description: 获取排班规则下拉框选项
 */
const getSchedulingRuleSelectOption = () => {
  let params = {
    settingType: "ShiftManagement",
    settingTypeCode: "SchedulingRuleSelectOption"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((data: any) => {
    ruleSelectOption.value = data;
  });
};
/**
 * @description: 获取排班规则记录
 */
const getShiftSchedulingRules = () => {
  let params = {
    departmentID: userStore.departmentID
  };
  schedulingService.getShiftSchedulingRules(params).then((data: any) => {
    shiftSchedulingRules.value = data;
  });
};
/**
 * @description: 通过type筛选出符合条件的下拉选项
 * @param type
 * @return
 */
const getSelectOptions = (type: string) => {
  return ruleSelectOption.value.filter((option) => option.type === type);
};
/**
 * @description: 保存排班规则
 * @param shiftSchedulingRule
 * @return
 */
const saveRule = (shiftSchedulingRule: Record<string, any>) => {
  schedulingService.saveShiftSchedulingRule(shiftSchedulingRule).then((data: any) => {
    if (data) {
      showMessage("success", "保存成功！");
      getShiftSchedulingRules();
    }
  });
};
</script>
<style lang="scss">
.shift-scheduling-rule {
}
</style>
