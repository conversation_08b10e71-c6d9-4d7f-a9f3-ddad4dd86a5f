/*
 * FilePath     : \src\views\setting\employeeRole\types\employeeRoleView.ts
 * Author       : 张现忠
 * Date         : 2024-03-07 10:27
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-07-22 15:51
 * Description  :人员角色interface
 * CodeIterationRecord:
 */
/**
 * 员工角色View
 */
export interface employeeRoleView {
  /**
   * 工号
   */
  employeeID: string;
  /**
   * 人员名称
   */
  employeeName: string;
  /**
   * 角色名称（可能多个角色名称拼接）
   */
  roleNames: string[];
  /**
   * @remarks 员工当前所有角色对应的角色ID
   */
  authorityRoleIDs: number[];
  /**
   * @remarks 修改人
   */
  modifyEmployeeName: string;
  /**
   * 修改时间
   */
  modifyDateTime: string;
  /**
   * 部门ID
   */
  departmentID: number;
}
