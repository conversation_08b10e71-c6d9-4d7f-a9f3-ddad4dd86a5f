<!--
 * FilePath     : \nursing-management-web\src\views\examineManagement\components\examineStatusCodeSelector.vue
 * Author       : 来江禹
 * Date         : 2024-08-24 08:29
 * LastEditors  : 张现忠
 * LastEditTime : 2024-11-07 14:26
 * Description  :
 * CodeIterationRecord:
 -->
<template>
  <div class="examine-status-code-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="statusCode"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="item in statusCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "@/components/selector/hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "考核状态"
  },
  modelValue: {
    type: String
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 140
  },
  showBlankOptions: {
    type: Boolean,
    default: false
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let statusCode = useVModel(props, "modelValue", emits);

let statusCodeOptions = ref<Array<Record<any, any>>>([]);
const params: SettingDictionaryParams = {
  settingType: "ExaminationManagement",
  settingTypeCode: "ExaminationMain",
  settingTypeValue: "StatusCode",
  index: Math.random()
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  statusCodeOptions.value = datas;
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, statusCodeOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.examine-status-code-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
