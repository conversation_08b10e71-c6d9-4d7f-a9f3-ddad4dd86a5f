<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\qcLevelSelector.vue
 * Author       : 郭鹏超
 * Date         : 2023-08-13 08:21
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-13 11:39
 * Description  : 质控级别选择器
 * CodeIterationRecord:
-->
<template>
  <div class="qc-level-selector">
    <label v-if="label">{{ label + "：" }}</label>
    <el-select :disabled="disabled" class="level-select" v-model="qcLevel">
      <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: undefined
  },
  label: {
    type: String
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 150
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  }
});

onMounted(async () => {
  getFormLevelList();
  qcLevel.value = props.modelValue;
});

const levelOptions = ref<any[]>([]);
const emit = defineEmits(["update:modelValue", "change"]);
const qcLevel = useVModel(props, "modelValue", emit);
watch(qcLevel, (val) => {
  emit(
    "change",
    levelOptions.value.find((option: any) => option.hierarchicalQCFormID === val)
  );
});
let { list } = toRefs(props);
const getFormLevelList = () => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  if (list?.value) {
    levelOptions.value = list.value;
    return;
  }
  // 字典数据
  const param: SettingDictionaryParams = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCForm",
    settingTypeValue: "HierarchicalQCFormLevel"
  };
  settingDictionaryService.getSettingDictionaryDict(param).then((datas: any) => {
    levelOptions.value = datas;
  });
};
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
</script>

<style lang="scss">
.qc-level-selector {
  display: inline-block;
  width: v-bind(selectorWidth);
}
</style>
