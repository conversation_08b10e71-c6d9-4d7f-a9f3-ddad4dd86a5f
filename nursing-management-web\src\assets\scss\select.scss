.el-form-item__content {
  .el-select__placeholder {
    color: #000000;
  }
}
.el-select__wrapper {
  box-shadow: 0 0 0 1px #999999 inset !important;
}
.el-select-dropdown {
  .el-select-dropdown__list {
    margin: 0 !important;
    .el-select-dropdown__item {
      height: 24px;
      line-height: 24px;
      padding: 0 5px;
      margin: 0;
      &.hover {
        @include select-style();
      }
      &:not(:last-child) {
        border-bottom: 1px dashed #d9d9d9;
      }
    }
  }
  &.is-multiple {
    .el-select-dropdown__item.selected.hover {
      @include select-style();
    }
  }
}
.el-select__tags-text {
  color: $base-color;
  // font-weight: bold;
}

/* 级联选择器 */
.el-cascader-menu__list {
  .el-cascader-node {
    height: 25px;
    line-height: 25px;
    &.hover {
      @include select-style();
    }
    &:not(:last-child) {
      border-bottom: 1px dashed #d9d9d9;
    }
    .el-cascader-node__postfix {
      font-size: 15px;
    }
  }
}
