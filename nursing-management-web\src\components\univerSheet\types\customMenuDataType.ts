/*
 * FilePath     : \src\components\univerSheet\types\customMenuDataType.ts
 * Author       : 苏军志
 * Date         : 2024-10-05 08:22
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-21 11:54
 * Description  : 自定义菜单类型
 * CodeIterationRecord:
 */
export interface customMenuDataType {
  /**
   * @description: 菜单类型
   */
  menuType: string;
  /**
   * @description: 菜单数据
   */
  menuData: Record<string, any>;
  /**
   * @description: 菜单数据属性和自定义菜单属性对应关系
   */
  keyMap?: Record<keyType, string>;
  /**
   * @description: 菜单点击回调方法
   */
  callback?: Function;
  /**
   * @description: 获取菜单是否禁用的方法
   */
  getDisabled?: Function;
  /**
   * @description: 获取菜单是否选择的方法
   */
  getActivated?: Function;
}
/**
 * @description: keyMap类型
 * id:菜单唯一id；icon：菜单图标；tooltip：菜单说明；title：菜单名称；
 */
export type keyType = "id" | "icon" | "tooltip" | "title";
