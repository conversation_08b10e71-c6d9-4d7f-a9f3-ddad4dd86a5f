/*
 * FilePath     : \src\hooks\useMQMessage.ts
 * Author       : 苏军志
 * Date         : 2023-12-06 18:29
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:49
 * CodeIterationRecord: MQ消息处理
 */
const { mqSetting, getMqClient } = useMQ();
// eslint-disable-next-line id-match
import type { RouteLocationRaw, RouteRecordNormalized, Router } from "vue-router";
import { useUtils } from "./useUtils";
const { showAlert } = useUtils();
/**
 * MQ消息处理
 */
export function useMQMessage(router: Router) {
  /**
   * @description: 根据消息类型显示消息
   * @param message
   * @param exchangeName
   * @param routingKey
   * @return
   */
  const displayMessage = (message: MQMessage, exchangeName?: string, routingKey?: string) => {
    const messageContent: string = parseMessageContent(message);
    if (!messageContent) {
      return;
    }
    switch (message.Type) {
      case MQMessageType.Alert:
        showAlert("success", messageContent);
        break;
      case MQMessageType.Message:
        showMessage("success", messageContent);
        break;
      case MQMessageType.Confirm:
        confirmBox(messageContent, "");
        break;
      case MQMessageType.Notification: {
        // 系统通知特殊处理
        if (routingKey === "SystemNotice") {
          eventBus.emit("SystemNotice", messageContent);
          return;
        }
        const routeLocation = getNavigateToPath(message);
        if (!message.Link || routeLocation) {
          showNotification(messageContent, "", 0, () => {
            routeLocation && router.push(routeLocation);
          });
        }
        break;
      }
      default:
        showMessage("success", messageContent);
        break;
    }
  };
  /**
   * @description: 获取推送路由
   * @param message 消息
   * @return
   */
  const getNavigateToPath = (message: MQMessage): RouteLocationRaw | undefined => {
    if (!message.Link || message.ClientType !== 1) {
      return undefined;
    }
    const url = new URL(message.Link, window.location.origin);
    const { pathname, searchParams } = url;
    // 检核路由是否存在
    const isRouteMatched = (path: string) => router.resolve(path).matched.find((route: RouteRecordNormalized) => route.path === path);

    if (searchParams.size === 0 && !message.UrlParams) {
      return isRouteMatched(pathname) ? { path: pathname } : undefined;
    }
    // 带上变量，因目前质控是restful风格，所以这里拼接的是restful风格的路由
    let pathWithParam = `${pathname}${Array.from(searchParams.values())
      .map((key) => `/${key}`)
      .join("")}`;
    if (message.UrlParams) {
      const urlParams = new URLSearchParams(message.UrlParams);
      pathWithParam += `?${urlParams.toString()}`;
    }
    return pathWithParam;
  };

  /**
   * @description: 解析消息内容
   * @param message
   * @return
   */
  const parseMessageContent = (message: MQMessage): string => {
    let messageContent: string = "";
    if (typeof message.Message === "string") {
      messageContent = message.Message as string;
      return messageContent;
    }
    // 根据后端传的消息格式化参数解析消息
    if (message.MessageFormatter) {
      // 暂时用不到，先不写逻辑
      messageContent = JSON.stringify(message.Message);
      return messageContent;
    }
    messageContent = JSON.stringify(message.Message);
    return messageContent;
  };
  return {
    /**
     * @description: 根据员工编号订阅单人消息
     * @param employeeID 员工编号
     * @param callBack 回调函数，若不传走默认，否则 传入的回调
     * @return
     */
    createMQByEmployeeID(employeeID: string, callBack?: Function) {
      if (mqSetting.isOpen) {
        const mqClient = getMqClient();
        // 订阅单人MQ消息
        mqClient.receive(employeeID, (message: MQMessage) => {
          if (callBack) {
            callBack(message);
          } else {
            displayMessage(message, employeeID, employeeID);
          }
        });
      }
    },
    /**
     * @description: 根据交换机和条件订阅广播消息
     * @param exchangeName 交换机名称
     * @param routingKey 仅在消息发送类型为广播时可用，路由键/条件
     *  限定为一个由`.`分隔的字符串，其中每词可以包含如下两种特殊字符
     * *：匹配一个字符 eg:*.orange.*：匹配orange开头和结尾的单词
     * #：匹配0~n个字符 eg:lazy.#：匹配lazy开头的单词
     * #: 如果路由模式仅是一个井号 (#)，那么它将匹配所有的 routing keys。
     * @param callBack 回调函数，若不传走默认，否则 传入的回调
     * @return
     */
    createMQByBroadcast(exchangeName: string, routingKey?: string, callBack?: Function) {
      if (mqSetting.isOpen) {
        const mqClient = getMqClient();
        // 订阅广播MQ消息
        mqClient.subscribe(exchangeName, routingKey, (message: MQMessage) => {
          if (callBack) {
            callBack(message, exchangeName, routingKey);
          } else {
            displayMessage(message, exchangeName, routingKey);
          }
        });
      }
    }
  };
}
