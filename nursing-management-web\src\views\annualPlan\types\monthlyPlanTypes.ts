/*
 * FilePath     : /src/views/annualPlan/types/monthlyPlanTypes.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-27 20:15
 * Description  : 月度计划DTO类型定义（包含Command和Query）
 * CodeIterationRecord: 
 */
import type { workType, annualPrincipal } from "./common";
import type { importWorkDto } from "./importWorkDto";

/**
 * 保存月度计划工作参数 - 月度工作项
 */
interface workDtoForSaveCommand {
  /**
   * 工作主键
   */
  monthlyPlanDetailID: string;
  /**
   * 所属分类
   */
  typeID: number;
  /**
   * 分解目标任务字典ID
   */
  apInterventionID?: number | undefined;
  /**
   * 工作类型
   */
  workType: workType;
  /**
   * 序号
   */
  sort?: number | undefined;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作内容
   */
  workContent: string;
  /**
   * 是否临时新增
   */
  isTemp: boolean;
  /**
   * 负责人信息
   */
  principals: annualPrincipal[];
}

/**
 * 保存月度计划工作参数DTO
 */
export interface saveMonthlyWorksCommand {
  /**
   * 月度计划主键
   */
  monthlyPlanMainID: string;
  /**
   * 待保存月度计划工作列表
   */
  workViews: workDtoForSaveCommand[];
}

/**
 * 导入月度计划工作命令
 */
export interface importMonthlyWorksCommand {
  /**
   * 年度计划主表ID
   */
  annualPlanMainID: string;
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string | undefined;
  /**
   * 年份
   */
  annual: number;
  /**
   * 工作所属月度
   */
  month: number;
  /**
   * 科室ID
   */
  departmentID: number;
  /**
   * 工作内容
   */
  workViews: importWorkDto[];
  /**
   * 是否是首次导入
   */
  isFirstImport: boolean;
}

/**
 * 更新月度计划工作命令
 */
export interface updateMonthlyPlanWorkCommand {
  /**
   * 主键
   */
  monthlyPlanDetailID: string;
  /**
   * 工作内容
   */
  workContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作类别
   */
  workType: workType;
  /**
   * 序号
   */
  sort?: number | undefined;
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人集合
   */
  principals: annualPrincipal[];
}

/**
 * 删除月度计划工作参数
 */
export interface deleteMonthlyWorkQuery {
  /**
   * 月度计划详情ID
   */
  monthlyPlanDetailID: string;
}

/**
 * 查询月度计划主键参数
 */
export interface getMonthlyPlanMainIDQuery {
  /**
   * 年度计划主表ID
   */
  annualPlanMainID: string;
  /**
   * 月份
   */
  month: number;
}

/**
 * 发布月度计划参数
 */
export interface publishMonthlyPlanCommand {
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string;
}

/**
 * 查询月度计划工作参数
 */
export interface getMonthlyWorksQuery {
  /**
   * 年度计划主表ID
   */
  annualPlanMainID: string;
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string;
}

/**
 * 查询月度计划状态参数
 */
export interface getMonthlyPlanStatusQuery {
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string;
}

/**
 * 重排序月度计划工作命令
 */
export interface resetMonthlyWorksSortCommand {
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string;
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 工作ID与新序号的映射
   */
  planWorkIDAndSort: Record<string, number>;
}

/**
 * 查询可导入月度计划工作参数
 */
export interface getCanImportMpWorksQuery {
  /**
   * 季度计划主表ID
   */
  quarterPlanMainID: string;
  /**
   * 月度计划主表ID
   */
  monthlyPlanMainID: string;
  annual: number;
  /**
   * 月份
   */
  month: number;
  /**
   * 科室ID
   */
  departmentID: number;
}

/**
 * 查询上级部门月度计划工作参数
 */
export interface getMonthlyPlanQuickReferenceVoQuery {
  /**
   * 年度
   */
  annual: number;
  /**
   * 月度
   */
  month: number;
  /**
   * 科室ID
   */
  departmentID: number;
  /**
   * 指定执行项目字典ID
   */
  apInterventionID?: number;
}

/**
 * 查询月度计划预览参数
 */
export interface getMonthlyPlanPreviewQuery {
  /**
   * 月度计划主键
   */
  monthlyPlanMainID: string;
}

/**
 * 查询浏览月度计划视图参数
 */
export interface getBrowseMPViewsQuery {
  /**
   * 年度
   */
  year: number;
}