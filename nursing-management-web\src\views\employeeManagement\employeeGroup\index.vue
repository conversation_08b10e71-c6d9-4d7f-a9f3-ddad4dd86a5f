<!--
 * FilePath     : \src\views\employeeManagement\employeeGroup\index.vue
 * Author       : 杨欣欣
 * Date         : 2025-06-11 17:50
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 17:04
 * Description  : 用户组管理
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="employee-group-manage" headerHeight="auto" :drawerOptions="drawerOptions">
    <template #header>
      <el-button class="add-button" v-permission:B="1" type="primary" @click="createEmployeeGroup"> 新增 </el-button>
    </template>
    <el-table row-key="groupID" :data="employeeGroups" stripe border height="100%">
      <el-table-column prop="groupID" label="主键" :width="convertPX(100)" v-if="sessionStore.debugMode" />
      <el-table-column prop="groupName" label="组名" :width="convertPX(150)" />
      <el-table-column label="成员" class-name="members">
        <template #default="{ row }">
          <el-tag v-for="{ employeeID, employeeName } in row.members" :key="employeeID" effect="dark" class="member-tag">
            {{ employeeName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" label="维护人" :width="convertPX(120)" />
      <el-table-column label="最近维护时间" :width="convertPX(160)" align="center">
        <template #default="{ row }">
          <div v-formatTime="{ value: row.modifyDateTime, type: 'dateTime' }"></div>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(200)" align="center">
        <template #default="{ row, $index }">
          <template v-if="userStore.employeeID === row.modifyEmployeeID">
            <el-tooltip content="编辑">
              <i class="iconfont icon-edit" v-permission:B="3" @click.stop="editEmployeeGroup(row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteEmployeeGroup(row.groupID, $index)"></i>
            </el-tooltip>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <employee-group-editor ref="employeeGroupEditor" :employee-group="editingEmployeeGroup" @submit="updateEmployeeGroup">
        <template #default="{ value, validateForm }">
          <div class="operate-section">
            <el-button @click="drawerOptions.cancel">取消</el-button>
            <el-button type="primary" v-permission:B="2" @click="handleEmployeeGroupSave(value, validateForm)">保存</el-button>
          </div>
        </template>
      </employee-group-editor>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX = inject("convertPX") as any;
const { sessionStore, userStore } = useStore();
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "编辑用户组",
  showDrawer: false,
  drawerSize: "40%",
  showCancel: false,
  showConfirm: false,
  className: "group-editor-drawer",
  cancel: () => {
    editingEmployeeGroup.value = {};
    drawerOptions.value.showDrawer = false;
  }
});

onMounted(async () => {
  await getEmployeeGroups();
});

//#region 列表
const employeeGroups = ref<EmployeeGroupVo[]>([]);
/**
 * @description: 获取用户组列表
 */
const getEmployeeGroups = async () => {
  employeeGroups.value = await employeeGroupService.getEmployeeGroups();
};
/**
 * @description: 编辑用户组
 * @param employeeGroup 用户组对象
 * @return
 */
const editEmployeeGroup = (employeeGroup: EmployeeGroupVo) => {
  editingEmployeeGroup.value = employeeGroup;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 删除用户组
 * @param groupID 用户组ID
 * @param index 用户组在列表中的索引
 * @return
 */
const deleteEmployeeGroup = async (groupID: number, index: number) => {
  if (groupID === 0) {
    return;
  }
  confirmBox("确定删除？", "", async (flag: boolean) => {
    if (!flag) {
      return;
    }
    const params = {
      groupID
    };
    const result = await employeeGroupService.deleteEmployeeGroup(params);
    if (result) {
      employeeGroups.value.splice(index, 1);
      showMessage("success", "删除成功");
      return;
    }
  });
};
//#endregion

const isAdd = computed(() => !editingEmployeeGroup.value || editingEmployeeGroup.value.groupID === 0);
const handleEmployeeGroupSave = (value: EditorResult, validateForm: Function) =>
  isAdd.value ? addEmployeeGroup(value, validateForm) : updateEmployeeGroup(value, validateForm);
/**
 * @description: 保存前检查
 * @param validateForm 表单验证方法
 * @return
 */
const saveCheck = async (validateForm: Function) => {
  if (!validateForm) {
    throw new Error("用户组编辑组件未正常提供 validateForm 方法，请检查程序");
  }
  return await validateForm();
};
//#region 新建
/**
 * @description: 新建用户组
 */
const createEmployeeGroup = () => {
  editingEmployeeGroup.value = {
    groupID: 0,
    groupName: "",
    members: [],
    modifyEmployeeName: userStore.userName,
    modifyDateTime: datetimeUtil.getNow()
  };
  drawerOptions.value.showDrawer = true;
};
type EditorResult = {
  groupName: string;
  members: MemberInformation[];
  memberIDs: string[];
};
/**
 * @description: 持久化新增用户组
 * @param employeeGroup 用户组对象
 * @param validateForm 表单验证方法
 * @return
 */
const addEmployeeGroup = async ({ groupName, members, memberIDs }: EditorResult, validateForm: Function) => {
  if (!(await saveCheck(validateForm))) {
    return;
  }
  const params: AddEmployeeGroupDto = {
    groupName: groupName,
    employeeIDs: memberIDs
  };
  const groupID = await employeeGroupService.addEmployeeGroup(params);
  if (groupID) {
    employeeGroups.value.push({
      groupID: groupID,
      groupName: groupName,
      members: members,
      modifyEmployeeID: "",
      modifyEmployeeName: userStore.userName,
      modifyDateTime: datetimeUtil.getNow()
    });
    editingEmployeeGroup.value = {};
    drawerOptions.value.showDrawer = false;
    return;
  }
  editingEmployeeGroup.value = {};
  drawerOptions.value.showDrawer = false;
};
//#endregion

//#region 更新
const editingEmployeeGroup = ref<Partial<EmployeeGroupVo>>({});
/**
 * @description: 更新用户组
 * @param employeeGroup 用户组对象
 * @param validateForm 表单验证方法
 * @return
 */
const updateEmployeeGroup = async ({ groupName, memberIDs }: EditorResult, validateForm: Function) => {
  if (!(await saveCheck(validateForm))) {
    return;
  }
  if (
    groupName === editingEmployeeGroup.value.groupName &&
    editingEmployeeGroup.value.members?.every((employee) => memberIDs.includes(employee.employeeID))
  ) {
    drawerOptions.value.showDrawer = false;
    return;
  }
  const params: UpdateEmployeeGroupDto = {
    groupID: editingEmployeeGroup.value.groupID!,
    groupName: groupName,
    employeeIDs: memberIDs
  };
  const result = await employeeGroupService.updateEmployeeGroup(params);
  if (!result) {
    return;
  }
  drawerOptions.value.showDrawer = false;
  getEmployeeGroups();
};
//#endregion
</script>
<style scoped lang="scss">
.employee-group-manage :deep(.add-button) {
  margin: 8px 0;
}
.group-editor-drawer {
  height: 100%;
}
.operate-section {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
:deep(.members) > .cell {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
.member-tag {
  font-size: 20px;
  line-height: 25px;
  height: auto;
}
</style>
