/*
 * FilePath     : \src\api\menuService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 苏军志
 * LastEditTime : 2023-09-01 19:26
 * Description  : 菜单相关Api接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";

export class menuService {
  private static getMenuListApi: string = "/menu/GetMenuList";

  // 获取菜单
  public static getMenuList(params: any) {
    return http.get(this.getMenuListApi, params, { loadingText: Loading.LOAD });
  }
}
