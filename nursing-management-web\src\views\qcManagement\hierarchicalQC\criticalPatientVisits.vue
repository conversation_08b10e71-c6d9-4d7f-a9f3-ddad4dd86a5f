<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\criticalPatientVisits.vue
 * Author       : 郭鹏超
 * Date         : 2024-07-08 14:44
 * LastEditors  : 马超
 * LastEditTime : 2025-06-25 11:24
 * Description  : 危重患者访视
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="critical-patient-visits" headerHeight="55" :drawerOptions="drawerOptions">
    <template #header>
      <div class="filter-condition">
        <div class="left">
          <el-radio-group v-model="searchView.profileID">
            <el-radio-button
              v-for="(type, index) in criticalPatientTypeList"
              :key="index"
              :label="type.label"
              :value="type.value"
              @change="getDepartmentList()"
            />
          </el-radio-group>
          <span>日期:</span>
          <el-date-picker
            v-model="searchView.startDate"
            type="date"
            class="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :clearable="false"
            :disabled-date="(date:Date)=>disabledDate(date,'start')"
            @change="getDepartmentList()"
          />
          <span>-</span>
          <el-date-picker
            v-model="searchView.endDate"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            class="date"
            :clearable="false"
            :disabled-date="(date:Date)=>disabledDate(date, 'end')"
            @change="getDepartmentList()"
          />
          <el-input v-if="searchView.profileID === '10'" v-model="patientChartNo" placeholder="请输入患者住院号" class="search-input">
            <template #append>
              <i @click="searchKeyPatient" class="iconfont icon-search" />
            </template>
          </el-input>
          <div class="right-controls">
            <el-radio-group v-model="searchView.visitsFlag" @change="filterVisitList">
              <el-radio :value="false">未访视</el-radio>
              <el-radio :value="true">已访视</el-radio>
            </el-radio-group>
            <span v-if="searchView.visitsFlag">
              <el-radio-group v-model="readFlag" @change="filterReadLists">
                <el-radio :value="false">未阅读</el-radio>
                <el-radio :value="true">已阅读</el-radio>
              </el-radio-group>
            </span>
          </div>
        </div>
        <export-excel :exportExcelOption="exportExcelOption">
          <el-button class="print-button" @click="createExportExcelParam"> 导出数据 </el-button>
        </export-excel>
      </div>
    </template>
    <department-check-box
      v-model:selectedDistrict="districtValue"
      v-model:checkedDepartments="departmentCheckList"
      :district-options="districtOptions"
      :department-groups="departmentGroups"
      :count-field="'noVisitCount'"
      :disabled="deptOptionFlag"
      @district-change="changeDistrict"
    />
    <record-and-main-layout class="visit-main-record" v-model="switchArr" :recordHiddenHeight="135" headerHeight="5px">
      <template #recordHeader></template>
      <template #recordContent>
        <div class="patient-view">
          <dynamic-table
            v-model="recordTableView.tableData"
            :headerList="recordTableView.tableHeader"
            @rowClick="recordRowClick"
            :row-class-name="getRowClassName"
          >
            <template #operate="scope">
              <span v-if="isAreaDirector">
                <el-tooltip content="新增">
                  <i class="iconfont icon-add" v-permission:B="1" @click.stop="addVisits(scope.row, true)"></i>
                </el-tooltip>
                <el-tooltip content="忽略">
                  <i class="iconfont icon-ignore" v-permission:B="1" @click.stop="handleIgnoreClick(scope.row)"></i>
                </el-tooltip>
                <el-tooltip v-if="searchView.visitsFlag" content="删除">
                  <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteAllRecord(scope.row)"></i>
                </el-tooltip>
              </span>
            </template>
          </dynamic-table>
        </div>
      </template>
      <template #mainHeader>
        <div class="qc-record">
          <span class="qc-record-title">考核记录</span>
          <span v-if="isAreaDirector"><el-button class="add-button" @click="addVisits(currentRecord!, true)">新增</el-button></span>
        </div>
      </template>
      <template #mainContent>
        <dynamic-table v-model="qcRecordTableView.tableData" :headerList="qcRecordTableView.tableHeader">
          <template #operate="scope">
            <span v-if="isAreaDirector"
              ><el-tooltip content="修改">
                <i class="iconfont icon-edit" v-permission:B="3" @click.stop="modifyVisits(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteRecord(scope.row)"></i> </el-tooltip
            ></span>
            <span v-else>
              <el-tooltip content="阅读">
                <i class="iconfont icon-preview" v-permission:B="3" @click.stop="modifyVisits(scope.row)"></i>
              </el-tooltip>
            </span>
          </template>
        </dynamic-table>
      </template>
    </record-and-main-layout>
    <template #drawerContent>
      <qcForm v-model="saveView" ref="qcFormDom" :formData="formData" @getDetails="getDetails"></qcForm>
    </template>
    <template #drawerOtherFooter>
      <div>
        <strong>标准得分：{{ saveView.point }}</strong>
        <span v-if="isAreaDirector">
          <el-button @click="drawerOptions.showDrawer = false">取消</el-button>
          <el-button v-permission:B="8" class="print-button" @click="saveVisits('T')">暂存</el-button>
          <el-button v-permission:B="2" type="primary" @click="saveVisits('S')">保存</el-button>
        </span>
        <span v-else>
          <el-button v-permission:B="2" type="primary" @click="saveAlreadyRecord">阅读</el-button>
        </span>
      </div>
    </template>
    <el-drawer
      v-model="ignoreDialogVisible"
      title="忽略原因"
      size="30%"
      direction="rtl"
      :with-header="true"
      @close="ignoreReason = ''"
      class="ignore-drawer-mini"
    >
      <div class="ignore-drawer-content">
        <el-input
          v-model="ignoreReason"
          type="textarea"
          :rows="5"
          placeholder="请输入忽略原因"
          maxlength="200"
          show-word-limit
          class="ignore-drawer-input"
        />
        <div class="ignore-drawer-btns">
          <el-button class="ignore-drawer-btn" @click="ignoreDialogVisible = false">取消</el-button>
          <el-button class="ignore-drawer-btn" type="primary" @click="handleIgnoreConfirm">确定</el-button>
        </div>
      </div>
    </el-drawer>
  </base-layout>
</template>

<script lang="ts" setup>
import { useVisitsAndSupervisionOperations } from "./hooks/useVisitsAndSupervisionOperations";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
const { getIsAreaDirector } = useQcCommonMethod();
const { userStore } = useStore();
const isAreaDirector = ref<boolean>(getIsAreaDirector());
const readFlag = ref<boolean>(false);
let currentMaintenance = ref<Record<string, any>>();
onMounted(async () => {
  await getCriticalPatientType();
  await getDepartmentList();
  document.oncontextmenu = (event) => event.stopPropagation();
});
onBeforeUnmount(() => {
  document.oncontextmenu = (event) => event.preventDefault();
});
// 组件显示隐藏控制开关
const switchArr = ref<boolean[]>([true, false]);
/**
 * @description: 获取访视类型
 */
const getCriticalPatientType = async () => {
  let params: SettingDictionaryParams = {
    settingType: "Medical",
    settingTypeCode: "PatientProfileRecord",
    settingTypeValue: "ProfileID"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    criticalPatientTypeList.value = datas;
    (criticalPatientTypeList.value ?? []).length !== 0 && (searchView.value.profileID = datas[0].value);
  });
};

/**
 * @description: 开始结束时间禁用设置
 * @param date
 * @param type
 * @return
 */
const disabledDate = (date: Date, type: string) => {
  const formatDate = datetimeUtil.formatDate(date, "yyyy-MM-dd");
  const dateDifference = datetimeUtil.getTimeDifference(
    formatDate,
    searchView.value[type === "start" ? "endDate" : "startDate"],
    "date",
    "D"
  );
  if (!dateDifference) {
    return;
  }
  return type === "start" ? Number(dateDifference) < 0 : Number(dateDifference) > 0;
};
const districtResponse = ref<Record<string, any>[]>([]);
const districtOptions = ref<any>([]);
const districtValue = ref<any>();
const departmentGroups = ref<any>([]);
const searchViewRes = ref<any>();
let recordTableView = ref<Record<string, Record<string, any>[]>>({});
const criticalPatientTypeList = ref<Record<string, string>[]>();
const departmentCheckList = ref<any[]>([]);
const searchView = ref<Record<string, any>>({
  visitsFlag: !getIsAreaDirector(),
  startDate: datetimeUtil.addDate(datetimeUtil.getNowDate(), -14, "yyyy-MM-dd"),
  endDate: datetimeUtil.getNowDate(),
  profileID: "",
  stationIDs: [],
  chartNo: []
});
/**
 * @description: 获取访视患者信息
 */
const getCriticalPatientVisitsRecord = () => {
  hierarchicalQCService.getCriticalPatientVisitsRecord(searchView.value).then((res: any) => {
    res && (searchViewRes.value = res);
    filterVisitList();
  });
};
const filterReadLists = () => {
  switchArr.value = [true, false];
  filterVisitList();
};
/**
 * @description: 监听筛选条件
 * @param searchView
 * @return
 */
watch(departmentCheckList, async () => {
  recordTableView.value.tableData = [];
  if (switchArr.value[1]) {
    switchArr.value = [true, false];
  }
  departmentCheckList.value.forEach((element) => {
    if (searchView.value.visitsFlag) {
      let currentRecord: any = searchViewRes.value.tableData.filter((item: any) => item.occDepartmentID === element && item.lastVisitScore);
      if (currentRecord.length > 0) {
        currentRecord.forEach((item: any) => {
          recordTableView.value.tableData.push(item);
        });
      }
    } else {
      let currentRecord: any = searchViewRes.value.tableData.filter(
        (item: any) => item.occDepartmentID === element && !item.lastVisitScore
      );
      currentRecord.forEach((item: any) => {
        recordTableView.value.tableData.push(item);
      });
    }
    filterReadList();
  });
});
/**
 * @description: 筛选访视患者信息
 * @return
 */
// 修改 filterVisitList 函数，切换未访视时重置阅读状态
const filterVisitList = () => {
  // 当切换到未访视时，重置阅读状态为未阅读
  if (!searchView.value.visitsFlag) {
    readFlag.value = false;
  }

  // 如果是通过住院号查询，直接显示所有数据
  if (isChartNoSearch.value) {
    recordTableView.value.tableData = common.clone(searchViewRes.value.tableData);
    if (searchView.value.visitsFlag) {
      filterReadList();
    }
    return;
  }

  // 原有逻辑：按病区筛选
  let result: any[] = [];
  if (searchView.value.visitsFlag) {
    departmentCheckList.value.forEach((element) => {
      const filteredData = searchViewRes.value.tableData.filter((item: any) => item.occDepartmentID === element && item.lastVisitScore);
      result = result.concat(filteredData);
    });
  } else {
    if (switchArr.value[1]) {
      switchArr.value = [true, false];
    }
    departmentCheckList.value.forEach((element) => {
      const filteredData = searchViewRes.value.tableData.filter((item: any) => item.occDepartmentID === element && !item.lastVisitScore);
      result = result.concat(filteredData);
    });
  }
  recordTableView.value.tableData = result;
  if (searchView.value.visitsFlag) {
    filterReadList();
  }
};

// 修改 filterReadList 函数，增加状态判断
const filterReadList = () => {
  // 仅在已访视状态下进行阅读筛选
  if (!searchView.value.visitsFlag) {
    return;
  }
  if (readFlag.value) {
    recordTableView.value.tableData = recordTableView.value.tableData.filter((item: any) => item.isRead);
  } else {
    recordTableView.value.tableData = recordTableView.value.tableData.filter((item: any) => !item.isRead);
  }
};

let currentRecord = ref<Record<string, any>>();
/**
 * @description:访视患者信息行点击
 * @param row
 * @return
 */
const recordRowClick = (row: any) => {
  // 未访视 无访视维护记录查看
  if (!searchView.value.visitsFlag) {
    switchArr.value[1] = false;
    return;
  }
  switchArr.value[1] = !switchArr.value[1];
  currentRecord.value = switchArr.value[1] ? row : undefined;

  // 恢复原有逻辑，但添加对住院号查询的特殊处理
  if (switchArr.value[1]) {
    // 展开维护记录时，如果是住院号查询，保持所有数据
    if (isChartNoSearch.value) {
      // 保持当前数据不变
    } else {
      // 原有逻辑：只显示当前行
      recordTableView.value.tableData = [row];
    }
  } else {
    // 关闭维护记录时，恢复筛选后的数据
    filterVisitList();
  }

  // 根据访视信息获取访视记录
  switchArr.value[1] && getVisitsQcRecord();
};
let qcRecordTableView = ref<Record<string, Record<string, any>[]>>({});
/**
 * @description: 获取访视记录
 */
const getVisitsQcRecord = () => {
  let params = {
    sourceID: currentRecord.value?.sourceID,
    sourceType: currentRecord.value?.sourceType,
    profileID: currentRecord.value?.profileID,
    templateCode: currentRecord.value?.templateCode
  };
  hierarchicalQCService.getVisitsQcRecord(params).then((res: any) => {
    res && (qcRecordTableView.value = res);
  });
};
/**
 * @description: 抽屉控制参数
 */
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false
});
import { saveClass } from "./types/hierarchicalSaveView";
const saveView = reactive(new saveClass());
saveView.guidanceLabel = "存在问题及指导：";
saveView.improvementShowFlag = false;
const { record, details, formData, getDetails, save, add, modify, initFormData } = useVisitsAndSupervisionOperations(
  saveView,
  currentRecord,
  currentMaintenance
);
/**
 * @description:访视记录新增
 * @param row
 * @param addFlag
 * @return
 */
const addVisits = (row: Record<string, any>, addFlag: boolean) => {
  let { nmDepartmentName, bedNumber, patientName } = row;
  drawerOptions.value.drawerTitle = nmDepartmentName + "-" + bedNumber + "床-" + patientName;
  let params = {
    profileID: searchView.value.profileID,
    qcMainID: saveView.hierarchicalQCMainID
  };
  add(row, addFlag, undefined);
  initFormData();
  hierarchicalQCService.getVisitsQcAssessView(params).then((res) => {
    if (res) {
      formData.value = res;
    }
  });
  drawerOptions.value.showDrawer = true;
};

/**
 * @description: 访视记录修改
 * @param row
 * @return
 */
const modifyVisits = async (row: Record<string, any>) => {
  currentMaintenance.value = row;
  // 先调用 modify 函数，它会设置 saveView 的其他属性
  initFormData();
  modify(row, async () => {
    let params = {
      profileID: searchView.value.profileID,
      qcMainID: saveView.hierarchicalQCMainID
    };
    return await hierarchicalQCService.getVisitsQcAssessView(params);
  });
  // 在 modify 之后设置 qcDate，确保不会被覆盖
  saveView.qcDate = row.visitDateTime;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description:访视记录保存
 * @param saveType
 * @return
 */
const saveVisits = async (saveType: string) => {
  save(saveType, (qcMainAndDetail) => {
    let params = {
      visitsFormID: currentRecord.value?.visitsFormID,
      upNMDepartmentID: currentRecord.value?.upNmDepartmentID,
      visitsRecord: record.value,
      visitsDetails: details.value,
      nurseEmployeeID: currentRecord.value?.nurseEmployeeID,
      qcMainAndDetail
    };
    hierarchicalQCService.saveVisitsRecord(params).then(async (res) => {
      res && showMessage("success", "保存成功");
      drawerOptions.value.showDrawer = false;
      currentMaintenance.value = undefined;
      switchArr.value[1] ? getVisitsQcRecord() : getCriticalPatientVisitsRecord();
    });
  });
};
/**
 * @description: 删除所有访视记录
 * @param row
 * @return
 */
const deleteAllRecord = (row: Record<string, any>) => {
  const { sourceType, sourceID } = row;
  let params = {
    sourceType,
    sourceID,
    relatedTableName: "HierarchicalQCRecord"
  };
  confirmBox("确定要删除所有访视记录么？", "访视记录删除", (flag: Boolean) => {
    if (flag) {
      hierarchicalQCService.deleteAllVisitsRecord(params).then((res) => {
        if (res) {
          showMessage("success", "删除成功");
          switchArr.value[1] = false;
          getCriticalPatientVisitsRecord();
        }
      });
    }
  });
};
/**
 * @description: 删除单条访视记录
 * @param row
 * @return
 */
const deleteRecord = (row: Record<string, any>) => {
  let params = {
    patientProfileRecordIDs: [row.patientProfileRecordID]
  };
  confirmBox("确定要删除访视记录么？", "访视记录删除", (flag: Boolean) => {
    if (flag) {
      hierarchicalQCService.deleteVisitsRecord(params).then((res) => {
        if (res) {
          showMessage("success", "删除成功");
          switchArr.value[1] = false;
          getCriticalPatientVisitsRecord();
        }
      });
    }
  });
};
const deptOptionFlag = ref<boolean>(false);
/**
 * @description: 获取病区列表
 * @return
 */
const getDepartmentList = async () => {
  districtOptions.value = [];
  let params = {
    employeeID: userStore.employeeID
  };
  await dictionaryService.getVisitsDeptOptions(params).then((res: any) => {
    districtResponse.value = res;
    districtResponse.value.forEach((item: any) => {
      districtOptions.value.push({
        label: item.label,
        value: item.value
      });
    });
    if (districtOptions.value.length === 1) {
      deptOptionFlag.value = true;
    }
    districtValue.value = districtOptions.value[0].value;
  });
  await changeDistrict();
  if (isAreaDirector.value) {
    recordTableView.value.tableData = [];
    departmentCheckList.value = [userStore.departmentID];
    departmentCheckList.value.forEach((element) => {
      let currentRecord: any = searchViewRes.value.tableData.filter((item: any) => item.occDepartmentID === element && item.lastVisitScore);
      if (currentRecord.length > 0) {
        currentRecord.forEach((item: any) => {
          recordTableView.value.tableData.push(item);
        });
      }
    });
    filterReadList();
  }
};
/**
 * @description: 片区改变
 * @param value
 * @return
 */
const changeDistrict = async () => {
  departmentGroups.value = [];
  let currentDistrict: any = districtResponse.value.find((item) => item.value === districtValue.value);
  if (currentDistrict.children.length <= 0) {
    return;
  }
  departmentGroups.value = currentDistrict.children;
  searchView.value.stationIDs = currentDistrict.children.map((item: any) => item.value);
  await hierarchicalQCService.getCriticalPatientVisitsRecord(searchView.value).then((res: any) => {
    res && (searchViewRes.value = res);
    recordTableView.value.tableHeader = searchViewRes.value.tableHeader;
  });
  departmentGroups.value.forEach((item: any) => {
    // 找到searchViewRes.value中和当前item.value相同的数据集合
    let currentData = searchViewRes.value?.tableData.filter((data: any) => data.occDepartmentID === item.value);
    if (!currentData || currentData.length === 0) {
      item.noVisitCount = "0/0";
      return; // 跳过空数据的情况
    }
    let filterData = currentData.filter((data: any) => !data.lastVisitScore);
    item.noVisitCount = `${filterData.length}/${currentData.length}`;
  });
  departmentGroups.value;
};
/**
 * @description: 获取行样式
 * @param row 行数据
 * @param rowIndex 行索引
 * @return
 */
const getRowClassName = ({ row }: { row: any }) => {
  if (row.dischargeDateTime) {
    return "discharge-row";
  }
  if (
    row.startDateTime &&
    !row.lastVisitScore &&
    datetimeUtil.addDate(row.startDateTime, 3, "yyyy-MM-dd") < datetimeUtil.getNowDate("yyyy-MM-dd")
  ) {
    return "no-evaluate-row";
  }
  return "";
};
// 导出的Excel参数
const exportExcelOption = ref<ExportExcelView[]>([]);
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(recordTableView.value.tableData);
  const columns = recordTableView.value.tableHeader.reduce((acc, item) => {
    if (item.prop) {
      acc[item.prop] = item.label;
    }
    return acc;
  }, {});
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: "危重患者访视记录",
    sheetName: "危重患者访视记录",
    columnData: columns,
    tableData: cloneData
  });
};
/**
 * @description: 访视记录已阅读
 * @return
 */
const saveAlreadyRecord = async () => {
  let params = {
    hierarchicalQCMainID: saveView.hierarchicalQCMainID
  };
  await hierarchicalQCService.saveReadRecord(params).then((res) => {
    if (res) {
      showMessage("success", "该记录已阅读");
      getCriticalPatientVisitsRecord();
      getVisitsQcRecord();
    }
    drawerOptions.value.showDrawer = false;
  });
};
const patientChartNo = ref<string>("");
// 添加标志来区分是否是通过住院号查询
const isChartNoSearch = ref<boolean>(false);

/**
 * @description: 根据患者住院号获取患者信息
 * @return
 */
const searchKeyPatient = () => {
  if (!patientChartNo.value) {
    showMessage("warning", "请输入患者住院号");
    return;
  }
  searchView.value.chartNo = [patientChartNo.value];
  isChartNoSearch.value = true; // 设置标志
  hierarchicalQCService.getCriticalPatientVisitsRecord(searchView.value).then((res: any) => {
    if (res) {
      searchViewRes.value = res;
      recordTableView.value.tableHeader = searchViewRes.value.tableHeader;
      updateDepartmentCounts();
      searchView.value.chartNo = [];
      filterVisitList();
      isChartNoSearch.value = false;
    }
  });
};

/**
 * @description: 更新病区统计数据
 */
const updateDepartmentCounts = () => {
  if (!searchViewRes.value?.tableData || !departmentGroups.value) {
    return;
  }

  departmentGroups.value.forEach((item: any) => {
    // 找到searchViewRes.value中和当前item.value相同的数据集合
    let currentData = searchViewRes.value.tableData.filter((data: any) => data.occDepartmentID === item.value);
    if (!currentData || currentData.length === 0) {
      item.noVisitCount = "0/0";
      return; // 跳过空数据的情况
    }
    let filterData = currentData.filter((data: any) => !data.lastVisitScore);
    item.noVisitCount = `${filterData.length}/${currentData.length}`;
  });
};

// 忽略弹窗相关参数
const ignoreDialogVisible = ref(false);
const ignoreReason = ref("");
const ignoreRow = ref<any>(null);
const handleIgnoreClick = (row: any) => {
  ignoreReason.value = "";
  ignoreDialogVisible.value = true;
  ignoreRow.value = row;
};
/**
 * @description: 忽略数据保存
 */
const handleIgnoreConfirm = () => {
  if (!ignoreReason.value.trim()) {
    showMessage("warning", "请填写忽略原因");
    return;
  }
  ignoreRow.value.Remarks = ignoreReason.value;
  hierarchicalQCService.saveIgnoreVisitsRecord(ignoreRow.value).then((res) => {
    if (res) {
      getCriticalPatientVisitsRecordWithUpdateCount();
    }
  });
  ignoreDialogVisible.value = false;
};
/**
 * @description: 获取访视记录并更新病区统计数据
 */
const getCriticalPatientVisitsRecordWithUpdateCount = () => {
  hierarchicalQCService.getCriticalPatientVisitsRecord(searchView.value).then((res: any) => {
    if (res) {
      searchViewRes.value = res;
      filterVisitList();
      updateDepartmentCounts();
    }
  });
};
</script>

<style lang="scss">
.critical-patient-visits {
  height: 100%;
  .qc-record {
    width: 100%;
  }
  .add-button {
    float: right;
    margin-top: 8px;
  }
  .patient-view {
    width: 100%;
    height: 100%;
  }
  .left-view {
    float: left;
    border: 1px solid #d6d4d4;
    width: 300px;

    padding: 0 10px;
    box-sizing: border-box;
  }
  .top-warp {
    margin-top: 10px;
    margin-bottom: 20px;
  }
  .bottom-warp {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .el-checkbox {
      flex: 1;
    }
  }
  .el-checkbox-group {
    line-height: normal;
    font-size: unset;
  }
  .el-radio-group {
    font-size: unset;
  }
  .checkbox-group-warp {
    border-bottom: 1px solid #dddddd;
    width: 100%;
    @include flex-aline(row, space-between);
    .count {
      width: 65px;
      text-align: center;
      height: 100%;
      font-size: 20px;
      background-color: #e6a23c;
      border-radius: 20px;
      color: #ffffff;
    }
  }
  .checkbox-group:last-child {
    border-bottom: none;
  }
  .el-checkbox {
    width: 100%;
    overflow: hidden;
    --el-border: none !important;
    .el-checkbox__inner {
      border: none !important;
      box-shadow: none !important;
    }
  }
  .el-checkbox__label {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    box-sizing: border-box;
    color: $base-color;
    margin-bottom: 0px;
  }
  .filter-condition {
    @include flex-aline(row, space-between);
    .left {
      @include flex-aline(row, start);
      flex-wrap: wrap;
      gap: 10px;

      .search-input {
        width: 300px;
      }

      .right-controls {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
    margin-bottom: 0px;
    .date {
      width: 200px;
    }
  }
  .checkbox-selected {
    width: 85%;
  }
  .visit-main-record {
    .base-header {
      @include flex-aline(row, start);
      margin-bottom: 0px;
      .el-switch {
        margin-left: 10px;
      }
      .date {
        width: 200px;
      }
      .el-radio--large {
        margin-right: 10px !important;
      }
    }
  }
  .dynamic-table {
    & .el-table--striped tr.el-table__row--striped,
    & .el-table__body tr.el-table__row.discharge-row {
      background-color: #fdf6ec !important;
    }
    & .el-table--striped tr.el-table__row--striped,
    & .el-table__body tr.el-table__row.no-evaluate-row {
      .cell {
        color: #ff0000 !important;
      }
    }
  }
}
.ignore-drawer-mini .el-drawer__header {
  padding: 10px 16px 6px 16px;
  font-size: 14px;
}
.ignore-drawer-mini .el-drawer__body {
  padding: 8px 16px 0 16px;
}
.ignore-drawer-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.ignore-drawer-input {
  width: 100%;
  font-size: 13px;
  line-height: 1.2;
  padding: 2px 0;
}
.ignore-drawer-btns {
  text-align: right;
  margin-top: 12px;
}
.ignore-drawer-btn {
  padding: 0 8px;
  font-size: 13px;
}
</style>
