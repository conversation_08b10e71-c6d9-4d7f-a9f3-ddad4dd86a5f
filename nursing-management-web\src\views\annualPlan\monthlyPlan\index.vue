<!--
 * FilePath     : \src\views\annualPlan\monthlyPlan\index.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-17 11:22
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 11:45
 * Description  : 月度计划维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="monthly-plan-maintain" headerHeight="auto">
    <template #header>
      <div class="first-header">
        <annual-plan-header v-model.month="month" :year="annual" />
        <span>
          <el-button :disabled="monthPlanStatus" type="success" @click="publishMonthPlan">{{ monthPlanStatus ? "已" : "" }}发布</el-button>
          <el-button v-if="!planManagementStore.readOnly" @click="openBatchImportDialog">导入</el-button>
          <el-button v-if="!planManagementStore.readOnly" type="primary" class="" @click="saveWorks">保存</el-button>
          <el-button type="primary" @click="createOrUpdateTasks">展出为任务</el-button>
        </span>
      </div>
      <div class="second-header">
        <el-badge :value="keyAndRoutineWorksCount[0]" class="check-tag-badge">
          <el-check-tag checked>重点</el-check-tag>
        </el-badge>
        <el-badge :value="keyAndRoutineWorksCount[1]" class="check-tag-badge">
          <el-check-tag :checked="showRoutineWorks" @change="() => (showRoutineWorks = !showRoutineWorks)">常规</el-check-tag>
        </el-badge>
      </div>
    </template>
    <div class="mp-work-plan-body">
      <!-- 平铺的分类和明细项 -->
      <div class="left-wrapper" ref="leftWrapper">
        <el-empty class="work-empty" v-if="!mpWorksByTypeList?.length">
          <template #description>
            <span class="empty-desc">
              暂无数据，你可以通过<span class="import-link" @click="openBatchImportDialog">导入</span>来挑选要添加的工作
            </span>
          </template>
        </el-empty>
        <work-table
          v-else
          v-for="(byTypeGroup, index) in mpWorksByTypeList"
          :key="byTypeGroup.typeID"
          v-model="mpWorksByTypeList[index]"
          ref="workTables"
          period="monthly"
          :planStatus="monthPlanStatus"
          :showRoutineWorks="showRoutineWorks"
          @update="updateWork"
          @delete="deleteWork"
          @editPrincipal="openPrincipalDialog"
          @editWorkReference="openWorkQuoteDialog"
          @resetSort="resetWorksSort"
        />
      </div>
      <div class="right-wrapper">
        <el-anchor :container="leftWrapper">
          <el-anchor-link
            v-for="byTypeGroup in mpWorksByTypeList"
            :key="byTypeGroup.typeID"
            :href="`#type-${byTypeGroup.typeID}`"
            :title="byTypeGroup.typeName"
          />
        </el-anchor>
      </div>
    </div>
    <!-- 批量导入弹窗 -->
    <work-batch-import-dialog
      v-model="batchImportDialogVisible"
      :departmentID
      :leftPanelData
      :rightPanelTemplate
      @confirm="handleImportWorks"
    />
    <!-- 负责人选择弹窗 -->
    <principal-selector-dialog
      v-model="principalSelectorVisible"
      :principals="principalDialogCurrentWork?.principals!"
      @confirm="handleUpdateWorkPrincipals"
    />
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanTime } from "@/hooks/usePlanTime";
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
import workBatchImportDialog from "../components/workBatchImportDialog.vue";
import principalSelectorDialog from "../components/principalSelectorDialog.vue";
import workTable from "../components/workTable.vue";
import workSelector from "../components/workSelector.vue";
import type { planAndWorksVo } from "../types/planAndWorksVo";
import type { getMonthlyPlanQuickReferenceVoQuery, saveMonthlyWorksCommand } from "../types/monthlyPlanTypes";
import type { planWorkImportVo } from "../types/planWorkImportVo";
import type { importWorkDto } from "../types/importWorkDto";
import { monthlyPlanMaintainService } from "@/api/annualPlan/monthlyPlanMaintainService";
import { usePrincipalDialog } from "../maintain/hooks/usePrincipalSelectorDialog";
import type { Action, MessageBoxState } from "element-plus/es/components/message-box/src/message-box.type";

type planWork = planAndWorksVo["children"][number];
const { getPlanAnnual, getPlanMonth } = usePlanTime();
const annual = getPlanAnnual();
const planManagementStore = usePlanManagementStore();
const { annualPlanMainID, monthlyPlanMainID, departmentID } = storeToRefs(planManagementStore);
const month = ref<number>(getPlanMonth().month);
const quarter = computed<number>(() => datetimeUtil.getQuarterByMonth(month.value));
watch<number, false>(quarter, () => planManagementStore.setQuarterPlanMainID(quarter.value, true));

onMounted(async () => {
  await planManagementStore.setAnnualPlanMainID();
  await planManagementStore.setMonthlyPlanMainID(month);
  await init();
});
watch<number, false>(departmentID, async () => {
  resetVariables();
  await planManagementStore.setAnnualPlanMainID(true);
  planManagementStore.setMonthlyPlanMainID(month, true).then(async (res) => res && (await init()));
});
watch<number, false>(month, async (newValue) => {
  resetVariables();
  planManagementStore.setMonthlyPlanMainID(newValue, true).then(async (res) => res && (await init()));
});
/**
 * @description: 初始化月度计划
 * @return
 */
const init = async () => {
  if (!monthlyPlanMainID.value) {
    return;
  }
  await Promise.all([getWorks(), getPlanStatus()]);
};

//#region 表格
const mpWorksByTypeList = ref<planAndWorksVo[]>([]);
type workTableType = InstanceType<typeof workTable>;
const workTables = useTemplateRef<workTableType[]>("workTables");
/**
 * @description: 变量重置
 */
const resetVariables = () => {
  mpWorksByTypeList.value = [];
  monthPlanStatus.value = false;
  planManagementStore.setReadOnly(false);
};

/**
 * @description: 获取计划工作
 * @return
 */
const getWorks = async () => {
  if (!monthlyPlanMainID.value) {
    return [];
  }
  const params = {
    annualPlanMainID: annualPlanMainID.value,
    monthlyPlanMainID: monthlyPlanMainID.value
  };
  mpWorksByTypeList.value = await monthlyPlanMaintainService.getMonthlyWorks(params);
};
/**
 * @description: 更新工作
 * @param workView 工作
 * @return
 */
const updateWork = (work: planWork) => {
  if (!work.monthlyPlanDetailID) {
    showMessage("error", "工作主键不能为空，请联系管理员");
    return;
  }
  monthlyPlanMaintainService.updateMonthlyWork({
    monthlyPlanDetailID: work.monthlyPlanDetailID,
    workContent: work.workContent,
    requirement: work.requirement,
    workType: work.workType,
    principalName: work.principalName,
    principals: work.principals
  });
};
/**
 * @description: 删除工作
 * @param monthlyPlanDetailID 月度计划工作主键
 * @return
 */
const deleteWork = async (monthlyPlanDetailID: string) => {
  await monthlyPlanMaintainService.deleteMonthlyWork({ monthlyPlanDetailID });
  await getWorks();
};
/**
 * @description: 批量保存工作
 * @return
 */
const saveWorks = async () => {
  // 搜集已勾选行
  if (!workTables.value) {
    showMessage("error", "发生内部错误，请联系管理员");
    return [];
  }
  const toSaveWorks = workTables.value.flatMap((workTableRef) => workTableRef.getToSaveWorks());
  if (!toSaveWorks.length) {
    showMessage("warning", "没有需要保存的工作");
    return;
  }
  const params: saveMonthlyWorksCommand = {
    monthlyPlanMainID: monthlyPlanMainID.value,
    workViews: toSaveWorks.map((planWork) => ({
      monthlyPlanDetailID: planWork.monthlyPlanDetailID!,
      typeID: planWork.typeID,
      apInterventionID: planWork.apInterventionID,
      workType: planWork.workType,
      sort: planWork.sort,
      workContent: planWork.workContent,
      requirement: planWork.requirement,
      isTemp: planWork.isTemp,
      principalName: planWork.principalName,
      principals: planWork.principals
    }))
  };
  const res = await monthlyPlanMaintainService.saveMonthlyWorks(params);
  if (res) {
    showMessage("success", "保存成功");
    nextTick(() => {
      workTables.value?.forEach((workTable) => {
        workTable.clearSelection();
      });
    });
    await getWorks();
  }
};
/**
 * @description: 重排序
 * @param typeID 分类字典ID
 * @param planWorkIDAndSort 工作ID及新序号
 * @return
 */
const resetWorksSort = async (typeID: number, planWorkIDAndSort: Record<string, number>) => {
  const params = {
    monthlyPlanMainID: monthlyPlanMainID.value,
    typeID: typeID,
    planWorkIDAndSort
  };
  await monthlyPlanMaintainService.resetMonthlyWorksSort(params);
};
//#endregion

//#region 过滤与统计
const showRoutineWorks = ref<boolean>(false);
// 计算重点工作数量
const keyAndRoutineWorksCount = computed(() =>
  mpWorksByTypeList.value.reduce(
    (total, group) => {
      const currentTypeKeyWorksCount = group.children.filter((work) => work.workType === 1).length;
      const currentTypeRoutineWorksCount = group.children.length - currentTypeKeyWorksCount;
      total[0] = total[0] + currentTypeKeyWorksCount;
      total[1] = total[1] + currentTypeRoutineWorksCount;
      return total;
    },
    [0, 0]
  )
);
//#endregion

//#region 发布
onUnmounted(() => {
  planManagementStore.setReadOnly(false);
});
const monthPlanStatus = ref<boolean>(false);
/**
 * @description: 获取计划发布状态
 */
const getPlanStatus = async () => {
  monthPlanStatus.value = await monthlyPlanMaintainService.getMonthlyPlanStatus({ monthlyPlanMainID: monthlyPlanMainID.value });
  planManagementStore.setReadOnly(monthPlanStatus.value);
};
/**
 * @description: 发布月度计划
 */
const publishMonthPlan = async () => {
  if (!monthlyPlanMainID.value) {
    showMessage("error", "暂无数据，请先维护月度计划");
    return;
  }
  confirmBox("确认要发布计划吗？", "", async (flag: boolean) => {
    if (!flag) {
      return;
    }
    const params = {
      monthlyPlanMainID: monthlyPlanMainID.value
    };
    monthPlanStatus.value = await monthlyPlanMaintainService.publishMonthlyPlan(params);
    planManagementStore.setReadOnly(monthPlanStatus.value);
  });
};
//#endregion

/**
 * @description: 展出任务
 */
const createOrUpdateTasks = async () => {
  if (!monthPlanStatus.value) {
    showMessage("error", "请先发布月度计划");
    return;
  }
  const works = mpWorksByTypeList.value
    .flatMap((byType) => byType.children)
    .map((work) => ({
      key: work.monthlyPlanDetailID,
      apInterventionID: work.apInterventionID,
      workContent: work.workContent,
      requirement: work.requirement,
      typeID: work.typeID,
      principalIDs: work.principals.map(({ employeeID }) => employeeID)
    }));
  if (works.some((work) => !(work.principalIDs?.length ?? 0))) {
    showMessage("warning", "存在未设置负责人工作，将不会展出对应工作");
  }
  const params = {
    year: annual,
    month: month.value,
    departmentID: departmentID.value,
    works
  };
  const result = await annualScheduleService.createOrUpdateTasks(params);
  if (result) {
    showMessage("success", "展出成功，可至计划执行查看");
  }
};

//#region 锚点
const leftWrapper = ref<HTMLElement>();
//#endregion

const isFirstImport = computed<boolean>(() => !mpWorksByTypeList.value.length);
//#region 批量导入弹窗
const batchImportDialogVisible = ref<boolean>(false);
const leftPanelData = ref<planWorkImportVo[]>([]);
const rightPanelTemplate = ref<planAndWorksVo[]>([]);
/**
 * @description: 打开批量导入弹窗
 */
const openBatchImportDialog = async () => {
  if (mpWorksByTypeList.value.length) {
    rightPanelTemplate.value = mpWorksByTypeList.value;
  }
  await Promise.all([
    planManagementStore.setQuarterPlanMainID(datetimeUtil.getQuarterByMonth(month.value), true),
    planManagementStore.setMonthlyPlanMainID(month)
  ]);
  const params = {
    quarterPlanMainID: planManagementStore.quarterPlanMainID,
    monthlyPlanMainID: planManagementStore.monthlyPlanMainID,
    annual,
    month: month.value,
    departmentID: toValue(departmentID)
  };
  leftPanelData.value = await monthlyPlanMaintainService.getCanImportMpWorksGroupByPlanThenType(params);
  batchImportDialogVisible.value = true;
};
/**
 * @description: 处理批量导入确认
 */
const handleImportWorks = async (workViews: importWorkDto[]) => {
  if (!workViews.length) {
    showMessage("warning", "没有选择要导入的工作");
    return;
  }
  const params = {
    annualPlanMainID: planManagementStore.annualPlanMainID,
    monthlyPlanMainID: isFirstImport.value ? undefined : planManagementStore.monthlyPlanMainID,
    annual,
    month: month.value,
    departmentID: departmentID.value,
    workViews: workViews,
    isFirstImport: isFirstImport.value
  };
  const planMainID = await monthlyPlanMaintainService.importMonthlyWorks(params);
  if (!planMainID) {
    showMessage("error", "导入失败");
  }
  if (isFirstImport.value) {
    monthlyPlanMainID.value = planMainID;
  }
  batchImportDialogVisible.value = false;
  getWorks();
};
//#endregion

//#region 快捷参考弹窗
/**
 * @description: 打开快捷参考弹窗
 * @param work 点击的工作
 * @param selectRowFunc 切换行
 * @return
 */
const openWorkQuoteDialog = async (work: planWork, selectRowFunc: (row: planWork) => void) => {
  if (!work.apInterventionID) {
    showMessage("warning", "没有可以参考的工作");
    return;
  }
  const params: getMonthlyPlanQuickReferenceVoQuery = {
    annual,
    month: month.value,
    departmentID: departmentID.value,
    apInterventionID: work.apInterventionID
  };
  const quickRefWorks = await monthlyPlanMaintainService.getMonthlyPlanQuickReferenceVos(params);
  if (!quickRefWorks.length) {
    showMessage("warning", "没有可以参考的工作");
    return;
  }
  // TODO：后续将MessageBox封装为hook，方便复用
  let currentSelectWork: planWorkImportVo["children"][number]["children"][number] | undefined = undefined;
  ElMessageBox({
    message: h(workSelector, {
      planToWorkMap: quickRefWorks.reduce((map, view) => {
        map.set(view.planName, view.children[0].children[0]);
        return map;
      }, new Map<string, planWorkImportVo["children"][number]["children"][number]>()),
      onSelect: (selectedWork: planWorkImportVo["children"][number]["children"][number]) => {
        currentSelectWork = selectedWork;
      }
    }),
    customStyle: { maxWidth: "448px" },
    showCancelButton: true,
    beforeClose: (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action !== "confirm") {
        currentSelectWork = undefined;
        done();
        return;
      }
      if (!work || !currentSelectWork) {
        done();
        return;
      }
      work.workContent = currentSelectWork.workContent;
      work.requirement = currentSelectWork.requirement;
      selectRowFunc(work);
      done();
    }
  });
};
//#endregion

//#region 负责人选择弹窗
const { principalSelectorVisible, principalDialogCurrentWork, openPrincipalDialog, handleUpdateWorkPrincipals } = usePrincipalDialog();
//#endregion
</script>
<style lang="scss" scoped>
.monthly-plan-maintain {
  gap: 8px;
  background-color: #f3f3f3;
  :deep(.base-header) {
    padding-bottom: 8px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    .first-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      line-height: auto;
      width: 100%;
    }
    .second-header {
      display: flex;
      gap: 8px;
      .check-tag-badge {
        .el-badge__content {
          top: 8px;
          right: 16px;
        }
      }
    }
  }
  .mp-work-plan-body {
    height: 100%;
    display: flex;
    .left-wrapper {
      width: 85%;
      overflow-y: auto;
      scrollbar-width: none;
      .work-empty {
        height: 100%;
        .empty-desc {
          font-size: 16px;
          color: #909399;
        }
        .import-link {
          color: #409eff;
          cursor: pointer;
          text-decoration: underline;
        }
      }
      &::-webkit-scrollbar {
        display: none;
      }
    }
    .right-wrapper {
      flex: 1;
      min-width: 0;
      padding-left: 48px;
      .month-title {
        line-height: 24px;
        letter-spacing: 1px;
        font-weight: 600;
        font-size: 20px;
      }
      .el-anchor__link {
        font-size: 18px;
        letter-spacing: 1px;
        line-height: 1.6;
        &:not(.is-active) {
          color: #707070;
        }
      }
    }
  }
}
</style>
