/*
 * FilePath     : \src\api\employeeToDepartmentService.ts
 * Author       : 张现忠
 * Date         : 2024-03-18 10:48
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-10-29 16:26
 * Description  :
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";
export class employeeDepartmentSwitchService {
  private static getEmployeeDepartmentSwitchListApi: string = "EmployeeDepartmentSwitch/GetEmployeeDepartmentSwitchList";
  private static saveEmployeeDepartmentSwitchApi: string = "EmployeeDepartmentSwitch/SaveEmployeeDepartmentSwitch";
  private static deleteEmployeeDepartmentSwitchApi: string = "EmployeeDepartmentSwitch/DeleteEmployeeDepartmentSwitch";
  private static getEmployeeSwitchCascaderApi: string = "EmployeeDepartmentSwitch/GetEmployeeSwitchCascader";
  /**
   * 获取人员角色列表
   * @param params
   * @returns array
   */
  public static GetEmployeeDepartmentSwitchList(params?: any) {
    return http.get(this.getEmployeeDepartmentSwitchListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 保存人员角色
   * @param params
   * @returns bool
   */
  public static SaveEmployeeDepartmentSwitch(params: any) {
    return http.post(this.saveEmployeeDepartmentSwitchApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * 删除人员角色
   * @param params
   * @returns bool
   */
  public static DeleteEmployeeDepartmentSwitch(params: any) {
    return http.post(this.deleteEmployeeDepartmentSwitchApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }

  /**
   * @description: 获取用户拥有权限部门的级联Option
   * @param params
   * @return
   */
  public static getEmployeeSwitchCascader(params?: any) {
    return http.get(this.getEmployeeSwitchCascaderApi, params, { loadingText: Loading.LOAD });
  }
}
