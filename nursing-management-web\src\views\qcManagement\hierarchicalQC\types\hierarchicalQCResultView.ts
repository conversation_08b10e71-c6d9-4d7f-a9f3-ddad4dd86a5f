/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\hierarchicalQCResultView.ts
 * Author       : 来江禹
 * Date         : 2023-09-20 16:09
 * LastEditors  : 马超
 * LastEditTime : 2025-04-22 17:21
 * Description  :
 * CodeIterationRecord:
 */
/**
 * 表单下拉框
 */
export interface theme {
  /**
   * 标题
   */
  label: string;
  /**
   * value值
   */
  value: string;
}
/**
 * 质控主记录数据类型
 */
export interface recordRow {
  /**
   * 主记录主键
   */
  hierarchicalQCRecordID: string;
  /**
   * 主题ID
   */
  hierarchicalQCSubjectID: string;
  /**
   * 质控主题
   */
  formName: string;
  /**
   *  考核日期
   */
  examineDate?: Date;
  /**
   * 考核人
   */
  examineEmployee: string;
  /**
   * 考核人ID
   */
  examineEmployeeIDList: string[];
  /**
   * 考核对象
   */
  examineObject: string;
  /**
   * 末次分数
   */
  lastPoint: string;
  /**
   * 首次考核时间
   */
  firstQCDateTime: Date;
  /**
   * 末次考核时间
   */
  lastQCDateTime: Date;
  /**
   * 考核次数
   */
  examineNumbers: number;
  /**
   * 审核人
   */
  auditNurse: string;
  /**
   * 状态
   */
  status: string;
  /**
   * 质控评估模板类型码
   */
  templateCode: string;
  /**
   * 质控类型
   */
  qcType: number;
  /**
   * 质控对象标识ID
   */
  qcObjectID: number;
  /**
   * 科室
   */
  examineDepartmentID: number;
  /**
   * 主题ID
   */
  qcSubjectID: string;
  /**
   * 审核人
   */
  verifierEmployeeID: string;
  /**
   * 质控开始时间
   */
  startDate: Date;
  /**
   * 达标分数
   */
  minPassingScore?: number;
}
/**
 * 维护记录数据类型
 */
export interface mainRow {
  /**
   * 维护记录ID
   */
  hierarchicalQCMainID: string;
  /**
   * 考核次数
   */
  number: string;
  /**
   * 考核日期
   */
  examineDate: Date;
  /**
   * 考核人
   */
  examineEmployee: string;
  /**
   * 当前考核人对应工号
   */
  currExamineEmployeeID: string;
  /**
   * 考核对象
   */
  examineObject: string;
  /**
   * 分数
   */
  point: string;
  /**
   * 是否可读
   */
  reader: string;
  /**
   *  是否改进
   */
  improvement?: string;
  /**
   * 提交状态
   */
  submitStatus: string;
  /**
   * 指导
   */
  guidance?: string;
  /**
   * 质控对象标识ID
   */
  qcObjectID: number;
  /**
   * 审核状态
   */
  auditStatus: string;
}
