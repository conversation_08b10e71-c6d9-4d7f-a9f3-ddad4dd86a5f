<!--
 * FilePath     : \src\views\changePassword.vue
 * Author       : 杨欣欣
 * Date         : 2024-07-10 11:50
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-07-16 15:56
 * Description  : 修改密码
 * CodeIterationRecord:
 -->
<template>
  <div class="change-password">
    <div class="change-password-warp">
      <el-form ref="formRef" class="change-password-pane" label-position="top" label-width="auto" :model="formData" :rules="rules">
        <el-form-item label="旧密码" prop="oldPassword" required>
          <el-input v-model="formData.oldPassword" type="password" placeholder="请输入旧密码" class="change-password-input" />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword" required>
          <el-input v-model="formData.newPassword" type="password" placeholder="请输入新密码" class="change-password-input" />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword" required>
          <el-input v-model="formData.confirmPassword" type="password" placeholder="请确认新密码" class="change-password-input" />
        </el-form-item>
        <el-form-item>
          <div class="change-password-button">
            <el-button type="primary" @click="submit">提交</el-button>
          </div>
        </el-form-item>
      </el-form>
      <el-divider>登录方式</el-divider>
      <div class="change-password-type-selector">
        <el-radio-group v-model="loginType">
          <el-radio value="oa" border>OA</el-radio>
          <el-radio value="his" border>HIS</el-radio>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const { userStore } = useStore();
const { validateRule } = useForm();
const formData = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});
const loginType = ref("his");
const formRef = ref<any>();
const rules = reactive({
  oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
  newPassword: [
    { required: true, message: "新密码不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value === formData.oldPassword) {
          callback(new Error("新密码不能与旧密码相同"));
          return;
        }
        callback();
      },
      trigger: "blur"
    }
  ],
  confirmPassword: [
    { required: true, message: "确认新密码不能为空", trigger: "blur" },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value !== formData.newPassword) {
          callback(new Error("两次输入的新密码不一致"));
          return;
        }
        callback();
      },
      trigger: "blur"
    }
  ]
});
const userID = computed(() => (loginType.value === "oa" ? userStore.oaUserID : userStore.hisUserID));
/**
 * @description: 提交
 */
const submit = async () => {
  const isValid = await validateRule(formRef);
  if (!isValid) {
    return;
  }
  const params = {
    loginType: loginType.value,
    passWord: formData.oldPassword,
    userID: userID.value,
    newPassword: formData.confirmPassword
  };
  const result = await userLoginService.changePassword(params);
  if (result) {
    showMessage("success", "修改密码成功");
    useLogout().loginOut();
  }
};
</script>
<style lang="scss">
$placeholder-color: #fda85e;
$main-color: #108bf7;
.change-password {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .change-password-warp {
    box-shadow: 10px 10px 30px 10px rgba(0, 0, 0, 0.2);
    background-color: #ffffff;
    padding-top: 10px;
    display: flex;
    flex-direction: column;
    width: 20%;
    .change-password-pane {
      margin-top: 18px;
      padding: 0 10px;
      box-sizing: border-box;
      .change-password-input {
        .el-input-group__prepend {
          color: #000000;
        }
        .el-input__inner {
          font-size: 14px;
          height: 32px;
          padding: 0 8px;
        }
      }
      .change-password-button {
        display: flex;
        justify-content: center;
        width: 100%;
        .el-button {
          width: 100%;
          font-size: 24px;
          height: 42px;
          margin: 0;
        }
      }
    }
    .change-password-type-selector {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      .el-radio-group {
        justify-content: center;
        gap: 16px;
        .el-radio {
          margin-right: 0 !important;
        }
      }
    }
    .change-password-type {
      display: inline-block;
      width: 56px;
      height: 56px;
      line-height: 56px;
      text-align: center;
      font-weight: bold;
      font-size: 20px;
      border-radius: 50px;
      color: #ffffff;
      cursor: pointer;
      box-shadow: 2px 3px 6px 4px rgba(0, 0, 0, 0.1);
      &.oa {
        background-color: #fc8720;
      }
      &.his {
        background-color: $base-color;
      }
      &:hover:not(.selected) {
        opacity: 0.7;
      }
      &.selected {
        background-color: $main-color;
      }
    }
  }
}
</style>
