/*
 * FilePath     : \src\views\post\types\postDescriptionView.ts
 * Author       : LX
 * Date         : 2023-09-21 11:32
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:33
 * Description  :
 */
export class postDescription {
  /**
   * 说明书编码
   */
  postDescriptionCode?: string;
  /**
   * 说明书名称
   */
  postDescriptionName: string;
  /**
   * 岗位ID
   */
  postID?: number;
  /**
   * 岗位名称
   */
  postName?: string;
  /**
   *制定部门
   */
  createDepartment?: string;
  /**
   *直接上级
   */
  superiors?: string;
  /**
   *直接下级
   */
  junior?: string;
  /**
   *岗位定员
   */
  postNumber?: string;
  /**
   *所辖人数
   */
  headCount?: string;
  /**
   *状态
   */
  status?: string;
  /**
   *版本号
   */
  version?: string;
  /**
   *创建人
   */
  addPerson?: string;
  /**
   *创建时间
   */
  addDateTime?: string;
  /**
   *修订人
   */
  modifyPerson?: string;
  /**
   *修订时间
   */
  modifyDateTime?: string;
  /**
   *审批人
   */
  approver?: string;
  /**
   *审批时间
   */
  approveDateTime?: string;
  /**
   *签发人
   */
  signer?: string;
  /**
   *签发时间
   */
  signDateTime?: string;
  /**
   *从业资格要求
   */
  qualificationRequirements?: string;
  /**
   *教育水平
   */
  educationalLevel?: string;
  /**
   *培训经历
   */
  trainingRecord?: string;
  /**
   *其他
   */
  other?: string;
  /**
   *岗位职责
   */
  responsibility?: string;
  /**
   *绩效评价
   */
  performanceEvaluation?: string;
  /**
   *岗位SOP标准
   */
  sop?: string;
  /**
   *部门ID
   */
  departmentID?: number;
  /**
   *新增标记
   */
  addFlag?: Boolean;
  /**
   *编辑标记
   */
  editFlag?: Boolean;
  /**
   *新增员工编号
   */
  addEmployeeID?: string;
  /**
   *异动员工编号
   */
  modifyEmployeeID?: string;
  /**
   *创建部门ID
   */
  createDepartmentID?: number;
  /**
   *岗位说明书状态
   */
  statusCode?: string;
  /**
   *签发人
   */
  signerName?: string;
  constructor() {
    this.postDescriptionName = "";
  }
}
export class departmentPost {
  /**
   *部门岗位编码
   */
  postID?: number;
  /**
   *部门岗位名称
   */
  departmentPostName?: string;
}
