<!--
 * FilePath     : \src\views\examineManagement\examinerSchedule.vue
 * Author       : 张现忠
 * Date         : 2025-03-10 16:11
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-03 10:44
 * Description  : 实操类考核监考人监考计划维护
 * CodeIterationRecord:
 -->

<template>
  <base-layout :drawerOptions="drawerOptions" class="examiner-schedule">
    <template #header>
      <label>监考日期：</label>
      <el-date-picker
        v-model="filterDate"
        type="daterange"
        range-separator="至"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        class="header-date"
        @change="getExaminerScheduleList()"
      />
      <label class="header-label">考核计划：</label>
      <el-select
        class="examination-record-select"
        placeholder="请选择考核计划"
        v-model="selectedExaminationRecordID"
        clearable
        @change="getExaminerScheduleList()"
      >
        <el-option
          v-for="item in examinationRecordList"
          :key="item.examinationRecordID"
          :label="item.examinationName"
          :value="item.examinationRecordID"
        />
      </el-select>
      <employee-selector
        label="监考人"
        v-model="selectedEmployeeID"
        clearable
        filterable
        :list="examinersOptions"
        @change="getExaminerScheduleList()"
      />
      <el-button class="add-button" v-permission:B="1" @click="addRecord">新增</el-button>
    </template>

    <el-table :data="planList" border stripe height="100%">
      <el-table-column :min-width="convertPX(200)" label="考核名称" prop="examinationName" />
      <el-table-column :width="convertPX(160)" label="监考日期" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.scheduleDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(160)" label="监考时段" align="center">
        <template #default="{ row }">
          <span>{{ row.scheduleTimeRange.join("-") }}</span>
        </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(100)" label="监考人" prop="examinerName" align="center" />
      <el-table-column :min-width="convertPX(100)" label="考核地点" prop="location" align="center" />
      <el-table-column :width="convertPX(120)" label="新增人" prop="addEmployeeName" align="center" />
      <el-table-column :width="convertPX(160)" label="新增时间" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(100)" label="修改人" prop="modifyEmployeeName" align="center" />
      <el-table-column :width="convertPX(160)" label="修改时间" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(90)" align="center">
        <template #default="{ row }">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" v-permission:B="3" @click="editRecord(row)" />
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteRecord(row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 抽屉  -->
    <template #drawerContent>
      <el-form :model="planData" ref="submitRefs" label-width="100px" :rules="rules">
        <el-form-item label="监考人：" prop="examiners">
          <employee-selector
            v-model="planData.examiners"
            label=""
            :list="examinersOptions"
            :width="370"
            multiple
            :multiCollapse="false"
            @select="filterExaminationRecord()"
          />
        </el-form-item>
        <el-form-item label="监考日期：" prop="scheduleDate">
          <el-date-picker
            class="examination-form-item"
            v-model="planData.scheduleDate"
            type="date"
            placeholder="请选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disableDate"
            @change="filterExaminationRecord()"
          />
        </el-form-item>
        <el-form-item label="监考时间：" prop="scheduleTimeRange">
          <el-time-picker
            class="examination-form-item"
            v-model="planData.scheduleTimeRange"
            :default-time="['08:00', '17:00']"
            is-range
            format="HH:mm"
            value-format="HH:mm"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="考核计划：" prop="examinationRecordIDs">
          <el-select
            class="examination-form-item"
            v-model="planData.examinationRecordIDs"
            placeholder="请选择考核计划"
            :multiple="true"
            :collapse-tags="false"
          >
            <el-option
              v-for="item in examinationRecords"
              :key="item.examinationRecordID"
              :label="item.examinationName"
              :value="item.examinationRecordID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考核地点：" prop="location">
          <el-input class="examination-form-item" v-model="planData.location" placeholder="请输入考核地点" clearable />
        </el-form-item>
        <template v-if="!planData.examinerScheduleID">
          <el-form-item label="批量生成计划：">
            <el-switch v-model="planData.batchFlag" />
          </el-form-item>
          <template v-if="planData.batchFlag">
            <el-form-item v-if="planData.batchFlag" label="间隔天数：" prop="batchInterval">
              <el-input-number class="examination-form-item" v-model="planData.batchInterval" :min="1" :max="30" placeholder="间隔天数" />
            </el-form-item>
            <el-form-item v-if="planData.batchFlag" label="截至日期：" prop="batchEndDate">
              <el-date-picker
                class="examination-form-item"
                v-model="planData.batchEndDate"
                type="date"
                placeholder="请选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </template>
        </template>
      </el-form>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
// #region 引入
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
import type { examinerScheduleView, examinerScheduleForm } from "./types/examinerScheduleView";
// #endregion
const planData = ref<examinerScheduleForm>({} as examinerScheduleForm);
const planList = ref<examinerScheduleView[]>([]);
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const submitRefs = ref<any>();
const examinationRecordList = ref<Record<string, any>[]>([]);
const selectedExaminationRecordID = ref<string>();
const selectedEmployeeID = ref<string>();
const examinersOptions = ref<Record<string, any>[]>([]);
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: () => saveRecord()
});

// 表单验证规则
const rules = {
  examiners: [{ required: true, message: "请选择监考人", trigger: "change" }],
  examinationRecordIDs: [{ required: true, message: "请选择考核计划", trigger: "change" }],
  scheduleDate: [{ required: true, message: "请选择监考日期", trigger: "change" }],
  scheduleTimeRange: [{ required: true, message: "请选择监考时间段", trigger: "change" }],
  batchInterval: [{ required: true, message: "请输入间隔天数", trigger: "change" }],
  batchEndDate: [{ required: true, message: "请输入截至日期", trigger: "change" }]
};
// #endregion

// #region 初始化
onMounted(async () => {
  selectedEmployeeID.value = userStore.employeeID;
  await getNotFinishedPracticalRecords();
  getExaminerScheduleList();
});
// #endregion

// #region 操作事件
/**
 * @description: 新增事件
 */
const addRecord = () => {
  let index = examinersOptions.value.findIndex((option: Record<string, any>) => option.value === userStore.employeeID);
  if (index === -1) {
    showMessage("warning", "非监考人不能添加监考计划！！");
    return;
  }
  planData.value = {
    examiners: [userStore.employeeID],
    scheduleDate: "",
    examinationRecordIDs: selectedExaminationRecordID.value ? [selectedExaminationRecordID.value] : []
  } as examinerScheduleForm;
  toggleDrawer("新增监考计划");
};
let oldPlanData: examinerScheduleForm | undefined = undefined;
/**
 * @description: 编辑记录事件
 */
const editRecord = async (row: Record<string, any>) => {
  let editFlag = true;
  if (row.statusCode === "1") {
    await confirmBox("已存在预约记录，确定要修改吗？", "确定要编辑该记录吗？", (flag: boolean) => {
      editFlag = flag;
    });
  }
  if (!editFlag) {
    return;
  }
  planData.value = {
    examinerScheduleID: row.examinerScheduleID,
    examinationRecordIDs: row.examinationRecordIDs,
    examiners: row.examiners,
    scheduleDate: row.scheduleDate,
    scheduleTimeRange: row.scheduleTimeRange,
    statusCode: row.statusCode,
    location: row.location
  };
  oldPlanData = common.clone(planData.value);
  filterExaminationRecord(true);
  toggleDrawer("编辑监考计划");
};

/**
 * @description: 打开抽屉
 * @param title 抽屉标题
 * @param showDrawer 是否显示抽屉
 * @returns
 */
const toggleDrawer = (title: string | undefined, showDrawer: Boolean = true) => {
  title && (drawerOptions.value.drawerTitle = title);
  drawerOptions.value.showDrawer = showDrawer;
};

const examinationRecords = ref<Record<string, any>[]>([]);
/**
 * @description: 根据监考人过滤考核计划
 * @returns
 */
const filterExaminationRecord = (initFlag: boolean = false) => {
  if (!initFlag) {
    planData.value.examinationRecordIDs = [];
  }
  examinationRecords.value = [];
  if (!planData.value.examiners?.length || !planData.value.scheduleDate) {
    return;
  }
  examinationRecords.value = examinationRecordList.value.filter((record: Record<string, any>) => {
    // 检查所有监考人是否都在当前考核计划的监考人列表中
    const hasExaminer = planData.value.examiners.every((item: string) => record.examiners.some((examiner: any) => examiner.value === item));
    // 检查日期是否在考核计划的时间范围内
    const scheduleDate = datetimeUtil.formatDate(planData.value.scheduleDate, "yyyy-MM-dd");
    const isInRange =
      datetimeUtil.formatDate(record.startDateTime, "yyyy-MM-dd") <= scheduleDate &&
      datetimeUtil.formatDate(record.endDateTime, "yyyy-MM-dd") >= scheduleDate;
    return hasExaminer && isInRange;
  });
};
/**
 * @description: 限制不能选择过去的日期进行监考(必须提前制定监考计划)
 * @param dateTime 日期
 * @returns true: 不禁用 false: 禁用
 */
const disableDate = (dateTime: Date) => {
  return datetimeUtil.getNow("yyyy-MM-dd") > datetimeUtil.formatDate(dateTime, "yyyy-MM-dd");
};
// #endregion

// #region 业务逻辑
/**
 * @description: 获取监考计划列表
 * @returns
 */
const getExaminerScheduleList = () => {
  let params = {
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    employeeID: selectedEmployeeID.value,
    examinationRecordID: selectedExaminationRecordID.value
  };
  examinerScheduleService.getExaminerScheduleList(params).then((result: any) => {
    if (result) {
      planList.value = result;
    }
  });
};

/**
 * @description:获取未完成的实操类考核计划
 */
const getNotFinishedPracticalRecords = async () => {
  await examineService.getNotFinishedPracticalRecords().then((records: any) => {
    if (records) {
      records.forEach((record: Record<string, any>) => {
        examinationRecordList.value.push(record);
        examinersOptions.value = [...examinersOptions.value, ...record.examiners];
      });
      // 监考人去重
      examinersOptions.value = [...new Set(examinersOptions.value.map((item) => JSON.stringify(item)))].map((item) => JSON.parse(item));
      // 如果当前登录人不是监考人，则清空页面中监考人筛选条件
      let index = examinersOptions.value.findIndex((option: Record<string, any>) => option.value === selectedEmployeeID.value);
      if (index === -1) {
        selectedEmployeeID.value = undefined;
      }
    }
  });
};
/**
 * @description: 删除记录
 * @param examinerScheduleID 记录ID
 * @returns
 */
const deleteRecord = (row: Record<string, any>) => {
  if (row.statusCode === "1") {
    showMessage("warning", "已经有预约的监考计划不允许删除！");
    return;
  }
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    const params = { examinerScheduleID: row.examinerScheduleID };
    examinerScheduleService.deleteExaminerSchedule(params).then((result: any) => {
      if (result) {
        showMessage("success", "删除成功！");
        getExaminerScheduleList();
      }
    });
  });
};
let { validateRule } = useForm();
/**
 * @description: 保存记录
 */
const saveRecord = async () => {
  if (oldPlanData && JSON.stringify(oldPlanData) === JSON.stringify(planData.value)) {
    toggleDrawer(undefined, false);
    return;
  }
  if (!(await validateRule(submitRefs))) {
    return;
  }
  await examinerScheduleService.saveExaminerSchedule(planData.value).then((result: any) => {
    if (result) {
      toggleDrawer(undefined, false);
      showMessage("success", "保存成功！");
      // 如果新增监考人不包含当前登录人，则清空页面中监考人筛选条件，获取所有监考人的监考计划
      if (!planData.value.examiners.includes(selectedEmployeeID.value ?? "")) {
        selectedEmployeeID.value = undefined;
      }
      getExaminerScheduleList();
    }
  });
};
// #endregion
</script>
<style lang="scss">
.examiner-schedule {
  .header-label {
    margin-left: 20px;
  }
  .header-date {
    width: 320px !important;
  }
  .examination-record-select {
    margin-right: 20px;
    display: inline-block;
    width: 300px;
  }
  .examination-form-item {
    width: 370px;
  }
}
</style>
