<!--
 * FilePath     : \src\components\itemDetailList.vue
 * Author       : 胡长攀
 * Date         : 2024-09-26 00:40
 * LastEditors  : 来江禹
 * LastEditTime : 2025-02-25 10:39
 * Description  : 明细列表组件
 * CodeIterationRecord:
 -->
<template>
  <div class="item-detail-list">
    <draggable v-if="dragFlag && detailList.length > 1" v-model="detailList" handle=".icon-move" :itemKey="'detail'">
      <template #item="{ index }">
        <render-detail
          v-model="detailList[index]"
          :index="index"
          :isDraggable="true"
          :itemType="props.itemType"
          :placeholder="props.placeholder"
          :content="props.content"
        />
      </template>
    </draggable>
    <template v-else>
      <render-detail
        v-for="(item, index) in detailList"
        v-model="detailList[index]"
        :key="index"
        :index="index"
        :itemType="props.itemType"
        :placeholder="props.placeholder"
        :content="props.content"
      />
    </template>
    <div class="btn-container">
      <el-button v-if="props.itemType !== 'Judgment'" class="add-option-item" text type="primary" @click="addDetail">
        <i class="iconfont icon-add" />
        添加
      </el-button>
      <el-button v-if="props.itemType !== 'Judgment'" class="multi-add-btn" text type="primary" @click="openBatchDialog">
        <i class="iconfont icon-add" />
        批量添加
      </el-button>
    </div>
  </div>

  <!-- 批量添加弹窗 -->
  <el-dialog v-model="showBatchDialog" title="批量添加题目明细" :close-on-click-modal="false" append-to-body>
    <div>
      <el-input
        class="batch-input"
        type="textarea"
        :rows="30"
        v-model="batchContent"
        placeholder="请输入选项内容，每行一个选项"
        resize="none"
      />
    </div>
    <template #footer>
      <span>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchAdd">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import draggable from "@/components/drag/draggable/src/vuedraggable.js";
// eslint-disable-next-line id-match
import { ElTooltip, ElCheckbox, ElInput } from "element-plus";
import { ref } from "vue";

const props = defineProps({
  itemType: {
    type: String,
    default: ""
  },
  modelValue: {
    type: Array<Record<string, any>>,
    default: () => []
  },
  itemID: {
    type: [String, Number],
    default: undefined
  },
  placeholder: {
    type: String,
    default: "请输入答案明细"
  },
  content: {
    type: String,
    default: "正确答案"
  },
  dragFlag: {
    type: Boolean,
    default: false
  },
  defaultAnswerOptionCount: {
    type: Number,
    default: 0
  }
});

const emits = defineEmits(["update:modelValue", "change", "select"]);
const detailList = useVModel(props, "modelValue", emits);

// 批量添加相关变量
const showBatchDialog = ref(false);
const batchContent = ref<string>("");

// 初始化
onMounted(() => {
  if (props.modelValue.length) {
    return;
  }
  if (props.itemType === "Judgment") {
    handleJudgmentDetail();
    return;
  }
  setDefaultOptionsByItemType();
});

/**
 * @description：处理判断题类型明细数据
 */
const handleJudgmentDetail = () => {
  let judgmentDetailList = [
    {
      itemDetailID: common.guid(),
      content: "正确",
      itemID: props.itemID ?? undefined,
      selectFlag: true
    },
    {
      itemDetailID: common.guid(),
      content: "错误",
      itemID: props.itemID ?? undefined,
      selectFlag: false
    }
  ];
  if (detailList.value?.length === 2) {
    let trueItem = detailList.value.find((detail) => detail.content === "正确");
    let falseItem = detailList.value.find((detail) => detail.content === "错误");
    if (!trueItem || !falseItem) {
      detailList.value = judgmentDetailList;
    }
  } else {
    detailList.value = judgmentDetailList;
  }
};

/**
 * @description：处理选择正确答案选项
 * @param item
 */
const chooseTrueAnswer = (item?: any) => {
  if (props.itemType === "Judgment" || props.itemType === "SingleChoice") {
    detailList.value.forEach((detail) => {
      if (detail.itemDetailID === item?.itemDetailID && item?.selectFlag) {
        detail.selectFlag = true;
      } else {
        detail.selectFlag = false;
      }
    });
  }
};

// 监听题目类型变化
watch(
  () => props.itemType,
  () => {
    detailList.value = [];
    if (props.itemType === "Judgment") {
      handleJudgmentDetail();
      return;
    }
    setDefaultOptionsByItemType();
  }
);

/**
 * @description：新增题目明细
 */
const addDetail = () => {
  let insertDetail = {
    itemDetailID: common.guid(),
    content: "",
    itemID: props.itemID ?? undefined,
    selectFlag: props.itemType === "ShortAnswer"
  };
  detailList.value = [...detailList.value, insertDetail];
};

/**
 * @description：打开批量添加弹窗
 */
const openBatchDialog = () => {
  // 如果已有明细，将其填充到文本框中
  if (detailList.value.length > 0) {
    batchContent.value = detailList.value.map((detail) => detail.content).join("\n");
  }
  showBatchDialog.value = true;
};

/**
 * @description：处理批量添加
 */
const handleBatchAdd = () => {
  if (!batchContent.value.trim()) {
    showMessage("warning", "请先输入批量添加内容");
    return;
  }
  const lines = batchContent.value.split("\n").filter((line) => line.trim());
  const newDetails = lines.map((line) => ({
    itemDetailID: common.guid(),
    content: line.trim(),
    itemID: props.itemID ?? undefined,
    selectFlag: props.itemType === "ShortAnswer"
  }));

  detailList.value = newDetails; // 替换原有内容
  showBatchDialog.value = false;
};

/**
 * @description：删除项目
 * @param item
 */
const deleteDetail = (item: any) => {
  detailList.value = detailList.value.filter((detail) => detail.itemDetailID !== item.itemDetailID);
};

/**
 * @description：设置默认选项（按照参数设置需要设置的默认选项数）
 */
const setDefaultOptionsByItemType = () => {
  // 单选、多选题 设置默认选项
  if (!["SingleChoice", "MultipleChoice"].some((itemType) => itemType === props.itemType)) {
    return;
  }
  if (props.defaultAnswerOptionCount > 0) {
    detailList.value = Array.from({ length: props.defaultAnswerOptionCount }, (_, index) => ({
      itemDetailID: common.guid(),
      content: "",
      itemID: props.itemID ?? undefined,
      selectFlag: false
    }));
  }
};
// 提取公共渲染部分
const renderDetail = defineComponent({
  name: "renderDetail",
  props: {
    modelValue: {
      type: Object as () => Record<string, any>,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    isDraggable: {
      type: Boolean
    },
    itemType: {
      type: String
    },
    placeholder: {
      type: String
    },
    content: {
      type: String
    }
  },
  setup(props, { emit }) {
    const element = useVModel(props, "modelValue", emit);
    return () => {
      const children: any[] = [];
      // 如果是可拖拽的，则添加拖拽图标
      if (props.isDraggable) {
        children.push(
          h(
            ElTooltip,
            { effect: "dark", content: "拖拽排序", placement: "top" },
            { default: () => h("i", { class: "iconfont icon-move" }) }
          )
        );
      }
      // 渲染输入框
      const inputVnode = h(ElInput, {
        modelValue: props.modelValue.content,
        "onUpdate:modelValue": (value) => (element.value.content = value),
        placeholder: props.placeholder,
        autosize: { minRows: 1, maxRows: 5 },
        type: "textarea"
      });
      children.push(inputVnode);

      if (props.itemType !== "ShortAnswer") {
        const slots = {
          default: () =>
            h(ElCheckbox, {
              class: "answer-input",
              modelValue: element.value.selectFlag,
              "onUpdate:modelValue": (value) => (element.value.selectFlag = value),
              onChange: () => {
                chooseTrueAnswer(props.modelValue);
              }
            })
        };
        const toolTipProps: Record<string, any> = {
          effect: "dark",
          content: props.content,
          placement: "top"
        };
        children.push(h(ElTooltip, toolTipProps, slots));
      }

      if (detailList.value.length > 1 && props.itemType !== "Judgment") {
        children.push(
          h(
            ElTooltip,
            { effect: "dark", placement: "top", content: "删除" },
            { default: () => h("i", { class: "iconfont icon-delete", onClick: () => deleteDetail(element.value) }) }
          )
        );
      }
      return h("div", { class: "detail-content", key: props.index }, children);
    };
  }
});
</script>

<style lang="scss">
.item-detail-list {
  position: relative;
  .detail-content {
    display: flex;
    align-items: center;
    margin: 5px 0;
    .answer-input {
      margin: 0 10px !important;
    }
  }
  .btn-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 8px;
    .add-option-item,
    .multi-add-btn {
      padding: 4px 8px;
      height: auto;
      font-size: 14px;
    }
  }
}
</style>
