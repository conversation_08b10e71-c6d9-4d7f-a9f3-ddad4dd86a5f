/*
 * FilePath     : \nursing-management-web\src\views\qcManagement\hierarchicalQC\types\hierarchicalQcTrack.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-21 10:11
 * LastEditors  : 孟昭永
 * LastEditTime : 2025-01-10 16:20
 * Description  :
 * CodeIterationRecord:
 */
/**
 * 质控查询View
 */
export class searchClass {
  yearMonth: string;
  hierarchicalQCFormLevel?: string;
  hierarchicalQCFormID?: number;
  departmentID?: number;
  score?: number;
  scoreOption: Array<any>;
  /**
   * @description: 质控开始日期
   */
  startDate: string;
  /**
   * @description: 质控结束日期
   */
  endDate: string;
  constructor() {
    this.yearMonth = datetimeUtil.getNowDate("yyyy-MM");
    this.hierarchicalQCFormLevel = "3";
    this.score = 5;
    this.scoreOption = [];
    this.startDate = "";
    this.endDate = "";
  }
  getScoreOption(score?: number) {
    this.scoreOption = [{ label: "不考核", value: 0 }];
    for (let i = 0; i < (score ?? 5); i++) {
      this.scoreOption.push({ label: "小于" + (i + 1) + "分", value: i + 1 });
    }
  }
}
