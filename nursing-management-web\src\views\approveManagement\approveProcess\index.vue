<!--
 * FilePath     : \nursing-management-web\src\views\approveManagement\approveProcess\index.vue
 * Author       : 杨欣欣
 * Date         : 2023-09-05 15:09
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-03 16:36
 * Description  : 审批流程设置档列表
 * CodeIterationRecord:
-->
<template>
  <base-layout class="process-main" :drawerOptions="drawerOptions">
    <template #header>
      <el-button class="add-button" @click="editApproveProcess()">新增</el-button>
    </template>
    <el-table border stripe :data="approveProcesses">
      <el-table-column prop="processName" label="名称" />
      <el-table-column prop="processDescription" label="描述" />
      <el-table-column prop="departmentNames" label="适用科室" />
      <el-table-column prop="addEmployeeName" label="制定人" />
      <el-table-column label="制定时间" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'dateTime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(125)">
        <template #default="scope">
          <div v-if="scope.row.addEmployeeID === userStore.employeeID">
            <el-tooltip content="启用">
              <span
                class="iconfont icon-open"
                v-visibilityHidden="scope.row.statusCode === approveProcessStatus.UnEnable"
                @click="enableProcess(scope.row.approveProcessID)"
              />
            </el-tooltip>
            <el-tooltip content="修改">
              <span class="iconfont icon-edit" @click="editApproveProcess(scope.row)" />
            </el-tooltip>
            <el-tooltip content="停用">
              <span
                class="iconfont icon-stop"
                v-visibilityHidden="scope.row.statusCode === approveProcessStatus.Enable"
                @click="disableProcess(scope.row.approveProcessID)"
              />
            </el-tooltip>
            <el-tooltip content="删除">
              <span
                class="iconfont icon-delete"
                v-visibilityHidden="scope.row.statusCode !== approveProcessStatus.Enable"
                @click="deleteApprovalProcess(scope.row)"
              />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <base-layout class="process-maintain" headerHeight="auto">
        <template #header>
          <el-form :model="modifyData" :rules="rules" inline ref="formRef">
            <div class="process-attrs">
              <el-form-item label="分类：" prop="proveCategory">
                <prove-category-selector v-model="modifyData!.proveCategory" label="" @select="selectCategory" />
              </el-form-item>
              <el-form-item label="名称：" prop="processName">
                <el-input v-model="modifyData!.processName" />
              </el-form-item>
            </div>
            <div class="process-attrs">
              <el-form-item label="适用科室：" prop="departmentIDs">
                <department-selector
                  v-model="modifyData!.departmentIDs"
                  :props="{
                    multiple: true
                  }"
                  :disableOptions="disableOptions"
                  label=""
                  showWholeHospital
                />
              </el-form-item>
              <el-form-item label="描述：" class="desc">
                <el-input v-model="modifyData!.processDescription" type="textarea" />
              </el-form-item>
            </div>
            <el-form-item label="申请模板：" class="template">
              <el-input v-model="modifyData!.contentTemplate" type="textarea" autosize />
            </el-form-item>
          </el-form>
        </template>
        <flow-diagram :nodes="modifyData!.nodes" />
      </base-layout>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import type { approveProcess } from "./types/approveProcess";
import type { approveProcessNode } from "./types/approveProcessNode";
import { approveProcessStatus } from "./types/approveProcessStatus";
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
const approveProcesses = ref<approveProcess[]>([]);
type modifyDataType = approveProcess & { nodes: approveProcessNode[] };
const modifyData = ref<Partial<modifyDataType>>();
// 监听在抽屉内编辑数据时，是否发生了修改
const { ignoreUpdates } = watchIgnorable(modifyData, () => (changed.value = true), { deep: true });
const formRef = ref();
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    if (!changed.value) {
      drawerOptions.value.showDrawer = false;
      return;
    }
    await saveApproveProcess(formRef);
  },
  beforeClose: () => {
    if (!changed.value || !drawerOptions.value.showConfirm) {
      drawerOptions.value.showDrawer = false;
      return;
    }
    confirmBox("有未保存的数据，是否确认关闭？", "系统提示", (isConfirm: boolean) => isConfirm && (drawerOptions.value.showDrawer = false));
  }
});
const changed = ref(false);
const rules = ref({
  processName: [{ required: true, message: "请输入审批流程名称", trigger: "blur" }],
  proveCategory: [{ required: true, message: "请选择审批分类", trigger: "blur" }],
  departmentIDs: [{ required: true, message: "请选择适用科室", trigger: "blur" }]
});

// 获取审批流程列表
const init = async () => {
  await approveProcessService.getApproveProcesses().then((res: any) => (approveProcesses.value = res));
};
onMounted(init);
/**
 * @description: 编辑审批流程
 * @param row 审批流程数据
 * @return
 */
const editApproveProcess = async (row?: approveProcess) => {
  drawerOptions.value.showConfirm = true;
  if (row) {
    const params = { approveProcessID: row.approveProcessID };
    await approveProcessService.getApproveProcessNodes(params).then((res: any) => {
      ignoreUpdates(() => {
        modifyData.value = {
          ...row,
          nodes: res
        };
      });
    });
    if (row.statusCode === approveProcessStatus.Enable) {
      drawerOptions.value.showConfirm = false;
    }
  } else {
    ignoreUpdates(() => {
      modifyData.value = {
        processName: "",
        processDescription: "",
        nodes: [],
        proveCategory: "",
        departmentIDs: [],
        statusCode: approveProcessStatus.UnEnable
      };
    });
  }
  drawerOptions.value.drawerTitle = `${modifyData.value?.approveProcessID ? "修改" : "新增"}${modifyData.value?.processName ?? ""}审批流程`;
  // 打开抽屉
  drawerOptions.value.showDrawer = true;
  // 重置为未修改状态
  changed.value = false;
};

/**
 * @description: 保存审批流程
 * @return
 */
const saveApproveProcess = async (formEl: any) => {
  if (!(await saveCheck(formEl))) {
    return;
  }
  if (!changed.value) {
    drawerOptions.value.showDrawer = false;
  }
  const params = toRaw(modifyData.value);
  const saveFunc = modifyData.value?.approveProcessID
    ? approveProcessService.updateApproveProcess
    : approveProcessService.addApproveProcess;
  await saveFunc(params).then(async (res: any) => {
    if (res) {
      await init();
      showMessage("success", "保存成功");
    }
  });
  drawerOptions.value.showDrawer = false;
};
/**
 * @description: 保存前校验
 * @return
 */
const saveCheck = async (formEl: any) => {
  let passed = await formEl.value!.validate((valid: boolean) => {
    if (valid) {
      return true;
    }
    showMessage("error", "请输入必填项");
    return false;
  });
  if (!passed) {
    return passed;
  }
  if (!modifyData.value?.nodes?.length) {
    showMessage("error", "请完善审批流程节点");
    return false;
  }
  for (let index = 0; index < modifyData.value.nodes.length; index++) {
    const node = modifyData.value.nodes[index];
    if (!node.nodeDetails.length) {
      showMessage("error", `请完善审批节点${index + 1}，至少需要一个审批人/岗`);
      return false;
    }
  }
  return true;
};
/**
 * @description: 删除审批流程
 * @param row 审批流程数据
 * @return
 */
const deleteApprovalProcess = async (row: approveProcess) => {
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    const params = { approveProcessID: row.approveProcessID };
    await approveProcessService.deleteApproveProcess(params).then(async (res: any) => {
      if (res) {
        await init();
        showMessage("success", "删除成功");
      }
    });
  });
};
/**
 * @description: 启用审批流程
 * @param approveProcessID 审批流程ID
 * @return
 */
const enableProcess = async (approveProcessID: string) => {
  confirmBox("启用后无法再修改流程，确定要启用么？", "启用流程", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    const params = { approveProcessID };
    await approveProcessService.enableApproveProcess(params).then(async (res: any) => {
      if (res) {
        await init();
        showMessage("success", "启用成功");
      }
    });
  });
};
/**
 * @description: 停用审批流程
 * @param approveProcessID 审批流程ID
 * @return
 */
const disableProcess = async (approveProcessID: string) => {
  confirmBox("确定要停用么？", "停用流程", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    const params = { approveProcessID };
    await approveProcessService.disableApproveProcess(params).then(async (res: any) => {
      if (res) {
        await init();
        showMessage("success", "停用成功");
      }
    });
  });
};
const disableOptions = ref<number[]>([]);
/**
 * @description: 选择审批分类触发回调
 * @param item 选中的分类
 * @return
 */
const selectCategory = (item: CascaderList<string>) => {
  // `!`表示非空断言，即告诉TS编译器，此处必不会是undefined，不要报错。只推荐在流程上必然不为空的字段上使用
  modifyData.value!.processName = item.label;
  // 获取当前分类所有审批流程适用科室，这些科室在选择适用科室时需要将对应选项置为禁用
  const params = {
    typeCode: item.value
  };
  approveProcessService.getEnableDepartmentIDsByProveCategory(params).then((res: any) => (disableOptions.value = res));
};
</script>
<style lang="scss">
.process-main {
  .process-maintain {
    justify-content: center;
    .base-content {
      overflow-x: hidden;
    }
    .process-attrs {
      display: flex;
      flex-wrap: nowrap;
    }
    .template {
      width: 100%;
    }
    .zoom {
      position: fixed;
      right: 0;
      z-index: 999;
    }
  }
}
</style>
