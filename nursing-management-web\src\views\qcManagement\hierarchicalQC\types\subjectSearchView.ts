/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\subjectSearchView.ts
 * Author       : 来江禹
 * Date         : 2023-12-04 16:23
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-11-05 10:42
 * Description  : 获取主题参数
 * CodeIterationRecord:
 */
export interface subjectSearchView {
  /**
   * 质控类型 NormalWorkingFormType：常态工作控制   NodeQCFormType：节点式督导
   */
  qcType: "nodeQCFormType" | "normalWorkingFormType" | "visitsFormType" | "specialFormType" | undefined;
  /**
   * 质控级别
   */
  qcLevel: string;
  /**
   * @description: 年月范围
   */
  yearMonthRange?: string[];
  /**
   * @description: 年月
   */
  yearMonth?: string;
  /**
   * 主题类别
   */
  formType?: string;
  /**
   * 部门ID
   */
  departmentID?: number;
  /**
   * @description: 主键ID
   */
  hierarchicalQCSubjectID?: string;
}
