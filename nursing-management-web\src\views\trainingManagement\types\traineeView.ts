/*
 * FilePath     : \src\views\trainingManagement\types\traineeView.ts
 * Author       : 张现忠
 * Date         : 2024-09-20 11:25
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-12-17 08:59
 * Description  : 培训学员管理页面返回的学员信息interface
 * CodeIterationRecord:
 */
export interface traineeView {
  [key: string]: any;
  /**
   * 人员培训记录ID
   */
  trainingLearnerID: string;
  /**
   * 学员ID
   */
  employeeID: string;
  /**
   * 学员名称
   */
  employeeName: string;
  /**
   * 学员所在部门ID
   */
  departmentID: string;
  /**
   * 学员所在部门
   */
  departmentName: string;
  /**
   * 护理等级
   */
  capabilityLevel: string;
  /**
   * 课程进度
   */
  progress: string;
  /**
   * 学习次数
   */
  learningCount: number;
  /**
   * 培训时长
   */
  trainingDuration: number;
  /**
   * 最后一次的培训时间
   */
  lastTrainingTime: string;
  /**
   * 学生对老师培训点评建议
   */
  learnerEvaluation?: string;

  /**
   * 老师对学生本次培训点评
   */
  teacherEvaluation?: string;

  /**
   * 学生对老师培训点评建议ID
   */
  learnerEvaluationID?: string;

  /**
   * 老师对学生本次点评ID
   */
  teacherEvaluationID?: string;

  /**
   * 是否为班长标志
   */
  monitorFlag: boolean;
  /**
   * 课程满意度（分）
   */
  courseSatisfaction?: number;
  /**
   * 课程建议
   */
  courseRecommendations: string;
}
