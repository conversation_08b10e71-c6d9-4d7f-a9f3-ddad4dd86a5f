<!--
 * FilePath     : \src\components\file\index.vue
 * Author       : 郭鹏超
 * Date         : 2023-10-19 09:47
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-31 10:48
 * Description  : 文件导入导出组件
 * CodeIterationRecord:
-->
<template>
  <div class="file">
    <export-excel v-if="fileOption.typeArr.includes('exportExcel')" :exportExcelOption="fileOption.exportExcelOption!"></export-excel>
    <import-excel
      v-if="fileOption.typeArr.includes('importExcel')"
      @getExcelData="getExcelData"
      :importExcelOption="fileOption.importExcelOption"
    ></import-excel>
    <export-pdf v-if="fileOption.typeArr.includes('exportPdf')" :exportPdfOption="fileOption.exportPdfOption"></export-pdf>
    <export-word v-if="fileOption.typeArr.includes('exportWord')" :exportWordOption="fileOption.exportWordOption"></export-word>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  fileOption: {
    type: Object as () => FilePropsView,
    default: () => {
      return {};
    }
  }
});
const emits = defineEmits(["getExcelData"]);
/**
 * description: excel导入数据返回
 * param {*} value
 * return {*}
 */
const getExcelData = (excelDataView: ImportExcelReturnView) => {
  emits("getExcelData", excelDataView);
};
</script>

<style lang="scss">
.file {
  display: inline-block;
}
</style>
