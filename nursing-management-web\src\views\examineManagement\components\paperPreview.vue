<!--
 * relative     : \nursing-management-web\src\views\examineManagement\components\paperPreview.vue
 * Author       : 张现忠
 * Date         : 2025-03-10 14:43
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-29 11:29
 * Description  : 试卷查看组件
 * CodeIterationRecord:
 -->

<template>
  <div class="paper-preview">
    <div class="tool-bar">
      <el-button text type="primary" @click="toggleDescription">
        <zhy-icon name="description" color="#08c979" />
        <span>{{ `${showDescription ? "隐藏" : "显示"}解析` }}</span>
      </el-button>
    </div>
    <zhy-form-renderer class="paper" ref="rendererForm" :formData="formData" />
  </div>
</template>

<script setup lang="ts">
import type { dynamicFormData } from "zhytech-ui";

defineProps<{
  formData: dynamicFormData<Record<string, any>>;
}>();

const showDescription = ref(false);
const rendererForm = shallowRef();

/**
 * @description: 显示/隐藏项目说明/答案解析
 */
const toggleDescription = () => {
  rendererForm.value.toggleDescription();
  showDescription.value = !showDescription.value;
};
</script>

<style lang="scss">
.paper-preview {
  height: 100%;
  .tool-bar {
    padding: 0 10px;
    box-sizing: border-box;
    text-align: right;
    border-bottom: 1px solid #000000;
    min-height: 50px;
    .el-button.is-text:not(.is-disabled) {
      position: relative;
      span {
        z-index: 100;
      }
      &:hover * {
        color: #ffffff;
      }
      &:hover::before {
        transform-origin: 0 0;
        transform: scaleX(1);
      }
      &::before {
        content: "";
        display: block;
        position: absolute;
        top: -8px;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 7px 0;
        border-radius: 6px;
        background-color: #fc8720;
        transform: scaleX(0);
        transform-origin: 100% 100%;
        transition: transform 0.6s cubic-bezier(0.53, 0.21, 0, 1);
      }
    }
  }
  .paper {
    height: calc(100% - 50px);
  }
}
</style>
