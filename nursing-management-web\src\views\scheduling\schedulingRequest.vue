<!--
 * FilePath     : \src\views\scheduling\schedulingRequest.vue
 * Author       : 马超
 * Date         : 2023-08-19 11:05
 * LastEditors  : 马超
 * LastEditTime : 2025-03-04 15:04
 * Description  : 预约排班申请页面(PC)
 * CodeIterationRecord: 3714-作为IT人员，我需要开发排班预约申请功能(PC)，以利排班自动化
-->
<template>
  <base-layout class="scheduling-request-record" :drawerOptions="drawerOptions">
    <template #header>
      <span v-permission:S="12">
        <span class="header-label">显示全部：</span>
        <el-switch v-model="showAllSwitch" @change="getSchedulingRequestData" />
      </span>
      <span class="header-label">申请日期：</span>
      <el-date-picker
        class="date-range-picker"
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        @change="filterRecords"
      />
      <el-button v-permission:B="1" class="add-button" @click="addRecord">新增</el-button>
    </template>
    <el-table :data="showRecords" stripe border>
      <el-table-column prop="postType" label="预约类型" :min-width="convertPX(40)"></el-table-column>
      <el-table-column prop="startDateFormat" label="开始日期" :min-width="convertPX(100)"></el-table-column>
      <el-table-column prop="endDateFormat" label="结束日期" :min-width="convertPX(100)"></el-table-column>
      <el-table-column prop="days" label="休假天数" :min-width="convertPX(30)" :align="'right'">
        <template v-slot="scope">
          {{ scope.row.days || totalDays(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="addDateTime" label="申请时间" :min-width="convertPX(80)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="addEmployeeName" label="申请人" :min-width="convertPX(120)"></el-table-column>
      <el-table-column label="状态" :min-width="convertPX(40)">
        <template #default="{ row }">
          <el-tag :type="getApproveStatusTag(row.statusCode)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(120)">
        <template #default="scope">
          <el-tooltip content="修改">
            <i v-permission:B="3" v-visibilityHidden="getButtonShow(scope.row)" @click="editRow(scope.row)" class="iconfont icon-edit"></i>
          </el-tooltip>
          <el-tooltip content="撤销">
            <i
              v-if="showRevokeFlag"
              v-permission:B="26"
              v-visibilityHidden="showRevokeButton(scope.row.statusCode, scope.row.addEmployeeID)"
              @click="startRevoke(scope.row)"
              class="iconfont icon-revoke"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              v-permission:B="4"
              v-visibilityHidden="getButtonShow(scope.row)"
              @click="deleteRow(scope.row.schedulingRequestRecordID)"
              class="iconfont icon-delete"
            ></i>
          </el-tooltip>
          <el-tooltip content="提交审批">
            <i
              v-permission:B="3"
              v-visibilityHidden="!scope.row.approveFlag"
              @click="manualSubmissionApprove('SchedulingRequest', scope.row.schedulingRequestRecordID, getSchedulingRequestData)"
              class="iconfont icon-save"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 修改和新增弹窗 -->
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName === 'addOrModify'"
        ref="submitRefs"
        label-width="auto"
        :model="currRow"
        class="form-style"
        :rules="rules"
      >
        <!-- 申请人 -->
        <el-form-item label="申请人：">
          <el-input label="" v-model="applicantName" disabled class="user-name-input"> </el-input>
        </el-form-item>
        <!-- 预约类型 -->
        <el-form-item label="预约类型：" prop="departmentPostID">
          <department-post-selector
            v-model="currRow.departmentPostID"
            :postType="'4'"
            :departmentID="userStore.departmentID"
            label=""
            :width="386"
          ></department-post-selector>
        </el-form-item>
        <!-- 开始日期 -->
        <el-form-item label="开始日期：" prop="startDate">
          <el-date-picker v-model="currRow.startDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <!-- 开始午别 -->
        <el-form-item label="开始午别：" prop="startNoon">
          <noon-selector v-model="currRow.startNoon" label="" :width="386"></noon-selector>
        </el-form-item>
        <!-- 结束日期 -->
        <el-form-item label="结束日期：" prop="endDate">
          <el-date-picker
            v-model="currRow.endDate"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <!-- 结束午别 -->
        <el-form-item label="结束午别：" prop="endNoon">
          <noon-selector v-model="currRow.endNoon" label="" :width="386"></noon-selector>
        </el-form-item>
        <el-form-item label="休假天数：" prop="days">
          <span v-if="days">{{ days }} 天</span>
        </el-form-item>
        <el-form-item label="申请原因：" prop="reason">
          <el-input type="textarea" v-model="currRow.reason" maxlength="200" show-word-limit rows="3" />
        </el-form-item>
      </el-form>
      <revoke-approval
        v-if="drawerOptions.drawerName === 'revoke'"
        v-model:drawer-options="drawerOptions"
        :revokeFormData="revokeFormData!"
        :id="currRow.schedulingRequestRecordID"
        :isSource="true"
        @refreshData="getSchedulingRequestData()"
      />
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import revokeApproval from "@/views/approveManagement/approveRecord/components/revokeApproval.vue";
const convertPX: any = inject("convertPX");
const { userStore } = useStore() as any;
const { getApproveStatusTag } = useStatusTag();
const { showRevokeFlag, manualSubmissionApprove } = useApproval();
const showRecords = ref<Record<string, any>[]>([]);
let schedulingRequestRecord = ref<Record<string, any>[]>([]);
let showAllSwitch = ref<boolean>(true);
let editFlag = ref<boolean>();
let schedulingRequestRecordID = ref<string>("");
const dateRange = ref<[string, string]>([
  datetimeUtil.addDate(Date.now(), -30, "yyyy-MM-dd"),
  datetimeUtil.formatDate(Date.now(), "yyyy-MM-dd")
]);
const submitRefs = ref({}) as any;
onMounted(() => {
  getSchedulingRequestData();
});
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    // 新增或修改
    saveAndEditRecord();
  },
  cancel: () => toggleDrawer(false, false)
});
let currRow = ref<Record<string, any>>({});
let applicantName = ref<String>(userStore.userName);
const { getButtonShow, showRevokeButton } = useButton();
const revokeFormData = ref<Record<string, any>[]>([]);
const rules = reactive({
  departmentPostID: [
    {
      required: true,
      message: "请选择预约类型",
      trigger: "change"
    }
  ],
  startDate: [
    {
      required: true,
      message: "请选择开始日期",
      trigger: "change"
    }
  ],
  startNoon: [
    {
      required: true,
      message: "请选择开始午别",
      trigger: "change"
    }
  ],
  endDate: [
    {
      required: true,
      message: "请选择结束日期",
      trigger: "change"
    }
  ],
  endNoon: [
    {
      required: true,
      message: "请选择结束午别",
      trigger: "change"
    }
  ],
  reason: [
    {
      required: true,
      message: "请输入申请原因",
      trigger: "blur"
    }
  ]
});
const days = computed(() => {
  if (!currRow.value) {
    return;
  }
  return totalDays(currRow.value);
});
/**
 * @description: 计算天数
 * @param row
 * @return
 */
const totalDays = (row: Record<string, any>) => {
  return datetimeUtil.getDateNoonDays(row);
};
/**
 * @description: 获取排班预约记录
 */
const getSchedulingRequestData = () => {
  if (!showAllSwitch.value) {
    getSingleSchedulingRequest();
  } else {
    getDepartmentSchedulingRequest();
  }
};
/**
 * @description:获取单人排班预约记录
 */
const getSingleSchedulingRequest = () => {
  let params = {
    employeeID: userStore.employeeID
  };

  schedulingService.getSingleSchedulingRequest(params).then((res: any) => {
    if (res) {
      schedulingRequestRecord.value = res;
      filterRecords(dateRange.value);
    }
  });
};

/**
 * @description: 获取科室排班预约记录
 */
const getDepartmentSchedulingRequest = () => {
  let params = {
    departmentID: userStore.departmentID
  };
  schedulingService.getDepartmentSchedulingRequest(params).then((res: any) => {
    if (res) {
      schedulingRequestRecord.value = res;
      filterRecords(dateRange.value);
    }
  });
};

/**
 * @description: 根据申请日期过滤记录
 * @param dateRange 日期范围
 * @return
 */
const filterRecords = (dateRange: [string, string]) => {
  if (!dateRange) {
    showRecords.value = schedulingRequestRecord.value;
    return;
  }
  let [startDate, endDate] = dateRange;
  let records = schedulingRequestRecord.value.filter(
    (record: Record<string, any>) =>
      datetimeUtil.formatDate(record.addDateTime, "yyyy-MM-dd") >= startDate &&
      datetimeUtil.formatDate(record.addDateTime, "yyyy-MM-dd") <= endDate
  );
  showRecords.value = records;
};
// #region 前端的预处理逻辑
/**
 * @description: 禁止选择当前时间之前的日期
 * @return {*}
 * @param {*} time 组件下拉框中的所有时间点
 */
const disabledDate = (time: Date) => {
  if (!currRow.value.startDate) {
    return false;
  }
  return datetimeUtil.formatDate(time, "yyyy-MM-dd") < currRow.value.startDate;
};
/**
 * description: 新增记录
 * return {*}
 */
const addRecord = () => {
  drawerOptions.value.drawerTitle = "新增排班预约";
  drawerOptions.value.drawerName = "addOrModify";
  currRow.value = { addEmployeeID: userStore.employeeID };
  toggleDrawer(true, false);
};
/**
 * @description: 编辑记录
 * @param row 排班预约记录
 * @return {*}
 */
const editRow = (row: any) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  currRow.value = common.clone(row);
  applicantName.value = currRow.value.addEmployeeName;
  drawerOptions.value.drawerName = "addOrModify";
  drawerOptions.value.drawerTitle = "修改排班预约";
  schedulingRequestRecordID.value = currRow.value.schedulingRequestRecordID;
  toggleDrawer(true, true);
};
import { useUtils } from "../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 保存记录
 * @return {*}
 */
const saveAndEditRecord = () => {
  let params: any = {
    departmentPostID: currRow.value.departmentPostID,
    startDate: currRow.value.startDate,
    endDate: currRow.value.endDate,
    startNoon: currRow.value.startNoon,
    endNoon: currRow.value.endNoon,
    modifyEmployeeID: userStore.employeeID,
    days: days.value,
    reason: currRow.value.reason
  };
  if (editFlag.value) {
    params.schedulingRequestRecordID = schedulingRequestRecordID.value;
  } else {
    params.addEmployeeID = userStore.employeeID;
    params.statusCode = "0";
    params.departmentID = userStore.departmentID;
  }
  schedulingService.saveSchedulingRequestRecord(params).then((res: any) => {
    if (!res.recordSaveFlag) {
      return;
    } else if (!res.approveSaveFlag) {
      showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
    } else {
      editFlag.value ? showMessage("success", "修改成功") : showMessage("success", "添加成功");
    }
    getSchedulingRequestData();
  });
  toggleDrawer(false, false);
};

/**
 * @description: 删除调班申请
 * @param {*} schedulingRequestRecordID 排班预约记录ID
 * @return {*}
 */
const deleteRow = async (schedulingRequestRecordID: string) => {
  deleteConfirm("", (res: boolean) => {
    if (res) {
      schedulingService.deleteScheduleRequestRecord({ schedulingRequestRecordID: schedulingRequestRecordID }).then((respBool: any) => {
        if (respBool) {
          showMessage("success", "删除成功！");
          getSchedulingRequestData();
        }
      });
    }
  });
};
/**
 * @description: 开始撤销审批
 * @param row 排班预约记录
 * @returns
 */
const startRevoke = (row: any) => {
  currRow.value = row;
  revokeFormData.value = [
    { label: "申请人", value: currRow.value.addEmployeeName },
    { label: "预约类型", value: currRow.value.postType },
    { label: "开始日期", value: currRow.value.startDateFormat },
    { label: "结束日期", value: currRow.value.endDateFormat }
  ];
  drawerOptions.value.drawerTitle = "撤销审批";
  drawerOptions.value.drawerName = "revoke";
  toggleDrawer(true, false);
};
/**
 * @description: 切换抽屉
 * @param openFlag 打开或关闭抽屉
 * @param edit1Flag 是否为编辑状态
 * @returns
 */
const toggleDrawer = (openFlag: boolean, edit1Flag: boolean) => {
  drawerOptions.value.showDrawer = openFlag;
  editFlag.value = edit1Flag;
};
// 用于调整时间选择器的宽度与其他选择器宽度相同
const datePickerWidth = computed(() => `${convertPX(386)}px`);
</script>
<style lang="scss">
.scheduling-request-record {
  height: 100%;
  width: 100%;
  .header-label {
    margin-left: 10px;
    margin-right: 10px;
  }
  .date-range-picker {
    width: 360px;
  }
  .form-style {
    margin-top: 20px;
    .user-name-input {
      width: 386px;
    }
    .el-form-item__label-wrap {
      align-items: center;
    }
    .el-date-editor {
      .el-input__wrapper {
        width: v-bind(datePickerWidth);
      }
    }
  }
}
</style>
