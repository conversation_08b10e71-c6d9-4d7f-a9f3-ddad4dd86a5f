<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\personalInformation\components\descriptions\personalDescriptions.vue
 * Author       : 来江禹
 * Date         : 2023-08-01 18:11
 * LastEditors  : 苏军志
 * LastEditTime : 2024-04-27 11:20
 * Description  : 人员个人信息描述组件
 * CodeIterationRecord: 3671-作为护理管理人员，我需要护士个人信息档案，以便查看护士相关档案信息
-->
<template>
  <el-descriptions border v-model="descriptionsData" v-if="descriptionsData" :column="3">
    <el-descriptions-item
      :min-width="`${convertPX(100)}px`"
      v-for="(item, index) in propData"
      :span="item.span"
      :key="index"
      :label="item.label"
      >{{ descriptionsData[item.prop] }}
    </el-descriptions-item>
  </el-descriptions>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
import descriptionsProp from "./descriptionsData";
const props = defineProps({
  // 描述组件数据
  descriptionsData: {
    type: Object,
    default: () => {
      return {};
    }
  },
  // 描述组件配置数据名
  dataName: {
    type: String,
    default: () => {
      return "";
    }
  }
});
const { descriptionsData } = toRefs(props);
const propData = reactive(descriptionsProp[props.dataName]);
</script>
