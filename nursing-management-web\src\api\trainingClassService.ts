/*
 * FilePath     : \nursing-management-web\src\api\trainingClassService.ts
 * Author       : 孟昭永
 * Date         : 2024-07-10 15:48
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-14 14:47
 * Description  : 培训群组相关接口
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class trainingClassService {
  // 培训群组相关接口的基本URL
  private static controllerBaseUrl: string = "/TrainingClass";
  // 获取培训群组列表的API接口URL
  private static getTrainingClassListApi: string = this.controllerBaseUrl + "/GetTrainingClassList";
  // 保存培训群组的API接口URL
  private static saveTrainingClassApi: string = this.controllerBaseUrl + "/SaveTrainingClass";
  // 删除培训群组的API接口URL
  private static deleteTrainingClassByIDApi: string = this.controllerBaseUrl + "/DeleteTrainingClassByID";
  // 获取培训群组课程列表的API接口URL
  private static getTrainingClassCourseListByMainIDApi: string = this.controllerBaseUrl + "/GetTrainingClassCourseListByMainID";
  // 获取培训群组选择器options数据
  private static getTrainClassOptionsApi:string = this.controllerBaseUrl +"/GetTrainClassOptions";
  /**
   * 获取培训群组列表
   * @param params 请求参数
   * @returns 返回培训群组列表
   */
  public static async getTrainingClassList(): Promise<any> {
    return await http.get(this.getTrainingClassListApi, { loadingText: Loading.LOAD });
  }

  /**
   * 保存培训群组
   * @param params 培训群组信息
   * @returns 返回保存结果
   */
  public static async saveTrainingClass(params: any): Promise<any> {
    return await http.post(this.saveTrainingClassApi, params, { loadingText: Loading.SAVE });
  }

  /**
   * 删除培训群组
   * @param params 待删除的培训群组ID
   * @returns 返回删除结果
   */
  public static async deleteTrainingClassByID(params: any): Promise<any> {
    return await http.post(this.deleteTrainingClassByIDApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }

  /**
   * 获取培训群组课程列表
   * @param params 培训群组ID
   * @returns 返回培训群组课程列表
   */
  public static async getTrainingClassCourseListByMainID(params: any): Promise<any> {
    return await http.get(this.getTrainingClassCourseListByMainIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取培训群组selector选择器中的下拉选项集合
   * @returns 返回培训群组下拉选项集合options
   */
  public static async getTrainClassOptions() {
    return await http.get(this.getTrainClassOptionsApi, undefined, { loadingText: Loading.LOAD });
  }
}
