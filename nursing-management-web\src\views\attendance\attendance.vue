<!--
 * FilePath     : \src\views\attendance\attendance.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:42
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:02
 * Description  : 手动排班/排班表
 * CodeIterationRecord:
-->

<template>
  <base-layout class="attendance" :showFooter="!!restPostList.length" footerHeight="auto">
    <template #header>
      <div class="header-left">
        <department-selector v-permission:DL="13" v-model="selectDepartmentID" label="部门" @change="init"></department-selector>
        <span>排班月份:</span>
        <el-date-picker
          class="year-month"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          v-model="yearMonth"
          @change="getAttendanceDatas"
          :clearable="false"
        >
        </el-date-picker>
        <span>排班顺序:</span>
        <el-radio-group class="sort-flag" v-model="sortFlag" @change="getAttendanceDatas()">
          <el-radio-button label="排班" value="true" />
          <el-radio-button label="人事" value="false" />
        </el-radio-group>
      </div>

      <div class="header-right">
        <!-- 考勤表才呈现 -->
        <span v-if="readonly && selectDepartmentID === userStore.departmentID">
          <span class="header-label">显示全科：</span>
          <el-switch v-model="showAll" @change="getAttendanceDatas" />
        </span>
        <template v-else>
          <el-button v-if="attendanceApprovalStatus === '2'" v-permission:B="24" class="" type="primary" @click="attendanceCheck"
            >上传到OA</el-button
          >
          <el-button
            v-if="attendanceApprovalStatus === '0' && !manualSubmitFlag"
            v-permission:B="24"
            class="add-button"
            @click="attendanceApproval"
            >提交考勤审批</el-button
          >
          <el-button v-if="attendanceApprovalStatus === '1'" v-permission:B="24" class="print-button" @click="cancelAttendanceApproval">
            取消审核
          </el-button>
          <el-button v-if="attendanceApprovalStatus === '0' && !manualSubmitFlag" v-permission:B="2" type="primary" @click="saveAttendance">
            保存
          </el-button>
          <el-tooltip v-if="attendanceApprovalStatus === '0' && !manualSubmitFlag" content="依据排班表生成考勤表" placement="top">
            <el-button v-permission:B="2" type="primary" class="edit-button" @click="createAttendanceRecord()">
              依据排班生成考勤
            </el-button>
          </el-tooltip>
          <el-button
            v-if="manualSubmitFlag"
            v-permission:B="24"
            class="add-button"
            @click="manualSubmissionApprove('AttendanceApproval', attendanceApproveRecordID, getAttendanceDatas)"
            >手动提交审批</el-button
          >
        </template>
      </div>
    </template>
    <el-table
      v-if="attendanceTable.rows?.length"
      ref="attendanceELTable"
      height="100%"
      border
      :data="attendanceTable.rows"
      :class="['attendance-table', { readonly: readonly }]"
      :span-method="tableRowSpanMethod"
    >
      <!-- 第一层表头 -->
      <el-table-column
        v-for="(oneColumn, oneIndex) in attendanceTable.columns"
        :key="oneIndex"
        :label="oneColumn.name"
        :property="oneColumn.key"
        :min-width="convertPX(oneColumn.width ? oneColumn.width : 50)"
        :align="oneColumn.align || 'left'"
        :fixed="oneColumn.fixed || false"
      >
        <template v-if="!oneColumn.childColumns" v-slot="{ row }">
          <div v-if="!readonly && oneColumn.componentType" class="attendance-cell can-edit" @click="row[oneColumn.key + 'EditFlag'] = true">
            <template v-if="row[oneColumn.key + 'EditFlag']">
              <el-input
                v-if="oneColumn.componentType === 'T'"
                v-model="row[oneColumn.key]"
                @blur="row[oneColumn.key + 'EditFlag'] = false"
              ></el-input>
              <el-input-number
                v-else-if="oneColumn.componentType === 'TN'"
                v-model="row[oneColumn.key]"
                :controls="false"
                @blur="row[oneColumn.key + 'EditFlag'] = false"
              ></el-input-number>
            </template>
            <span v-else>{{ row[oneColumn.key] }}</span>
          </div>
          <div v-else class="attendance-cell">
            <span v-if="oneColumn.key == 'noonType'">{{ row[oneColumn.key]?.label }}</span>
            <span v-else-if="oneColumn.key == 'employee'">{{ row[oneColumn.key]?.employeeName }}</span>
            <span v-else>{{ row[oneColumn.key] }}</span>
          </div>
        </template>
        <template v-if="oneColumn.childColumns">
          <!-- 第二层表头 -->
          <el-table-column
            v-for="(twoColumn, twoIndex) in oneColumn.childColumns"
            :key="twoIndex"
            :label="twoColumn.name"
            :property="twoColumn.key"
            :min-width="convertPX(twoColumn.width ? twoColumn.width : 50)"
            :align="twoColumn.align || 'left'"
            :fixed="twoColumn.fixed || false"
          >
            <template v-if="!twoColumn.childColumns" v-slot="{ row }">
              <div
                v-if="!readonly && twoColumn.componentType"
                class="attendance-cell can-edit"
                @click="row[twoColumn.key + 'EditFlag'] = true"
              >
                <template v-if="row[twoColumn.key + 'EditFlag']">
                  <el-input
                    v-if="twoColumn.componentType === 'T'"
                    v-model="row[twoColumn.key]"
                    @blur="row[twoColumn.key + 'EditFlag'] = false"
                  ></el-input>
                  <el-input-number
                    v-else-if="twoColumn.componentType === 'TN'"
                    v-model="row[twoColumn.key]"
                    :min="0"
                    :controls="false"
                    @blur="row[twoColumn.key + 'EditFlag'] = false"
                  ></el-input-number>
                </template>
                <span v-else>{{ row[twoColumn.key] }}</span>
              </div>
            </template>
            <template v-if="twoColumn.childColumns">
              <!-- 第三层表头 -->
              <el-table-column
                v-for="(threeColumn, threeIndex) in twoColumn.childColumns"
                :key="threeIndex"
                :label="threeColumn.name"
                :property="threeColumn.key"
                :min-width="convertPX(threeColumn.width ? threeColumn.width : 50)"
                :align="threeColumn.align || 'left'"
                :fixed="threeColumn.fixed || false"
              >
                <template v-slot="{ row, $index }">
                  <div
                    :class="[
                      'attendance-cell data',
                      /* 当前单元格 */
                      { current: currentRowIndex === $index && currentColumnIndex === threeColumn.index },
                      /* 当前单元格 */
                      { rest: row[threeColumn.key]?.restPostID }
                    ]"
                    @click="selectCell($index, threeColumn.index)"
                    @dblclick="showCellMenu($event)"
                  >
                    {{ row[threeColumn.key]?.value }}
                  </div>
                </template>
              </el-table-column>
            </template>
          </el-table-column>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <!-- 休假岗说明 -->
      <div class="footer-description">
        <span v-for="(restPost, index) in restPostList" :key="index">
          {{ `${restPost.localLabel}：${restPost.label}` }}
        </span>
      </div>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
const { userStore } = useStore();
const route = useRoute();
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
const { manualSubmissionApprove } = useApproval();
// 休假岗类型
const restPostType: string = "4";
let readonly = route.params.readonly === "true";
const selectDepartmentID = ref<number>(userStore.departmentID);
const yearMonth = ref<string>(datetimeUtil.getNowDate("yyyy-MM"));
const showAll = ref<boolean>(false);
const attendanceELTable = ref<any>();
const restPostList = ref<Record<string, any>>([]);
const attendanceTable = ref<TableView>({} as TableView);
let attendanceColumns: TableColumn[] = [];
let restColumns: TableColumn[] = [];
const currentRowIndex = ref<number>(-1);
const currentColumnIndex = ref<number>(-1);
let mergeColumnKeys: string[] = [];
const attendanceApprovalStatus = ref<string>("");
const sortFlag = ref<boolean>(true);
onBeforeMount(() => {
  if (readonly) {
    showAll.value = false;
  } else {
    showAll.value = true;
  }
  init();
});

/**
 * @description: 初始化
 */
const init = () => {
  if (selectDepartmentID.value !== userStore.departmentID) {
    showAll.value = true;
  }
  // 获取休假岗位列表
  let { getDepartmentPostData } = useDictionaryData();
  getDepartmentPostData(selectDepartmentID.value, false, restPostType).then((data) => {
    let attendanceItem = {
      label: "在岗",
      localLabel: "/"
    };
    restPostList.value = [attendanceItem, ...data];
  });
  getAttendanceDatas();
};
const manualSubmitFlag = ref<boolean>(false);
const attendanceApproveRecordID = ref<string>("");
/**
 * @description: 获取考勤数据
 */
const getAttendanceDatas = () => {
  mergeColumnKeys = [];
  attendanceTable.value = { columns: [], rows: [] };
  const params = {
    departmentID: selectDepartmentID.value,
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M"),
    employeeID: showAll.value ? undefined : userStore.employeeID,
    sortFlag: sortFlag.value
  };
  attendanceService.getAttendanceDatas(params).then((data: any) => {
    attendanceTable.value = data.attendanceTable;
    manualSubmitFlag.value = data.approveFlag;
    attendanceApproveRecordID.value = data.attendanceApproveRecordID;
    // 考勤日期列
    attendanceColumns = attendanceTable.value.columns[2].childColumns!;
    // 休假统计列
    restColumns = attendanceTable.value.columns[5].childColumns!;
    if (attendanceTable.value?.columns?.length) {
      // 获取要合并行的列
      getMergeColumnKeys(attendanceTable.value?.columns);
      // 合并行
      setTableRowSpanArr(attendanceTable.value?.rows, mergeColumnKeys);
    }
    nextTick(() => attendanceELTable.value.doLayout());
  });
  getAttendanceStatus();
};

/**
 * @description: 获取要合并行的列
 * @param columns
 * @return
 */
const getMergeColumnKeys = (columns: TableColumn[]) => {
  columns.forEach((column) => {
    if (column.mergeFlag) {
      if (column.key === "employee") {
        mergeColumnKeys.push(column.key);
      } else {
        mergeColumnKeys.push(`${column.key},employee`);
      }
    }
    if (column.childColumns) {
      getMergeColumnKeys(column.childColumns);
    }
  });
};

//#region  单元格快捷键操作
const { delete: del, ctrl, z, y } = useMagicKeys();
// 建立attendanceELTable快照，用于撤销、恢复操作
const { undo, redo, canUndo, canRedo } = useDebouncedRefHistory(attendanceTable, { deep: true, debounce: 100 });
// 监听快捷键
watch([() => del.value, () => ctrl.value, () => z.value, () => y.value], () => {
  // 删除
  if (del.value && currentRowIndex.value !== -1 && currentColumnIndex.value !== -1) {
    // 找到对应列
    const column: TableColumn | undefined = attendanceColumns.find((column) => column.index === currentColumnIndex.value);
    if (!column) {
      return;
    }
    let row = attendanceTable.value.rows[currentRowIndex.value];
    row[column.key].value = "";
    row[column.key].restPostID = undefined;
    // 计算出勤天数和休假统计
    statisticsRow(row.employee.employeeID);
  }
  // 撤销
  if (ctrl.value && z.value) {
    canUndo && undo();
    // 如果撤销到变量定义了 就恢复一次，防止页面空白
    if (attendanceTable.value.columns.length <= 0) {
      canRedo && redo();
    }
  }
  // 恢复
  if (ctrl.value && y.value) {
    canRedo && redo();
  }
});
//#endregion

//#region 编辑考勤
/**
 * @description: 选择单元格
 * @param rowIndex 行
 * @param columnIndex 列
 * @return
 */
const selectCell = (rowIndex: number, columnIndex: number) => {
  // 只读模式直接返回
  if (readonly) {
    return;
  }
  const column: TableColumn | undefined = attendanceColumns.find((column) => column.index === columnIndex);
  // 非考勤日期列 不可选择
  if (!column) {
    return;
  }
  currentRowIndex.value = rowIndex;
  currentColumnIndex.value = columnIndex;
};
/**
 * @description: 显示菜单
 * @param event 鼠标事件
 * @return
 */
const showCellMenu = (event: MouseEvent) => {
  // 只读模式直接返回
  if (readonly) {
    return;
  }
  if (["1", "2", "5"].includes(attendanceApprovalStatus.value)) {
    return;
  }
  // 找到对应列
  const column: TableColumn | undefined = attendanceColumns.find((column) => column.index === currentColumnIndex.value);
  if (!column) {
    return;
  }
  let row = attendanceTable.value.rows[currentRowIndex.value];
  let attendanceData = row[column.key];
  let menuItems: Record<string, any>[] = [];
  restPostList.value.forEach((restPost: Record<string, any>) => {
    let checked = attendanceData.value === restPost.localLabel;
    menuItems.push({
      // 菜单项目的class
      customClass: `context-menu-item ${checked ? "checked" : ""}`,
      checked: checked,
      // 菜单项目显示内容
      label: restPost.label,
      // 菜单项目下面显示分割线  只有一个时不显示
      divided: true,
      onClick: () => {
        // 单元格赋值
        row[column.key].value = restPost.localLabel;
        row[column.key].restPostID = restPost.value;
        // 计算出勤天数和休假统计
        statisticsRow(row.employee.employeeID);
      }
    });
  });
  // 获取挂载容器
  const container = document.getElementById("attendance-table");
  // 重新计算相对于挂载容器的位置
  const clickPosition = proxy.$transformMenuPosition(event.target as HTMLElement, event.offsetX, event.offsetY, container);
  const contextMenuOptions = {
    getContainer: container,
    x: clickPosition.x,
    y: clickPosition.y,
    // 菜单将自动调整其位置，以防止溢出容器
    adjustPosition: true,
    // 用户滚动鼠标时是否关闭菜单
    closeWhenScroll: true,
    customClass: "attendance-context-menu",
    items: menuItems
  };
  proxy.$showContextMenu(contextMenuOptions);
};

/**
 * @description: 重新统计个人出勤天数及休假统计
 * @param employeeID
 * @return
 */
const statisticsRow = (employeeID: string) => {
  let actualAttendanceDays: number = 0;
  let restDays: number = 0;
  let restStatistics: { [key: string]: number } = {};
  // 根据人员统计上午和下午
  let employeeRows = attendanceTable.value.rows.filter((row) => row.employee.employeeID === employeeID);
  employeeRows.forEach((row) => {
    for (const key in row) {
      // 组装考勤明细
      if (attendanceColumns?.find((column) => column.key === key) && row[key]?.value) {
        if (row[key]?.restPostID) {
          // 休假统计
          restDays += 0.5;
          if (Object.keys(restStatistics).includes(String(row[key]?.restPostID))) {
            restStatistics[row[key]?.restPostID] += 0.5;
          } else {
            restStatistics[row[key]?.restPostID] = 0.5;
          }
        } else {
          // 出勤天数统计
          actualAttendanceDays += 0.5;
        }
      }
    }
  });
  // 重新赋值
  employeeRows.forEach((row) => {
    for (const key in row) {
      if (Object.keys(restStatistics).includes(key)) {
        row[key] = restStatistics[key];
      } else if (attendanceTable.value.columns[5].childColumns?.find((childColumn) => childColumn.key === key)) {
        row[key] = "";
      }
    }
    row.actualAttendanceDays = actualAttendanceDays;
    row.restDays = restDays;
  });
};
//#endregion
/**
 * @description: 生成考勤表
 */
const createAttendanceRecord = async () => {
  const params = {
    departmentID: selectDepartmentID.value,
    attendanceYear: Number(datetimeUtil.formatDate(yearMonth.value, "yyyy")),
    attendanceMonth: Number(datetimeUtil.formatDate(yearMonth.value, "M"))
  };
  let createFlag = true;
  // 生成前先检核下是否被编辑过，如已编辑过  弹窗提醒
  await attendanceService.checkAttendanceEdit(params).then(async (ret: any) => {
    if (ret) {
      await confirmBox(`${yearMonth.value}考勤已调整过，是否依据${yearMonth.value}排班表覆盖？`, "系统提示", (flag: boolean) => {
        if (!flag) {
          createFlag = false;
        }
      });
    }
  });
  if (createFlag) {
    await attendanceService.createAttendanceRecord(params).then((data: any) => {
      if (data) {
        getAttendanceDatas();
        showMessage("success", "生成考勤表成功！");
      }
    });
  }
};
//#region 保存考勤表
/**
 * @description: 保存考勤表
 */
const saveAttendance = () => {
  const attendanceView = getAttendanceView();
  attendanceService.saveAttendance(attendanceView).then((data: any) => {
    if (data) {
      showMessage("success", "保存成功");
      getAttendanceDatas();
    }
  });
};
/**
 * @description: 组装保存考勤参数
 */
const getAttendanceView = () => {
  let attendanceView: Record<string, any> = {
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M"),
    departmentID: selectDepartmentID.value,
    employeeAttendanceList: []
  };
  attendanceTable.value.rows.forEach((row) => {
    let employeeAttendance = attendanceView.employeeAttendanceList.find(
      (employeeAttendance: Record<string, any>) => employeeAttendance.employeeID === row.employee.employeeID
    );
    let isSameEmployee = true;
    if (!employeeAttendance) {
      isSameEmployee = false;
      employeeAttendance = {
        attendanceRecordID: row.attendanceRecordID,
        employeeID: row.employee.employeeID,
        actualAttendanceDays: row.actualAttendanceDays,
        restDays: row.restDays,
        eveningShiftDays: row.eveningShiftDays,
        nightShiftDays: row.nightShiftDays,
        wholeNightShiftDays: row.wholeNightShiftDays,
        remark: row.remark,
        restStatistics: [],
        attendanceDetails: []
      };
    }

    for (const key in row) {
      // 组装考勤明细
      if (attendanceColumns?.find((column) => column.key === key) && row[key]?.value) {
        employeeAttendance.attendanceDetails.push({
          noonTypeID: row.noonType?.value,
          attendanceDate: row[key].attendanceDate,
          attendanceContent: row[key].value
        });
      }
      // 组装休假统计, 排除休假合计,同一人只添加一次
      if (restColumns?.find((column) => column.key === key) && row[key] && key !== "restDays" && !isSameEmployee) {
        employeeAttendance.restStatistics.push({
          restPostID: Number(key),
          statisticsDays: row[key]
        });
      }
    }
    if (!isSameEmployee) {
      attendanceView.employeeAttendanceList.push(employeeAttendance);
    }
  });
  return attendanceView;
};
/**
 * @description: 考勤回传OA
 */
const attendanceCheck = async () => {
  let param = {
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M"),
    departmentID: selectDepartmentID.value
  };
  await attendanceService.SyncAttendanceDataToOA(param).then((data: any) => {
    if (data) {
      showMessage("success", "考勤已回传");
    }
    getAttendanceStatus();
  });
};
import { useUtils } from "../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 考勤审核
 */
const attendanceApproval = async () => {
  let param = {
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M"),
    departmentID: selectDepartmentID.value,
    proveCategory: "AA-090"
  };
  await attendanceService.AttendanceApproval(param).then((res: any) => {
    if (!res.recordSaveFlag) {
      return;
    } else if (!res.approveSaveFlag) {
      showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
    } else {
      showMessage("success", "保存成功");
    }
    getAttendanceDatas();
  });
};
/**
 * @description: 取消考勤审核
 */
const cancelAttendanceApproval = () => {
  const params = {
    departmentID: selectDepartmentID.value,
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M")
  };
  attendanceService.CancelAttendanceApproval(params).then((data: any) => {
    if (data) {
      showMessage("success", "已取消考勤审核");
      getAttendanceStatus();
    }
  });
};
/**
 * @description: 获取考勤状态
 */
const getAttendanceStatus = async () => {
  const params = {
    departmentID: selectDepartmentID.value,
    attendanceYear: datetimeUtil.formatDate(yearMonth.value, "yyyy"),
    attendanceMonth: datetimeUtil.formatDate(yearMonth.value, "M")
  };
  await attendanceService.GetAttendanceStatus(params).then((data: any) => {
    data && (attendanceApprovalStatus.value = data);
  });
};
//#endregion
</script>

<style lang="scss">
.attendance {
  height: 100%;
  width: 100%;
  .base-header {
    @include flex-aline(row, space-between);
    & > div {
      @include flex-aline(row, start);
      height: 100%;
      .el-button {
        margin: 0 !important;
      }
      .year-month {
        width: 200px !important;
      }
      .sort-flag {
        //margin: 0 15px 0 10px;
        width: 200px !important;
      }
    }
  }

  .attendance-table {
    &.readonly * {
      cursor: default !important;
    }
    .el-table__row td {
      padding: 0 !important;
    }
    .cell {
      padding: 0 !important;
      .attendance-cell {
        padding: 5px;
        height: 34px;
        &.can-edit {
          cursor: pointer;
          max-height: 60px;
        }
        &.data {
          cursor: pointer;
          border: 1px solid transparent;
          &.current {
            border-color: #0000ff;
          }
        }
        .el-input-number {
          width: 95%;
        }
      }
    }
  }
  .footer-description {
    margin: 10px 0 0 30px;
    span {
      color: #000000;
      margin-right: 10px;
    }
  }
}
.attendance-context-menu {
  .context-menu-item {
    cursor: pointer;
    &:hover {
      @include select-style();
      .mx-checked-mark {
        fill: #ffffff;
      }
    }
    &.checked {
      color: $base-color;
      font-weight: bold;
      .mx-checked-mark {
        fill: $base-color;
      }
    }
  }
}
</style>
