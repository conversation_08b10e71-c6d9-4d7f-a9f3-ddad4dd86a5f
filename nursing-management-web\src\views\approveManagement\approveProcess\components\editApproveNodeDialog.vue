<!--
 * FilePath     : \src\views\approveManagement\approveProcess\components\editApproveNodeDialog.vue
 * Author       : 杨欣欣
 * Date         : 2023-12-03 15:41
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-01-05 14:07
 * Description  : 编辑审批节点弹窗
 * CodeIterationRecord:
 -->
<template>
  <el-dialog class="edit-node" title="审批人" width="30%">
    <el-form :model="node" :rules="rules" ref="formRef">
      <el-form-item label="时限（分钟）：" prop="approveTimeLimit">
        <el-input-number class="time-limit-input" controls-position="right" v-model="node!.approveTimeLimit" :min="0" />
      </el-form-item>
      <el-form-item label="方式：" prop="approveModel">
        <el-radio-group v-model="node!.approveModel">
          <el-radio label="1" :disabled="sequentSignDisabled">顺序签</el-radio>
          <!-- <el-radio label="2">会签</el-radio> -->
          <el-radio label="3">或签</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="已选：">
        <el-tag
          class="node-detail-tag"
          v-for="(nodeDetail, index) in node!.nodeDetails"
          :key="index"
          closable
          @close="deleteNodeDetail(nodeDetail)"
        >
          {{ nodeDetail.name }}
        </el-tag>
        <approver-selector @add="addDetailToNode" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="emits('cancel')">取消</el-button>
      <el-button type="primary" @click="approveNodeSubmit(formRef)">确定</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { approveProcessNode } from "../types/approveProcessNode";
import type { approveProcessNodeDetail } from "../types/approveProcessNodeDetail";
const props = defineProps({
  node: {
    type: Object as () => Partial<approveProcessNode>,
    required: true
  }
});
const formRef = ref();
const emits = defineEmits(["update:node", "cancel", "submit"]);
const node = useVModel(props, "node", emits);
const rules = ref({
  approveTimeLimit: [{ required: true, message: "请输入时限（分钟），0代表无时限", trigger: "blur" }],
  approveModel: [{ required: true, message: "请选择审批方式", trigger: "blur" }]
});
// 当选择了职务 或 多个人员作为审批节点的明细时，需要禁用顺序签
const sequentSignDisabled = computed(() =>
  Boolean(node.value.nodeDetails?.length || node.value.nodeDetails?.some((detail) => detail.nodeDetailType === 2))
);
whenever(
  () => sequentSignDisabled.value,
  () => (node.value.approveModel = undefined)
);
/**
 * @description: 删除节点明细
 * @param nodeDetail 节点明细
 * @return
 */
const deleteNodeDetail = (nodeDetail: approveProcessNodeDetail) => {
  const deleteNodeDetailIndex = node.value.nodeDetails!.indexOf(nodeDetail);
  node.value.nodeDetails!.splice(deleteNodeDetailIndex, 1);
};
/**
 * @description: 添加新节点明细到节点对象
 * @param nodeDetail 节点明细
 * @return
 */
const addDetailToNode = (nodeDetail: approveProcessNodeDetail) => {
  node.value.nodeDetails!.push({
    ...nodeDetail,
    approveNodeID: node.value.approveNodeID as string
  });
};
/**
 * @description: 保存审批节点检核
 * @param formEl 表单实例
 * @return
 */
const approveNodeSubmit = async (formEl: Record<string, any>) => {
  await formEl!.validate((valid: boolean) => {
    if (!valid) {
      return;
    }
    emits("submit");
  });
};
</script>
<style lang="scss">
.edit-node {
  .time-limit-input {
    width: 100px;
  }
  .node-detail-tag {
    margin-right: 8px;
  }
}
</style>
