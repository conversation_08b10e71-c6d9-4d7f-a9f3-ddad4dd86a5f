/* * FilePath : \src\components\selector\entryDateSelector.vue * Author : 苏军志 * Date : 2023-08-14 18:56 * LastEditors : 苏军志 *
LastEditTime : 2023-09-19 09:46 * Description : 5 *px CodeIterationRecord: */
<!--
 * FilePath     : e:\NursingManagement\nursing-management-web\src\components\selector\entryDateSelector.vue
 * Author       : 孟昭永
 * Date         : 2023-08-13 14:54
 * LastEditors  : 郭鹏超
 * LastEditTime : 2023-11-07 10:58
 * Description  : 入职日期选择器
 * CodeIterationRecord:
-->
<template>
  <div class="entry-date-selector">
    <span v-if="label">{{ label }}：</span>
    <el-date-picker
      class="entry-select"
      v-model="entryDate"
      type="daterange"
      range-separator="-"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
      :unlink-panels="true"
      @change="change"
    />
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  label: {
    type: String,
    default: "入职日期"
  },
  modelValue: {
    type: Array<String>
  }
});

// 双向绑定
const emits = defineEmits(["update:modelValue", "change"]);
let entryDate = useVModel(props, "modelValue", emits);
const change = () => {
  emits("change");
};
</script>
<style lang="scss">
.entry-date-selector {
  display: inline-block;
  .entry-select {
    width: 260px;
    margin-right: 10px;
  }
  .el-date-editor {
    padding: 4px 20px;
  }
}
</style>
