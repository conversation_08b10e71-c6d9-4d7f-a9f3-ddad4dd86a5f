/*
 * FilePath     : \src\hooks\useStatusTag.ts
 * Author       : 苏军志
 * Date         : 2024-04-06 09:44
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-14 18:12
 * Description  : 状态tag类型
 * CodeIterationRecord:
 */

export function useStatusTag() {
  return {
    /**
     * @description: 获取审批状态tag类型
     * @param status
     * @return
     */
    getApproveStatusTag(status?: string) {
      if (!status) {
        return "info";
      }
      // 0：申请提交、1：审核中、2：审批通过、3：审批未通过、4：审核撤回
      const tagType: Record<string, string> = {
        0: "warning",
        1: "primary",
        2: "success",
        3: "danger",
        4: "info"
      };
      return tagType[status] || "info";
    },
    /**
     * @description: 获取使用状态tag类型
     * @param status
     * @return
     */
    getUseStatusTag(status?: string) {
      if (!status) {
        return "info";
      }
      const tagType: Record<string, string> = {
        0: "danger",
        1: "success"
      };
      return tagType[status] || "info";
    },
    /**
     * @description: 获取考核状态tag类型
     * @param statusCode
     * @return
     */
    getExamineStatusTag(statusCode?: string) {
      if (!statusCode) {
        return "info";
      }
      // 1：待考核、2：签到、3：考核中、4：已交卷、5：作弊交卷、6：强制交卷
      const tagType: Record<string, string> = {
        1: "warning",
        2: "primary",
        3: "success",
        4: "success",
        5: "danger",
        6: "warning",
        7: "primary"
      };
      return tagType[statusCode] || "info";
    },
    getExaminationAppointmentStatusTag(status?: string) {
      if (!status) {
        return "info";
      }
      // 1：预约、2：撤销预约
      const tagType: Record<string, string> = {
        1: "success",
        2: "danger"
      };
      return tagType[status] || "info";
    },
    getAuditStatusTag(status?: string) {
      if (!status) {
        return "info";
      }
      // 0：申请提交、1：审核中、2：审批通过、3：审批未通过、4：审核撤回
      const tagType: Record<string, string> = {
        0: "info",
        1: "warning",
        2: "success",
        3: "danger",
        4: "info",
        5: "success",
        6: "success"
      };
      return tagType[status] || "info";
    }
  };
}
