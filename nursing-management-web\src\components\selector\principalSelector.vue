<!--
 * FilePath     : /src/components/selector/principalSelector.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-27 15:11
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 19:07
 * Description  : 年度计划制定-负责人选择器
 * CodeIterationRecord:
 -->
<template>
  <transfer
    v-model="employeeIDs"
    :data="employees"
    :titles="['人员', '负责人']"
    :filterable="true"
    :props="{
      key: 'value'
    }"
  >
    <template #left-header>
      <department-selector v-model="departmentID" />
    </template>
    <template #right-header>
      <span>分组名称：</span>
      <el-autocomplete
        v-if="hook"
        class="group-name"
        v-model="groupName"
        :fetch-suggestions="getSuggestion"
        value-key="principalGroupName"
        clearable
        @select="setPrincipals"
      >
      </el-autocomplete>
      <el-input v-else class="group-name" v-model="groupName" />
    </template>
  </transfer>
</template>
<script setup lang="ts">
import transfer from "../transfer/index.vue";
const { createJointEmployeeNamesFunc, recommendGroups, hook } = defineProps({
  createJointEmployeeNamesFunc: {
    type: Function,
    required: true
  },
  recommendGroups: {
    type: Array<Record<string, any>>,
    default: undefined
  },
  hook: {
    type: Function,
    default: undefined
  }
});
const employeeIDs = defineModel<string[]>("principalIDs", { required: true });
const groupName = defineModel<string>("customGroupName", { required: true });
const { getSuggestion, setPrincipals } = hook ? hook(recommendGroups) : { getSuggestion: () => {}, setPrincipals: () => {} };
const { userStore } = useStore();
//#region 人员信息数据加载
const departmentID = ref<number>(userStore.departmentID);
const employees = ref<Record<string, any>[]>([]);
const { getEmployeeData } = useDictionaryData();
const setEmployees = async () => {
  const res = await getEmployeeData(false, departmentID.value, Math.random());
  const selectedEmployees = employees.value.filter((item) => employeeIDs.value.includes(item.value));
  const currentDepartmentEmployees = res.filter((item) => !employeeIDs.value.includes(item.value));
  employees.value = [...currentDepartmentEmployees, ...selectedEmployees];
};
onMounted(async () => await setEmployees());
watch(departmentID, async () => await setEmployees());
//#endregion
createJointEmployeeNamesFunc(employees);

defineExpose({
  setEmployees
});
</script>
<style scoped lang="scss">
.group-name {
  width: 150px;
}
</style>
