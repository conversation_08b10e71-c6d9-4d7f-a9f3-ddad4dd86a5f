/*
 * FilePath     : \src\components\univerSheet\types\sheetParamType.ts
 * Author       : 苏军志
 * Date         : 2024-10-14 17:49
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-31 17:24
 * Description  : 格模板需要的参数
 * CodeIterationRecord:
 */
export interface sheetParamType {
  /**
   * @description: 行数
   */
  rowCount?: number;
  /**
   * @description: 列数
   */
  columnCount?: number;
  /**
   * @description: 最大列数
   */
  maxColumnCount?: number;
  /**
   * @description: 列标题占用行数
   */
  headerRowCount?: number;
  /**
   * @description: 默认单元格高度
   */
  defaultRowHeight?: number;
  /**
   * @description: 默认单元格宽度
   */
  defaultColumnWidth?: number;
  /**
   * @description: 隐藏行标题，1隐藏；0显示，不传默认0
   */
  hiddenRowHeader?: number;
  /**
   * @description: 隐藏列标题，1隐藏；0显示，不传默认0
   */
  hiddenColumnHeader?: number;
  /**
   * @description: 列标题字体颜色
   */
  headerFontColor?: string;
  /**
   * @description: 列标题背景颜色
   */
  headerBackgroundColor?: string;
  /**
   * @description: 列数据，用于设置指定列的宽度
   */
  columnData?: Record<string, Record<string, any>>;
  /**
   * @description: 行数据，用于设置指定行的高度
   */
  rowData?: Record<string, Record<string, any>>;
  /**
   * @description: 单元格合并数据
   */
  mergeData?: Record<string, any>[];
  /**
   * @description: 单元格数据
   */
  cellData?: Record<string, Record<string, any>>;
}
