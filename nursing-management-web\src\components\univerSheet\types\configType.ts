/*
 * FilePath     : \src\components\univerSheet\types\configType.ts
 * Author       : 苏军志
 * Date         : 2024-11-26 15:36
 * LastEditors  : 苏军志
 * LastEditTime : 2024-11-26 15:56
 * Description  : 配置类型
 * CodeIterationRecord:
 */
import { menuEnum } from "../types/menuEnum";
export interface configType {
  /**
   * @description: 是否显示顶部
   */
  showHeader?: boolean;
  /**
   * @description: 是否显示顶部工具栏
   */
  showToolbar?: boolean;
  /**
   * @description: 是否显示顶部公式编辑栏
   */
  showFormula?: boolean;
  /**
   * @description: 是否显示底部
   */
  showFooter?: boolean;
  /**
   * @description: 列标题栏占用行数
   */
  headerRowCount?: number;
  /**
   * @description: 显示行号的结束序号
   */
  showRowIndexEnd?: number;
  /**
   * @description: 列标题字体颜色
   */
  headerFontColor?: string;
  /**
   * @description: 列标题背景色
   */
  headerBackgroundColor?: string;
  /**
   * @description: 显示菜单列表
   */
  showMenuList?: menuEnum[];
  /**
   * @description: 撤销回调
   */
  onUndo?: Function;
  /**
   * @description: 恢复回调
   */
  onRedo?: Function;
  /**
   * @description: 单元格清除内容回调方法
   */
  onClearCell?: Function;
  /**
   * @description: 列发生变化时回调方法(包含新增列、删除列、改变列的位置)
   */
  onColumnChange?: Function;
  /**
   * @description: 改变行的位置
   */
  onMoveRows?: Function;
  /**
   * @description:  单元格内容发生前
   */
  onBeforeCellDataChange?: Function;
  /**
   * @description:  单元格内容发生变化
   */
  onCellDataChange?: Function;
  // /**
  //  * @description:  单元格评论/批注发生变化
  //  */
  // onCommentChange?: Function;
  /**
   * @description: 单元格悬浮
   */
  onCellHover?: Function;
}
