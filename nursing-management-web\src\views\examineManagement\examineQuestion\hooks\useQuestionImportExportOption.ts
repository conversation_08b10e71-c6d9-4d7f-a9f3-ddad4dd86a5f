/*
 * relative     : \nursing-management-web\src\views\examineManagement\examineQuestion\hooks\useQuestionImportExportOption.ts
 * Author       : 张现忠
 * Date         : 2025-04-04 10:52
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-10 11:38
 * Description  : 设置导入导出题目参数
 * CodeIterationRecord:
 */

import type { Reactive } from "vue";
export const useQuestionImportExportOption = () => {
  type ParseExcelFunction = (file: File, sheetName: string, firstFixedColumnName: string) => Promise<any>;
  // #region excel导入文档配置
  // 理论题库column title
  const theoreticalColumnData = {
    questionContent: "题目名称",
    examinationQuestionType: "题目类型（根据题型对照填写）",
    content: "题目明细",
    selectFlag: "正确答案（1正确，0错误）",
    difficultyLevel: "题目难度（根据难度对照填写）",
    questionTag: "题目标签",
    instructions: "题目说明",
    filterWeight: "题目权重",
    analysis: "答案解析"
  };
  // 实践题库column title
  const practicalColumnData = {
    questionContent: "考核项目",
    score: "分值"
  };
  // 理论题库导入excel配置
  const theoreticalImportExcelOption = {
    buttonName: "题目导入",
    sheetName: "题库题目",
    firstFixedColumnName: "题目名称",
    columnData: {
      ...theoreticalColumnData
    }
  };
  // 实践题库导入excel配置
  const practicalImportExcelOption = {
    buttonName: "题目导入",
    sheetName: "题库题目",
    firstFixedColumnName: "考核项目",
    columnData: {
      ...practicalColumnData
    },
    tableData: []
  };
  // #endregion

  // #region excel导出文档配置

  const theoreticalExportExcelOption = [
    {
      buttonName: "题目导出",
      fileName: "题目导出文件",
      sheetName: "题库题目",
      firstFixedColumnName: "题目名称",
      columnData: {
        ...theoreticalColumnData
      },
      tableData: []
    }, // 题目标签sheet
    {
      sheetName: "题目标签对照",
      columnData: {
        questionTagID: "标签ID",
        questionTagName: "标签名称"
      },
      tableData: []
    } as any,
    // 题目难度
    {
      sheetName: "题目难度对照",
      columnData: {
        difficultyLevelID: "难度ID",
        difficultyLevelName: "难度名称"
      },
      tableData: []
    } as any,
    {
      sheetName: "题目类型对照",
      columnData: {
        questionTypeID: "类型ID",
        questionTypeName: "类型名称"
      },
      tableData: []
    } as any
  ] as ExportExcelView[];

  const practicalExportExcelOption = [
    {
      buttonName: "题库模板导出",
      fileName: "题库模板导出文件",
      sheetName: "题库题目",
      columnData: {
        ...practicalColumnData
      },
      tableData: []
    }
  ] as ExportExcelView[];

  // #endregion

  // #region 理论题库导出数据设置
  const theoreticalExportTableData = [
    {
      questionContent: "病员坐在轮椅上时，为防止跌倒，应嘱病员( )",
      examinationQuestionType: "SingleChoice",
      difficultyLevel: "Easy",
      questionTag: "1",
      instructions: "",
      filterWeight: "1",
      analysis: "",
      content: "扶好扶手，尽量向后坐",
      selectFlag: "1"
    },
    {
      questionContent: "",
      examinationQuestionType: "",
      difficultyLevel: "",
      questionTag: "",
      instructions: "",
      filterWeight: "",
      analysis: "",
      content: "扶好扶手，尽量向前坐",
      selectFlag: "0"
    },
    {
      questionContent: "",
      examinationQuestionType: "",
      difficultyLevel: "",
      questionTag: "",
      instructions: "",
      filterWeight: "",
      analysis: "",
      content: "两手放膝上，向前坐",
      selectFlag: "0"
    },
    {
      questionContent: "",
      examinationQuestionType: "",
      difficultyLevel: "",
      questionTag: "",
      instructions: "",
      filterWeight: "",
      analysis: "",
      content: "两手放膝上，尽量向后坐",
      selectFlag: "0"
    },
    {
      questionContent: "使用约束带时，对病人应重点观察局部皮肤颜色有无变化",
      examinationQuestionType: "Judgment",
      difficultyLevel: "Easy",
      questionTag: "1",
      instructions: "",
      filterWeight: "1",
      analysis: "",
      content: "正确",
      selectFlag: "1"
    },
    {
      questionContent: "",
      examinationQuestionType: "",
      difficultyLevel: "",
      questionTag: "",
      instructions: "",
      filterWeight: "",
      analysis: "",
      content: "错误",
      selectFlag: "0"
    }
  ] as any;
  const practicalExportTableData = [
    {
      questionContent: "人员要求：仪表大方、举止端庄、服装、鞋帽整洁，佩戴胸卡、修剪指甲、洗手",
      score: "2"
    },
    {
      questionContent: "环境评估：安静、清洁，温湿度适宜，光线充足,符合无菌操作、职业防护要求",
      score: "2"
    },
    {
      questionContent:
        "物品准备：治疗车上层：PDA、输入液体（在治疗准备室内按无菌要求插入输液器）、不同型号的留置针、一次性备用针头、输液接头、无菌透明敷料、预冲式导管冲洗器、止血带、治疗巾、消毒物品（吉尔碘、棉签）、医用胶贴、无菌或清洁手套、速干手消毒剂",
      score: "2"
    },
    {
      questionContent: "物品准备：治疗车下层：生活垃圾袋、医疗废物桶、利器盒、回收物盛放容器",
      score: "1"
    },
    {
      questionContent: "物品准备：检查无菌物品的有效期",
      score: "2"
    },
    {
      questionContent: "评估：评估患者的年龄、病情、意识状态、心肺功能、药物过敏史、输注药物性质等",
      score: "2"
    },
    {
      questionContent: "评估：评估患者的心理状态及合作程度",
      score: "2"
    },
    {
      questionContent: "评估：评估穿刺部位的皮肤、穿刺史、血管状况及肢体活动度",
      score: "2"
    },
    {
      questionContent:
        '身份核实：备齐物品，推车至床旁，用"PDA"按照标准化核对流程核对病人信息（姓名、腕带等）及核对医嘱执行单（未核对不得分）',
      score: "4"
    },
    {
      questionContent: "告知：告知病人操作目的、方法及配合要点，取得病人配合，协助病人大小便，取得舒适体位（未告知不得分）",
      score: "4"
    },
    {
      questionContent:
        "排气：弃去输液器外包装，将输液瓶挂在输液架上面，输液管末端与留置针输液街头连接，排气至输液接头处，关闭调节器，悬挂输液管端头合适，不得甩碰到地面或床面",
      score: "2"
    },
    {
      questionContent: "排气：莫非氏管液面高度合适（1/2或2/3满）",
      score: "1"
    },
    {
      questionContent: "排气：第一次排液体后输液管下段官腔内无气泡",
      score: "2"
    }
  ];
  // #endregion

  practicalExportExcelOption[0].tableData = practicalExportTableData;
  theoreticalExportExcelOption[0].tableData = theoreticalExportTableData;

  // #region 理论题库导出标签数据设置方法
  /**
   * @description: 设置理论题库导出标签数据
   * @param fileOption 文件组件参数
   * @param tagDict 题目标签字典
   * @returns
   */
  const setTheoreticalExportTagTableData = (fileOption: Reactive<FilePropsView>, tagDict: Record<string, any>) => {
    if (!tagDict) {
      return;
    }
    if (fileOption.exportExcelOption && fileOption.exportExcelOption.length) {
      fileOption.exportExcelOption[1].tableData = tagDict["QuestionTag"]?.map((item: any) => {
        return {
          questionTagID: item.key,
          questionTagName: item.value
        };
      });
      fileOption.exportExcelOption[2].tableData = tagDict["DifficultyLevel"]?.map((item: any) => {
        return {
          difficultyLevelID: item.key,
          difficultyLevelName: item.value
        };
      });
      fileOption.exportExcelOption[3].tableData = tagDict["ExaminationQuestionType"]?.map((item: any) => {
        return {
          questionTypeID: item.key,
          questionTypeName: item.value
        };
      });
    }
  };
  // #endregion

  return {
    /**
     * description: 设置文件组件参数
     * @param fileOption 文件组件参数
     * @param type 题库类型 理论/实践
     * @param tagDict 题目标签字典
     * @param parseExcelFunc 解析excel的函数
     * @returns
     */
    setFileOption(fileOption: Reactive<FilePropsView>, type: string, tagDict: Record<string, any>, parseExcelFunc: ParseExcelFunction) {
      if (type === "practical") {
        fileOption.importExcelOption = practicalImportExcelOption;
        fileOption.exportExcelOption = practicalExportExcelOption;
        fileOption.exportExcelOption = practicalExportExcelOption;

        fileOption.importExcelOption.parseExcelFunc = parseExcelFunc;
      } else {
        fileOption.importExcelOption = theoreticalImportExcelOption;
        fileOption.exportExcelOption = theoreticalExportExcelOption;
        fileOption.importExcelOption.parseExcelFunc = parseExcelFunc;
        setTheoreticalExportTagTableData(fileOption, tagDict);
      }
      return fileOption;
    },
    /**
     * @description: 设置理论题库导出标签数据
     * @param fileOption 文件组件参数
     * @param tagDict 题目标签字典
     * @returns
     */
    setTheoreticalExportTagTableData
  };
};
