/*
 * FilePath     : e:\NursingManagement\nursing-management-web\src\utils\setting.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 17:25
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-04-08 11:10
 * Description  : 将/public/static/config.json里的配置项读取出来
 * CodeIterationRecord:
 */
import common from "@/utils/common";
import http from "@/utils/http";
import axios from "axios";
// 先置空baseURL，读取本地配置文件
axios.defaults.baseURL = "";
let appConfig: any;

/**
 * @description: 初始化配置
 */
const initSetting = () => {
  axios.get("/static/config.json").then((res) => {
    appConfig = res.data;
    const isInnerServer = common.storage("isInnerServer");
    if (isInnerServer === undefined) {
      // 判断下是否为内网
      common.checkServer(appConfig.innerServerUrl, (flag: boolean) => {
        common.storage("isInnerServer", flag);
        setServerApiUrl();
      });
    } else {
      setServerApiUrl();
    }
  });
};
/**
 * @description: 设置API服务地址
 */
const setServerApiUrl = async () => {
  const isInnerServer = common.storage("isInnerServer");
  let serverUrl = `${isInnerServer ? appConfig.innerServerUrl : appConfig.outerServerUrl}/api`;
  console.log(`%c当前运行环境为：${isInnerServer ? "内网" : "外网"}：${serverUrl}`, "color:#0000ff;");
  common.session("serverUrl", serverUrl);
  let mqUrl = isInnerServer ? appConfig.mqSetting.innerUrl : appConfig.mqSetting.outerUrl;
  delete appConfig.mqSetting.innerUrl;
  delete appConfig.mqSetting.outerUrl;
  appConfig.mqSetting.url = mqUrl;
  common.session("mqSetting", appConfig.mqSetting);
  //统计web地址
  common.session("statisticsWebUrl", appConfig.statisticsSetting[isInnerServer ? "innerStatisticsWebUrl" : "outStatisticsWebUrl"]);
  http.setApiUrl(serverUrl);
};
/**
 * @description: 设置测试API服务地址
 */
const setMockApiUrl = () => http.setApiUrl(appConfig.mockServerUrl);

export { initSetting, setServerApiUrl, setMockApiUrl };
