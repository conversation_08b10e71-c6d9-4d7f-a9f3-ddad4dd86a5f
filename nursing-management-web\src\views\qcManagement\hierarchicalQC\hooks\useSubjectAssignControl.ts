/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useSubjectAssignControl.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-08 11:02
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:43
 * Description  :
 * CodeIterationRecord:
 */
import { subjectAssignClass } from "../types/hierarchicalQCSubjectView";

export const subjectAssignControl = (
  subjectAssignTableView: subjectAssignClass,
  subjectAssignDepartmentList: Ref<any[]>,
  subjectAssignCopyDepartmentList: Ref<any[]>
) => {
  /**
   * description: 保存内容检核
   * return {*}
   */
  interface Massage {
    [key: string]: string;
  }
  const saveCheck = () => {
    let massage: Massage = {
      hierarchicalQCEmploy: "请补充完整 质控人 列数据",
      departmentViews: "请补充完整 质控科室 列数据",
      verifierEmployee: "请补充完整 审核人 列数据"
    };
    for (let index = 0; index < Object.keys(massage).length; index++) {
      const key = Object.keys(massage)[index];
      if (subjectAssignTableView.assignTableData.find((item: any) => !item[key]?.length)) {
        showMessage("warning", massage[key]);
        return true;
      }
      if (subjectAssignTableView.assignTableData.find((item: any) => item.verifierEmployee?.length > 1)) {
        showMessage("warning", "审核人只能放置一个！");
        return true;
      }
    }
    return false;
  };
  return {
    /**
     * description: 指派新增按钮
     * return {*}
     */
    subjectPlanAdd() {
      subjectAssignTableView.assignTableData = [
        ...subjectAssignTableView.assignTableData,
        {
          departmentViews: [],
          verifierEmployee: [],
          hierarchicalQCEmploy: []
        }
      ];
    },
    /**
     * description: 人员删除
     * return {*}
     */
    deleteEmployee(row: any, index: any) {
      row.splice(index, 1);
    },
    /**
     * description: 部门删除
     * param {any} row
     * param {any} index
     * return {*}
     */
    deleteDepartment(row: any, index: any) {
      subjectAssignDepartmentList.value = [row[index], ...subjectAssignDepartmentList.value];
      subjectAssignCopyDepartmentList.value = [row[index], ...subjectAssignCopyDepartmentList.value];
      row.splice(index, 1);
    },
    /**
     * description: 指派表格行数据删除
     * param {*} index
     * return {*}
     */
    deleteSubjectPlanItem(index: any) {
      confirmBox("确定要删除此条指派么？", "指派删除", (flag: Boolean) => {
        if (flag) {
          subjectAssignDepartmentList.value = [
            ...subjectAssignTableView.assignTableData[index].departmentViews,
            ...subjectAssignDepartmentList.value
          ];
          subjectAssignCopyDepartmentList.value = [
            ...subjectAssignTableView.assignTableData[index].departmentViews,
            ...subjectAssignCopyDepartmentList.value
          ];
          subjectAssignTableView.assignTableData.splice(index, 1);
        }
      });
    },
    /**
     * description: 部门从左侧移动至右侧 copy的集合一并删除 避免还能搜索到
     * return {*}
     */
    departmentRemove(value: any) {
      subjectAssignCopyDepartmentList.value.splice(
        subjectAssignCopyDepartmentList.value.findIndex((department) => department.departmentID === value?.element?.departmentID),
        1
      );
    },
    /**
     * description: 指派保存
     * return {*}
     */
    async saveSubjectAssign() {
      if (saveCheck()) {
        return false;
      }
      await hierarchicalQCService.saveSubjectAssign(subjectAssignTableView).then(() => showMessage("success", "保存成功！"));
      return true;
    }
  };
};
