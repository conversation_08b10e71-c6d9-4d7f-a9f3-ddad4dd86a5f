<!--
 * FilePath     : \src\views\examineManagement\components\examinationTypeSelector.vue
 * Author       : 来江禹
 * Date         : 2024-08-14 09:22
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-06 11:12
 * Description  : 考核类型下拉框
 * CodeIterationRecord:
 -->
<template>
  <div class="examination-type-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="typeID"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "@/components/selector/hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "考核类型"
  },
  modelValue: {
    type: String
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 140
  },
  showBlankOptions: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    required: true
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let typeID = useVModel(props, "modelValue", emits);

let typeOptions = ref<Array<Record<any, any>>>([]);
const params: SettingDictionaryParams = {
  settingType: "ExaminationManagement",
  settingTypeCode: "ExaminationType",
  settingTypeValue: props.type,
  index: Math.random()
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  typeOptions.value = datas;
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, typeOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.examination-type-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
