/*
 * relative     : \src\views\qcManagement\hierarchicalQC\setting\hierarchicalQcTrack.ts
 * Author       : 郭鹏超
 * Date         : 2024-11-01 09:50
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:43
 * Description  :考核追踪画面差异配置
 * CodeIterationRecord:
 */
let { userStore } = useStore();
class hierarchicalQcTrackOption {
  qcLevel: string;
  departmentID?: number = undefined;
  formType: string | undefined;
  subjectOptions: Record<string, any>[];
  qcDepartmentOptions: Record<string, any>[];
  qcEmployeeOptions: Record<string, any>[];
  scoreOption: Record<string, any>[];
  isShowDetailScore: boolean = false;
  restrictExamineDateFlag: boolean = false;
  qcType: "nodeQCFormType" | undefined;
  constructor(props: Record<string, any>) {
    this.qcType = "nodeQCFormType";
    this.qcLevel = props.routerQcLevel;
    this.formType = props.formType;
    this.departmentID = props.routerQcLevel === "1" ? userStore.departmentID : undefined;
    this.subjectOptions = [];
    this.qcDepartmentOptions = [];
    this.qcEmployeeOptions = [];
    this.scoreOption = [];
    this.getQCEmployeeOptions();
    this.getAddRecordRestrictDateSetting();
  }
  getAddRecordRestrictDateSetting() {
    let params: SettingDictionaryParams = {
      settingType: "HierarchicalQC",
      settingTypeCode: "SystemSwitch",
      settingTypeValue: `AddRecordRestrictDate_${this.qcLevel}`,
      index: Math.random()
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      this.restrictExamineDateFlag = Boolean(respBool);
    });
  }
  /**
   * @description: 获取主题已指派人员
   */
  getQCEmployeeOptions() {
    let params: SettingDictionaryParams = {
      settingType: "QualityControl",
      settingTypeCode: "QualityScoreDetailSwitch",
      settingTypeValue: "QualityScore"
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      this.isShowDetailScore = Boolean(respBool);
    });
  }
  /**
   * @description:组装以table 数据为基础的options
   * @param tableData
   * @return
   */
  getSearchOptions(tableData: Record<string, any>[]) {
    this.subjectOptions = [];
    this.qcDepartmentOptions = [];
    this.qcEmployeeOptions = [];
    if (tableData?.length === 0) {
      return;
    }

    // 使用 Set 来去重
    const subjectSet = new Set();
    const departmentSet = new Set();
    const employeeSet = new Set();

    tableData.forEach((item: any) => {
      // 去重 subjectOptions
      const subjectKey = item.hierarchicalQCSubjectID;
      if (!subjectSet.has(subjectKey)) {
        subjectSet.add(subjectKey);
        this.subjectOptions.push({
          label: item.subjectName,
          value: subjectKey
        });
      }

      // 去重 qcDepartmentOptions
      const departmentKey = item.departmentID;
      if (!departmentSet.has(departmentKey)) {
        departmentSet.add(departmentKey);
        this.qcDepartmentOptions.push({
          label: item.department,
          value: departmentKey
        });
      }

      // 去重 qcEmployeeOptions
      const examineEmployeeNameList = (item.examineEmployee ?? "").split("、");
      (item.examineEmployeeID ?? []).forEach((employeeID: string, index: number) => {
        if (!employeeSet.has(employeeID)) {
          employeeSet.add(employeeID);
          this.qcEmployeeOptions.push({
            label: examineEmployeeNameList[index],
            value: employeeID
          });
        }
      });
    });
    let maxScore = Math.max(...tableData.flatMap((td: any) => td.trackDetails.map((detail: any) => detail.fullMark)));
    this.getScoreOption(maxScore);
  }
  /**
   * @description: 获取分数options
   * @param maxScore
   * @return
   */
  getScoreOption(maxScore: number) {
    this.scoreOption = [
      { label: "不考评", value: -1 },
      { label: "未满分", value: 0 }
    ];
    if (!this.isShowDetailScore || !maxScore) {
      return;
    }
    this.scoreOption = [{ label: "不考评", value: -1 }];
    for (let i = 0; i < maxScore; i++) {
      this.scoreOption.push({ label: "小于" + (i + 1) + "分", value: i + 1 });
    }
  }
}

export default hierarchicalQcTrackOption;
