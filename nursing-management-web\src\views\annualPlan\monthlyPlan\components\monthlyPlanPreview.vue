<!--
 * FilePath     : /src/views/annualPlan/monthlyPlan/components/monthlyPlanPreview.vue
 * Author       : 马超
 * Date         : 2025-06-21 11:04
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:55
 * Description  : 月度计划预览组件
 * CodeIterationRecord:
-->
<template>
  <base-layout class="monthly-plan-preview">
    <template #header>
      <span class="filter-tags">
        <el-check-tag :checked="true">重点</el-check-tag>
        <el-check-tag :checked="showRoutineWorks" @change="() => (showRoutineWorks = !showRoutineWorks)">常规</el-check-tag>
      </span>
      <slot></slot>
    </template>
    <div class="body" v-if="isDataReady">
      <div class="content" ref="leftWrapper">
        <template v-for="(planType, index) in displayWorks" :key="planType.typeId">
          <div class="type-title" :id="`type-${planType.typeId}`">
            分类{{ numberToChinese(index + 1) + "、" + getTypeContent(planType.typeId) }}
          </div>
          <el-table class="preview-table" :data="planType.planWorks" border stripe row-class-name="plan-row">
            <el-table-column label="工作内容" prop="workContent" :min-width="convertPX(200)" />
            <el-table-column label="工作要求" prop="requirement" :min-width="convertPX(200)" />
            <el-table-column label="负责人" prop="principalName" :width="convertPX(160)" />
          </el-table>
        </template>
      </div>
      <div class="anchor">
        <el-anchor :container="leftWrapper">
          <el-anchor-link v-for="(anchorItem, index) in anchorData" :key="index" :href="`#${anchorItem.id}`" :title="anchorItem.title" />
        </el-anchor>
      </div>
    </div>
  </base-layout>
</template>

<script setup lang="ts">
import { monthlyPlanMaintainService } from "@/api/annualPlan/monthlyPlanMaintainService";
import type { monthlyPlanPreview, monthlyPlanPreviewType, monthlyPlanPreviewWork } from "../../types/monthlyPlanPreview";
import { useAnchor } from "../../hooks/useAnchor";

const convertPX = inject("convertPX") as any;
const { monthlyPlanMainId } = defineProps<{
  monthlyPlanMainId: string;
  modelValue: boolean;
}>();

const showRoutineWorks = defineModel<boolean>({ default: false });
const numberToChinese = common.numberToChinese;

// 使用锚点组合式函数
const { leftWrapper, getTypeList, getTypeContent, createAnchorData } = useAnchor();

const monthlyPlanData = ref<monthlyPlanPreview | undefined>();
const isDataReady = ref<boolean>(false);
const displayWorks = computed<
  | {
      planWorks: monthlyPlanPreviewWork[];
      typeId: number;
    }[]
  | undefined
>(() => {
  return monthlyPlanData.value?.planTypes.map<monthlyPlanPreviewType>((planType: monthlyPlanPreviewType) => ({
    ...planType,
    planWorks: showRoutineWorks.value
      ? planType.planWorks
      : planType.planWorks.filter((work: monthlyPlanPreviewWork) => work.workType === 1)
  }));
});
onMounted(async () => {
  document.oncontextmenu = (event) => event.stopPropagation();
  monthlyPlanData.value = await monthlyPlanMaintainService.getMonthlyPlanPreview({ monthlyPlanMainID: monthlyPlanMainId });
  await getTypeList(typeListIds.value);
  isDataReady.value = true;
});

const typeListIds = computed<number[] | undefined>(() => monthlyPlanData.value?.planTypes.map((planType) => planType.typeId).flat());

const anchorData = computed(() => createAnchorData(monthlyPlanData.value?.planTypes));

/**
 * @description: 组件卸载之前重新禁用右键菜单
 */
onBeforeUnmount(() => {
  document.oncontextmenu = (event) => event.preventDefault();
});
</script>

<style lang="scss" scoped>
.monthly-plan-preview {
  height: 100%;
  .filter-tags :deep(.el-check-tag) {
    margin-left: 8px;
  }
  .body {
    display: flex;
    height: 100%;
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }

      .type-title {
        font-size: 24px;
        color: #ff0000;
        font-weight: bold;
        margin: 4px 0;
      }

      .preview-table {
        margin-bottom: 8px;
        :deep(.plan-row .cell) {
          font-size: 20px;
          margin: 5px 0;
          line-height: 1.6;
        }
      }
    }
    .anchor {
      background-color: #ffffff;
      width: 200px;
      padding: 24px 0 0 24px;
      :deep(.el-anchor) {
        .el-anchor__link {
          font-size: 18px;
          letter-spacing: 1px;
          line-height: 1.6;
        }
      }
    }
  }
}
</style>
