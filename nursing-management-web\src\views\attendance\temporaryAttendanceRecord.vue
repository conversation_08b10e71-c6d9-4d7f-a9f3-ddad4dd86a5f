<!--
 * FilePath     : \src\views\attendance\temporaryAttendanceRecord.vue
 * Author       : 来江禹
 * Date         : 2024-02-16 16:27
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-08 10:08
 * Description  : 临时出勤记录页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="temporary-attendance" :drawerOptions="drawerOptions">
    <template #header>
      <span v-permission:S="12">显示全科：</span>
      <el-switch v-model="showAllDataSwitch" @change="getTableData()" />
      <span class="header-month">月份：</span>
      <el-date-picker
        class="year-month"
        type="month"
        format="YYYY-MM"
        value-format="YYYY-MM"
        v-model="attendanceMonth"
        :clearable="false"
        @change="getTableData()"
      >
      </el-date-picker>

      <el-button v-permission:B="1" class="add-button" @click="addAttendance()">新增</el-button>
    </template>
    <el-table :data="temporaryAttendanceData" border stripe height="100%">
      <el-table-column label="出勤人" prop="attendanceEmployee" :width="convertPX(260)"></el-table-column>
      <el-table-column label="出勤日期" :width="convertPX(200)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.attendanceDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="出勤小时数" prop="attendanceHours" :width="convertPX(150)" align="center"></el-table-column>
      <el-table-column label="出勤标记" prop="temporaryAttendanceName" :width="convertPX(300)" align="center"></el-table-column>
      <el-table-column label="备注" prop="remark" :min-width="convertPX(300)"></el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(100)">
        <template #default="scope">
          <el-tooltip :content="i18nText.modify">
            <i class="iconfont icon-edit" v-permission:B="3" @click="modifyData(scope.row)"></i>
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteData(scope.row.temporaryAttendanceRecordID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="submitRefs" label-width="auto" :model="drawerData" :rules="rules" class="drawer-form">
        <el-form-item label="出 勤 人：" prop="attendanceEmployeeID">
          <employee-selector label="" v-model="drawerData.attendanceEmployeeID" :departmentID="drawerData.departmentID" />
        </el-form-item>
        <el-form-item label="出勤日期：" prop="attendanceDate">
          <el-date-picker
            class="form-select"
            type="date"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            v-model="drawerData.attendanceDate"
            :clearable="false"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="出勤小时：" prop="attendanceHours">
          <el-input-number class="form-select" v-model.number="drawerData.attendanceHours" :precision="1" :min="0" :step="0.5" :max="24" />
        </el-form-item>
        <el-form-item label="出勤标记：" prop="temporaryAttendanceID">
          <el-select v-model="drawerData.temporaryAttendanceID" clearable class="form-select">
            <el-option v-for="item in temporaryAttendancePostSetting" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input class="form-select" v-model="drawerData.remark"></el-input>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入及变量声明
import type { temporaryAttendance } from "./types/temporaryAttendance";
const { proxy } = getCurrentInstance() as any;
let { userStore } = useStore();
const attendanceMonth = ref(datetimeUtil.getNowDate("yyyy-MM"));
const showAllDataSwitch = ref<boolean>(false);
const temporaryAttendanceData = ref<Array<temporaryAttendance>>();
const convertPX: any = inject("convertPX");
const submitRefs = ref({}) as any;
const drawerData = ref<temporaryAttendance>({}) as any;
const temporaryAttendancePostSetting = ref<Array<OptionView>>();
const i18nText = computed(() => {
  return {
    add: proxy.$t("button.add"),
    delete: proxy.$t("button.delete"),
    modify: proxy.$t("button.modify"),
    operation: proxy.$t("label.operation")
  };
});
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "临时出勤记录",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    save();
  }
});
const rules = reactive({
  attendanceEmployeeID: [
    {
      required: true,
      message: "请选择出勤人",
      trigger: "change"
    }
  ],
  attendanceDate: [
    {
      required: true,
      message: "请选择出勤日期",
      trigger: "change"
    }
  ],
  attendanceHours: [
    {
      required: true,
      message: "请填写出勤小时数",
      trigger: "change"
    }
  ]
});
//#endregion

//#region 初始化
onMounted(() => {
  getTableData();
  getTemporaryAttendanceSetting();
});
//#endregion

//#region 业务逻辑
/**
 * @description: 新增
 */
const addAttendance = () => {
  drawerOptions.value.drawerTitle = "临时出勤记录";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerTitle = `新增${drawerOptions.value.drawerTitle}`;
  drawerData.value.departmentID = userStore.departmentID;
  drawerData.value.temporaryAttendanceRecordID = undefined;
  drawerData.value.attendanceEmployeeID = "";
  drawerData.value.attendanceDate = "";
  drawerData.value.attendanceHours = undefined;
  drawerData.value.remark = "";
  drawerData.value.temporaryAttendanceID = undefined;
};
/**
 * @description: 保存
 */
const save = () => {
  if (drawerData.value.attendanceHours === 0) {
    showMessage("warning", "出勤小时数不可为0！");
    return;
  }
  let params = {
    temporaryAttendanceRecordID: drawerData.value.temporaryAttendanceRecordID ?? undefined,
    attendanceEmployeeID: drawerData.value.attendanceEmployeeID,
    attendanceDate: drawerData.value.attendanceDate,
    attendanceHours: drawerData.value.attendanceHours,
    remark: drawerData.value.remark,
    departmentID: drawerData.value.departmentID,
    temporaryAttendanceID: drawerData.value.temporaryAttendanceID
  };
  attendanceService.saveTemporaryAttendanceData(params).then((res: any) => {
    if (res) {
      showMessage("success", "保存成功！");
      showAllDataSwitch.value = true;
      getTableData();
    }
    drawerOptions.value.showDrawer = false;
  });
};
/**
 * @description: 获取数据
 */
const getTableData = () => {
  let param = {
    employeeID: userStore.employeeID,
    showAllFlag: showAllDataSwitch.value,
    month: attendanceMonth.value,
    departmentID: userStore.departmentID
  };
  attendanceService.getTemporaryAttendanceList(param).then((res: any) => {
    if (res) {
      temporaryAttendanceData.value = res;
    }
  });
};
/**
 * @description: 编辑数据
 * @param row
 * @return
 */
const modifyData = (row: temporaryAttendance) => {
  drawerOptions.value.drawerTitle = "临时出勤记录";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerTitle = `编辑${drawerOptions.value.drawerTitle}`;
  drawerData.value.departmentID = row.departmentID;
  drawerData.value.temporaryAttendanceRecordID = row.temporaryAttendanceRecordID;
  drawerData.value.attendanceEmployeeID = row.attendanceEmployeeID;
  drawerData.value.attendanceDate = row.attendanceDate;
  drawerData.value.attendanceHours = row.attendanceHours;
  drawerData.value.remark = row.remark;
  drawerData.value.temporaryAttendanceID = row.temporaryAttendanceID;
};
/**
 * @description: 删除
 * @param recordID
 * @return
 */
const deleteData = (recordID: number) => {
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    attendanceService.deleteTemporaryAttendanceData({ recordID: recordID }).then((res) => {
      if (res) {
        showMessage("success", "删除成功！");
        getTableData();
      }
    });
  });
};
/**
 * @description:获取配置
 */
const getTemporaryAttendanceSetting = () => {
  let param = {
    departmentID: userStore.departmentID
  };
  attendanceService.getTemporaryAttendancePostSetting(param).then((res: any) => {
    if (res) {
      temporaryAttendancePostSetting.value = res;
    }
  });
};
//#endregion
</script>
<style lang="scss">
.temporary-attendance {
  .year-month {
    width: 160px;
  }
  .header-month {
    margin-left: 20px;
  }
  .drawer-form {
    .form-select {
      width: 200px;
    }
  }
}
</style>
