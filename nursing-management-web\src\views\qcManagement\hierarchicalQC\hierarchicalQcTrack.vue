<!--
 * FilePath     : \nursing-management-web\src\views\qcManagement\hierarchicalQC\hierarchicalQcTrack.vue
 * Author       : 郭鹏超
 * Date         : 2023-09-21 09:44
 * LastEditors  : 孟昭永
 * LastEditTime : 2025-01-11 08:32
 * Description  :考核追踪画面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="hierarchical-qc-track" :drawerOptions="drawerOptions" headerHeight="50px">
    <template #header>
      <label>年月:</label>
      <el-date-picker
        type="monthrange"
        range-separator="至"
        format="YYYY-MM"
        value-format="YYYY-MM"
        v-model="subjectSearch.yearMonthRange"
        class="header-yearMonth-range"
      >
      </el-date-picker>
      <label>维护组织:</label>
      <department-switch-cascader @select="pageOptions.changeFormType(subjectSearch)" v-model="subjectSearch.departmentID">
      </department-switch-cascader>
      <label>类别:</label>
      <form-type-selector label="" :width="150" v-model="subjectSearch.formType" :qcType="subjectSearch.qcType"></form-type-selector>
      <label>主题:</label>
      <el-select clearable v-model="subjectSearch.hierarchicalQCSubjectID" style="width: 180px">
        <el-option v-for="item in pageOption.subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <label>被考核部门:</label>
      <el-select clearable v-model="recordSearch.qcDepartmentID" style="width: 120px">
        <el-option v-for="item in pageOption.qcDepartmentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <label>考核人:</label>
      <el-select clearable v-model="recordSearch.employeeID" style="width: 120px">
        <el-option v-for="item in pageOption.qcEmployeeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <label>得分:</label>
      <el-select v-model="recordSearch.score" clearable class="header-score">
        <el-option v-for="(item, index) in pageOption.scoreOption" :key="index" :label="item.label" :value="item.value"> </el-option>
      </el-select>
    </template>
    <el-table height="100%" :data="tableData" stripe border>
      <el-table-column prop="subjectName" label="考核主题" :min-width="convertPX(120)" align="left"></el-table-column>
      <el-table-column label="考核日期" :width="convertPX(180)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.examineDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="examineEmployee" label="考核人" :width="convertPX(140)" align="left"></el-table-column>
      <el-table-column prop="department" label="被跟踪部门" :min-width="convertPX(80)" align="left"></el-table-column>
      <el-table-column label="分数" :width="convertPX(100)" align="center">
        <template #default="{ row }">
          <span :class="row.minPassingScore && row.point < row.minPassingScore ? 'red' : ''">{{ row.point }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="reader" label="阅读" :width="convertPX(100)" align="center"></el-table-column>
      <el-table-column prop="submit" label="未满分或不考核项" :min-width="convertPX(140)" align="left">
        <template #default="scope">
          <div v-for="(trackItem, index) in scope.row.trackDetails" :key="index">
            {{ getNotFullScoreContent(trackItem, index + 1) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(120)" align="center">
        <template #default="scope">
          <el-tooltip content="跟踪考核">
            <i
              v-permission:B="1"
              v-visibilityHidden="getButtonAuthority(scope.row.examineEmployeeID)"
              class="iconfont icon-add"
              @click.stop="openRecordDrawer(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              v-permission:B="4"
              v-visibilityHidden="getButtonAuthority(scope.row.examineEmployeeID)"
              class="iconfont icon-delete"
              @click.stop="deleteMain(scope.row)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <qcForm
        v-model="saveView"
        ref="qcFormDom"
        :formData="formData"
        :disabledExamineDateStart="disabledExamineDateStart"
        :disabledExamineDateEnd="disabledExamineDateEnd"
        @getDetails="getDetails"
      ></qcForm>
    </template>
    <template #drawerOtherFooter>
      <strong>总分：{{ saveView.point }}</strong>
      <el-button @click="drawerOptions.showDrawer = false">取消</el-button>
      <el-button v-permission:B="2" class="print-button" @click="saveMainAndDetails(false)">暂存</el-button>
      <el-button v-permission:B="2" type="primary" @click="saveMainAndDetails(true)">保存</el-button>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const route = useRoute();
const disabledExamineDateStart = ref<string>();
const disabledExamineDateEnd = ref<string>();
import qcForm from "./components/qcForm.vue";
import formTypeSelector from "./components/qcFormTypeSelector.vue";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
const { getButtonAuthority, deleteQcMain, saveQcMainAndDetails, setSavePoint, setSaveDetails, getNotFullScoreContent } =
  useQcCommonMethod();
/**
 * @description: 初始化
 */
onMounted(async () => {
  await getTableData();
  pageOption.value.getSearchOptions(tableData.value);
});
import qcOptions from "./setting/index";
import hierarchicalQcTrack from "./setting/hierarchicalQcTrack";

// 差异配置初始化
const pageOptions = ref<qcOptions>(
  new qcOptions("hierarchicalQcTrack", { routerQcLevel: route.params.qcLevel as string, qcType: "nodeQCFormType" })
);
const pageOption = ref<hierarchicalQcTrack>(pageOptions.value.pageOption as unknown as hierarchicalQcTrack);
import type { subjectSearchView } from "./types/subjectSearchView";
import type { recordSearchView } from "./types/recordSearchView";
// 筛选条件初始化
const subjectSearch = ref<subjectSearchView>({
  yearMonthRange: [datetimeUtil.getNowDate("yyyy-MM"), datetimeUtil.getNowDate("yyyy-MM")],
  qcType: pageOptions.value.qcType,
  qcLevel: pageOption.value.qcLevel,
  departmentID: pageOption.value.departmentID,
  formType: pageOption.value.formType,
  hierarchicalQCSubjectID: undefined
});
const recordSearch = ref<recordSearchView>({
  qcDepartmentID: undefined,
  employeeID: undefined
});
watch(subjectSearch.value, async () => {
  await getTableData();
  pageOption.value.getSearchOptions(tableData.value);
});
watch(recordSearch.value, () => getTableData());

const tableData = ref<Record<string, any>[]>([]);
/**
 * @description: 获取主记录数据
 * @return
 */
const getTableData = async () => {
  let params = {
    ...subjectSearch.value,
    startYearMonth: subjectSearch.value.yearMonthRange![0],
    endYearMonth: subjectSearch.value.yearMonthRange![1],
    ...recordSearch.value
  };
  await hierarchicalQCService.getTrackTableData(params).then((res: any) => {
    if (res) {
      tableData.value = res;
    }
  });
};
import { saveClass } from "./types/hierarchicalSaveView";
const saveView = reactive(new saveClass());
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "跟踪考核",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false,
  confirm: async () => {
    await saveMainAndDetails(true);
  }
});
/**
 * @description: 考核弹窗打开
 * @param item
 * @return
 */
const openRecordDrawer = async (item: any) => {
  drawerOptions.value.showDrawer = true;
  saveView.templateCode = item.templateCode;
  saveView.hierarchicalQCMainID = "";
  saveView.hierarchicalQCRecordID = item.hierarchicalQCRecordID;
  saveView.hierarchicalQCSubjectID = item?.hierarchicalQCSubjectID;
  saveView.guidance = item.guidance;
  saveView.improvement = item.improvement;
  saveView.qcDate = datetimeUtil.getNowDate("yyyy-MM-dd");
  await getFormTemplate(item);
  if (pageOption.value.restrictExamineDateFlag) {
    disabledExamineDateStart.value = item?.startDate ?? "";
    disabledExamineDateEnd.value = item?.endDate ?? "";
  }
};

const qcFormDom = ref<any>();
/**
 * @description: 考核暂存或保存
 * @param saveFlag
 * @return
 */
const saveMainAndDetails = async (saveFlag: boolean) => {
  setSaveDetails(saveView);
  await saveQcMainAndDetails(saveView, qcFormDom.value.rendererForm, saveFlag, isSaveMethod);
};

/**
 * @description: 保存成功后执行
 */
const isSaveMethod = async () => {
  drawerOptions.value.showDrawer = false;
  getTableData();
  showMessage("success", "保存成功");
};

/**
 * @description: 删除维护记录
 * @param row
 * @return
 */
const deleteMain = async (row: any) => {
  if (!row?.hierarchicalQCMainID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  deleteQcMain(row.hierarchicalQCMainID, getTableData);
};

import type { dynamicFormData, formAttribute } from "zhytech-ui";
const formData = ref<dynamicFormData<formAttribute>>();
/**
 * @description: 获取质控评估内容
 * @param careMain
 * @return
 */
const getFormTemplate = async (careMain: any) => {
  formData.value = undefined;
  let params = {
    templateCode: careMain.templateCode,
    careMainID: careMain.hierarchicalQCMainID,
    trackFlag: true
  };
  await hierarchicalQCService.getQCAssessView(params).then((res: any) => {
    if (res) {
      formData.value = res;
    }
  });
};

/**
 * @description: 获取质控内容点选数据
 * @param data
 * @param fileList 文件或图片
 * @return
 */
const getDetails = (data: Record<string, any>[], fileList: Record<string, any>[]) => {
  saveView.templateDetails = data;
  saveView.templateFileList = fileList;
  setSavePoint(saveView, formData.value!);
};
</script>

<style lang="scss">
.hierarchical-qc-track {
  .base-header {
    @include flex-aline();
    .header-yearMonth-range {
      flex-grow: unset;
      width: 200px;
    }
    .header-score {
      width: 100px;
    }
  }
  .red {
    color: #ff0000;
  }
}
</style>
