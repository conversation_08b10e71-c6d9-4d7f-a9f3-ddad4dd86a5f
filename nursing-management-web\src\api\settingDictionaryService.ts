/*
 * FilePath     : \src\api\settingDictionaryService.ts
 * Author       : 来江禹
 * Date         : 2024-08-06 10:45
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-11 10:45
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";
export class settingDictionaryService {
  private static getSettingDictionaryDictApi: string = "SettingDictionary/GetSettingDictionaryDict";
  private static getLanguageListApi: string = "SettingDictionary/GetLanguageList";
  private static getApprovalJumpSettingApi: string = "SettingDictionary/GetApprovalJumpSetting";
  private static getSettingSwitchApi: string = "SettingDictionary/GetSettingSwitch";
  private static getProveCategoryCascaderListApi: string = "SettingDictionary/GetProveCategoryCascaderList";
  private static getOrganizationTypesApi: string = "SettingDictionary/GetOrganizationTypes";
  private static getSettingDictionaryMaintainApi: string = "SettingDictionary/GetSettingDictionaryMaintain";
  private static deleteSettingDictionaryMaintainApi: string = "SettingDictionary/DeleteSettingDictionaryMaintain";
  private static saveSettingDictionaryMaintainApi: string = "SettingDictionary/SaveSettingDictionaryMaintain";
  private static getSettingDictionaryByCodeValueAPi: string = "SettingDictionary/GetSettingDictionaryByCodeValue";
  private static getCascaderSettingDictionaryApi: string = "SettingDictionary/GetCascaderSettingDictionary";
  /**
   * @description 获取非国标字典数据
   * @param params SettingDictionaryParams
   * @returns
   */
  public static getSettingDictionaryDict(params: any) {
    return http.get(this.getSettingDictionaryDictApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  }
  /**
   * 获取语言列表
   * @param params
   * @returns
   */
  public static getLanguageList(params?: any) {
    return http.get(this.getLanguageListApi, params);
  }
  /**
   * @description: 获取审批跳转设置
   * @param params
   * @returns
   */
  public static getApprovalJumpSetting(params?: any) {
    return http.get(this.getApprovalJumpSettingApi, params);
  }
  /**
   * @description: 获取开关配置
   * @param params
   * @returns
   */
  public static getSettingSwitch(params?: any) {
    return http.get(this.getSettingSwitchApi, params);
  }
  /**
   * 获取审批分类级联选择器数据
   * @param params
   * @returns
   */
  public static getProveCategoryCascaderList(params?: any) {
    return http.get(this.getProveCategoryCascaderListApi, params);
  }
  /**
   * 获取组织架构类型
   * @param params
   * @returns
   */
  public static getOrganizationTypes() {
    return http.get(this.getOrganizationTypesApi);
  }
  /**
   * @description: 获取考核/培训分类/AdministrationDictionary数据
   * @param params
   * @return
   */
  public static getSettingDictionaryMaintain(params?: any) {
    return http.get(this.getSettingDictionaryMaintainApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:删除分类/AdministrationDictionary数据
   * @param params
   * @return
   */
  public static deleteSettingDictionaryMaintain(params?: any) {
    return http.post(this.deleteSettingDictionaryMaintainApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存分类/AdministrationDictionary数据
   * @param params
   * @return
   */
  public static saveSettingDictionaryMaintain(params?: any) {
    return http.post(this.saveSettingDictionaryMaintainApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description 根据SettingTypeCode和SettingTypeValue获取配置键值对配置
   * @param params settingTypeCode,settingTypeValues
   * @returns
   */
  public static getSettingDictionaryByCodeValue(params: any) {
    return http.get(this.getSettingDictionaryByCodeValueAPi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description 根据SettingTypeCode和SettingTypeValue获取配置级联数据
   * @param params settingTypeCode,settingTypeValues
   * @returns
   */
  public static getCascaderSettingDictionary(params: any) {
    return http.get(this.getCascaderSettingDictionaryApi, params, { loadingText: Loading.LOAD });
  }
}
