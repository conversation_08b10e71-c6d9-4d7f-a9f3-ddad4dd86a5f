<!--
 * FilePath     : \src\views\scheduling\shiftScheduling.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:42
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-16 15:50
 * Description  : 排班功能
 * CodeIterationRecord:
-->
<template>
  <base-layout class="shift-scheduling" :showFooter="true" footerHeight="auto" headerHeight="auto">
    <template #header>
      <div class="top-where">
        <div class="top-left">
          <operation-instruction v-if="!readonly"> </operation-instruction>
          <el-radio-group class="scheduling-type" v-model="schedulingType" @change="getShiftSchedulingData()">
            <el-radio-button label="月排班" value="1" />
            <el-radio-button label="周排班" value="2" />
          </el-radio-group>
          <template v-if="schedulingType === '1'">
            月份：
            <el-date-picker
              class="year-month"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              v-model="yearMonth"
              @change="getShiftSchedulingData"
              :clearable="false"
            >
            </el-date-picker>
          </template>
          <template v-if="schedulingType === '2'">
            周：
            <el-date-picker
              class="year-month-week"
              type="week"
              format="YYYY年第ww周(MM月)"
              value-format="YYYY-MM-DD"
              v-model="yearWeek"
              @change="getShiftSchedulingData"
              :clearable="false"
            >
            </el-date-picker>
          </template>
          <template v-if="readonly">
            <el-text v-if="!shiftScheduling.shiftSchedulingRecordIDs?.length" type="danger">未排班</el-text>
            <template v-else>
              <!-- 岗位 -->
              <department-post-selector
                label="部门岗位"
                v-model="filterPostID"
                :departmentID="userStore.departmentID"
                @change="filterSchedulingTableData"
              ></department-post-selector>
              <!-- 岗位标记 -->
              岗位标记：
              <el-select class="post-mark" placeholder="请选择岗位标记" v-model="postMarkID" clearable @change="filterSchedulingTableData">
                <el-option v-for="(item, index) in postMarkOptions" :key="index" :label="item.text" :value="item.markID"> </el-option>
              </el-select>
            </template>
          </template>
          <template v-else>
            <el-text v-if="!shiftScheduling.shiftSchedulingRecordIDs?.length" type="danger"> 未排班 </el-text>
            <template v-else>
              <el-text :type="shiftScheduling.statusCode === '0' ? 'warning' : 'success'">
                {{ shiftScheduling.statusCode === "0" ? "已暂存" : shiftScheduling.statusCode === "1" ? "已发布" : "未排班" }}
              </el-text>
              <el-text v-if="dataChangeFlag" type="danger" class="change-flag">(有调整)</el-text>
            </template>
          </template>
        </div>
        <div class="top-right">
          <template v-if="readonly">
            <template v-if="shiftScheduling.shiftSchedulingRecordIDs?.length">
              <el-button v-if="schedulingType !== schedulingTypeWeek" class="query-button" @click="createMonthExportExcelParam">
                排班月统计
              </el-button>
              <div v-if="schedulingType == schedulingTypeWeek">
                <export-excel :exportExcelOption="exportExcelOption">
                  <el-button class="query-button" @click="createMonthExportExcelParam"> 导出月统计 </el-button>
                </export-excel>
              </div>
              <el-tooltip content="打印排班表" placement="top">
                <el-button class="print-button" @click="printSchedulingTable()">打印排班表</el-button>
              </el-tooltip>
            </template>
          </template>
          <template v-else>
            <el-button v-if="schedulingType !== schedulingTypeWeek" class="query-button" @click="showMonthlyStatisticsFlag = true">
              排班月统计
            </el-button>
            <el-button v-permission:B="2" class="edit-button" @click="copyScheduling()">
              {{ `复制上${schedulingTypeText}排班` }}
            </el-button>
            <el-tooltip content="暂存后只有排班人员可以查看" placement="top">
              <el-button v-permission:B="2" class="print-button" @click="saveShiftScheduling('0')">暂存</el-button>
            </el-tooltip>
            <el-tooltip content="发布后本部门所有人员可以查看" placement="top">
              <el-button v-permission:B="2" type="primary" @click="saveShiftScheduling('1')">发布</el-button>
            </el-tooltip>
          </template>
        </div>
      </div>
    </template>
    <scheduling-table
      v-if="shiftSchedulingTable?.rows?.length"
      v-model:data="shiftSchedulingTable"
      :tableParams="{
        readonly,
        month,
        schedulingType,
        shiftSchedulingRecordIDs: shiftScheduling.shiftSchedulingRecordIDs,
        noonList,
        departmentPostList,
        shiftSchedulingMarkList: shiftSchedulingParameter.shiftSchedulingMarkList,
        restrictCapabilityLevel,
        oneScreenDisplay,
        restDayShowStyle,
        noonDutyMarkID: noonDutyMarkID || 0,
        employeeRemainingRestDaysDict: shiftSchedulingParameter.employeeRemainingRestDaysDict,
        dailyStatisticsPostCondition: shiftSchedulingParameter.dailyStatisticsPostCondition,
        monthlyStatisticsPostCondition: shiftSchedulingParameter.monthlyStatisticsPostCondition,
        schedulingTemplateList: shiftSchedulingParameter.schedulingTemplateList,
        shiftSchedulingStatisticsTable,
        restMap: restMap
      }"
      @changeEmployeeSort="changeEmployeeSort"
      @shiftSchedulingChange="shiftSchedulingChange"
    >
    </scheduling-table>
    <template #footer>
      <div class="remark">
        <span class="remark-label">备注：</span>
        <el-input v-if="!readonly" v-model="shiftScheduling.remark" placeholder="请输入备注"></el-input>
        <span v-else>{{ shiftScheduling.remark }}</span>
      </div>
      <scheduling-explanation-info
        :requiredAttendanceDays="shiftScheduling?.requiredAttendanceDays"
        :postDescriptionList="postDescriptionList"
        :restDescriptionList="restDescriptionList"
        :shiftSchedulingMarkList="shiftSchedulingParameter.shiftSchedulingMarkList"
      >
      </scheduling-explanation-info>
    </template>
    <!-- 排班岗位月统计浮动窗口 -->
    <scheduling-monthly-statistics
      v-if="showMonthlyStatisticsFlag"
      v-model="showMonthlyStatisticsFlag"
      :month="month"
      :noonDutyMarkID="noonDutyMarkID"
      :shiftSchedulingTable="shiftSchedulingStatisticsTable"
      :statisticsPostColumns="shiftSchedulingParameter.monthlyStatisticsPostCondition"
      :employeeRemainingRestDaysDict="shiftSchedulingParameter.employeeRemainingRestDaysDict"
      :exportExcelOption="exportExcelOption"
    ></scheduling-monthly-statistics>
    <!-- 排班打印表格 -->
    <scheduling-print-table
      v-if="prinTableData?.prinTableTitles?.length"
      :tableData="prinTableData"
      :holidayList="holidayList"
      @close="prinTableData = {}"
    ></scheduling-print-table>
  </base-layout>
</template>

<script setup lang="ts">
import { useSchedulingControl } from "./hooks/useSchedulingControl";
import { useSchedulingPrint } from "./hooks/useSchedulingPrint";
import { useSchedulingStatistics } from "./hooks/useSchedulingStatistics";
const { userStore } = useStore();
// #region  定义变量
// 休假岗
const restPostType: string = "4";
// 下夜班岗位ID
const nightOffPostID: number = 999;
// 周排班类型
const schedulingTypeWeek: string = "2";

// 排班类型
const schedulingType = ref<string>("1");
const schedulingTypeText = computed(() => (schedulingType.value !== schedulingTypeWeek ? "月" : "周"));
const shiftSchedulingTable = ref<TableView>({} as TableView);
const shiftSchedulingTableClone = ref<TableView>({} as TableView);
const shiftSchedulingParameter = ref<Record<string, any>>({});
const yearMonth = ref(datetimeUtil.getNowDate("yyyy-MM"));
const yearWeek = ref(datetimeUtil.getNowDate("yyyy-MM-dd"));
const month = computed(() => datetimeUtil.formatDate(schedulingType.value !== schedulingTypeWeek ? yearMonth.value : yearWeek.value, "M"));
const monthStartDate = computed(() =>
  datetimeUtil.getMonthFirstDay(schedulingType.value !== schedulingTypeWeek ? `${yearMonth.value}-01` : yearWeek.value)
);
const monthEndDate = computed(() =>
  datetimeUtil.getMonthLastDay(schedulingType.value !== schedulingTypeWeek ? `${yearMonth.value}-01` : yearWeek.value)
);
const restrictCapabilityLevel = ref<boolean>(true);
const oneScreenDisplay = ref<boolean>(true);
const restDayShowStyle = ref<string>("1");
const schedulingEmployeeSortList = ref<Record<string, any>[]>([]);
const shiftScheduling = ref<Record<string, any>>({});
const postDescriptionList = ref<Record<string, string>[]>([]);
const restDescriptionList = ref<string[]>([]);
const showMonthlyStatisticsFlag = ref<boolean>(false);
const shiftSchedulingStatisticsTable = ref<TableView>({} as TableView);
const dataChangeFlag = ref<boolean>(false);
let noonList: Record<string, string>[] = [];
const filterPostID = ref<number>();
const postMarkID = ref<number>();
const noonDutyMarkID = ref<number | undefined>();
const postMarkOptions = ref<Record<string, any>>({});
const { filterTableDatas, printShiftSchedulingTable } = useSchedulingPrint();
const specialColumns: string[] = ["capabilityLevel", "employeeName"];
const route = useRoute();
const readonly = computed(() => route.params.readonly === "true");
// #endregion

// 引入用户无交互监听hooks，开启定时器,5分钟触发一次
const { startWatch, stopWatch } = useUserNoInteraction(
  5 * 60 * 1000,
  async () => dataChangeFlag.value && (await saveShiftScheduling("0", "系统自动保存中……"))
);
watch(
  readonly,
  () => {
    // 只读关闭自动保存
    if (readonly.value) {
      stopWatch();
    } else {
      // 开启无操作自动保存
      startWatch();
    }
  },
  { immediate: true }
);

// #region 初始化数据
// 暴漏给父路由
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    getShiftSchedulingData();
  }
});
let departmentPostList: Record<string, any>[] = [];
let holidayList: Record<string, any>[] = [];
/**
 * @description: 页面挂载前钩子函数
 */
onBeforeMount(async () => {
  // 获取部门岗位清单
  let { getDepartmentPostData } = useDictionaryData();
  await getDepartmentPostData(userStore.departmentID, true, undefined, monthStartDate.value).then((data) => {
    departmentPostList = data;
    holidayList = data.filter((item) => item.type === restPostType);
    dealDepartmentPosts();
  });
  await getNoonList();
  // 获取排班相关参数
  await getShiftSchedulingParameter();
  // 获取排班数据
  await getShiftSchedulingData();
});
/**
 * @description:
 */
onBeforeRouteLeave((to, from, next) => {
  if (!readonly.value && dataChangeFlag.value) {
    confirmBox("您修改的排班尚未保存，确定要离开吗？", "", (flag: boolean) => {
      return next(flag);
    });
  } else {
    return next();
  }
});

/**
 * @description: 部门岗位数据转换处理
 */
const dealDepartmentPosts = () => {
  postDescriptionList.value = [];
  restDescriptionList.value = [];
  departmentPostList.forEach((departmentPost: Record<string, any>) => {
    // 非休假岗说明
    if (departmentPost.type !== restPostType && departmentPost.value !== nightOffPostID) {
      const postIndex = postDescriptionList.value.findIndex(
        (postDescription) => postDescription.workingTimeRange === departmentPost.workingTimeRange
      );
      if (postIndex === -1) {
        postDescriptionList.value.push({ workingTimeRange: departmentPost.workingTimeRange, postName: departmentPost.localLabel });
      } else {
        postDescriptionList.value[postIndex].postName = `${postDescriptionList.value[postIndex].postName}、${departmentPost.localLabel}`;
      }
    }
    if (departmentPost.type === restPostType) {
      // 休假岗说明
      restDescriptionList.value.push(`${departmentPost.localLabel}：${departmentPost.label}`);
    }
  });
};
//#region 排班月统计相关逻辑
/**
 * @description: 导出Excel参数
 */
const exportExcelOption = ref<ExportExcelView[]>([]);
const { getStatisticsMonthlyData } = useSchedulingStatistics();
/**
 * @description: 创建月统计导出Excel参数
 */
const createMonthExportExcelParam = () => {
  exportExcelOption.value = [];
  let exportDatas: Record<string, any>[] = [];
  let columns = createExportColumns();
  let monthlyData = getStatisticsMonthlyData(
    month.value,
    shiftSchedulingStatisticsTable.value,
    shiftSchedulingParameter.value.employeeRemainingRestDaysDict,
    shiftSchedulingParameter.value.monthlyStatisticsPostCondition,
    noonDutyMarkID.value
  );
  monthlyData.forEach((item) => {
    let exportData: { [key: string]: any } = {};
    columns.forEach((column) => {
      if (item[column.key.split("_")[1]] !== undefined && item[column.key.split("_")[1]] !== null) {
        exportData[column.key] = item[column.key.split("_")[1]];
      }
    });
    exportDatas.push(exportData);
  });
  const columnsObj = columns.reduce((accumulator: any, current) => {
    accumulator[current.key] = current.value;
    return accumulator;
  }, {});
  exportExcelOption.value.push({
    buttonName: "导出月排班",
    fileName: `排班岗位月统计（${month.value}月）`,
    sheetName: "排班月统计",
    columnData: columnsObj,
    tableData: exportDatas
  });
  if (schedulingType.value !== schedulingTypeWeek) {
    showMonthlyStatisticsFlag.value = true;
  }
};
/**
 * @description: 创建导出数据列
 */
const createExportColumns = () => {
  let columns: { key: any; value: string }[] = [];
  let i = 0;
  let excelColumns = {
    employeeName: "姓名",
    employeeRemainingRestDays: "可休天数",
    attendanceDays: "出勤",
    noonDutyDays: "中午值班",
    restDays: "休",
    annualRestDays: "年休"
  };
  for (let key in excelColumns) {
    columns.push({
      key: i + "_" + key,
      value: excelColumns[key as keyof typeof excelColumns]
    });
    i++;
  }
  for (let key in shiftSchedulingParameter.value.monthlyStatisticsPostCondition) {
    columns.push({
      key: i + "_" + shiftSchedulingParameter.value.monthlyStatisticsPostCondition[key].conditionValue,
      value: shiftSchedulingParameter.value.monthlyStatisticsPostCondition[key].conditionKey
    });
    i++;
  }
  return columns;
};
//#endregion
/**
 * @description: 获取午别字典
 */
const getNoonList = async () => {
  // 通过hooks从数据库获取数据
  const params: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "JobPositions",
    settingTypeValue: "NoonType",
    index: Math.random()
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    noonList = datas;
  });
};
const changeEmployeeSort = (sortData: Record<string, any>[]) => {
  schedulingEmployeeSortList.value = sortData;
  dataChangeFlag.value = schedulingEmployeeSortList.value.length > 0;
};
/**
 * @description: 复制上个月的排班数据
 */
const copyScheduling = async () => {
  let copyFlag: boolean = true;
  if (shiftScheduling.value.shiftSchedulingRecordIDs?.length) {
    await confirmBox("此操作将覆盖现有排班数据，是否继续？", "系统提示", (flag: boolean) => {
      if (!flag) {
        copyFlag = false;
      }
    });
  }
  if (copyFlag) {
    const date = getStartAndEndDate();
    let params: Record<string, any> = {
      departmentID: userStore.departmentID,
      schedulingType: schedulingType.value,
      startDate: date.startDate,
      endDate: date.endDate
    };
    schedulingService.copyScheduling(params).then((res: any) => {
      if (res) {
        getShiftSchedulingData();
      }
    });
  }
};

/**
 * @description: 保存排班
 * @param statusCode 保存类型，0：暂存，1：保存并发布
 * @param loadingContent 是否显示loading
 */
const saveShiftScheduling = async (statusCode: string, loadingContent?: string) => {
  // 如果后台线程正在运行，则先停止
  if (!loadingContent && showMonthlyStatisticsFlag.value) {
    showMonthlyStatisticsFlag.value = false;
  }
  // 获取排班明细数据
  const shiftSchedulingDetails: Record<string, any>[] = getShiftSchedulingDetails();
  if (shiftSchedulingDetails?.length === 0) {
    !loadingContent && showMessage("error", "没有要保存的排班数据！");
    return;
  }
  const date = getStartAndEndDate();
  let shiftSchedulingData: Record<string, any> = {
    shiftSchedulingRecordIDs: shiftScheduling.value.shiftSchedulingRecordIDs,
    remark: shiftScheduling.value.remark,
    departmentID: userStore.departmentID,
    schedulingType: schedulingType.value,
    startDate: date.startDate,
    endDate: date.endDate,
    autoFlag: false,
    statusCode: statusCode,
    shiftSchedulingDetails: shiftSchedulingDetails
  };
  if (schedulingEmployeeSortList.value) {
    shiftSchedulingData.schedulingEmployeeSortList = schedulingEmployeeSortList.value;
  }
  await schedulingService.saveShiftSchedulingData(shiftSchedulingData, loadingContent).then(async (res: any) => {
    if (res) {
      dataChangeFlag.value = false;
      await getShiftSchedulingData();
    }
  });
};
const getStartAndEndDate = () => {
  let result: Record<string, any> = {};
  if (schedulingType.value !== schedulingTypeWeek) {
    result = {
      startDate: datetimeUtil.getMonthFirstDay(`${yearMonth.value}-01`),
      endDate: datetimeUtil.getMonthLastDay(`${yearMonth.value}-01`)
    };
  } else {
    result = {
      startDate: datetimeUtil.getWeekStartDate(yearWeek.value),
      endDate: datetimeUtil.getWeekEndDate(yearWeek.value)
    };
  }
  let startMonth = datetimeUtil.formatDate(result.startDate, "MM");
  let endMonth = datetimeUtil.formatDate(result.endDate, "MM");
  result.isTransMoon = startMonth !== endMonth;
  return result;
};
/**
 * @description: 获取排班明细数据
 */
const getShiftSchedulingDetails = () => {
  const { getEmployeeSecondmentNoon, canScheduling } = useSchedulingControl(shiftSchedulingTable.value);
  let shiftSchedulingDetails: Record<string, any>[] = [];
  // 处理当前日期有半天借调或被借调情况
  shiftSchedulingTable.value.rows.forEach((row, rowIndex) => {
    const columnKeys = Object.keys(row);
    let transferInColumnIndex = -1;
    let transferOutColumnIndex = -1;
    // 找到人员转入科日期列序号
    if (row.employee.transferInDate) {
      transferInColumnIndex = shiftSchedulingTable.value.columns.findIndex(
        (column) => column.key === datetimeUtil.formatDate(row.employee.transferInDate, "yyyyMMdd")
      );
    }
    // 找到人员转出科日期列序号
    if (row.employee.transferOutDate) {
      transferOutColumnIndex = shiftSchedulingTable.value.columns.findIndex(
        (column) => column.key === datetimeUtil.formatDate(row.employee.transferOutDate, "yyyyMMdd")
      );
    }
    columnKeys.forEach((columnKey) => {
      const column = shiftSchedulingTable.value.columns.find((column) => column.key === columnKey);
      const columnIndex: number = column ? column.index : 0;
      // 转科前的数据直接跳过
      if (transferInColumnIndex !== -1 && columnIndex < transferInColumnIndex) {
        return;
      }
      // 转科后的数据直接跳过
      if (transferOutColumnIndex !== -1 && columnIndex >= transferOutColumnIndex) {
        return;
      }
      const hasPost = Reflect.ownKeys(row[columnKey]?.noonPost || {}).length;
      const hasMark = row[columnKey]?.markList?.length;
      if (hasPost || hasMark) {
        let employeeSecondmentNoon = getEmployeeSecondmentNoon(rowIndex, columnIndex);
        if (hasPost) {
          const noonPostIDs = Object.keys(row[columnKey]?.noonPost);
          let newNoonPost: Record<string, any> = {};
          // 过滤掉借调到本部门人员在原部门的排班
          // 过滤掉本部门借调出去的人员在借调部门的排班
          if (row.employee?.secondmentList?.length || row.employee?.secondedList?.length) {
            noonPostIDs.forEach((noonPostID) => {
              if (canScheduling(employeeSecondmentNoon, noonPostID)) {
                newNoonPost[noonPostID] = row[columnKey]?.noonPost[noonPostID];
              }
            });
            // 如果过滤完还有值 或 无岗位有备注的情况，重新赋值
            if (Reflect.ownKeys(newNoonPost).length || hasMark) {
              // 克隆一个新对象，否则页面会卡（不知道具体原因）
              let shiftSchedulingDetail = common.clone(row[columnKey]);
              shiftSchedulingDetail.noonPost = newNoonPost;
              shiftSchedulingDetails.push(shiftSchedulingDetail);
            }
          } else {
            // 正常人员
            shiftSchedulingDetails.push(row[columnKey]);
          }
        } else if (hasMark) {
          row[columnKey].employeeID = row.employee?.employeeID;
          row[columnKey].schedulingDate = shiftSchedulingTable.value.columns[columnIndex]?.value;
          shiftSchedulingDetails.push(row[columnKey]);
        }
      }
    });
  });
  return shiftSchedulingDetails;
};
/**
 * @description: 筛选排班数据
 */
const filterSchedulingTableData = () => {
  // 根据排班类型获取开始和结束日期
  const date = getStartAndEndDate();
  const filterStartDate = Number(datetimeUtil.formatDate(date.startDate, "yyyyMMdd"));
  const filterEndDate = Number(datetimeUtil.formatDate(date.endDate, "yyyyMMdd"));
  if (monthStartDate.value === date.startDate && monthEndDate.value === date.endDate && !filterPostID.value && !postMarkID.value) {
    shiftSchedulingTable.value = shiftSchedulingTableClone.value;
    return;
  }
  let datas = ref<TableView>({ columns: [], rows: [] });
  // 筛选列
  datas.value.columns = shiftSchedulingTableClone.value.columns.filter(
    // 保留人员列、月统计列 和符合条件的日期列
    (column) => specialColumns.includes(column.key) || (Number(column.key) >= filterStartDate && Number(column.key) <= filterEndDate)
  );
  // 重置列的index，避免排班逻辑混乱
  datas.value.columns.forEach((column, index) => {
    if (!specialColumns.includes(column.key)) {
      column.index = index;
      if (column.childColumns?.length) {
        column.childColumns[0].index = index;
      }
    }
  });
  // 筛选行
  shiftSchedulingTableClone.value.rows.forEach((row) => {
    let newRow: Record<string, any> = {};
    Object.keys(row).forEach((key) => {
      if (Number(key) >= filterStartDate && Number(key) <= filterEndDate) {
        newRow[key] = row[key];
      }
    });
    if (filterPostID.value) {
      newRow = filterTableDatas(newRow, row, "noonPost", filterPostID.value, "departmentPostID");
    }
    if (postMarkID.value) {
      newRow = filterTableDatas(newRow, row, "markList", postMarkID.value, "markID");
    }
    if (Reflect.ownKeys(newRow).length) {
      newRow["capabilityLevel"] = row["capabilityLevel"];
      newRow["employeeName"] = row["employeeName"];
      newRow["employee"] = row["employee"];
      datas.value.rows.push(newRow);
    }
  });
  // 表格赋值
  shiftSchedulingTable.value = datas.value;
  shiftSchedulingChange();
};

/**
 * @description: 获取排班相关参数
 */
const getShiftSchedulingParameter = async () => {
  const params = {
    departmentID: userStore.departmentID,
    startDate: monthStartDate.value,
    endDate: monthEndDate.value
  };
  await schedulingService.getShiftSchedulingParameter(params).then((data: any) => {
    shiftSchedulingParameter.value = data;
    postMarkOptions.value = shiftSchedulingParameter.value.shiftSchedulingMarkList;
    // 排班类型
    schedulingType.value = shiftSchedulingParameter.value.shiftSchedulingRuleDict["1"] || "1";
    // 层级限制
    restrictCapabilityLevel.value = shiftSchedulingParameter.value.shiftSchedulingRuleDict["2"] === "true";
    // 一屏显示
    oneScreenDisplay.value = shiftSchedulingParameter.value.shiftSchedulingRuleDict["3"] === "true";
    // 休假显示方式，1平铺，2鼠标悬浮，不显示
    restDayShowStyle.value = shiftSchedulingParameter.value.shiftSchedulingRuleDict["4"] || "1";
    noonDutyMarkID.value = postMarkOptions.value?.find((mark: Record<string, any>) => mark.text === "中午值班")?.markID;
  });
};

/**
 * @description: 获取排班数据
 * @return
 */
const getShiftSchedulingData = async () => {
  shiftSchedulingTable.value = { rows: [], columns: [] };
  shiftSchedulingTableClone.value = { rows: [], columns: [] };
  shiftScheduling.value = {};
  schedulingEmployeeSortList.value = [];
  const date = getStartAndEndDate();
  const params = {
    departmentID: userStore.departmentID,
    schedulingType: schedulingType.value,
    startDate: date.startDate,
    endDate: date.endDate,
    statusCode: readonly.value ? "1" : undefined
  };
  await schedulingService.getShiftSchedulingData(params).then((data: any) => {
    if (data) {
      if (data.shiftSchedulingTable) {
        shiftSchedulingTableClone.value = data.shiftSchedulingTable;
        dataChangeFlag.value = false;
        // 周排班
        if (schedulingType.value === schedulingTypeWeek) {
          filterSchedulingTableData();
        } else {
          shiftSchedulingTable.value = data.shiftSchedulingTable;
          shiftSchedulingChange();
        }
        delete data.shiftSchedulingTable;
      }
      shiftScheduling.value = data;
      // 如果是新增，初始排班人员排序
      if (!shiftScheduling.value.shiftSchedulingRecordIDs?.length) {
        // 组装顺序
        schedulingEmployeeSortList.value = [];
        shiftSchedulingTable.value.rows.forEach((row, index) => {
          schedulingEmployeeSortList.value.push({
            employeeID: row.employee.employeeID,
            sort: index + 1
          });
        });
      }
    }
  });
};
// #endregion
const shiftSchedulingChange = (callBack?: Function) => {
  callBack && (dataChangeFlag.value = true);
  if (schedulingType.value === schedulingTypeWeek) {
    // 为了计算月统计，将周数据替换后传入统计组件
    shiftSchedulingTable.value.columns.forEach((column) => {
      // 排除 人员列、月统计列
      if (!specialColumns.includes(column.key)) {
        shiftSchedulingTable.value.rows.forEach((row, index) => {
          shiftSchedulingTableClone.value.rows[index][column.key] = row[column.key];
        });
      }
    });
    shiftSchedulingStatisticsTable.value = shiftSchedulingTableClone.value;
  } else {
    shiftSchedulingStatisticsTable.value = shiftSchedulingTable.value;
  }
  // 显示休假天数时才实时计算
  if (restDayShowStyle.value !== "3") {
    setRestMap();
  }
  callBack?.();
};
const restMap = ref<Map<string, Map<string, Record<string, number>>>>(new Map());
// 产假岗位ID
const maternityPostID: number = 184;
// 年休假岗位ID
const annualPostID: number = 186;
/**
 * @description: 实时统计排班休假
 */
const setRestMap = () => {
  restMap.value = new Map();
  let date = getStartAndEndDate();
  let month = datetimeUtil.formatDate(date.endDate, "MM");
  shiftSchedulingStatisticsTable.value.rows.forEach((row) => {
    const employeeID = row.employee.employeeID;
    let count = 0;
    let maternityCount = row.employee.maternityLeaveDays || 0;
    let annualCount = row.employee.annualLeaveDays || 0;
    let employeeRestMap: Map<string, Record<string, number>> = new Map();
    let isTransMoonFlag = false;
    shiftSchedulingStatisticsTable.value.columns.forEach((column, index) => {
      if (index >= specialColumns.length) {
        // 如果是周排班存在跨月，第二个月重新计算
        if (date.isTransMoon && !isTransMoonFlag && month === datetimeUtil.formatDate(column.value, "MM")) {
          count = 0;
          isTransMoonFlag = true;
        }
        let cellData = row[column.key];
        if (cellData?.noonPost) {
          let noonPosts: Record<string, any>[] = Object.values(cellData.noonPost);
          const restNoons = noonPosts.filter((noonPost) => noonPost.postType === restPostType);
          if (restNoons?.length) {
            count += 0.5 * restNoons.length;
            const maternityPosts = restNoons.filter((noonPost) => noonPost.departmentPostID === maternityPostID);
            // 当前列休产假天数
            let tempMaternityCount: number = 0;
            if (maternityPosts?.length) {
              maternityCount += 0.5 * maternityPosts?.length;
              tempMaternityCount = maternityCount;
            }
            // 当前列休年休假天数
            let tempAnnualCount: number = 0;
            const annualPosts = restNoons.filter((noonPost) => noonPost.departmentPostID === annualPostID);
            if (annualPosts?.length) {
              annualCount += 0.5 * annualPosts?.length;
              tempAnnualCount = annualCount;
            }
            employeeRestMap.set(column.key, { count, maternityCount: tempMaternityCount, annualCount: tempAnnualCount });
          }
        }
      }
    });
    if (employeeRestMap.size && !restMap.value.has(employeeID)) {
      restMap.value.set(employeeID, employeeRestMap);
    }
  });
};

let prinTableData = ref<Record<string, any>>({});
/**
 * @description: 打印排班表
 */
const printSchedulingTable = () => {
  prinTableData.value = printShiftSchedulingTable(shiftSchedulingTable.value, restMap.value, restPostType);
  let title = "";
  if (schedulingType.value === schedulingTypeWeek) {
    const date = getStartAndEndDate();
    title = `${date.startDate}至${date.endDate}排班表`;
  } else {
    title = `${yearMonth.value}月排班表`;
  }
  prinTableData.value.title = title;
};
</script>

<style lang="scss">
.shift-scheduling {
  height: 100%;
  width: 100%;
  .top-where {
    display: flex;
    justify-content: space-between;
    .top-left,
    .top-right {
      display: flex;
      align-items: center;
    }
    .scheduling-type {
      margin: 0 15px 0 10px;
    }
    .year-month {
      width: 200px !important;
      margin-right: 15px;
    }
    .year-month-week {
      width: 270px !important;
      margin-right: 15px;
    }
    .year-month-day {
      width: 200px !important;
    }
    .post-mark {
      width: 120px !important;
    }
    .el-text {
      font-weight: bold;
    }
    .show-day-summary {
      display: inline-block;
      margin-left: 15px;
    }
  }
  .remark {
    display: flex;
    align-items: center;
    margin: 8px 0 4px 0;
    padding: 2px 20px;
    background-color: #fbe0d7;
    .remark-label {
      word-break: keep-all;
    }
    .el-input {
      flex: auto;
      width: 100%;
      .el-input__wrapper {
        width: 100%;
      }
    }
  }
}
</style>
