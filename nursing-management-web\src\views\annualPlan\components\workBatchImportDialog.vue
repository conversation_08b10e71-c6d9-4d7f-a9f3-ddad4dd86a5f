<!--
 * FilePath     : /src/views/annualPlan/components/workBatchImportDialog.vue
 * Author       : 杨欣欣
 * Date         : 2025-06-27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 16:10
 * Description  : 批量导入工作弹窗组件
 * CodeIterationRecord:
 -->
<template>
  <div class="work-batch-import-dialog">
    <el-dialog v-model="visible" title="批量导入工作">
      <work-batch-import v-model="targetData" :sourceData :departmentID />
      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { planWorkImportVo } from "../types/planWorkImportVo";
import type { importWorkDto } from "../types/importWorkDto";
import type { planAndWorksVo } from "../types/planAndWorksVo";

type typeGroup = planWorkImportVo["children"][number];

const { departmentID, rightPanelTemplate, leftPanelData } = defineProps<{
  departmentID: number;
  leftPanelData: planWorkImportVo[];
  rightPanelTemplate: planAndWorksVo[] | undefined;
}>();
// eslint-disable-next-line no-spaced-func
const emit = defineEmits<{
  (e: "confirm", works: importWorkDto[]): void;
  (e: "cancel"): void;
}>();
const targetData = ref<typeGroup[]>([]);
const baseTargetData = {
  children: [],
  typeName: "未分类工作",
  typeID: 0
};

const visible = defineModel({
  required: true
});
const sourceData = ref<planWorkImportVo[]>([]);
/**
 * 初始化弹窗数据
 */
const init = async () => {
  sourceData.value = leftPanelData;
  if (rightPanelTemplate?.length) {
    targetData.value = rightPanelTemplate.map((type) => ({
      ...type,
      children: []
    }));
  } else {
    const typeGroupsByCurrentDepartment =
      sourceData.value
        .find((plan) => plan.departmentID === departmentID)
        ?.children?.map((type) => ({
          ...type,
          children: []
        })) ?? [];
    targetData.value = [...typeGroupsByCurrentDepartment, common.clone(baseTargetData)];
  }
};

whenever(visible, init, { immediate: true });

/**
 * 处理确认操作
 */
const handleConfirm = async () => {
  const hasUnClassifyWork = targetData.value?.find((byType) => byType.typeID === 0)?.children?.length;
  if (hasUnClassifyWork) {
    showMessage("error", "请先分类未分类的工作");
    return "";
  }
  const works = targetData.value?.flatMap((byType) => {
    let sort = 1;
    return byType.children.map((work) => {
      return {
        typeID: byType.typeID,
        apInterventionID: work.apInterventionID,
        workContent: work.workContent,
        requirement: work.requirement,
        isTemp: work.isTemp,
        planMonths: work.planMonths,
        principalName: work.principalName,
        principals: work.principals,
        sort: sort++
      };
    });
  });
  emit("confirm", works);
};

/**
 * 处理取消操作
 */
const handleCancel = () => {
  visible.value = false;
};
</script>

<style lang="scss" scoped>
.work-batch-import-dialog {
  :deep(.el-dialog__body) {
    background-color: #f3f3f3;
    padding: 0;
  }
  :deep(.el-dialog__footer) {
    padding-top: 8px;
  }
}
</style>
