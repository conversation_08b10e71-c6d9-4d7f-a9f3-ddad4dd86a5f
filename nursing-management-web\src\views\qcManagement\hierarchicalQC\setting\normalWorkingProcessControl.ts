/*
 * relative     : \src\views\qcManagement\hierarchicalQC\setting\normalWorkingProcessControl.ts
 * Author       : 郭鹏超
 * Date         : 2024-10-31 08:35
 * LastEditors  : 孟昭永
 * LastEditTime : 2025-01-11 09:22
 * Description  : 常态工作控制差异配置
 * CodeIterationRecord:
 */
let { userStore } = useStore();
class normalWorkingProcessControlOption {
  qcLevel: string;
  departmentID: number;
  formType: string | undefined;
  formDisabled: boolean = false;
  qcType: "normalWorkingFormType" | undefined;
  restrictExamineDateFlag: boolean = false;
  constructor(props: Record<string, any>) {
    this.qcType = "normalWorkingFormType";
    this.qcLevel = props.routerQcLevel ?? props?.qcLevel;
    this.departmentID = props?.departmentID ?? userStore.departmentID;
    this.formType = props?.formType;
    this.getAddRecordRestrictDateSetting();
  }
  /**
   * @description: 质控时间范围获取
   */
  getAddRecordRestrictDateSetting() {
    let params: SettingDictionaryParams = {
      settingType: "HierarchicalQC",
      settingTypeCode: "SystemSwitch",
      settingTypeValue: `AddRecordRestrictDate_${this.qcLevel}`,
      index: Math.random()
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      this.restrictExamineDateFlag = Boolean(respBool);
    });
  }
}

export default normalWorkingProcessControlOption;
