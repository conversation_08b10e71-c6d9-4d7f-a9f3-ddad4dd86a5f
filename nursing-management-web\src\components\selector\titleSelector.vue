<!--
 * FilePath     : \src\components\selector\titleSelector.vue
 * Author       : 孟昭永
 * Date         : 2023-08-13 11:55
 * LastEditors  : 来江禹
 * LastEditTime : 2024-08-15 15:15
 * Description  : 职称选择器
 * CodeIterationRecord:
-->
<template>
  <div class="title-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="positionIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="(item, index) in positionOptions" :key="index" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "职称"
  },
  modelValue: {
    type: [String, Array<string>]
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 240
  },
  type: {
    type: String,
    default: "Title_Nursing"
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let positionIDs = useVModel(props, "modelValue", emits);

let positionOptions = ref<Array<Record<any, any>>>([]);
// 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
let { list } = toRefs(props);
if (list?.value) {
  positionOptions.value = list.value;
} else {
  const params: SettingDictionaryParams = {
    settingType: "EmployeeManagement",
    settingTypeCode: "EmployeeProfessionalPosition",
    settingTypeValue: "ProfessionalTypeCode"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    positionOptions.value = datas;
  });
}
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, positionOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.title-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
