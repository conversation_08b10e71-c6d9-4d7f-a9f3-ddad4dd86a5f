/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\onJobInformation\hooks\useSelectDict.ts
 * Author       : 张现忠
 * Date         : 2023-09-20 15:42
 * LastEditors  : 来江禹
 * LastEditTime : 2024-08-15 16:09
 * Description  :
 * CodeIterationRecord:
 */

/**
 * @description:奖惩级别和将成类型字典数据
 * @return
 */
export function useSelectDict() {
  return {
    /**
     * @description: 奖惩级别
     * @return
     */
    async getRewardLevels() {
      const params: SettingDictionaryParams = {
        settingType: "EmployeeManagement",
        settingTypeCode: "EmployeeReward",
        settingTypeValue: "RewardLevelCode"
      };
      let rewardLevelDict: Record<any, any>[] = [];
      await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
        if (datas.length > 0) {
          rewardLevelDict = datas;
          return rewardLevelDict;
        }
        rewardLevelDict = [
          { label: "国家级", value: "1" },
          { label: "省部级", value: "2" },
          { label: "市级", value: "3" },
          { label: "县区级", value: "4" },
          { label: "本单位", value: "5" }
        ];
      });
      return rewardLevelDict;
    },
    /**
     * @description: 奖惩类别
     * @return
     */
    async getRewardTypes() {
      // 需要换参数
      const param: SettingDictionaryParams = {
        settingType: "EmployeeManagement",
        settingTypeCode: "EmployeeReward",
        settingTypeValue: "RewardTypeCode"
      };
      let rewardTypeDict: Record<any, any>[] = [];
      await settingDictionaryService.getSettingDictionaryDict(param).then((datas: any) => {
        if (datas.length > 0) {
          rewardTypeDict = datas;
          return rewardTypeDict;
        }
      });
      return rewardTypeDict;
    }
  };
}
