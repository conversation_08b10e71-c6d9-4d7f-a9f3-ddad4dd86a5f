/*
 * FilePath     : \nursing-management-web\src\views\examineManagement\examineQuestion\hooks\useImportQuestions.ts
 * Author       : 张现忠
 * Date         : 2024-06-08 11:16
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-26 17:35
 * Description  : 导入题目hooks
 * CodeIterationRecord:
 */
import { utils } from "xlsx";
import { useExcel } from "@/hooks/useExcel";
import { useQuestionImportExportOption } from "./useQuestionImportExportOption";
import { type Ref } from "vue";
export default function useImportQuestions(tagDict: Ref<Record<string, any>>) {
  const { fixPrecisionLoss, readExcelFile } = useExcel();
  const { setFileOption, setTheoreticalExportTagTableData } = useQuestionImportExportOption();
  /**
   * description: file组件参数
   */
  const fileOption = reactive<FilePropsView>({
    typeArr: ["exportExcel", "importExcel"]
  });
  // #region 解析excel文件并转换成tree结构的数据
  const columnDict = ref<Record<string, any>[]>([]);
  /**
   * description: 表格数据返回数组对象
   * param {*} ws
   * param {*} rowIndex
   * return {*}
   */
  const dealExcel = (ws: any, rowIndex: number): Record<string, any>[] | Record<string, any> | undefined => {
    let dataArray: { [key: string]: any }[] = [];
    if (!Array.isArray(ws)) {
      return setItem(ws, rowIndex);
    }
    ws.forEach((sourceObj: { [key: string]: any }) => {
      let item = setItem(sourceObj, rowIndex);
      // 过滤掉空数据
      if (item) {
        dataArray = [...dataArray, item];
      }
    });
    return dataArray;
  };
  /**
   * @description: 根据excel一行中的数据转换成程序中需要的对象（参数名称从中文变成英文）
   * @param sourceObj 源对象
   * @param rowIndex 行号
   * @returns { Object} 构造完成的对象
   */
  const setItem = (sourceObj: any, rowIndex: number) => {
    const keys = Object.keys(sourceObj);
    const item: { [key: string]: any } = { rowIndex: rowIndex };
    let hasSetProperty = false;
    columnDict.value.forEach((arr) => {
      if (keys.includes(arr[1])) {
        let value = sourceObj[arr[1]];
        if (value || value === 0 || value === "0") {
          // 导入的数据只有日期时间类型的才是object，特殊处理
          if (typeof value === "object") {
            value = fixPrecisionLoss(value);
          }
          hasSetProperty = true;
          item[arr[0]] = typeof value === "string" ? value.trim() : value;
        }
      }
    });
    return hasSetProperty ? item : undefined;
  };
  /**
   * @description: 解析excel文件
   * @param file 解析的excel文件对象
   * @param sheetName 解析的sheet名称
   * @param fixedColumnKey 固定列第一个字段名称（从左到右）
   * @return {ImportExcelReturnView}
   */
  const parseExcel = async (file: File, sheetName: string | undefined, fixedColumnKey?: string): Promise<ImportExcelReturnView> => {
    const workbook = await readExcelFile(file);
    const worksheet = (sheetName && workbook.Sheets[sheetName]) || workbook.Sheets[workbook.SheetNames[0]];
    if (!worksheet) {
      throw new Error(`无法找到${sheetName}表格数据`);
    }
    const rawData: { [key: string]: any }[] = utils.sheet_to_json(worksheet, { header: 1 });
    // 查找固定列起始位置
    const fixedStartIndex = fixedColumnKey ? rawData[0].findIndex((col: string) => col === fixedColumnKey) : -1;
    if (fixedStartIndex === -1) {
      return {};
    }
    // 分离层级列和固定列
    const hierarchyColumns = rawData[0].slice(0, fixedStartIndex);
    const fixedColumns = rawData[0].slice(fixedStartIndex);
    const noHierarchyQuestions: Record<string, any>[] = [];
    // 构建树结构
    const tree: ParsedExcelNode[] = [];
    const nodeMap = new Map<string, ParsedExcelNode>();
    // 处理除了第一行的其他行数据
    rawData.slice(1).forEach((row, rowIndex: number) => {
      const currExcelQuestionRow = rowIndex + 2;
      // 层级列数据
      const hierarchyValues: { index: number; value: string }[] = row
        .slice(0, fixedStartIndex)
        .map((value: string, index: Number) => ({ value, index }))
        .filter((item: Record<string, any>) => item.value !== undefined && item.value !== null && item.value !== "");
      // 固定列数据
      const fixedData: Record<string, any> = fixedColumns.reduce((acc: Record<string, any>, col: string, i: number) => {
        acc[col] = row[fixedStartIndex + i];
        return acc;
      }, {});
      if (Object.keys(fixedData).length <= 0) {
        return;
      }
      // 无题库层级版本
      if (hierarchyValues.length === 0) {
        let convertedData = dealExcel(fixedData, currExcelQuestionRow);
        convertedData && noHierarchyQuestions.push(convertedData);
        return;
      }
      // 存在题库层级版本-构建题库层级树
      let currentNode: ParsedExcelNode | undefined = undefined;
      hierarchyValues.forEach(({ index, value }) => {
        const hierarchyID = hierarchyValues
          .filter((v) => v.index <= index)
          .map((v) => v.value)
          .join("-");

        if (!nodeMap.has(hierarchyID)) {
          // 构建 题库节点
          const node: ParsedExcelNode = {
            id: hierarchyID,
            name: value,
            level: index + 1,
            children: [],
            questions: []
          };
          // 设置父子关系（父子题库）
          if (currentNode) {
            currentNode.children.push(node);
          } else {
            tree.push(node);
          }

          nodeMap.set(hierarchyID, node);
          currentNode = node;
        } else {
          currentNode = nodeMap.get(hierarchyID);
        }
      });
      // 将题目对题库对应起来{questionBankID:string,question:[]}
      if (currentNode && Object.keys(fixedData).length > 0) {
        let convertedData = dealExcel(fixedData, currExcelQuestionRow);
        convertedData && (currentNode as ParsedExcelNode).questions.push(convertedData);
      }
    });

    return {
      tree,
      hierarchyColumns,
      fixedColumns,
      noHierarchyQuestions
    };
  };
  // #endregion

  // #region 根据excel解析出来的数据，创建题库和题目
  /**
   * @description: 创建题目选项对象
   */
  const createQuestionDetail = (row: Record<string, any>): Record<string, any> => ({
    content: row.content,
    selectFlag: Boolean(row.selectFlag && row.selectFlag !== "0")
  });
  /**
   * @description: 创建问题对象
   * @param row 题目数据
   * @returns
   */
  const createQuestion = (row: Record<string, any>): Record<string, any> => {
    return {
      questionContent: row.questionContent,
      examinationQuestionType: row.examinationQuestionType,
      difficultyLevel: row.difficultyLevel,
      questionTag: row.questionTag,
      instructions: row.instructions,
      filterWeight: row.filterWeight,
      analysis: row.analysis,
      questionDetail: [createQuestionDetail(row)]
    };
  };
  /**
   * @description: 判断是否是下一题
   * @param row excel解析出来的行数据
   * @returns
   */
  const isNextQuestion = (row: any): boolean => {
    const requiredFields = [
      "questionContent",
      "examinationQuestionType",
      "difficultyLevel",
      "questionTag",
      "instructions",
      "filterWeight",
      "analysis"
    ];
    return requiredFields.some((field) => row[field]);
  };
  /**
   * @description: 创建理论试题
   * @param questions excel解析出来的题目数据 node.questions
   * @returns
   */
  const createTheoreticalQuestion = (questions: Record<string, any>) => {
    return questions.reduce((accumulator: any, questionData: any) => {
      if (!isNextQuestion(questionData)) {
        // 如果不是新题目且存在内容，将详情添加到最后一个题目中
        if (questionData.content) {
          if (!accumulator[accumulator.length - 1]) {
            showMessage("warning", `行号:${questionData.rowIndex}处异常，请检查题库名称设置。`);
            throw new Error(`行号:${questionData.rowIndex}处异常，请检查题库名称设置。`);
          }
          accumulator[accumulator.length - 1].questionDetail.push(createQuestionDetail(questionData));
        }
      } else {
        checkQuestionData(questionData);
        // 检查题目是否有设置正确答案
        if (accumulator.length > 0) {
          let lastQuestionDetail = accumulator[accumulator.length - 1].questionDetail;
          const hasSelectFlagTrue = lastQuestionDetail.some((detailItem: Record<string, any>) => detailItem.selectFlag === true);
          if (!hasSelectFlagTrue) {
            showMessage("warning", `行号:${questionData.rowIndex}处异常，请检查上一道题的正确答案选项设置,或者答案列名是否正确。`);
            throw new Error(`行号:${questionData.rowIndex}处异常，请检查上一道题的正确答案选项设置,或者答案列名是否正确。`);
          }
        }
        // 如果是新题目，创建新的题目对象
        accumulator.push(createQuestion(questionData));
      }
      return accumulator;
    }, []);
  };
  /**
   * @description: 校验题目数据必要信息是否存在
   * @param questionData 题目数据
   * @returns
   * @throws Error 异常终止导入，并提示用户调整
   */
  const checkQuestionData = (questionData: any) => {
    const requiredFields = [
      { field: "questionContent", label: "题目名称" },
      { field: "examinationQuestionType", label: "题目类型" },
      { field: "difficultyLevel", label: "题目难度" }
    ];
    for (const { field, label } of requiredFields) {
      if (!questionData[field]) {
        showMessage("warning", `行号:${questionData.rowIndex}处异常，${label}不能为空或其他，请检查调整`);
        throw new Error(`行号:${questionData.rowIndex}处异常，${label}不能为空或其他，请检查调整`);
      }
    }
  };
  /**
   * @description: 创建实操类试题
   * @param node excel解析出来的节点数据
   * @returns
   */
  const createPracticalQuestion = (node: ParsedExcelNode) => {
    let questions: Record<string, any>[] = [];
    node.questions.forEach((questionData) => {
      questions.push({
        ...questionData,
        examinationQuestionType: "Scoring"
      });
    });

    return questions;
  };

  // #endregion

  /**
   * @description: 解析导入的excel题库树结构成题库和题目
   * @param tree excel题库解析后的树形结构数据
   * @param parentId
   * @param questionType 题目类型( 默认评分题，实操类试卷题目导入时,才会解析成tree类型的数据结构)
   * @returns
   */
  const parseQuestionTree = (node: ParsedExcelNode, parentId?: string, isPractical: boolean = true) => {
    const tips: string[] = [];
    let currentNode: Record<string, any> = {
      id: node.id,
      content: node.name,
      level: node.level,
      parentId: parentId,
      children: [],
      questions: []
    };
    // 处理题目
    if (node.questions && node.questions.length) {
      if (isPractical) {
        // 检核实操类题目是否导入理论题库 （实操类中不需要设置题目类型，默认为Scoring评分题）
        if (node.questions[0].examinationQuestionType) {
          showMessage("warning", "实操类题目不能导入理论题库！！");
          throw new Error("实操类题库不能导入理论题目");
        }
        currentNode.questions = createPracticalQuestion(node);
      } else {
        currentNode.questions = createTheoreticalQuestion(node.questions);
      }
    }
    // 递归处理子节点
    if (node.children && node.children.length) {
      node.children.forEach((child) => {
        const { node: childNode, tips: childTips } = parseQuestionTree(child, node.id, isPractical);
        currentNode.children.push(childNode);
        tips.push(...childTips);
      });
    }
    if ((!node.children || !node.children.length) && !(node.questions && node.questions.length)) {
      tips.push(`新增题库【<span style="color: red;">${node.name}</span>】下没有题目。`);
    }
    return {
      node: currentNode,
      tips
    };
  };
  // 初始化导入导出option配置
  setFileOption(fileOption, "theoretical", tagDict.value, parseExcel);
  fileOption.importExcelOption && (columnDict.value = Object.entries(fileOption.importExcelOption.columnData));

  return {
    /**
     * @description: Excel导入导出题库组件参数
     */
    fileOption,
    /**
     * @description: 导入题目
     * @param {any} params 题目数据
     * @return {Promise<boolean>} 返回导入结果
     */
    importQuestions: (params: any) => {
      return new Promise((resolve) => {
        examineService.importQuestions(params).then((res: any) => {
          resolve(Boolean(res));
        });
      });
    },
    /**
     * @description: 切换file组件的props参数（根据试卷类型切换）
     * @param isPractical 是否是实操类试卷题目导入
     */
    toggleExcelFileOptionByType: (isPractical: boolean) => {
      setFileOption(fileOption, isPractical ? "practical" : "theoretical", tagDict.value, parseExcel);
      fileOption.importExcelOption && (columnDict.value = Object.entries(fileOption.importExcelOption.columnData));
    },
    /**
     * @description 转换导出的数据为保存参数
     * @param parsedExcelDataView 处理后的excel数据
     * @returns {Record<string,any>}
     */
    transferJsonToSaveParams: (
      parsedExcelDataView: ImportExcelReturnView,
      isPractical: boolean = true
    ): Record<string, any> | undefined => {
      if ((!parsedExcelDataView.tree || !parsedExcelDataView.tree.length) && !parsedExcelDataView.noHierarchyQuestions?.length) {
        return undefined;
      }
      // 无题库层级的题目
      let noHierarchyQuestions: Record<string, any>[] = [];
      if (parsedExcelDataView.noHierarchyQuestions?.length) {
        noHierarchyQuestions = createTheoreticalQuestion(parsedExcelDataView.noHierarchyQuestions);
      }
      // 有题库层级的题目
      const result = parsedExcelDataView.tree?.reduce(
        (accumulator: any, node: any) => {
          const { node: treeNode, tips } = parseQuestionTree(node, undefined, isPractical);
          if (!treeNode) {
            return accumulator;
          }
          accumulator.bankAndQuestionTree.push(treeNode);
          accumulator.allTips.push(...tips.map((tip: string, index: number) => `${index + 1}、${tip}`).join("<br/>"));
          return accumulator;
        },
        { bankAndQuestionTree: [], allTips: [] }
      );
      return {
        bankAndQuestionTree: result?.bankAndQuestionTree,
        noHierarchyQuestions
      };
    },
    /**
     * @description: 导出理论试题的题库和题目
     * @param fileOption 导入导出文件配置
     * @param tagDict 题库标签字典
     * @returns fileOption 导入导出文件配置（已经设置标签sheet）
     */
    setTheoreticalExportTagTableData
  };
}
