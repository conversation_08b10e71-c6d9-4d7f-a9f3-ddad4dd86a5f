<!--
 * FilePath     : \src\views\setting\organizationDepartmentMaintain.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-08 15:02
 * LastEditors  : 苏军志
 * LastEditTime : 2025-01-06 17:01
 * Description  : 人员多组织架构部门维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="organization-department-maintain" :drawerOptions="drawerOptions">
    <template #header>
      <department-selector v-model="selectDepartmentID" v-permission:DL="13" />
      <el-button v-permission:B="1" class="add-button" @click="openDrawer()">新增</el-button>
    </template>
    <el-table :data="showEmployeeDepartments" border>
      <el-table-column label="姓名" prop="employeeName" :width="convertPX(100)" />
      <el-table-column label="所属组织架构/部门" :min-width="convertPX(200)">
        <template #default="{ row }">
          <tag
            :class="['department-tag', department.isMainDepartment ? 'main-department' : '']"
            v-for="department in row.departments"
            :key="department.departmentID"
            :name="department.localShowName"
            width="auto"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(120)">
        <template #default="{ row }">
          <el-tooltip content="修改">
            <i v-permission:B="3" class="iconfont icon-edit" @click="openDrawer(row)" />
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-permission:B="4" class="iconfont icon-delete" @click="deleteEmployeeDepartment(row.employeeID)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <div class="container">
        <!-- 查询出的部门 -->
        <div class="left-view">
          <div class="header">
            <div class="organization-selector">
              <label>组织架构：</label>
              <el-select v-model="selectOrganization" @change="filterDepartment">
                <el-option v-for="(value, key) in organizations" :key="key" :label="value" :value="key" />
              </el-select>
            </div>
            <div class="department-search">
              <label class="label">部门：</label>
              <el-input v-model="searchKey" class="input" clearable />
            </div>
          </div>
          <div class="drag-content">
            <drag
              v-model="showDepartments"
              :draggableOption="{ itemKey: 'departmentID', pull: 'clone', put: false }"
              :sortable="false"
              :moveCheck="putCheck"
              multiFlag
              showTipText
              tipText="请选择组织架构"
            >
              <template #content="{ element }">
                <tag>{{ element?.localShowName }}</tag>
              </template>
            </drag>
          </div>
        </div>
        <!-- 当前人员所属部门 -->
        <div class="right-view">
          <div class="header">
            <div v-if="isEdit">
              <label>人员：</label>
              <span class="input">{{ modifiedRow.employeeName }}</span>
            </div>
            <div v-else>
              <department-selector v-model="employeeSelectorDepartmentID" v-permission:DL="13" />
              <employee-selector
                class="employee-selector"
                v-model="modifiedRow.employeeID"
                :departmentID="employeeSelectorDepartmentID"
                :disabled="!employeeSelectorDepartmentID"
                filterable
                @change="getEmployeeDepartments"
              />
            </div>
          </div>
          <div class="drag-content">
            <drag
              v-model="modifiedRow.departments"
              :draggableOption="{
                itemKey: 'value',
                put: Boolean(modifiedRow.employeeID),
                pull: false,
                filter: '.main-department'
              }"
              :sortable="false"
              :tipText="modifiedRow.employeeID ? '请拖拽到此处' : '请选择人员'"
              showTipText
              height="100%"
            >
              <template #content="{ element, index }">
                <tag
                  :class="[element.isMainDepartment ? 'main-department' : '']"
                  :closeable="!element.isMainDepartment"
                  @remove="deleteEmployeeDepartment(modifiedRow.employeeID, index)"
                  >{{ element?.localShowName }}</tag
                >
              </template>
            </drag>
          </div>
        </div>
      </div>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
const { userStore } = useStore();
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  cancel: () => {
    modifiedRow.value = {};
    drawerOptions.value.showDrawer = false;
  },
  confirm: () => saveEmployeeToDepartments()
});

//#region 列表加载
const selectDepartmentID = ref(0);
// 人员所属部门列表
const employeeDepartments = ref<Record<string, any>[]>([]);
const showEmployeeDepartments = computed(() =>
  employeeDepartments.value.filter((item) =>
    item.departments.some((department: Record<string, any>) => department.departmentID === selectDepartmentID.value)
  )
);
onMounted(() => {
  selectDepartmentID.value = userStore.departmentID;
  getTableData();

  dictionaryService.getDepartmentViewsByOrganizationType({ organizationType: selectOrganization.value }).then((res: any) => {
    departments.value = res;
  });
  // 从SettingDictionary获取组织架构，用作下拉项
  settingDictionaryService.getOrganizationTypes().then((res: any) => (organizations = res));
});
/**
 * @description: 获取列表
 */
const getTableData = () => {
  employeeService.getEmployeeToDepartments().then((res: any) => {
    employeeDepartments.value = res;
  });
};
//#endregion

//#region 数据增改
// 当前修改的人员所属部门数据
const modifiedRow = ref<Record<string, any>>({});
// 组织架构字典集合
let organizations: Record<string, any> = {};
const selectOrganization = ref("1");
const searchKey = ref("");
let isEdit = false;
const employeeSelectorDepartmentID = ref(userStore.departmentID);
// 对应组织架构部门
const departments = ref<Record<string, any>[]>([]);
const showDepartments = computed(() =>
  searchKey.value
    ? departments.value.filter((item: Record<string, any>) => item.localShowName.includes(searchKey.value))
    : departments.value
);
/**
 * @description: 关键字过滤部门
 */
const filterDepartment = () => {
  dictionaryService.getDepartmentViewsByOrganizationType({ organizationType: selectOrganization.value }).then((res: any) => {
    departments.value = res;
  });
};
/**
 * @description: 打开弹窗，增改人员所属部门
 * @param modifyDepartments 修改的人员所属部门
 */
const openDrawer = (row?: Record<string, any>) => {
  drawerOptions.value.drawerTitle = `${row ? "修改" : "新增"}人员所属部门`;
  if (row) {
    modifiedRow.value = common.clone(row);
    isEdit = true;
  } else {
    modifiedRow.value = {
      employeeID: "",
      departments: []
    };
    isEdit = false;
  }
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 根据工号获取人员所属部门
 * @param employeeID
 * @return
 */
const getEmployeeDepartments = (employeeID: string) => {
  employeeService.getEmployeeDepartmentsByEmployeeID({ employeeID }).then((res: any) => {
    modifiedRow.value.departments = res;
  });
};

/**
 * @description: 拖拽放置校验
 * @param value
 * @return
 */
const putCheck = (value: any) => {
  if (value.to.innerText.includes(value.draggedContext.element.localShowName)) {
    showWarning(value.draggedContext.element.localShowName);
    return false;
  }
  return Boolean(modifiedRow.value.employeeID);
};

/**
 * @description: 防抖函数
 * @param func 函数
 * @param wait 等待时间
 * @param immediate 是否立即执行
 * @return
 */
const debounce = (func: Function, wait: number, immediate: boolean) => {
  let timeout: any;
  return (...args: any[]) => {
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate) {
        func(...args);
      }
    }, wait);
    if (callNow) {
      func(...args);
    }
  };
};
/**
 * @description: 显示重复添加部门警告。使用防抖以避免重复弹窗
 * @return
 */
const showWarning = debounce((name: string) => showMessage("warning", `${name}重复，添加失败`), 300, true);
/**
 * @description: 保存人员多组织架构部门
 */
const saveEmployeeToDepartments = () => {
  if (!modifiedRow.value.employeeID) {
    showMessage("error", "没有选择人员");
    return;
  }
  // departments属性为ref类型，需要转换为原始数据，否则后端接不到
  const params = {
    ...modifiedRow.value,
    departments: toRaw(modifiedRow.value.departments)
  };
  employeeService.saveEmployeeToDepartments(params).then(() => {
    showMessage("success", "保存成功");
    getTableData();
  });
};
//#endregion

/**
 * @description: 删除人员所属部门
 * @param employeeID 工号
 * @param index 索引，为空表示按人员删除全部所属部门
 * @return
 */
const deleteEmployeeDepartment = (employeeID: string, index?: number) => {
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    index !== undefined
      ? modifiedRow.value.departments.splice(index, 1)
      : employeeService.deleteEmployeeToDepartmentByEmployeeID({ employeeID }).then(() => getTableData());
  });
};
</script>
<style lang="scss">
.organization-department-maintain {
  .department-tag {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  .container {
    display: flex;
    justify-content: space-between;
    height: 100%;
    .left-view,
    .right-view {
      flex: 1;
      max-width: 49%;
      border: 1px solid $border-color;
      height: 100%;
      display: flex;
      flex-direction: column;
      .header {
        border-bottom: 1px solid $border-color;
        padding: 5px 20px;
        flex: 1;
        display: flex;
        .label {
          padding-left: 5px;
        }
        .input {
          width: 100px;
        }
      }
      .drag-content {
        overflow: auto;
        flex: 35;
      }
    }
  }
  .organization-selector {
    display: inline-block;
    .el-select {
      width: 120px;
    }
  }
  .main-department {
    background-color: #f5f7fa;
    color: #a8abb2;
  }
}
</style>
