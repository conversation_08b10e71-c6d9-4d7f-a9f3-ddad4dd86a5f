<!--
 * FilePath     : \src\views\annualPlan\index.vue
 * Author       : 苏军志
 * Date         : 22024-02-04 16:58
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-04 17:25
 * Description  : 年度计划相关
 * CodeIterationRecord:
-->
<template>
  <top-menu :menuList="tabMenus" ref="childPage" :before-leave="checkRequestComplete"></top-menu>
</template>

<script setup lang="ts">
import { ElLoading } from "element-plus";
import { usePlanManagementStore } from "./hooks/usePlanManagementStore";
import { useAnnualPlanDictionaryStore } from "./maintain/hooks/useAnnualPlanDictionaryStore";
import { useAnnualPlanMaintainStore } from "./maintain/hooks/useAnnualPlanMaintainStore";

const { sessionStore } = useStore();
const route = useRoute();
let childPage = ref<any>();
let parentRouterName = route.matched[route.matched.length - 1]?.name as string;
const tabMenus = ref(sessionStore.pageTopMenus[parentRouterName]);
/**
 * @description: 若存在未完成的请求，则阻止切换
 */
const checkRequestComplete = () => {
  const win = window as any;
  if (!win.apiRequestList.length) {
    return true;
  }
  // 每100毫秒检查一次所有请求是否完成
  return new Promise((resolve) => {
    let loadingInstance:Record<string, any> | undefined = undefined;
    const interval = setInterval(() => {
      if (win.apiRequestList.length === 0) {
        loadingInstance?.close();
        clearInterval(interval);
        resolve(true);
      } else {
        // 此处不能使用showLoading，因为这是在setTimeout中，会重复调用导致loadingCount计数异常
        // 多次调用不会创建多个实例，element-plus已经处理成单例模式了
        loadingInstance = ElLoading.service({
          lock: true,
          text: "正在保存，请稍候...",
          background: "rgba(0, 0, 0, 0.7)"
        });
      }
    }, 100);
  });
};
// 处理按F5刷新
if (!tabMenus.value) {
  parentRouterName = route.matched[route.matched.length - 2]?.name as string;
  tabMenus.value = sessionStore.pageTopMenus[parentRouterName];
}
onUnmounted(() => {
  useAnnualPlanMaintainStore().$dispose();
  usePlanManagementStore().$dispose();
  useAnnualPlanDictionaryStore().$dispose();
});
// 暴漏给父路由
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    nextTick(() => {
      if (childPage?.value) {
        childPage?.value.refreshData();
      }
    });
  }
});
</script>
