<!--
 * FilePath     : \src\components\selector\departmentSwitchSelector.vue
 * Author       : 张现忠
 * Date         : 2024-08-09 10:16
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-03 18:35
 * Description  : 部门切换选择器
 * CodeIterationRecord:
 -->

<template>
  <div class="department-switch-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="selectedDepartmentIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled || (deptOptions.length === 1 && !!selectedDepartmentIDs)"
      :placeholder="`请选择${label}`"
      :collapse-tags="multiCollapse"
      :collapse-tags-tooltip="multiCollapse"
      @change="handleChange"
    >
      <el-option v-for="(dept, index) in deptOptions" :key="index" :label="dept.label" :value="dept.value"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
//#region 引入
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const { getEmployeeDepartment } = useDictionaryData();
//#endregion
// #region Props
const props = defineProps({
  modelValue: [Number, Array<Number>],
  label: {
    type: String,
    default: "部门"
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 200
  },
  multiCollapse: {
    type: Boolean,
    default: true
  },
  employeeID: {
    type: String
  },
  organizationType: {
    type: String,
    default: "1"
  },
  list: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => []
  }
});
// #endregion

// #region 数据和计算属性
const deptOptions = ref<Record<string, any>[]>([]);
const convertPX: any = inject("convertPX");
const selectorWidth = computed(() => `${convertPX(props.width)}px`);
const emits = defineEmits(["update:modelValue", "change", "select"]);
const selectedDepartmentIDs = useVModel(props, "modelValue", emits);
// #endregion
// #region 生命周期钩子
onMounted(async () => {
  if (props.list?.length !== 0) {
    deptOptions.value = props.list;
    setDefaultValue();
    return;
  }
  await getEmployeeDepartmentOptions();
  setDefaultValue();
});

watch(
  () => props.employeeID,
  async (newVal) => {
    if (!newVal) {
      deptOptions.value = [];
      selectedDepartmentIDs.value = props.multiple ? [] : undefined;
      return;
    }
    if (props.list?.length !== 0) {
      deptOptions.value = props.list;
      setDefaultValue();
      return;
    }
    await getEmployeeDepartmentOptions();
    setDefaultValue();
  }
);
// #endregion
// #region 方法
/**
 * @description 根据员工ID和组织类型获取部门选项。
 * 如果未提供部门ID，则默认选择主职部门，没有主职就选择列表中的第一个部门。
 * @returns
 */
const getEmployeeDepartmentOptions = async () => {
  if (!props.employeeID) {
    return;
  }
  await getEmployeeDepartment(props.employeeID, props.organizationType).then((responseData: any) => {
    deptOptions.value = responseData;
  });
};

/**
 * @description 为部门选择器设置默认值。
 * 如果提供了 `departmentID`，则使用它。
 * 否则，选择主职部门，如果没有主职部门，则选择列表中的第一个部门。
 * @returns
 */
const setDefaultValue = () => {
  // 提供了部门ID数组
  if (selectedDepartmentIDs.value && Array.isArray(selectedDepartmentIDs.value) && selectedDepartmentIDs.value.length > 0) {
    let filteredDeptIDs = selectedDepartmentIDs.value.filter(
      (deptID: any) => deptOptions.value.findIndex((option: Record<string, any>) => deptID === option.value) !== -1
    );
    selectedDepartmentIDs.value = filteredDeptIDs;
    return;
  }
  // 提供了部门ID
  if (selectedDepartmentIDs.value && !Array.isArray(selectedDepartmentIDs.value)) {
    let index = deptOptions.value.findIndex((option: Record<string, any>) => selectedDepartmentIDs.value === option.value);
    if (index !== -1) {
      selectedDepartmentIDs.value = deptOptions.value[index].value;
      return;
    }
  }
  // 有传入部门ID，找不到对应的部门，默认为空
  if (selectedDepartmentIDs.value) {
    selectedDepartmentIDs.value = props.multiple ? [] : undefined;
    return;
  }
};
/**
 * @description 部门选择器值发生变化时触发。
 * @param value 部门ID或部门ID数组
 */
const handleChange = (value: number | number[]) => {
  exposeChange(value, emits);
  exposeSelect(value, deptOptions.value, "value", props.multiple, emits);
};
// #endregion
</script>

<style lang="scss">
.department-switch-selector {
  display: inline-block;
  margin-right: 14px;
  .selector-component {
    .is-disabled {
      background-color: #ffffff;
      .el-select__selected-item {
        color: #000000 !important;
      }
      .el-select__suffix {
        display: none;
      }
    }
  }
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
