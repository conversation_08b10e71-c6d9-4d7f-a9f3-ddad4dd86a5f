/*
 * FilePath     : \nursing-management-web\src\api\trainingService.ts
 * Author       : 张现忠
 * Date         : 2024-04-07 11:54
 * LastEditors  : 陈超然
 * LastEditTime : 2024-12-29 15:54
 * Description  : 实现课程相关接口请求
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class trainingService {
  // 管理课程相关接口的基本URL
  private static controllerBaseUrl: string = "/Course";
  // 获取课程列表的API接口URL
  private static getCourseSettingListApi: string = this.controllerBaseUrl + "/GetCourseSettingList";
  // 保存课程的API接口URL
  private static saveCourseSettingApi: string = this.controllerBaseUrl + "/SaveCourseSetting";
  // 删除课程的API接口URL
  private static deleteCourseSettingApi: string = this.controllerBaseUrl + "/DeleteCourseSetting";
  // 搜索课程的API接口URL
  private static searchCourseSettingApi: string = this.controllerBaseUrl + "/SearchCourseSetting";
  // 获取培训记录数据
  private static getTrainingRecordApi: string = this.controllerBaseUrl + "/GetTrainingRecord";
  // 删除培训记录
  private static deleteTrainingRecordApi: string = this.controllerBaseUrl + "/DeleteTrainingRecord";
  // 删除培训记录
  private static saveTrainingRecordApi: string = this.controllerBaseUrl + "/SaveTrainingRecord";
  // 获取课程级联下拉框数据
  private static getCourseSettingApi: string = this.controllerBaseUrl + "/GetCourseSetting";
  // 保存培训问卷模版数据
  private static saveEvaluationFormApi: string = this.controllerBaseUrl + "/SaveEvaluationForm";
  // 保存培训评价记录
  private static saveEvaluationDataApi: string = this.controllerBaseUrl + "/SaveEvaluationData";
  // 获取培训评价模板数据
  private static getEvaluationFormViewApi: string = this.controllerBaseUrl + "/GetEvaluationFormView";
  /**
   * 获取课程列表
   * @param params 请求参数
   * @returns 返回课程列表数据
   */
  public static async getCourseSettingList(params: any): Promise<any> {
    return await http.get(this.getCourseSettingListApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * 保存课程
   * @param params 课程信息
   * @returns 返回保存结果
   */
  public static async saveCourseSetting(params: any): Promise<any> {
    return await http.post(this.saveCourseSettingApi, params, { loadingText: Loading.SAVE, contentType: "multipart/form-data" });
  }

  /**
   * 删除课程
   * @param params 待删除的课程ID
   * @returns 返回删除结果
   */
  public static async deleteCourseSetting(params: any): Promise<any> {
    return await http.post(this.deleteCourseSettingApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * 根据课程名称模糊查询课程数据
   * @param params 搜索条件(课程名称)
   * @returns
   */
  public static async searchCourseSetting(params: any): Promise<any> {
    return await http.get(this.searchCourseSettingApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取培训记录数据
   * @param params
   * @return
   */
  public static async GetTrainingRecord(params: any): Promise<any> {
    return await http.post(this.getTrainingRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除培训记录
   * @param params
   * @return
   */
  public static async deleteTrainingRecord(params: any): Promise<any> {
    return await http.post(this.deleteTrainingRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存培训记录
   * @param params
   * @return
   */
  public static async saveTrainingRecord(params: any): Promise<any> {
    return await http.post(this.saveTrainingRecordApi, params, { loadingText: Loading.SAVE, contentType: "multipart/form-data" });
  }
  /**
   * @description: 获取课程级联下拉框数据
   * @param params
   * @return
   */
  public static async getCourseSetting(params: any): Promise<any> {
    return await http.get(this.getCourseSettingApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存培训问卷模版数据
   * @param params
   * @return
   */
  public static async saveEvaluationForm(params: any): Promise<any> {
    return await http.post(this.saveEvaluationFormApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存培训评价记录
   * @param params
   * @return
   */
  public static async saveEvaluationData(params: any): Promise<any> {
    return await http.post(this.saveEvaluationDataApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取培训评价模板数据
   * @param params
   * @return
   */
  public static async getEvaluationFormView(params: any): Promise<any> {
    return await http.get(this.getEvaluationFormViewApi, params, { loadingText: Loading.LOAD });
  }
}
