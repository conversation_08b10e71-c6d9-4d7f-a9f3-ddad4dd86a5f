/*
 * FilePath     : \src\main.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 08:42
 * LastEditors  : 苏军志
 * LastEditTime : 2024-12-20 19:40
 * Description  :
 * CodeIterationRecord:
 */
import "@/assets/iconfont/iconfont.css";
import "@/assets/scss/index.scss";
import i18n from "@/i18n/index"; // i18n国际化
import pinia from "@/stores/pinia"; // 状态管理
import "@/utils/Date.js";
import directives from "@/utils/directives"; // 自定义指令
import "@/utils/flexble.js"; // 自适应
import navigation from "@/utils/navigation"; // 导航卫士
import "@/utils/setting.ts";
import ContextMenuGlobal from "@imengyu/vue3-context-menu";
import "@imengyu/vue3-context-menu/lib/vue3-context-menu.css";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import ElTableInfiniteScroll from "el-table-infinite-scroll";
import elementPlus, { ElDialog, ElDrawer } from "element-plus";
import "element-plus/dist/index.css";
import locale from "element-plus/dist/locale/zh-cn.js";
import { createApp } from "vue";
import zhytechUI from "zhytech-ui";
import "zhytech-ui/dist/style.css";
import app from "./App.vue";
import router from "./router"; // 路由
dayjs.locale("zh-cn");

// 设置页面跳转规则
navigation.set(router);
// 初始化配置
initSetting();
// 创建实例
const appInstance = createApp(app);
appInstance.config.errorHandler = (err, vm, info) => {
  if (!err) {
    return;
  }
  console.error("Global Error Handler:", err, vm, info);
  // 在实际应用中，你可以根据需要采取适当的处理措施，比如发送错误报告给服务器
};

appInstance.config.globalProperties.$showContextMenu = ContextMenuGlobal.showContextMenu;
appInstance.config.globalProperties.$transformMenuPosition = ContextMenuGlobal.transformMenuPosition;
appInstance
  .use(directives)
  .use(pinia)
  .use(router)
  .use(i18n)
  .use(elementPlus, { size: "small", zIndex: 3000, locale })
  .use(ElTableInfiniteScroll as any)
  .use(zhytechUI, { theme: "light" })
  .mount("#app");

// 设置全局属性 弹窗默认点击遮罩层不关闭
ElDrawer.props.closeOnClickModal.default = false;
ElDialog.props.closeOnClickModal.default = false;
