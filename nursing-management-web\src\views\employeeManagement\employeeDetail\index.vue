<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\index.vue
 * Author       : 张现忠
 * Date         : 2023-07-24 16:36
 * LastEditors  : 苏军志
 * LastEditTime : 2024-04-23 18:36
 * Description  : 护士在职信息档案主页面
 * CodeIterationRecord：
                        2023-07-24 3661-作为护理管理人员，我需要护士在职信息档案，以便查看护士相关档案信息（21）-zxz
-->
<template>
  <base-layout class="employee-detail" headerHeight="auto">
    <template #header>
      <el-image class="user-img" :src="photoUrl" :preview-src-list="[photoUrl]" />
      <div class="user-display">
        <span class="employee-name">{{ headerInfo.employeeName }}</span>
        <div class="description-wrap">
          <div v-for="(item, index) in descriptionFormat" :key="index" class="description">
            <el-divider v-if="index !== 0" direction="vertical" class="divider" />
            <label>{{ item.label + "：" }}</label>
            <span> {{ item.format ? item.format(headerInfo?.[item.field]) : headerInfo?.[item.field] }}</span>
          </div>
        </div>
      </div>
      <el-tooltip content="返回上一页">
        <i class="iconfont icon-back" v-if="route?.query?.employeeID" @click="router.go(-1)"></i>
      </el-tooltip>
    </template>
    <top-menu :menuList="tabMenus" :routerQuery="{ employeeID: employeeID }" ref="childPage"></top-menu>
  </base-layout>
</template>

<script setup lang="ts">
const { sessionStore, userStore } = useStore();

const route = useRoute();
const router = useRouter();
// 用户基本信息
let headerInfo = ref({} as any);
let childPage = ref<any>();
let parentRouterName = route.matched[route.matched.length - 2]?.name as string;
const tabMenus = ref(sessionStore.pageTopMenus[parentRouterName]);

onMounted(() => {
  loadPersonalFileHeaderInfo();
});

const descriptionFormat = [
  {
    label: "部门",
    field: "departmentName"
  },
  {
    label: "性别",
    field: "sex"
  },
  {
    label: "入职时间",
    field: "entryDate",
    format: (val: any) => {
      if (!val) {
        return undefined;
      }
      return datetimeUtil.formatDate(val, "yyyy-MM-dd");
    }
  },
  {
    label: "职称",
    field: "professionalLevelName"
  },
  {
    label: "层级",
    field: "capabilityLevelName"
  }
];

let employeeID = route?.query?.employeeID ?? userStore?.employeeID;
// 获取人员照片
const { getEmployeePhoto } = useEmployee();
const photoUrl = getEmployeePhoto(employeeID as string);

/**
 * description: 获取用户个人档案头信息
 * return {*}
 */
const loadPersonalFileHeaderInfo = async () => {
  // 获取用户信息
  let param = {
    employeeID: employeeID
  };
  await employeeService.getPersonalFileHeaderInfo(param).then((respData: any) => {
    headerInfo.value = respData;
  });
};

// 暴漏给父路由
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    nextTick(() => {
      if (childPage?.value) {
        childPage?.value.refreshData();
      }
    });
  }
});
// 向所有子路由传递变量
provide("refreshEmployeeHeader", () => {
  loadPersonalFileHeaderInfo();
});
</script>

<style lang="scss">
.employee-detail {
  height: 100%;
  width: 100%;
  > .base-header {
    min-height: 200px;
    @include l-gradient-bg(right, lighten($base-color, 100%), lighten($base-color, 40%), lighten($base-color, 30%));
    display: flex;
    .user-img {
      width: 160px;
      margin: 5px;
      border-radius: 10px;
    }
    .user-display {
      flex: auto;
      margin-left: 40px;
      .employee-name {
        display: block;
        margin: 40px 0 20px 0;
        font-size: 36px;
      }
      .description-wrap {
        width: 90%;
        display: flex;
        .description {
          font-size: 26px;
          .divider {
            margin: 0 10px;
            border-color: $base-color;
          }
        }
      }
    }
    .icon-back {
      height: 30px;
      margin: -5px 2px 0 0;
      font-size: 30px;
    }
  }
}
</style>
