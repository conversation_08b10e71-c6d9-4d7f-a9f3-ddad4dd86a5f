<!--
 * FilePath     : \ccc.webe:\NursingManagement\nursing-management-web\src\components\selector\departmentPostSelector.vue
 * Author       : 张现忠
 * Date         : 2023-08-01 09:59
 * LastEditors  : 马超
 * LastEditTime : 2023-09-24 16:45
 * Description  : 岗位下拉选择器组件
 * CodeIterationRecord:
-->

<template>
  <div class="department-post-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="departmentPostIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
      @visible-change="visibleChange"
    >
      <el-option v-for="(item, index) in departmentPostOptions" :key="index" :label="item.label" :value="item.value"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "部门岗位"
  },
  modelValue: {
    type: [Number, Array<number>]
  },
  departmentID: {
    type: Number,
    required: true
  },
  postType: {
    type: String,
    default: undefined
  },
  /**
   * 是否显示全院公共岗位（休假）
   */
  showAll: {
    type: Boolean,
    default: false
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 120
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select", "visibleChange"]);
let departmentPostIDs = useVModel(props, "modelValue", emits);

let departmentPostOptions = ref<Array<Record<any, any>>>([]);
// 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
let { list } = toRefs(props);
if (list?.value) {
  departmentPostOptions.value = list.value;
} else {
  let { getDepartmentPostData } = useDictionaryData();
  getDepartmentPostData(props.departmentID, props.showAll, props.postType, undefined, Math.random()).then((datas) => {
    departmentPostOptions.value = datas;
  });
}
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, departmentPostOptions.value, "value", props.multiple, emits);
};
/**
 * 下拉框出现/隐藏时触发
 */
const visibleChange = (flag: boolean) => {
  emits("visibleChange", flag);
};
</script>

<style lang="scss">
.department-post-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
