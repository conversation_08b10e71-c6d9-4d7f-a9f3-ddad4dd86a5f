<!--
 * FilePath     : \src\views\scheduling\schedulingTemplate.vue
 * Author       : 苏军志
 * Date         : 2024-09-28 15:13
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-31 16:54
 * Description  : 排班模板
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="scheduling-template" :drawerOptions="drawerOptions">
    <template #header>
      <el-button v-permission:B="1" type="primary" class="add-button" @click="openEditDrawer()">新增</el-button>
    </template>
    <el-table :data="templateList" border stripe height="100%">
      <el-table-column label="序号" type="index" align="center" width="50" />
      <el-table-column label="模板名称" property="templateName" :min-width="convertPX(100)"> </el-table-column>
      <el-table-column label="模板说明" property="templateDescription" :min-width="convertPX(150)"> </el-table-column>
      <el-table-column label="状态" :width="convertPX(100)" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.statusCode" active-value="1" inactive-value="0" @change="updateTemplateStatus(row)" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" label="最后维护人" align="center" :width="convertPX(150)" />
      <el-table-column label="最后维护时间" align="center" :min-width="convertPX(120)">
        <template v-slot="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'datetime' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90" align="center">
        <template v-slot="{ row }">
          <el-tooltip content="修改">
            <i v-permission:B="4" class="iconfont icon-edit" @click="openEditDrawer(row)"></i>
          </el-tooltip>
          <el-tooltip content="复制">
            <i v-permission:B="1" class="iconfont icon-copy" @click="copyTemplate(row.schedulingTemplateRecordID)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-permission:B="4" class="iconfont icon-delete" @click="deleteTemplate(row.schedulingTemplateRecordID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <base-layout class="edit-drawer">
        <template #header>
          <el-form class="header-form" :inline="true" :model="templateData" label-suffix="：">
            <el-form-item label="模板名称" prop="templateName" class="template-name" required>
              <el-input v-model="templateData.templateName"></el-input>
            </el-form-item>
            <el-form-item label="模板说明" prop="templateDescription" class="template-description">
              <el-input v-model="templateData.templateDescription"></el-input>
            </el-form-item>
          </el-form>
        </template>
        <scheduling-template-univer ref="schedulingTemplate" :params="templateParams"></scheduling-template-univer>
      </base-layout>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const templateList = ref<Record<string, any>[]>([]);
const { userStore } = useStore() as any;
const headerRowCount: number = 1;
const templateParams = ref<Record<string, any>>({ selectionMode: false });
onBeforeMount(async () => {
  getTemplateRecords();
  await getNoonList();
  await getDepartmentPosts();
  await getShiftSchedulingMarks();
  templateParams.value.noonList = noonList;
  templateParams.value.departmentPostList = departmentPostList;
  templateParams.value.shiftSchedulingMarkList = shiftSchedulingMarkList;
});
/**
 * @description: 获取模板记录
 */
const getTemplateRecords = () => {
  let params = {
    departmentID: userStore.departmentID
  };
  schedulingTemplateService.getTemplateRecords(params).then((data: any) => {
    templateList.value = data || [];
  });
};

//#region 获取 noonList、departmentPostList、getShiftSchedulingMarks
let noonList: Record<string, string>[] = [];
/**
 * @description: 获取午别字典
 */
const getNoonList = async () => {
  // 通过hooks从数据库获取数据
  const params: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "JobPositions",
    settingTypeValue: "NoonType",
    index: Math.random()
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    noonList = datas;
  });
};

let departmentPostList: Record<string, any>[] = [];
/**
 * @description: 获取部门岗位清单
 */
const getDepartmentPosts = async () => {
  let { getDepartmentPostData } = useDictionaryData();
  await getDepartmentPostData(userStore.departmentID, true).then((result: any) => {
    departmentPostList = result || [];
  });
};
let shiftSchedulingMarkList: Record<string, any> = [];
/**
 * @description: 获取排班标记集合
 */
const getShiftSchedulingMarks = async () => {
  let params = {
    departmentID: userStore.departmentID
  };
  await schedulingTemplateService.getShiftSchedulingMarks(params).then((result: any) => {
    shiftSchedulingMarkList = result || [];
  });
};
//#endregion

//#region 打开新增\修改抽屉
// 抽屉参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  confirm: () => saveTemplateRecord()
});
const schedulingTemplate = ref<any>();
const templateData = ref<Record<string, any>>({});
/**
 * @description: 打开编辑抽屉
 * @param template 模板记录
 * @return
 */
const openEditDrawer = (template?: Record<string, any>) => {
  drawerOptions.value.drawerTitle = `${template ? "修改" : "新增"}排班模板`;
  templateData.value = {
    schedulingTemplateRecordID: template?.schedulingTemplateRecordID || "",
    templateName: template?.templateName || "",
    templateDescription: template?.templateDescription || ""
  };
  templateParams.value.schedulingTemplateRecordID = template?.schedulingTemplateRecordID;
  drawerOptions.value.showDrawer = true;
};
//#endregion

//#region 保存模板
/**
 * @description: 保存模板
 */
const saveTemplateRecord = () => {
  if (!templateData.value.templateName) {
    showMessage("error", "请输入模板名称！");
    return;
  }
  const { cellDatas, rowCount, columnCount } = schedulingTemplate.value.getSheetData();
  // 获取排班模板明细数据
  const schedulingTemplateDetails = getSchedulingTemplateDetails(cellDatas);
  if (schedulingTemplateDetails?.length === 0) {
    showMessage("error", "没有要保存的排班模板数据！");
    return;
  }
  const schedulingTemplateData: Record<string, any> = {
    schedulingTemplateRecordID: templateData.value.schedulingTemplateRecordID,
    templateName: templateData.value.templateName,
    templateDescription: templateData.value.templateDescription,
    departmentID: userStore.departmentID,
    rowCount,
    columnCount,
    schedulingTemplateDetails: schedulingTemplateDetails
  };
  schedulingTemplateService.saveTemplateRecord(schedulingTemplateData).then((result) => {
    if (result) {
      showMessage("success", "保存成功!");
      drawerOptions.value.showDrawer = false;
      getTemplateRecords();
    }
  });
};
/**
 * @description: 获取排班模板明细数据
 * @param cellDatas
 * @return
 */
const getSchedulingTemplateDetails = (cellDatas: Record<string, Record<string, any>>) => {
  let schedulingTemplateDetails: Record<string, any>[] = [];
  if (!cellDatas) {
    return schedulingTemplateDetails;
  }
  Object.keys(cellDatas).forEach((rowIndex) => {
    // 跳过标题行
    if (Number(rowIndex) < headerRowCount!) {
      return;
    }
    const rowData = cellDatas[rowIndex];
    // 循环列
    Object.keys(rowData).forEach((columnIndex) => {
      const columnData = rowData[columnIndex];
      // 没有排班数据 直接返回
      if (!columnData.custom) {
        return;
      }
      let schedulingTemplateDetail: Record<string, any> = {
        rowIndex: Number(rowIndex),
        columnIndex: Number(columnIndex),
        noonPost: columnData.custom?.noonPost,
        markList: columnData.custom?.markList
      };
      schedulingTemplateDetails.push(schedulingTemplateDetail);
    });
  });
  return schedulingTemplateDetails;
};
//#endregion

//#region 模板复制、删除、修改状态方法
const copyTemplate = (schedulingTemplateRecordID: string) => {
  schedulingTemplateService.copyTemplate({ schedulingTemplateRecordID }).then((result: any) => {
    if (result) {
      showMessage("success", "复制成功！");
      getTemplateRecords();
    }
  });
};
const updateTemplateStatus = (template: Record<string, any>) => {
  let params = {
    schedulingTemplateRecordID: template.schedulingTemplateRecordID,
    statusCode: template.statusCode
  };
  schedulingTemplateService.updateTemplateStatus(params).then((result: any) => {
    if (result) {
      showMessage("success", "保存成功！");
      getTemplateRecords();
    }
  });
};
const deleteTemplate = (schedulingTemplateRecordID: string) => {
  if (!schedulingTemplateRecordID) {
    return;
  }
  deleteConfirm("", (res: boolean) => {
    if (res) {
      schedulingTemplateService.deleteTemplate({ schedulingTemplateRecordID }).then((result: any) => {
        if (result) {
          showMessage("success", "删除成功！");
          getTemplateRecords();
        }
      });
    }
  });
};
//#endregion
</script>
<style lang="scss">
.scheduling-template {
  height: 100%;
  width: 100%;
  .edit-drawer {
    .header-form {
      .template-name {
        width: calc(30% - 10px);
      }
      .template-description {
        width: 65%;
      }
    }
    .univer-sheet-wrap {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
