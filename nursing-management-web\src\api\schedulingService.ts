/*
 * FilePath     : \src\api\schedulingService.ts
 * Author       : 马超
 * Date         : 2023-08-20 08:49
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:36
 * Description  : 排班相关接口Api
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";
export class schedulingService {
  private static getSingleSchedulingRequestApi: string = "/scheduling/GetSingleSchedulingRequest";
  private static getDepartmentSchedulingRequestApi: string = "/scheduling/GetDepartmentSchedulingRequest";
  private static saveSchedulingRequestRecordApi: string = "/scheduling/SaveSchedulingRequestRecord";
  private static deleteScheduleRequestRecordApi: string = "/scheduling/DeleteScheduleRequestRecord";
  private static getAdjustScheduleRecordApi: string = "/scheduling/GetAdjustScheduleRecord";
  private static saveAdjustScheduleRecordApi: string = "/scheduling/SaveAdjustScheduleRecord";
  private static deleteAdjustScheduleRecordApi: string = "/scheduling/DeleteAdjustScheduleRecord";
  private static getShiftSchedulingDataApi: string = "/scheduling/GetShiftSchedulingData";
  private static saveShiftSchedulingDataApi: string = "/scheduling/SaveShiftSchedulingData";
  private static getShiftSchedulingParameterApi: string = "/scheduling/GetShiftSchedulingParameter";
  private static getShiftSchedulingByEmployeeIDApi: string = "/scheduling/GetShiftSchedulingByEmployeeID";
  private static updateSchedulingEmployeeSortApi: string = "/scheduling/UpdateSchedulingEmployeeSort";
  private static getSchedulingPostByEmployeeIDApi: string = "/scheduling/GetSchedulingPostByEmployeeID";
  private static getRemainingRestDaysDataApi: string = "/scheduling/GetRemainingRestDaysData";
  private static saveRemainingRestDaysDataApi: string = "/scheduling/SaveRemainingRestDaysData";
  private static copySchedulingApi: string = "/scheduling/CopyScheduling";
  private static getSchedulingMarkSettingsApi: string = "/scheduling/getSchedulingMarkSettings";
  private static saveSchedulingMarkSettingsApi: string = "/scheduling/saveSchedulingMarkSettings";
  private static deleteSchedulingMarkSettingsApi: string = "/scheduling/deleteSchedulingMarkSettings";
  private static getShiftSchedulingRulesApi: string = "/scheduling/GetShiftSchedulingRules";
  private static saveShiftSchedulingRuleApi: string = "/scheduling/SaveShiftSchedulingRule";

  public static getSingleSchedulingRequest(params: any) {
    return http.get(this.getSingleSchedulingRequestApi, params, { loadingText: Loading.LOAD });
  }
  public static getDepartmentSchedulingRequest(params: any) {
    return http.get(this.getDepartmentSchedulingRequestApi, params, { loadingText: Loading.LOAD });
  }
  public static saveSchedulingRequestRecord(params: any) {
    return http.post(this.saveSchedulingRequestRecordApi, params, { loadingText: Loading.SAVE });
  }
  public static deleteScheduleRequestRecord(params: any) {
    return http.post(this.deleteScheduleRequestRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  public static getAdjustScheduleRecord(params: any) {
    return http.get(this.getAdjustScheduleRecordApi, params, { loadingText: Loading.LOAD });
  }
  public static saveAdjustScheduleRecord(params: any) {
    return http.post(this.saveAdjustScheduleRecordApi, params, { loadingText: Loading.SAVE });
  }
  public static deleteAdjustScheduleRecord(params: any) {
    return http.post(this.deleteAdjustScheduleRecordApi, params, { loadingText: Loading.DELETE });
  }
  /**
   * 获取部门时间段内的排班数据
   * @param params
   * @returns
   */
  public static getShiftSchedulingData(params: any) {
    return http.get(this.getShiftSchedulingDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 获取个人时间段内的排班数据
   * @param params
   * @returns
   */
  public static getShiftSchedulingByEmployeeID(params: any) {
    return http.get(this.getShiftSchedulingByEmployeeIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 保存排班数据
   * @param params
   * @returns
   */
  public static saveShiftSchedulingData(params: any, loadingContent?: string) {
    return http.post(this.saveShiftSchedulingDataApi, params, { loadingText: loadingContent || Loading.SAVE });
  }
  /**
   * 保存排班人员排序
   * @param params
   * @returns
   */
  public static updateSchedulingEmployeeSort(params: any) {
    return http.post(this.updateSchedulingEmployeeSortApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * 获取智能排班相关参数
   * @param params
   * @returns
   */
  public static getShiftSchedulingParameter(params: any) {
    return http.get(this.getShiftSchedulingParameterApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 获取员工指定日期的排班
   * @param params
   * @returns
   */
  public static getSchedulingPostByEmployeeID(params: any) {
    return http.get(this.getSchedulingPostByEmployeeIDApi, params);
  }
  /**
   * @description: 复制上月排班数据
   * @param params
   * @return
   */
  public static copyScheduling(params: any) {
    return http.post(this.copySchedulingApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取部门人员剩余休假天数数据
   * @param params
   * @return
   */
  public static getRemainingRestDaysData(params: any) {
    return http.get(this.getRemainingRestDaysDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存部门人员剩余休假天数数据
   * @param params
   * @return
   */
  public static saveRemainingRestDaysData(params: any) {
    return http.post(this.saveRemainingRestDaysDataApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取排班标记设定配置
   * @param params
   * @return
   */
  public static getSchedulingMarkSettings(params: any) {
    return http.get(this.getSchedulingMarkSettingsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存排班标记设定配置
   * @param params
   * @return
   */
  public static saveSchedulingMarkSettings(params: any) {
    return http.post(this.saveSchedulingMarkSettingsApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除排班标记设定配置
   * @param params
   * @return
   */
  public static deleteSchedulingMarkSettings(params: any) {
    return http.post(this.deleteSchedulingMarkSettingsApi, params, { loadingText: Loading.DELETE });
  }
  /**
   * @description: 获取排班规则设定记录
   * @param params
   * @return
   */
  public static getShiftSchedulingRules(params: any) {
    return http.get(this.getShiftSchedulingRulesApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存排班规则设定记录
   * @param params
   * @return
   */
  public static saveShiftSchedulingRule(params: any) {
    return http.post(this.saveShiftSchedulingRuleApi, params, { loadingText: Loading.SAVE });
  }
}
