<!--
 * FilePath     : \src\views\annualPlan\dictionary\annualPlanTypeList.vue
 * Author       : 胡长攀
 * Date         : 2024-01-28 11:28
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-15 14:29
 * Description  : 年度计划分类维护
 -->

<template>
  <base-layout class="annual-plan-type-list" :drawerOptions="drawerOptions">
    <template #header>
      <annual-plan-header :year="year" />
      <el-button v-permission:B="1" @click="addAnnualPlanType" class="add-button">{{ i18nText.add }}</el-button>
    </template>
    <el-table class="annual-plan-type-list-table" :data="annualPlanTypeList" stripe border>
      <el-table-column prop="sort" :label="i18nText.sort" :width="convertPX(70)">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="annualPlanTypeContent" :label="i18nText.annualPlanTypeContent" :min-width="convertPX(250)" />
      <el-table-column prop="addEmployeeName" :label="i18nText.addPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.addDateTime" :width="convertPX(220)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" :label="i18nText.modifyPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.modifyDateTime" :width="convertPX(220)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column :label="i18nText.operation" :width="convertPX(80)" align="center">
        <template #default="{ row }">
          <el-tooltip :content="i18nText.edit">
            <i v-permission:B="3" @click="editAnnualPlanType(row)" class="iconfont icon-edit" />
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i v-permission:B="4" @click="deleteAnnualPlanTypeList(row)" class="iconfont icon-delete" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="editAnnualPlanTypeForm" :model="editingAnnualPlanType" :rules="rules" label-position="left" label-width="auto">
        <el-form-item :label="i18nText.annualPlanTypeContent" prop="annualPlanTypeContent">
          <el-input
            :placeholder="i18nText.annualPlanTypeContent"
            v-model="editingAnnualPlanType.annualPlanTypeContent"
            :rows="3"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";

const { proxy } = getCurrentInstance() as any;
const convertPX: any = inject("convertPX");
const annualPlanTypeList = ref<Record<string, any>[]>([]);
const year = usePlanTime().getPlanAnnual();
const managementStore = usePlanManagementStore();

onMounted(async () => await getAnnualPlanTypeList());
watch(
  () => managementStore.departmentID,
  async () => {
    await getAnnualPlanTypeList();
  }
);
/**
 * @description: 获取年度计划分类字典
 */
const getAnnualPlanTypeList = async () => {
  const params = {
    departmentID: managementStore.departmentID
  };
  annualPlanTypeList.value = await annualPlanSettingService.getAnnualPlanTypeList(params);
};

//#region 弹窗
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  cancel: () => (drawerOptions.value.showDrawer = false),
  confirm: async () => await saveAnnualPlanTypeList()
});
/**
 * @description: 新增指标字典
 */
const addAnnualPlanType = () => {
  drawerOptions.value.drawerTitle = i18nText.value.add;
  drawerOptions.value.showDrawer = true;
  editingAnnualPlanType.value.typeID = 0;
  editingAnnualPlanType.value.annualPlanTypeContent = "";
};
/**
 * @description: 编辑指标字典
 * @param row 当前行
 * @return
 */
const editAnnualPlanType = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  drawerOptions.value.drawerTitle = i18nText.value.edit;
  drawerOptions.value.showDrawer = true;
  editingAnnualPlanType.value = common.clone(row);
};
//#endregion

//#region 表单
const editingAnnualPlanType = ref<Record<string, any>>({});
const editAnnualPlanTypeForm = ref<any>();
const rules = ref({
  annualPlanTypeContent: [{ required: true, message: "请输入分类名称", trigger: "blur" }]
});
/**
 * @description: 保存分类字典
 */
const saveAnnualPlanTypeList = async () => {
  let { validateRule } = useForm();
  if (!(await validateRule(editAnnualPlanTypeForm.value))) {
    showMessage("warning", "请填写必填项");
    return;
  }
  const params = {
    annualPlanTypeID: editingAnnualPlanType.value.annualPlanTypeID,
    annualPlanTypeContent: editingAnnualPlanType.value.annualPlanTypeContent,
    departmentID: managementStore.departmentID
  };
  annualPlanSettingService.saveAnnualPlanTypeList(params).then(() => {
    drawerOptions.value.showDrawer = false;
    showMessage("success", "保存成功");
    getAnnualPlanTypeList();
  });
};
//#endregion

/**
 * @description: 删除分类字典
 * @param row 当前行
 * @return
 */
const deleteAnnualPlanTypeList = async (row: Record<string, any>) => {
  if (!row?.annualPlanTypeID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  confirmBox("确定删除？", proxy.$t("tip.systemTip"), async (flag: boolean) => {
    if (!flag) {
      return;
    }
    // 添加检核逻辑
    if (!(await checkAnnualPlanTypeList(row))) {
      return;
    }
    await annualPlanSettingService.deleteAnnualPlanType(row).then(() => {
      showMessage("success", "删除成功");
      getAnnualPlanTypeList();
    });
  });
};

/**
 * @description: 检核该目标是否可删除
 * @param row 当前行
 * @return {*}
 */
const checkAnnualPlanTypeList = async (row: Record<string, any>) => {
  let flag = false;
  await annualPlanSettingService.checkAnnualPlanTypeList(row).then((res) => {
    if (res) {
      flag = true;
    }
  });
  return flag;
};
// 多语言处理
const i18nText = computed(() => {
  return {
    sort: proxy.$t("label.sort"),
    add: proxy.$t("button.add"),
    delete: proxy.$t("tip.delete"),
    edit: proxy.$t("tip.edit"),
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    buttonSave: proxy.$t("button.save"),
    cancel: proxy.$t("button.cancel"),
    annualPlanTypeContent: proxy.$t("annualPlanTypeList.annualPlanTypeContent"),
    addPerson: proxy.$t("annualPlanTypeList.addPerson"),
    addDateTime: proxy.$t("annualPlanTypeList.addDateTime"),
    modifyPerson: proxy.$t("annualPlanTypeList.modifyPerson"),
    modifyDateTime: proxy.$t("annualPlanTypeList.modifyDateTime")
  };
});
</script>
<style lang="scss">
.annual-plan-type-list {
  .el-select {
    width: 160px;
  }
  .annual-plan-type-list-table {
    height: 100%;
  }
}
</style>
