<!--
 * FilePath     : \src\views\scheduling\components\schedulingTable.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:42
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-26 19:44
 * Description  : 排班表
 * CodeIterationRecord:
-->
<template>
  <div class="scheduling-table">
    <div class="univer-sheet-wrap" ref="univerSheetWrapRef">
      <univer-sheet ref="univerSheetRef" v-if="templateData" :data="templateData" :config="config"></univer-sheet>
    </div>
    <el-dialog class="remark-edit" v-model="remarkParams.show" :title="remarkParams.title">
      <div class="label">备注：</div>
      <el-input v-model="remarkParams.remark" :rows="3" type="textarea" />
      <template #footer>
        <el-button @click="remarkParams.show = false">取消</el-button>
        <el-button type="primary" @click="setCellRemark">确定</el-button>
      </template>
    </el-dialog>
    <el-dialog class="template-select" v-model="showTemplateDialog" title="选择模板">
      <div class="template-wrap">
        <div class="tip">温馨提示：可以鼠标拖动选择一个区域，不选默认应用整个模板</div>
        <scheduling-template-univer
          v-if="showTemplateDialog"
          ref="schedulingTemplateRef"
          :params="templateParams"
        ></scheduling-template-univer>
      </div>
      <template #footer>
        <el-button @click="showTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="setTableValues(true)">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import type { sheetParamType } from "@/components/univerSheet/types/sheetParamType";
import type { configType } from "@/components/univerSheet/types/configType";
import { menuEnum } from "@/components/univerSheet/types/menuEnum";
import { useSchedulingControl } from "../hooks/useSchedulingControl";
import type { schedulingTableParam } from "../types/schedulingTableParam";
import { useSchedulingUniverUtil } from "../hooks/useSchedulingUniverUtil";
import { useUniverSheetTemplate } from "../hooks/useUniverSheetTemplate";
import { useSchedulingStatistics } from "../hooks/useSchedulingStatistics";
import { debounce } from "lodash-es";
const props = defineProps({
  /**
   * 记录排班数据是否改变
   */
  data: {
    type: Object as PropType<TableView>,
    required: true
  },
  /**
   * 排班表相关参数
   */
  tableParams: {
    type: Object as PropType<schedulingTableParam>,
    default: () => {}
  }
});
/**
 * 备注标记ID
 */
const remarkMarkID: number = 147;
/**
 * 周排班类型ID
 */
const schedulingTypeWeek: string = "2";
/**
 * 休假天数平铺显示
 */
const restDayTiled: string = "1";
// 双向绑定
const emits = defineEmits(["update:data", "changeEmployeeSort", "shiftSchedulingChange"]);
const shiftSchedulingTable = useVModel(props, "data", emits);
const {
  readonly,
  month,
  schedulingType,
  shiftSchedulingRecordIDs,
  noonList,
  departmentPostList,
  shiftSchedulingMarkList,
  restrictCapabilityLevel,
  oneScreenDisplay,
  restDayShowStyle,
  restMap,
  noonDutyMarkID,
  dailyStatisticsPostCondition,
  monthlyStatisticsPostCondition,
  shiftSchedulingStatisticsTable,
  employeeRemainingRestDaysDict,
  schedulingTemplateList
} = toRefs(props.tableParams);
watch(
  () => props.tableParams.restMap,
  (value) => {
    restMap.value = value;
  }
);
const { getStatisticsMonthlyData, getStatisticsMonthlyColumn } = useSchedulingStatistics();
const { getEmployeeSecondmentNoon, canScheduling, checkSeconded } = useSchedulingControl(shiftSchedulingTable.value);
const monthlyStatisticsColumns = getStatisticsMonthlyColumn(monthlyStatisticsPostCondition.value);
const univerSheetWrapRef = ref<any>();
const univerSheetRef = ref<any>();
const sheet = ref<any>();
const templateData = ref<Record<string, any> | undefined>();
const config = ref<Record<string, any>>({});
const headerRowCount: number = 2;
const columnCount =
  schedulingType.value === schedulingTypeWeek
    ? shiftSchedulingTable.value.columns.length + monthlyStatisticsColumns.length
    : shiftSchedulingTable.value.columns.length;
let rowCount: number = 0;
let statisticsStartRow: number = 0;
let initConfig: configType = {
  showHeader: !readonly.value,
  showFormula: false,
  showFooter: false,
  headerRowCount: headerRowCount,
  showRowIndexEnd: shiftSchedulingTable.value.rows.length + 1,
  headerFontColor: "#000000",
  headerBackgroundColor: "#ffef99",
  // showMenuList: [menuEnum.UNDO, menuEnum.REDO,menuEnum.COPY,menuEnum.PASTE, menuEnum.CLEAR_CONTENT],
  showMenuList: [menuEnum.CLEAR_CONTENT],
  onMoveRows: (newIndex: number, oldStartIndex: number, oldEndIndex: number) => changeEmployeeSort(newIndex, oldStartIndex, oldEndIndex),
  onBeforeCellDataChange: (cellRange: Record<string, number>, cellData: Record<string, any>) => checkCellChange(cellRange, cellData),
  onCellDataChange: (cellRange: Record<string, number>, cellData: Record<string, any>) => changeSchedulingData(cellRange, cellData),
  onCellHover: (rowIndex: number, columnIndex: number) => showTip(rowIndex, columnIndex)
};
const { initUniverConfig, tableConvertCellData, setStudentScheduling, getCellData, getSecondBackGroundColor, registerShortcutKeyEvent } =
  useSchedulingUniverUtil(
    initConfig,
    shiftSchedulingTable,
    false,
    restrictCapabilityLevel.value,
    (params: Record<string, any>) => openRemarkEdit(params),
    (templateID: string) => selectTemplate(templateID)
  );
onMounted(() => init());
let columnWidth: number = 0;
/**
 * @description: 初始化
 */
const init = () => {
  const configParams = {
    maxColumnCount: columnCount,
    noonList: noonList.value,
    departmentPostList: departmentPostList.value,
    shiftSchedulingMarkList: shiftSchedulingMarkList.value,
    schedulingTemplateList: schedulingTemplateList.value
  };
  // 初始化Univer表格配置参数
  config.value = initUniverConfig(configParams);
  getTemplateData();
  // univer组件渲染完执行
  nextTick(() => {
    univerSheetRef.value?.setCrosshairHighlightColor("#08c979", 0.25);
    sheet.value = univerSheetRef.value?.univerAPI?.getActiveWorkbook().getActiveSheet();
    // 设置特殊逻辑渲染：备注、列特殊说明、历史日期标题颜色变淡、周日分割线
    setSpecialLogicRender(true);
    // 设置只读区域
    setReadonlyRanges();
    // 设置日统计行高
    sheet.value?.setRowHeightsForced(statisticsStartRow, dailyStatisticsPostCondition.value.length, 20);
    computeDailyStatistics();
    // 如果是周排班计算月统计
    if (schedulingType.value === schedulingTypeWeek) {
      computeMonthlyStatistics();
      // 周排班 月统计标题可能会换行，这里设置下行高，避免文字显示不全
      sheet.value?.setRowHeightsForced(1, 1, 34);
    }
    registerShortcutKeyEvent(getCopyData, () => setTableValues(false, copyDatas));
  });
};
/**
 * @description: 获取模板信息
 */
const getTemplateData = async () => {
  statisticsStartRow = shiftSchedulingTable.value.rows.length + headerRowCount;
  rowCount = statisticsStartRow + (dailyStatisticsPostCondition.value.length || 0);
  // 设置合并单元格数据，标题人员和统计列
  let mergeData = [
    { startRow: 0, startColumn: 0, endRow: 0, endColumn: 1 },
    { startRow: statisticsStartRow, startColumn: 0, endRow: rowCount - 1, endColumn: 0 }
  ];
  if (schedulingType.value === schedulingTypeWeek) {
    // 月统计标题行
    mergeData.push({
      startRow: 0,
      startColumn: shiftSchedulingTable.value.columns.length,
      endRow: 0,
      endColumn: columnCount - 1
    });
    // 月统计标和日统计交叉区域
    mergeData.push({
      startRow: statisticsStartRow,
      startColumn: shiftSchedulingTable.value.columns.length,
      endRow: rowCount - 1,
      endColumn: columnCount - 1
    });
  }
  let getRestDayFun = undefined;
  if (restDayShowStyle.value === restDayTiled) {
    getRestDayFun = (rowIndex: number, columnIndex: number) => getRestDay(rowIndex, columnIndex);
  }
  const cellData = tableConvertCellData(rowCount, columnCount, noonList.value, getRestDayFun);
  const univerSheetWidth = univerSheetWrapRef.value.clientWidth;
  // 排除宽度，40：行标列宽；40：层级列宽；60：姓名列宽
  const excludeWidth = 40 + 40 + 60;
  // 动态获取(表格宽度 - excludeWidth)/ 表格最大列数 - 2(层级列和姓名列)
  columnWidth = (univerSheetWidth - excludeWidth) / (columnCount - 1.5);
  // 如果设置的不是一屏显示完且动态算的列宽小于最小宽度，取最小宽度
  if (!oneScreenDisplay.value && columnWidth < 60) {
    columnWidth = 60;
  }
  // univerSheet模板需要的参数
  let params: sheetParamType = {
    rowCount: rowCount,
    columnCount: columnCount,
    maxColumnCount: columnCount,
    headerRowCount: headerRowCount,
    defaultRowHeight: 40,
    defaultColumnWidth: columnWidth,
    headerBackgroundColor: initConfig.headerBackgroundColor,
    headerFontColor: initConfig.headerFontColor,
    cellData: cellData,
    mergeData: mergeData
  };
  templateData.value = useUniverSheetTemplate().getShiftSchedulingSheet(params);
};
let copyDatas: Record<number, Record<number, any>> = {};
/**
 * @description: 获取复制的数据
 */
const getCopyData = () => {
  const range = univerSheetRef.value.univerAPI?.getActiveWorkbook().getActiveSheet().getActiveRange();
  let copyRanges = range?.getRange();
  if (!copyRanges) {
    return;
  }
  copyDatas = {};
  const { cellDatas } = univerSheetRef.value.getSheetData();
  for (let rowIndex = copyRanges.startRow; rowIndex <= copyRanges.endRow; rowIndex++) {
    let rowData: Record<number, any> = {};
    for (let columnIndex = copyRanges.startColumn; columnIndex <= copyRanges.endColumn; columnIndex++) {
      rowData[columnIndex - copyRanges.startColumn] = cellDatas[rowIndex][columnIndex];
    }
    copyDatas[rowIndex - copyRanges.startRow] = rowData;
  }
};
/**
 * @description: 设置只读区域
 */
const setReadonlyRanges = (clearFlag?: boolean) => {
  let readonlyRanges: Record<string, number>[] = [];
  // 排班表  全部只读
  if (readonly.value) {
    readonlyRanges.push({ startRow: 0, startColumn: 0, endRow: rowCount - 1, endColumn: columnCount - 1, canMoveRow: 0 });
  } else {
    // 标题行
    readonlyRanges.push({ startRow: 0, startColumn: 0, endRow: headerRowCount - 1, endColumn: columnCount - 1, canMoveRow: 0 });
    // 人员列
    readonlyRanges.push({ startRow: 0, startColumn: 0, endRow: rowCount - 1, endColumn: 1, canMoveRow: 1 });
    // 日统计行
    readonlyRanges.push({
      startRow: statisticsStartRow,
      startColumn: 0,
      endRow: rowCount - 1,
      endColumn: columnCount - 1,
      canMoveRow: 0
    });
    let secondRanges: Record<string, number>[] = [];
    let transferRanges: Record<string, number>[] = [];
    shiftSchedulingTable.value.rows.forEach((row, rowIndex) => {
      shiftSchedulingTable.value.columns.forEach((column, columnIndex) => {
        // 借调被借调
        if (checkSeconded(rowIndex, columnIndex)) {
          secondRanges.push({
            startRow: rowIndex + headerRowCount,
            startColumn: columnIndex,
            endRow: rowIndex + headerRowCount,
            endColumn: columnIndex,
            canMoveRow: 1
          });
        }
        // 转科前排班数据
        if (row.employee.transferInDate && column.key === datetimeUtil.formatDate(row.employee.transferInDate, "yyyyMMdd")) {
          transferRanges.push({
            startRow: rowIndex + headerRowCount,
            startColumn: 2,
            endRow: rowIndex + headerRowCount,
            endColumn: columnIndex - 1
          });
        }
        // 转科后排班数据
        if (row.employee.transferOutDate && column.key === datetimeUtil.formatDate(row.employee.transferOutDate, "yyyyMMdd")) {
          transferRanges.push({
            startRow: rowIndex + headerRowCount,
            startColumn: columnIndex,
            endRow: rowIndex + headerRowCount,
            endColumn: shiftSchedulingTable.value.columns.length
          });
        }
      });
    });
    readonlyRanges = [...readonlyRanges, ...secondRanges, ...transferRanges];
    // 如果是周排班，添加月统计列
    if (schedulingType.value === schedulingTypeWeek) {
      readonlyRanges.push({
        startRow: 0,
        startColumn: shiftSchedulingTable.value.columns.length,
        endRow: rowCount - 1,
        endColumn: columnCount - 1,
        canMoveRow: 1
      });
      // 月统计和日统计交叉区域 设置权限 显示栅格背景
      const statisticsCrossoverRange = [
        {
          startRow: statisticsStartRow,
          startColumn: shiftSchedulingTable.value.columns.length,
          endRow: rowCount - 1,
          endColumn: columnCount - 1
        }
      ];
      univerSheetRef.value.setPermissionRanges(statisticsCrossoverRange, true);
    }
  }
  univerSheetRef.value.setReadonlyRanges(readonlyRanges, clearFlag);
  // 修复滚动条遮挡数据
  univerSheetRef.value.fixScrollBarPressData();
};
/**
 * @description: 检查单元格更改
 * @param cellRange
 * @param cellData
 * @return
 */
const checkCellChange = (cellRange: Record<string, number>, cellData: Record<string, any>) => {
  let isStatistics = false;
  if (cellRange.startRow >= statisticsStartRow) {
    isStatistics = true;
  }
  // 如果是周排班，判断第一行第一列的列号，如果大于月统计的开始列号 直接返回，避免无限循环
  if (schedulingType.value === schedulingTypeWeek && cellRange.startColumn >= shiftSchedulingTable.value.columns.length) {
    isStatistics = true;
  }
  if (isStatistics) {
    // 当普通单元格拖拉到统计列时
    const style = cellData[cellRange.startRow][cellRange.startColumn].s;
    if (typeof style === "string" && (style.includes("statistics-cell-style") || style.includes("header-cell-style"))) {
      return true;
    }
    return false;
  }
  return undefined;
};
/**
 * @description: 排班表发生改变
 *               数据发生变化时，会触发两次，第一次删除，第二次新增；
 *               数据删除时只触发一次删除；
 *               所以这里使用防抖，只执行最后一次，防止死循环
 * @param cellValue
 * @param cellData
 * @return
 */
const changeSchedulingData = debounce(async (cellRange: Record<string, number>, cellData: Record<string, any>) => {
  const { startRow, startColumn, endRow, endColumn } = cellRange;
  for (let index = startRow; index <= endRow; index++) {
    let rowIndex = index - headerRowCount;
    const employeeID = shiftSchedulingTable.value.rows[rowIndex]?.employee?.employeeID;
    if (!employeeID) {
      return;
    }
    for (let columnIndex = startColumn; columnIndex <= endColumn; columnIndex++) {
      const column: TableColumn | undefined = shiftSchedulingTable.value.columns.find((column) => column.index === columnIndex);
      if (!column) {
        return;
      }
      const columnData = cellData[index]?.[columnIndex];
      // 如果是拖拽复制，可能是借调/被借调半天的单元格，特殊处理
      if (!columnData.custom) {
        columnData.custom = {};
      }
      columnData.custom.noonPost = await dealSecondmentCellData(rowIndex, columnIndex, column.key, columnData);
      const oldData = common.clone(shiftSchedulingTable.value.rows[rowIndex][column.key]);
      const oldRemarkMark = oldData.markList.find((mark: any) => mark.markID === remarkMarkID);
      const newRemarkMark = columnData.custom.markList?.find((mark: any) => mark.markID === remarkMarkID);
      shiftSchedulingTable.value.rows[rowIndex][column.key].employeeID = employeeID;
      shiftSchedulingTable.value.rows[rowIndex][column.key].noonPost = columnData.custom.noonPost;
      shiftSchedulingTable.value.rows[rowIndex][column.key].markList = columnData.custom.markList;
      shiftSchedulingTable.value.rows[rowIndex][column.key].schedulingDate = column.value;
      const range = sheet.value.getRange(index, columnIndex, 1, 1);
      // 如果原来有备注，删除
      if (oldRemarkMark) {
        await range.clearComment();
      }
      // 如果新的有备注，新增
      if (newRemarkMark) {
        await range.addComment({ dataStream: newRemarkMark.markValue + "\r\n" });
      }
      // 如果是删除，要处理背景色，岗位和标记的背景删除，保留借调/被借调背景
      if (
        ((oldData?.noonPost && Object.keys(oldData?.noonPost).length) || oldData?.markList?.length || !oldData.schedulingDate) &&
        (!columnData?.custom?.noonPost || (!Object.keys(columnData?.custom?.noonPost).length && !columnData?.custom?.markList?.length))
      ) {
        const backgroundColor = getSecondBackGroundColor(
          shiftSchedulingTable.value.rows[rowIndex],
          shiftSchedulingTable.value.rows[rowIndex][column.key]
        );
        if (!backgroundColor) {
          range.setBackgroundColor("#ffffff");
        }
      }
      // 同步设置带教学生排班
      setStudentScheduling(columnData, range, sheet.value);
    }
  }
  triggerShiftSchedulingChange();
  nextTick(() => triggerShiftSchedulingChange(startRow, endRow));
}, 0);
/**
 * @description: 触发排班改变的逻辑
 * @param startRow
 * @param endRow
 * @return
 */
const triggerShiftSchedulingChange = (startRow?: number, endRow?: number) => {
  emits("shiftSchedulingChange", () => {
    nextTick(async () => {
      // 休假平铺显示 才重新赋值
      if (restDayShowStyle.value === restDayTiled && startRow && endRow) {
        // 设置休假天数
        await setRestDay(startRow, endRow);
      }
      // 计算日统计
      await computeDailyStatistics();
      // 如果是周排班计算月统计
      if (schedulingType.value === schedulingTypeWeek) {
        await computeMonthlyStatistics();
      }
    });
  });
};
/**
 * @description: 处理拖拽复制时经过的半天借调/被借单元格
 * @param rowIndex
 * @param columnIndex
 * @param columnKey
 * @param columnData
 * @return
 */
const dealSecondmentCellData = async (rowIndex: number, columnIndex: number, columnKey: string, columnData: Record<string, any>) => {
  const employeeSecondmentNoon = getEmployeeSecondmentNoon(rowIndex, columnIndex);
  const noonPost = columnData?.custom?.noonPost || {};
  // 不是借调/被借单元格，直接返回
  if (!employeeSecondmentNoon[0]) {
    return noonPost;
  }
  const oldNoonPost = shiftSchedulingTable.value.rows[rowIndex][columnKey].noonPost || {};
  let newNoonPost: Record<string, Record<string, any>> = {};
  noonList.value.forEach((noon: Record<string, any>) => {
    const flag = canScheduling(employeeSecondmentNoon, noon.value);
    newNoonPost[noon.value] = flag ? noonPost[noon.value] : oldNoonPost[noon.value];
  });
  const backgroundColor = getSecondBackGroundColor(
    shiftSchedulingTable.value.rows[rowIndex],
    shiftSchedulingTable.value.rows[rowIndex][columnKey]
  );
  const isSunday = shiftSchedulingTable.value.columns[columnIndex]?.isSunday;
  let newValue: Record<string, any> = getCellData(
    newNoonPost,
    columnData?.custom?.markList,
    noonList.value,
    undefined,
    backgroundColor,
    isSunday
  );
  nextTick(async () => {
    await sheet?.value.getRange(rowIndex + headerRowCount, columnIndex, 1, 1).setValue(newValue);
  });
  return newNoonPost;
};
/**
 * @description: 拖拽排序保存
 * @param newIndex 新下标
 * @param oldStartIndex 旧开始下标
 * @param oldEndIndex 旧结束下标
 */
const changeEmployeeSort = (newIndex: number, oldStartIndex: number, oldEndIndex: number) => {
  if (newIndex === oldStartIndex && oldStartIndex === oldEndIndex) {
    return;
  }
  // 互换位置
  const moveRows = shiftSchedulingTable.value.rows.splice(oldStartIndex - headerRowCount, oldEndIndex - oldStartIndex + 1);
  shiftSchedulingTable.value.rows.splice(newIndex - headerRowCount, 0, ...moveRows);
  // 组装顺序
  let schedulingEmployeeSortList: Record<string, any>[] = [];
  shiftSchedulingTable.value.rows.forEach((row, index) => {
    schedulingEmployeeSortList.push({
      shiftSchedulingRecordID: shiftSchedulingRecordIDs.value?.length ? shiftSchedulingRecordIDs.value[0] : undefined,
      employeeID: row.employee.employeeID,
      sort: index + 1
    });
  });
  // 如果保存过的排班，立即保存排序，否则等保存排班时再保存
  if (shiftSchedulingRecordIDs.value?.length) {
    schedulingService.updateSchedulingEmployeeSort(schedulingEmployeeSortList);
    nextTick(() => emits("changeEmployeeSort", []));
  } else {
    emits("changeEmployeeSort", schedulingEmployeeSortList);
  }
  // 因可能调整了借调人员，这里需要重新设置只读区域
  nextTick(() => setReadonlyRanges(true));
};

/**
 * @description: 计算日统计
 * @return
 */
const computeDailyStatistics = async () => {
  if (!dailyStatisticsPostCondition.value.length) {
    return;
  }
  const { rows, columns } = shiftSchedulingTable.value;
  let statisticsValues: Record<string, any>[][] = [];
  dailyStatisticsPostCondition.value.forEach((postCondition) => {
    let rowValues: Record<string, any>[] = [];
    columns.forEach((column: any, index: number) => {
      // 合计列
      if (index === 0) {
        rowValues.push({ v: "合计", s: "statistics-cell-style" });
        return;
      }
      // 条件列
      if (index === 1) {
        rowValues.push({ v: postCondition.conditionKey, s: "statistics-cell-style" });
        return;
      }
      let sum: number = 0;
      rows.forEach((row, rowIndex) => {
        const noonPost = row[column.key]?.noonPost;
        // 空值检查
        if (!noonPost) {
          return;
        }
        let employeeSecondmentNoon = getEmployeeSecondmentNoon(rowIndex, index);
        for (const noonType in noonPost) {
          if (
            canScheduling(employeeSecondmentNoon, noonType) &&
            postCondition.conditionValue.includes(noonPost[noonType].departmentPostID)
          ) {
            sum += 0.5;
          }
        }
      });
      rowValues.push({ v: sum, s: "statistics-cell-style" });
    });
    statisticsValues.push(rowValues);
  });
  await sheet.value
    ?.getRange(statisticsStartRow, 0, dailyStatisticsPostCondition.value.length, shiftSchedulingTable.value.columns.length)
    .setValues(statisticsValues);
};

/**
 * @description: 计算月统计
 */
const computeMonthlyStatistics = async () => {
  const monthlyStatisticsData = getStatisticsMonthlyData(
    month.value,
    shiftSchedulingStatisticsTable.value,
    employeeRemainingRestDaysDict.value,
    monthlyStatisticsPostCondition.value,
    noonDutyMarkID.value
  );
  if (!monthlyStatisticsData?.length) {
    return;
  }
  let statisticsValues: Record<string, any>[][] = [];
  let statisticsTitleData: Record<string, any>[][] = [[], []];
  shiftSchedulingTable.value.rows.forEach((row) => {
    const rowData = monthlyStatisticsData.find((data) => data.employeeID === row.employee.employeeID) || {};
    let rowValues: Record<string, any>[] = [];
    monthlyStatisticsColumns.forEach((statisticsColumn, index) => {
      if (!statisticsTitleData[0][index]) {
        statisticsTitleData[0][index] = { v: `排班岗位月统计（${month.value}月）`, s: "header-cell-style" };
      }
      if (!statisticsTitleData[1][index]) {
        statisticsTitleData[1][index] = { v: statisticsColumn.name, s: "header-cell-style" };
      }
      rowValues.push({ v: rowData[statisticsColumn.key], s: "monthly-statistics-cell-style" });
    });
    statisticsValues.push(rowValues);
  });
  statisticsValues = [...statisticsTitleData, ...statisticsValues];
  await sheet.value
    ?.getRange(0, shiftSchedulingTable.value.columns.length, statisticsStartRow, monthlyStatisticsColumns.length)
    .setValues(statisticsValues);
};
// #region 休假天数计算逻辑
/**
 * @description: 设置休假天数
 */
const setRestDay = async (startRow: number, endRow: number) => {
  for (let index = startRow; index <= endRow; index++) {
    let rowIndex = index - headerRowCount;
    shiftSchedulingTable.value.columns.forEach(async (column, columnIndex) => {
      // 跳过人员列
      if (columnIndex < 2) {
        return;
      }
      const cellData: Record<string, any> = shiftSchedulingTable.value.rows[rowIndex][column.key];
      const restDay = getRestDay(rowIndex, columnIndex);
      const backgroundColor = getSecondBackGroundColor(
        shiftSchedulingTable.value.rows[rowIndex],
        shiftSchedulingTable.value.rows[rowIndex][column.key]
      );
      const isSunday = shiftSchedulingTable.value.columns[columnIndex]?.isSunday;
      let newCellData: Record<string, any> = getCellData(
        cellData.noonPost,
        cellData.markList,
        noonList.value,
        restDay as number,
        backgroundColor,
        isSunday
      );
      await sheet?.value.getRange(index, columnIndex, 1, 1).setValue(newCellData);
    });
  }
};
/**
 * @description: 获取排班单元格本月第几天休息
 * @param row
 * @param columnKey
 * @param isHover 是否为鼠标悬浮
 * @return
 */
const getRestDay = (rowIndex: number, columnIndex: number, isHover: boolean = false) => {
  let restDay: number | string | undefined;
  const column: TableColumn | undefined = shiftSchedulingTable.value.columns.find((column) => column.index === columnIndex);
  if (!column) {
    return restDay;
  }
  if (!restMap.value?.size) {
    return restDay;
  }
  const employeeID = shiftSchedulingTable.value.rows[rowIndex]?.employee?.employeeID;
  if (!employeeID) {
    return restDay;
  }
  const map = restMap.value.get(employeeID);
  if (map?.size) {
    const counts: Record<string, number> = map.get(column.key) || {};
    if (isHover) {
      restDay = counts.maternityCount ? `<br/>第${counts.maternityCount}天产假` : "";
      restDay += counts.annualCount ? `<br/>第${counts.annualCount}天年休假` : "";
      restDay += counts.count ? `<br/>本月第${counts.count}天休假` : "";
      // 去除最前面的<br/>
      restDay && (restDay = restDay.replace("<br/>", ""));
    } else {
      restDay = counts.maternityCount || counts.annualCount || counts.count || undefined;
    }
  }
  return restDay;
};
// #endregion

/**
 * @description: 设置特殊逻辑渲染
 * @param initRemarkFlag 是否初始化备注
 * @return
 */
const setSpecialLogicRender = (initRemarkFlag: boolean) => {
  shiftSchedulingTable.value.rows.forEach((row, rowIndex) => {
    shiftSchedulingTable.value.columns.forEach(async (column, columnIndex) => {
      // 初始化备注
      if (initRemarkFlag) {
        initCellRemark(rowIndex, columnIndex, column);
      }
      // 今天之前的列标题颜色变淡
      if (column.key < datetimeUtil.getNowDate("yyyyMMdd")) {
        sheet.value?.getRange(0, columnIndex, headerRowCount, 1).setBackgroundColor("#f4edcb");
      }
      // 有特殊说明的列标题字体颜色标记为红色
      if (column.specialContent) {
        sheet.value?.getRange(0, columnIndex, headerRowCount, 1).setFontColor("#ff0000");
      }
      // 设置标题的周日分割线
      setTitleSundayDivider(rowIndex, columnIndex, column);
    });
  });
};
/**
 * @description: 设置标题的周日分割线
 * @param rowIndex
 * @param columnIndex
 * @param column
 * @return
 */
const setTitleSundayDivider = async (rowIndex: number, columnIndex: number, column: TableColumn) => {
  if (!column.isSunday) {
    return;
  }
  for (let index = 0; index < headerRowCount; index++) {
    const sundayColumnRange = sheet.value?.getRange(index, columnIndex, 1, 1);
    if (sundayColumnRange) {
      const data = sundayColumnRange.getCellData();
      const style = common.clone(sundayColumnRange.getCellStyleData());
      style.bd.r.s = 13;
      data.s = style;
      nextTick(() => sundayColumnRange.setValue(data));
    }
  }
};

// #region 排班备注相关逻辑
/**
 * @description: 初始化单元格备注
 * @param rowIndex
 * @param columnIndex
 * @param column
 * @return
 */
const initCellRemark = (rowIndex: number, columnIndex: number, column: TableColumn) => {
  // 跳过人员列
  if (columnIndex < 2) {
    return;
  }
  const markList: Record<string, any>[] = shiftSchedulingTable.value.rows[rowIndex][column.key].markList;
  // 跳过无标记
  if (!markList?.length) {
    return;
  }
  const remark = markList.find((mark) => mark.markID === remarkMarkID);
  // 没有备注 跳过
  if (!remark || !remark.markValue) {
    return;
  }
  const range = sheet?.value.getRange(rowIndex + headerRowCount, columnIndex, 1, 1);
  range.addComment({
    dataStream: remark.markValue + "\r\n"
  });
};

const remarkParams = ref<Record<string, any>>({});
/**
 * @description: 打开备注编辑框
 * @param params
 * @return
 */
const openRemarkEdit = (params: Record<string, any>) => {
  remarkParams.value = params;
  remarkParams.value.title = `${params.employeeName}${params.date}排班备注`;
  const markList: Record<string, any>[] =
    shiftSchedulingTable.value.rows[remarkParams.value.rowIndex][remarkParams.value.columnKey].markList;
  const remark = markList.find((mark) => mark.markID === remarkParams.value.markID);
  remarkParams.value.remark = remark?.markValue || "";
  remarkParams.value.show = true;
};
/**
 * @description: 设置备注
 */
const setCellRemark = async () => {
  const remarkMark = {
    markID: remarkParams.value.markID,
    markValue: remarkParams.value.remark
  };
  const range = sheet?.value.getRange(remarkParams.value.rowIndex + headerRowCount, remarkParams.value.columnIndex, 1, 1);
  let markList: Record<string, any>[] =
    shiftSchedulingTable.value.rows[remarkParams.value.rowIndex][remarkParams.value.columnKey].markList || [];
  if (markList?.length) {
    const index = markList.findIndex((mark) => mark.markID === remarkParams.value.markID);
    if (index !== -1) {
      // 如果值为空移除备注
      if (!remarkParams.value.remark) {
        markList.splice(index, 1);
        await range.clearComment();
      } else {
        // 如果原来就有备注，更新，否则新增
        if (markList[index].markValue) {
          const comment = range.getComment();
          await comment?.update({
            dataStream: remarkMark.markValue + "\r\n"
          });
        } else {
          await range.addComment({
            dataStream: remarkMark.markValue + "\r\n"
          });
        }
        markList[index].markValue = remarkMark.markValue;
      }
    } else {
      markList.push(remarkMark);
      await range.addComment({
        dataStream: remarkMark.markValue + "\r\n"
      });
    }
  } else {
    markList.push(remarkMark);
    await range.addComment({
      dataStream: remarkMark.markValue + "\r\n"
    });
  }
  shiftSchedulingTable.value.rows[remarkParams.value.rowIndex][remarkParams.value.columnKey].markList = markList;
  remarkParams.value.show = false;
};
// #endregion

// #region 排班模板相关逻辑
const schedulingTemplateRef = ref<any>();
const showTemplateDialog = ref<boolean>(false);
const templateParams = ref<Record<string, any>>({ selectionMode: true });
const selectTemplate = (templateID: string) => {
  templateParams.value.noonList = noonList;
  templateParams.value.departmentPostList = departmentPostList;
  templateParams.value.shiftSchedulingMarkList = shiftSchedulingMarkList;
  templateParams.value.schedulingTemplateRecordID = templateID;
  showTemplateDialog.value = true;
};
/**
 * @description: 将排班摸模板数据应用到排班表
 */
const setTableValues = (isTemplate: boolean, datas?: Record<number, Record<number, any>>) => {
  const range = sheet.value?.getActiveRange();
  if (!range) {
    return;
  }
  const { startRow, startColumn } = range.getRange();
  let cellDatas: any = datas;
  if (isTemplate) {
    cellDatas = schedulingTemplateRef.value.getSheetData();
  }
  if (!cellDatas) {
    return;
  }
  let newCellDatas: Record<number, Record<number, any>> = {};
  Object.keys(cellDatas).forEach((rowIndex) => {
    const newRowIndex = startRow + Number(rowIndex);
    // 大于排班表行数 跳过
    if (newRowIndex - headerRowCount >= shiftSchedulingTable.value.rows.length) {
      return;
    }
    const rowData = cellDatas[rowIndex];
    let newRowData: Record<number, any> = {};
    // 循环列
    Object.keys(rowData).forEach((columnIndex) => {
      const newColumnIndex = startColumn + Number(columnIndex);
      // 大于排班表列数 跳过
      if (newColumnIndex >= shiftSchedulingTable.value.columns.length) {
        return;
      }
      newRowData[newColumnIndex] = rowData[columnIndex];
    });
    newCellDatas[newRowIndex] = newRowData;
  });
  sheet?.value.getRange(startRow, startColumn, Object.keys(newCellDatas).length, Object.keys(newCellDatas).length).setValues(newCellDatas);
  if (isTemplate) {
    showTemplateDialog.value = false;
  }
};
// #endregion
/**
 * @description: 显示提示信息
 * @param rowIndex
 * @param columnIndex
 * @return
 */
const showTip = (rowIndex: number, columnIndex: number) => {
  // 跳过日统计行并关闭提示
  if (rowIndex >= statisticsStartRow) {
    univerSheetRef.value?.hiddenPopupMessage();
    return;
  }
  // 如果是标题行判断有没有特殊说明，有显示，没有返回
  if (rowIndex < headerRowCount && columnIndex < shiftSchedulingTable.value.columns.length) {
    const specialContent = shiftSchedulingTable.value.columns[columnIndex].specialContent;
    if (specialContent) {
      univerSheetRef.value?.showPopupMessage(rowIndex, columnIndex, specialContent);
    } else {
      univerSheetRef.value?.hiddenPopupMessage();
    }
    return;
  }
  // 非人员列行依据配置显示休假天数或关闭提示
  if (columnIndex >= 2) {
    if (restDayShowStyle.value === restDayTiled) {
      univerSheetRef.value?.hiddenPopupMessage();
      return;
    }
    const restDay = getRestDay(rowIndex - headerRowCount, columnIndex, true);
    if (restDay) {
      univerSheetRef.value?.showPopupMessage(rowIndex, columnIndex, restDay);
    } else {
      univerSheetRef.value?.hiddenPopupMessage();
    }
    return;
  }
  const employee = shiftSchedulingTable.value.rows[rowIndex - headerRowCount].employee;
  // 没有提示信息 就不显示
  if (!employee.tipContent) {
    univerSheetRef.value?.hiddenPopupMessage();
    return;
  }
  univerSheetRef.value?.showPopupMessage(rowIndex, columnIndex, employee.tipContent);
};
</script>

<style lang="scss">
.scheduling-table {
  height: 100%;
  width: 100%;
  .univer-sheet-wrap {
    height: 100%;
    width: 100%;
  }
}
.remark-edit.el-dialog {
  margin-top: 20% !important;
  width: 700px;
  height: 270px;
  .label {
    margin: 0 0 10px 5px;
  }
}
.template-select.el-dialog {
  .template-wrap {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px;
    box-sizing: border-box;
    .tip {
      color: #ff0000;
      margin: 0 10px 15px 10px;
    }
    .scheduling-template-univer {
      flex: auto;
    }
  }
}
</style>
