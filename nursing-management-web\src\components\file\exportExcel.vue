<!--
 * FilePath     : \src\components\file\exportExcel.vue
 * Author       : 郭鹏超
 * Date         : 2023-10-17 14:56
 * LastEditors  : 张现忠
 * LastEditTime : 2025-04-03 09:30
 * Description  : table数据导入excel文件
 * CodeIterationRecord:
-->
<template>
  <div class="export-excel" @click="exportExcelTable">
    <slot name="default">
      <el-button class="print-button">{{ exportExcelViews[0]?.buttonName ?? "生成Excel文件" }}</el-button>
    </slot>
  </div>
</template>
<script lang="ts" setup>
import * as XLSX from "xlsx";
const props = defineProps<{
  exportExcelOption: Array<ExportExcelView> | (() => Promise<ExportExcelView[]>);
}>();
const exportExcelViews = ref<ExportExcelView[]>([]);
/**
 * description: 数据生成Excel
 * return {*}
 */
const exportExcelTable = () => {
  if (!exportExcelViews.value.length) {
    return;
  }
  const wb = XLSX.utils.book_new();
  exportExcelViews.value.forEach((option) => {
    let arr: any = [];
    const titleArr = Object.values(option.columnData);
    const fieldArr = Object.keys(option.columnData);
    arr = option.tableData.map((obj) => {
      return fieldArr.map((field) => {
        return obj[field];
      });
    });
    arr.unshift(titleArr);
    const ws = XLSX.utils.aoa_to_sheet(arr);
    XLSX.utils.book_append_sheet(wb, ws, option.sheetName);
  });
  const fileName = exportExcelViews.value[0].fileName + ".xlsx";
  XLSX.writeFile(wb, fileName);
};

onMounted(async () => {
  exportExcelViews.value = typeof props.exportExcelOption === "function" ? await props.exportExcelOption() : props.exportExcelOption;
});
watch(
  () => props.exportExcelOption,
  async (excelOption) => {
    exportExcelViews.value = typeof excelOption === "function" ? await excelOption() : excelOption;
  }
);
</script>
<style lang="scss">
.export-excel {
  display: inline-block;
}
</style>
