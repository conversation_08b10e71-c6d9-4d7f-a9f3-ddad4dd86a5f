/*
 * FilePath     : \src\api\conditionService.ts
 * Author       : 来江禹
 * Date         : 2024-08-22 15:10
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-31 17:12
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class conditionService {
  private static controllerBaseUrl = "Condition";
  // 获取选择人员条件配置数据
  private static getConditionSelectComponentApi: string = this.controllerBaseUrl + "/GetConditionSelectComponent";
  // 保存条件内容
  private static saveConditionDataApi: string = this.controllerBaseUrl + "/SaveConditionData";
  // 获取规则集合数据
  private static getRuleListByTypeApi: string = this.controllerBaseUrl + "/GetRuleListByType";
  // 删除规则
  private static deleteRuleApi: string = this.controllerBaseUrl + "/DeleteRule";
  // 保存规则及明细数据
  private static saveRuleApi: string = this.controllerBaseUrl + "/SaveRule";
  // 获取条件表格格式数据
  private static getConditionTableFormatApi: string = this.controllerBaseUrl + "/GetConditionTableFormat";
  // 获取所有考核组卷规则记录
  private static getExaminationConditionRecordApi: string = this.controllerBaseUrl + "/GetExaminationConditionRecord";
  // 删除考核组卷规则记录
  private static deleteExaminationConditionRecordApi: string = this.controllerBaseUrl + "/DeleteExaminationConditionRecord";
  // 保存考核组卷规则记录
  private static saveExaminationConditionRecordApi: string = this.controllerBaseUrl + "/SaveExaminationConditionRecord";
  // 获取考核组卷规则条件详情数据
  private static getConditionDetailViewApi: string = this.controllerBaseUrl + "/GetConditionDetailView";
  // 根据试卷ID获取组卷规则明细数据
  private static getExaminationConditionEditViewApi: string = this.controllerBaseUrl + "/GetExaminationConditionEditView";
  /**
   * @description: 获取选择人员条件配置数据
   * @param params
   * @return
   */
  public static async getConditionSelectComponent(params: any): Promise<any> {
    return await http.get(this.getConditionSelectComponentApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存条件内容
   * @param params
   * @return
   */
  public static async saveConditionData(params: any): Promise<any> {
    return await http.post(this.saveConditionDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取规则集合数据
   * @param params
   * @return
   */
  public static async getRuleListByType(params: any): Promise<any> {
    return await http.get(this.getRuleListByTypeApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除规则
   * @param params
   * @return
   */
  public static async deleteRule(params: any): Promise<any> {
    return await http.post(this.deleteRuleApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存规则及明细数据
   * @param params
   * @return
   */
  public static async saveRule(params: any): Promise<any> {
    return await http.post(this.saveRuleApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取条件表格格式数据
   * @param params
   * @return
   */
  public static async getConditionTableFormat(params: any): Promise<any> {
    return await http.get(this.getConditionTableFormatApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取所有考核组卷规则记录
   * @param params
   * @returns
   */
  public static async getExaminationConditionRecord(params?: any): Promise<any> {
    return await http.get(this.getExaminationConditionRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除考核组卷规则记录
   * @param params
   * @returns
   */
  public static async deleteExaminationConditionRecord(params: any): Promise<any> {
    return await http.post(this.deleteExaminationConditionRecordApi, params, { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存考核组卷规则记录
   * @param params
   * @returns
   */
  public static async saveExaminationConditionRecord(params: any): Promise<any> {
    return await http.post(this.saveExaminationConditionRecordApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取考核组卷规则条件详情数据
   * @param params
   * @returns
   */
  public static async getConditionDetailView(params: any): Promise<any> {
    return await http.get(this.getConditionDetailViewApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description:  根据试卷ID获取考核计划
   * @param params 试卷ID
   * @returns
   */
  public static getExaminationConditionEditView(params: any) {
    return http.get(this.getExaminationConditionEditViewApi, params, { loadingText: Loading.LOAD });
  }
}
