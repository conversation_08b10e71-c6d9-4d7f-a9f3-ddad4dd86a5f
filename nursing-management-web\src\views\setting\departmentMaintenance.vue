<!--
 * FilePath     : \src\views\setting\departmentMaintenance.vue
 * Author       : 胡长攀
 * Date         : 2023-10-24 11:33
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:03
 * Description  : 部门对照维护
 -->

<template>
  <base-layout class="department-maintenance">
    <template #header>
      <el-select v-model="organizationType" class="department-maintenance-selector" @change="getDepartmentList">
        <el-option v-for="item in organizationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-permission:B="2" type="primary" class="right-button" @click="bachSave">保存</el-button>
      <el-button v-permission:B="1" class="add-button" @click="addDepartment">新增</el-button>
    </template>
    <el-table
      ref="departmentListViewsTable"
      @row-click="clickTable($event)"
      :data="departmentListViews"
      row-key="managementDepartmentID"
      border
      default-expand-all
    >
      <el-table-column type="selection" align="center" />
      <el-table-column :label="tableColumnLabel">
        <template #default="scope">
          <el-input
            v-if="scope.row.isEdit"
            class="table-input"
            v-model="scope.row.managementDepartment"
            @click.stop=""
            @blur="selectRow(scope.row)"
          ></el-input>
          <span v-else>{{ scope.row.managementDepartment }}</span>
        </template>
      </el-table-column>

      <el-table-column label="人事组织" v-if="organizationType === '1'">
        <template #default="scope">
          <el-select
            v-if="scope.row.isEdit"
            class="table-select"
            v-model="scope.row.hrDepartmentID"
            filterable
            multiple
            clearable
            placeholder="请选择部门"
            @change="changeHRDepartment(scope.row)"
          >
            <el-option v-for="item in departmentListOptions" :key="item.key" :label="item.value" :value="item.key" />
          </el-select>
          <span v-else v-html="scope.row.hrDepartment"></span>
        </template>
      </el-table-column>
      <el-table-column label="状态" :width="convertPX(120)" align="center">
        <template #default="scope">
          <el-switch
            v-if="!scope.row.isEdit || scope.row.managementDepartmentID !== 0"
            v-model="scope.row.isActive"
            inline-prompt
            @click.stop="enableOrDisableData(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(200)" fixed="right">
        <template #default="scope">
          <el-tooltip v-if="scope.row.managementDepartmentID !== 0" content="新增">
            <i v-permission:B="1" class="iconfont icon-add" @click.stop="addNewRow(scope.row)" />
          </el-tooltip>
          <el-tooltip content="修改">
            <i v-permission:B="3" class="iconfont icon-edit" @click.stop="updateRow(scope.row)" />
          </el-tooltip>
          <el-tooltip content="保存">
            <i v-permission:B="2" class="iconfont icon-save" @click.stop="saveSingleRow(scope.row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>
<script setup lang="ts">
import type { departmentMaintenanceView } from "./types/departmentMaintenanceView";
const departmentListViewsTable = ref();
const convertPX: any = inject("convertPX");
let departmentListViews = ref<departmentMaintenanceView[]>([]);
let copyDepartmentListViews = ref<departmentMaintenanceView[]>([]);
let departmentListOptions = ref();
// 组织架构,默认护理管理组织
const organizationType = ref("1");
// 组织架构
const organizationTypeOptions = [
  {
    value: "1",
    label: "护理管理组织"
  },
  {
    value: "2",
    label: "委员会"
  }
];
// 表格列标题
const tableColumnLabel = computed(() => {
  return organizationTypeOptions.filter((item) => {
    return item.value === organizationType.value;
  })[0].label;
});
// 定义一个默认行，新增使用
const currRow: departmentMaintenanceView = {
  managementDepartment: "",
  managementDepartmentID: 0,
  level: 0,
  hrDepartmentID: [],
  hrDepartment: "",
  upperLevelDepartmentID: 0,
  children: [],
  isEdit: true,
  isActive: true,
  organizationType: ""
};
onMounted(async () => {
  // 初始化
  await getDepartmentList();
});
/**
 * @description: 获取部门对应关系列表
 */
const getDepartmentList = async () => {
  await departmentMaintenanceService.getDepartmentVSDepartmentListView({ organizationType: organizationType.value }).then((data: any) => {
    if (data) {
      departmentListViews.value = data;
      copyDepartmentListViews.value = common.clone(departmentListViews.value);
    }
  });
};
/**
 * @description: 添加一级部门
 */
const addDepartment = () => {
  let newRow = common.clone(currRow);
  newRow.level = 1;
  newRow.organizationType = organizationType.value;
  departmentListViews.value.unshift(newRow);
  selectRow(newRow);
};
/**
 * @description: 子集添加部门
 */
const addNewRow = (row: departmentMaintenanceView) => {
  const rawRow = toRaw(row);
  let newRow = common.clone(currRow);
  newRow.level = row.level + 1;
  newRow.organizationType = organizationType.value;
  newRow.upperLevelDepartmentID = row.managementDepartmentID;
  /**
   * ref响应式，每次修改都会被追踪,都会渲染,
   * 通过toRaw方法拿到它的原始数据,对原始数据进行修改，不会被追踪,页面就不会渲染,
   * 这里在子节点为0的时候，对于row的子节点进行插入和删除操作，使其能够被追踪到，从而使前端渲染正常
   **/
  if (rawRow.children.length === 0) {
    row.children.unshift(newRow);
    row.children.splice(0, 1);
  }
  rawRow.children.unshift(newRow);
  selectRow(newRow);
  clickTable(rawRow, true);
};
/**
 * @description: 更新当前行状态
 */
const updateRow = (row: departmentMaintenanceView) => {
  if (row.managementDepartmentID === 0) {
    return;
  }
  // 修改该行的编辑状态
  row.isEdit = !row.isEdit;
};
/**
 * @description: 启用或停用当前部门
 */
const enableOrDisableData = async (row: departmentMaintenanceView) => {
  let showTipMessage = row.isActive ? "启用" : "停用";
  if (row.managementDepartmentID === 0) {
    return;
  }
  await confirmBox("是否" + showTipMessage + "该部门记录？", showTipMessage + "部门记录", (flag: Boolean) => {
    if (flag) {
      departmentMaintenanceService.enableOrDisableDepartment(row).then((data: any) => {
        if (data) {
          showMessage("success", showTipMessage + "成功");
          getDepartmentList();
        }
      });
    } else {
      row.isActive = !row.isActive;
    }
  });
};
/**
 * @description: 检核当前数据中是否已经存在该部门
 */
const checkRow = (row: departmentMaintenanceView) => {
  let result = false;
  const checkCallbackFunction = (views: departmentMaintenanceView[]) => {
    for (let index = 0; index < views.length; index++) {
      const view = views[index];
      if (result) {
        return result;
      }
      if (row.managementDepartment === view.managementDepartment && row.managementDepartmentID !== view.managementDepartmentID) {
        showMessage("warning", "已存在：" + row.managementDepartment);
        result = true;
        return result;
      }
      checkCallbackFunction(view.children);
    }
  };
  checkCallbackFunction(copyDepartmentListViews.value);
  return result;
};
/**
 * @description: 保存单行数据
 */
const saveSingleRow = async (row: departmentMaintenanceView) => {
  if (!row.managementDepartment) {
    showMessage("warning", "请填写护理管理组织名称！");
    return;
  }
  if (checkRow(row)) {
    return;
  }
  await departmentMaintenanceService.saveDepartmentVSDepartmentListView(row).then((data: any) => {
    if (data) {
      row.isEdit = false;
      showMessage("success", "保存成功");
      getDepartmentList();
    }
  });
};
/**
 * @description: 批量保存数据
 */
const bachSave = async () => {
  if (!departmentListViewsTable.value) {
    return;
  }
  let selectRows: departmentMaintenanceView[] = departmentListViewsTable.value.getSelectionRows();
  if (!selectRows || selectRows.length <= 0) {
    showMessage("warning", "请勾选您要保存的数据");
    return;
  }
  for (let index = 0; index < selectRows.length; index++) {
    // 必填项检查
    if (!selectRows[index].managementDepartment) {
      showMessage("warning", "请填写护理管理组织名称！");
      return;
    }
    if (checkRow(selectRows[index])) {
      return;
    }
  }
  // 保存请求
  await departmentMaintenanceService.saveDepartmentVSDepartmentListViews(selectRows).then((data: any) => {
    if (data) {
      selectRows.forEach((row) => (row.isEdit = false));
      departmentListViewsTable.value.clearSelection();
      showMessage("success", "保存成功");
    }
  });
  getDepartmentList();
};
/**
 * @description: 选中行
 */
const selectRow = (row: departmentMaintenanceView) => {
  nextTick(() => {
    if (departmentListViewsTable.value) {
      departmentListViewsTable.value.toggleRowSelection(row, true);
      if (row.children?.length) {
        row.children.forEach((child) => {
          // 可编辑状态的子节点复选框不取消勾选
          if (!child.isEdit) {
            departmentListViewsTable.value.toggleRowSelection(child, false);
          }
        });
      }
    }
  });
};
/**
 * @description: 展开行
 */
const clickTable = (row: any, flag?: boolean) => {
  departmentListViewsTable.value?.toggleRowExpansion(row, flag);
};
/**
 * @description: 获取部门数据
 */
dictionaryService.getHrmDepartmentDict().then((datas: any) => {
  departmentListOptions.value = datas ?? [];
});
/**
 * @description: 修改行的HR部门
 */
const changeHRDepartment = (row: departmentMaintenanceView) => {
  row.hrDepartment = "";
  if (!row.hrDepartmentID) {
    return;
  }
  // 获取HR部门名称
  if (row.hrDepartmentID) {
    for (let index = 0; index < row.hrDepartmentID.length; index++) {
      const department = departmentListOptions.value.find((option: any) => option.key === row.hrDepartmentID[index]);
      if (department) {
        row.hrDepartment += `${row.hrDepartment?.length === 0 ? "" : "，"}${department?.value}`;
      }
    }
  }
  selectRow(row);
};
</script>
<style lang="scss">
.department-maintenance {
  .table-input {
    width: 200px;
  }
  .table-select {
    width: 400px;
  }
  .department-maintenance-selector {
    width: 200px;
  }
}
</style>
