/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanEditDialog.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-27 15:01
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-06 19:17
 * Description  : 年度计划编辑弹窗状态与行为
 * CodeIterationRecord: 
 */
import type { planIndicator, planProject } from "../../types/annualPlanMain";

export function useAnnualPlanEditDialog() {
  const dialogTitle = computed(() => {
    if (!originalDetail.value) {
      return "";
    }
    return `${editingDetailType.value === "indicator" ? "策略指标" : "目标任务"}编辑`;
  });
  const dialogVisible = ref<boolean>(false);
  const originalDetail = ref<Partial<planIndicator> | Partial<planProject> | undefined>(undefined);
  const editingDetail = ref<Partial<planIndicator> | Partial<planProject> | undefined>(undefined);
  const editingDetailType = ref<"indicator" | "project" | "">("");
  return {
    dialogTitle,
    dialogVisible,
    originalDetail,
    editingDetail,
    editingDetailType,
    /**
     * @description: 打开弹窗
     * @param detailType 明细类型
     * @param detail 明细值
     * @return 
     */
    openDialog: (detailType: "indicator" | "project", detail: Partial<planIndicator> | Partial<planProject>) => {
      editingDetailType.value = detailType;
      originalDetail.value = detail;
      editingDetail.value = common.clone(detail);
      dialogVisible.value = true;
    },
    /**
     * @description: 关闭弹窗
     */
    closeDialog: () => {
      editingDetailType.value = "";
      originalDetail.value = undefined;
      editingDetail.value = undefined;
      dialogVisible.value = false;
    },
    /**
     * @description: 更新原值
     * @param detail 新的明细
     * @return 
     */
    updateOriginalDetail: (detail: Partial<planIndicator> | Partial<planProject>) => {
      if (!originalDetail.value) {
        return;
      }
      Object.assign(originalDetail.value, detail);
    }
  };
}
