$table-hover-bg: lighten($base-color, 40%);
$table-header-bg: #ffef99;
.el-table.el-table--striped tr.el-table__row--striped.current-row td,
.el-table .el-table__body tr.current-row td {
  background-color: $table-hover-bg !important;
}
/* 解决冻结列时滚动条无法拖动问题 */
.el-table--scrollable-x {
  .el-table__fixed-right,
  .el-table__fixed {
    height: calc(100% - 17px) !important;
  }
}
.el-table.el-table--striped tr.el-table__row--striped:hover td,
.el-table .el-table__body tr:hover > td {
  background-color: $table-hover-bg !important;
}
.el-table {
  border: 1px solid $border-color;
  .el-table__empty-block {
    width: 100% !important;
    margin: -1px 0 !important;
  }
  tbody:focus {
    outline: none;
  }
  /* 防止表格错位 */
  th.gutter {
    display: table-cell !important;
  }
  colgroup.gutter {
    display: table-cell !important;
  }
  th,
  td {
    border-color: $border-color !important;
  }
  th,
  th.is-leaf,
  .el-table__row td {
    padding: 3px;
    box-sizing: border-box;
  }
  th {
    color: #000000;
    &.el-table__cell {
      background-color: $table-header-bg !important;
      color: #000000;
      .cell {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        line-height: 24px;
        font-size: 16px;
      }
    }
  }
  td .cell {
    font-size: 14px;
  }
  .cell {
    padding: 1px 4px !important;
    box-sizing: border-box;
    color: #000000;
    .el-table__column-filter-trigger {
      line-height: 32px;
      margin-left: 3px;
    }
    .caret-wrapper {
      height: 32px;
      .sort-caret {
        &.ascending {
          top: 0;
        }
        &.descending {
          bottom: 0;
        }
      }
    }
    .el-date-editor {
      .el-input__prefix {
        display: none;
      }
      .el-input__inner {
        padding-left: 5px;
        padding-right: 2px;
      }
    }
  }

  /* 
  双层表头样式
  2020-1-5 李青原
  */
  thead.is-group th {
    background: $table-header-bg !important;
  }

  /*
  嵌套表格样式
  */
  .el-table__expanded-cell {
    .el-table__header-wrapper {
      th {
        background-color: #f1f1f6;
      }
    }
  }
}
.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #f4f1f1;
}
//表格头居中
.el-table .el-table__header .el-table__cell {
  text-align: center !important;
}
.el-table .el-table__expand-icon {
  margin-right: 10px !important;
  &.el-table__expand-icon--expanded {
    margin-right: 5px !important;
    margin-left: 6px !important;
  }
  .el-icon {
    font-size: 20px;
    color: #ff0000;
  }
}
.el-table .el-table__column-filter-trigger .el-icon {
  margin-left: 5px;
  font-size: 20px;
  color: #ff0000;
}
.el-table-filter {
  .el-table-filter__bottom {
    button {
      color: $base-color;
      &:nth-child(2) {
        color: #ff0000;
      }
    }
  }
}
