/*
 * FilePath     : \src\views\approveManagement\approveRecord\hooks\useApproveDynamicTable.ts
 * Author       : 张现忠
 * Date         : 2024-05-15 14:59
 * LastEditors  : 张现忠
 * LastEditTime : 2024-05-25 10:33
 * Description  :
 * CodeIterationRecord:
 */

import { dynamicTableSettingService } from "@/api/dynamicTableSettingService";
export const useApproveDynamicTable = () => {
  const tableHeaderList = ref<any>([]);
  return {
    tableHeaderList,
    /**
     * @description: 获取动态表头数据
     * @return
     */
    async getTableHeaderList(tableSubType:string ) {
      let params = {
        tableType: "proveCategory",
        tableSubType: tableSubType
      };
      // tableHeaderList.value = [];
      await dynamicTableSettingService.GetDynamicTableHeader(params).then((res:any) => {
        res && (tableHeaderList.value = res || []);
      });
    }
  };
};
