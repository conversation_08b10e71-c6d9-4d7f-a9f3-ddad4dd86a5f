/*
 * relative     : \nursing-management-web\src\views\trainingManagement\hooks\useDrawerToggle.ts
 * Author       : 张现忠
 * Date         : 2025-01-05 14:59
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-05 15:35
 * Description  : 抽屉开关控制
 * CodeIterationRecord:
 */
// eslint-disable-next-line id-match
import type { Ref } from "vue";

export function useDrawerToggle(drawerOptions: Ref<DrawerOptions>) {
  // #region 类型定义
  type courseDrawerName = "filePreview" | "addModifyCourse" | "classifyCourse";
  type trainingDrawerName = "filePreview" | "addModifyTrainingRecord";
  // #endregion

  // 备份初始弹窗配置
  let backInitDrawerOptions: DrawerOptions;
  backInitDrawerOptions = common.clone(drawerOptions.value);
  return {
    /**
     * 还原初始弹窗配置
     */
    restoreDrawerOptions: () => {
      drawerOptions.value.showCancel = backInitDrawerOptions.showCancel ?? true;
      drawerOptions.value.showConfirm = backInitDrawerOptions.showConfirm ?? true;
      return true;
    },
    /**
     * @description: 弹窗开关统一开关方法
     */
    toggleDrawer: (
      drawerName: courseDrawerName | trainingDrawerName,
      showDrawer: boolean,
      drawerSize?: string,
      drawerTitle?: string,
      showCancel: boolean = true,
      showConfirm: boolean = true
    ) => {
      drawerOptions.value.drawerName = drawerName;
      drawerOptions.value.showDrawer = showDrawer;
      drawerSize && (drawerOptions.value.drawerSize = drawerSize);
      drawerTitle && (drawerOptions.value.drawerTitle = drawerTitle);
      drawerOptions.value.showCancel = showCancel;
      drawerOptions.value.showConfirm = showConfirm;
    }
  };
}
