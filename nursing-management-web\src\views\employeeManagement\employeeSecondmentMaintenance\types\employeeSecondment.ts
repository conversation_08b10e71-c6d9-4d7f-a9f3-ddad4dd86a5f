/*
 * FilePath     : \src\views\employeeManagement\employeeSecondmentMaintenance\types\employeeSecondment.ts
 * Author       : 来江禹
 * Date         : 2023-11-06 11:41
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:37
 * Description  : 人员借调数据类型
 * CodeIterationRecord:
 */
/**
 * 人员借调数据类型
 */
export interface record {
  /**
   * 主键
   */
  employeeSecondmentRecordID?: Number;
  /**
   * HR员工ID
   */
  employeeID?: string;
  /**
   * 员工姓名
   */
  employeeName?: string;
  /**
   * 部门ID
   */
  departmentID?: number;
  /**
   * 部门名称
   */
  departmentName?: string;
  /**
   * 借调部门ID
   */
  secondmentDepartmentID?: number;
  /**
   * 借调部门
   */
  secondmentDepartmentName?: string;
  /**
   * 开始日期
   */
  startDate?: Date;
  /**
   * 到期日期
   */
  endDate?: Date;
  /**
   * 借调目的
   */
  secondmentPurpose?: string;
  /**
   * 借调目的名称
   */
  secondmentPurposeName?: string;
  /**
   * 借调类型
   */
  secondmentType?: string;
  /**
   * 借调类型名称
   */
  secondmentTypeName?: string;
  /**
   * 开始午别
   */
  startNoon?: string;
  /**
   * 开始午别名称
   */
  startNoonName?: string;
  /**
   * 结束午别
   */
  endNoon?: string;
  /**
   * 结束午别名称
   */
  endNoonName?: string;
  /**
   * 审批人
   */
  approveEmployeeName?: string;
  /**
   * 审批人ID
   */
  approveEmployeeID?: string;
  /**
   * 实际结束日期
   */
  actualEndDate?: Date;
  /**
   * 实际结束午别
   */
  actualEndNoon?: string;
  /**
   * 实际结束午别
   */
  actualEndNoonName?: string;
}
