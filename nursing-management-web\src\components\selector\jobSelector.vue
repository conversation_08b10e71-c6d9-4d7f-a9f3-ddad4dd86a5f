<!--
 * FilePath     : \src\components\selector\jobSelector.vue
 * Author       : 杨欣欣
 * Date         : 2023-12-02 14:54
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-14 14:25
 * Description  : 审批分类级联选择器
 * CodeIterationRecord:
 -->
<template>
  <div class="job-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select v-model="selectOptions" v-bind="attrs" class="selector-component" @change="handleChange">
      <el-option
        v-for="(jobOption, index) in jobOptions"
        :key="index"
        :label="jobOption.label"
        :value="jobOption.value"
        :disabled="jobOption.disabled"
      />
    </el-select>
  </div>
</template>

<script lang="ts" setup>
// #region props
const props = defineProps({
  modelValue: {
    type: [String, Array<String>]
  },
  label: {
    type: String,
    default: "职务"
  },
  width: {
    type: Number,
    default: 200
  },
  options: {
    type: Array<Record<string, any>>,
    default: () => undefined
  }
});
// 获取父组件传来的其余属性
const otherAttrs = useAttrs();
// 组装、透传props与事件给el-cascader组件
const attrs = computed(() => ({
  ...otherAttrs,
  // 组件固定attr，不可由外部覆盖
  placeholder: `请选择${props.label}`,
  filterable: true
}));
// #endregion

const jobOptions = ref<Record<string, any>[]>(props.options ?? []);
const emits = defineEmits(["update:modelValue", "select"]);
const selectOptions = useVModel(props, "modelValue", emits);

// 初始化时获取候选项
onMounted(() => {
  // 外界传递列表时，不再请求字典数据
  if (!jobOptions.value.length) {
    useDictionaryData()
      .getDepartmentToJobs()
      .then((data) => (jobOptions.value = data));
  }
});
const handleChange = (value: string) => {
  // 找到对应对象
  const jobOption = jobOptions.value.find((option) => option.value === value);
  // 抛出select事件
  emits("select", jobOption);
};

const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const cascaderWidth = computed(() => `${convertPX(width.value)}px`);
</script>
<style lang="scss">
.job-selector {
  display: inline-block;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(cascaderWidth));
}
</style>
