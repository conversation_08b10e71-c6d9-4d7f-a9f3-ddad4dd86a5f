/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanGroupActions.ts
 * Author       : 杨欣欣
 * Date         : 2025-05-08 09:27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-08 19:51
 * Description  : 年度计划目标分组状态与行为
 * CodeIterationRecord: 
 */
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import type { planGoal, planGroup } from "../../types/annualPlanMain";
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";
import type { MaybeRefOrGetter } from "vue";

export default function useAnnualPlanGroupActions(
  calculateNewSortByParent: <T, P extends { sort: number }>(
    array: { [key: string]: T[] } | T[],
    parentItems: MaybeRefOrGetter<P[]>,
    groupKey: keyof P & keyof T
  ) => Map<string, number>,
  convertToObject: <S extends { sort: number }>(items: { [key: string]: S[] } | S[], itemKey: keyof S) => Record<string, number>
) {
  const { sessionStore } = useStore();
  const planManagementStore = usePlanManagementStore();
  const maintainStore = useAnnualPlanMaintainStore();
  const groupNewSortByGoal = computed(() => {
    if (!Object.values(maintainStore.goalsByTypeID).length) {
      return new Map();
    }
    const result = calculateNewSortByParent(maintainStore.planGroups, Object.values(maintainStore.goalsByTypeID).flat(), "mainGoalID");
    return result;
  });
  const recommendations = ref<string[]>([]);
  let firstExpand = true;
  maintainStore.$onAction(({ name }) => {
    // 监听目标展开行为
    if (name === "addUnExpandedPlanGoalIDs" && firstExpand) {
      firstExpand = false;
      setRecommendations();
    }
  });

  /**
   * @description: 设置推荐项
   */
  const setRecommendations = async () => {
    const params = {
      mainID: usePlanManagementStore().annualPlanMainID
    };
    recommendations.value = await annualPlanMainService.getDepartmentOptions(params);
  };

  return {
    recommendations,
    /**
     * @description: 新增目标分组到对应策略目标
     * @param sourcePlanGoal 要新增到的策略目标
     * @return
     */
    addPlanGroupToPlanGoal: async (sourcePlanGoal: planGoal) => {
      const sort = groupNewSortByGoal.value.get(sourcePlanGoal.mainGoalID);
      if (!sort) {
        showMessage("error", "发生内部错误，请联系管理员！");
        throw new Error("根据策略目标计算新分组序号失败");
      }
      const newGroup: planGroup = {
        mainID: planManagementStore.annualPlanMainID,
        mainGoalID: sourcePlanGoal.mainGoalID,
        groupID: `temp_${common.guid()}`,
        responsibleDepartments: [],
        sort
      };
      maintainStore.planGroups.filter((planGroup) => planGroup.sort >= sort).forEach((group) => (group.sort += 1));
      maintainStore.planGroups.splice(sort - 1, 0, newGroup);
      ({ groupID: newGroup.groupID } = await maintainStore.savePlanGroup(newGroup));
    },
    /**
     * @description: 重算分组Sort
     * @param newIndex 新下标
     * @param oldIndex 旧下标
     * @param draggedGroup 被拖拽的分组
     * @return
     */
    resetPlanGroupsSort: async (newIndex: number, oldIndex: number, draggedGroup: planGroup) => {
      if (newIndex === oldIndex) {
        return;
      }
      // sortablejs只是dom排序，需要同步更新数据
      maintainStore.planGroups.splice(oldIndex, 1);
      maintainStore.planGroups.splice(newIndex, 0, draggedGroup);
      // TODO：可缩小范围，只更新被影响的分组即可，不用全部更新
      let indicatorSort = 1;
      let projectSort = 1;
      maintainStore.planGroups.forEach((group, index) => {
        group.sort = index + 1;
        maintainStore.indicatorsByGroupID[group.groupID]?.forEach((indicator) => (indicator.sort = indicatorSort++));
        maintainStore.projectsByGroupID[group.groupID]?.forEach((project) => (project.sort = projectSort++));
      });
      const params = {
        mainID: planManagementStore.annualPlanMainID,
        planGroupIDAndSort: convertToObject(maintainStore.planGroups, "groupID"),
        planIndicatorIDAndSort: convertToObject(maintainStore.indicatorsByGroupID, "detailID"),
        planProjectIDAndSort: convertToObject(maintainStore.projectsByGroupID, "detailID")
      };
      await annualPlanMainService.resetAnnualPlanGroupsSort(params);
    },
    /**
     * @description: 删除目标分组
     * @param deleteGroup 待删除的目标分组
     * @return
     */
    deletePlanGroup: async (deleteGroup: planGroup) => {
      await deleteConfirm("当前分组下的所有策略指标、目标任务、分解目标任务均会被删除，是否继续？", async (flag: boolean) => {
        if (!flag) {
          return;
        }
        const deleteGroupIndex = maintainStore.planGroups.indexOf(deleteGroup);
        maintainStore.planGroups.splice(deleteGroupIndex, 1);
        maintainStore.planGroups.forEach((group) => group.sort > deleteGroup.sort && (group.sort -= 1));

        if (!deleteGroup.groupID.includes("temp_")) {
          await annualPlanMainService.deleteAnnualGoalGroup({ groupID: deleteGroup.groupID });
        }
        if (sessionStore.debugMode) {
          // eslint-disable-next-line no-console
          console.log("分组已删除", deleteGroup);
        }
      });
    },
    /**
     * @description: 保存目标分组
     * @param newGroup 待保存的目标分组
     * @return
     */
    savePlanGroup: async (newGroup: planGroup) => {
      const groupID = await annualPlanMainService.saveAnnualPlanGroup(newGroup);
      newGroup.groupID = groupID;
      if (sessionStore.debugMode) {
        // eslint-disable-next-line no-console
        console.log("新分组已保存", newGroup);
      }
      return {
        groupID,
        responsibleDepartments: newGroup.responsibleDepartments
      };
    }
  };
}
