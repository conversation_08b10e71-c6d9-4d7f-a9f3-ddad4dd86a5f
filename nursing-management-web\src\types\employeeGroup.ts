/*
 * FilePath     : \src\types\employeeGroup.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-12 10:00
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 07:57
 * Description  : 用户组类型
 * CodeIterationRecord:
 */

/**
 * 用户组信息视图
 */
declare interface EmployeeGroupVo {
  /**
   * 主键
   */
  groupID: number;
  /**
   * 组名
   */
  groupName: string;
  /**
   * 组员信息
   */
  members: MemberInformation[];
  /**
   * 维护人工号
   */
  modifyEmployeeID: string;
  /**
   * 维护人
   */
  modifyEmployeeName: string;
  /**
   * 维护时间
   */
  modifyDateTime: string;
}

declare interface EmployeeGroupAsOption {
  id: number;
  name: string;
  members: EmployeeAsOption[];
}

declare interface MemberInformation {
  /**
   * 工号
   */
  employeeID: string;
  /**
   * 姓名
   */
  employeeName: string;
  /**
   * 所属部门
   */
  departmentName: string;
}

/**
 * 添加用户组请求参数
 */
declare interface AddEmployeeGroupDto {
  /**
   * 组名
   */
  groupName: string;
  /**
   * 组员ID列表
   */
  employeeIDs: string[];
}

/**
 * 更新用户组请求参数
 */
declare interface UpdateEmployeeGroupDto {
  /**
   * 主键
   */
  groupID: number;
  /**
   * 组名
   */
  groupName: string;
  /**
   * 组员ID列表
   */
  employeeIDs: string[];
}