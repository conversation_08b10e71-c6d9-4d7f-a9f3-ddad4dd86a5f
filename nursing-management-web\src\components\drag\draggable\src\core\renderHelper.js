/*
 * FilePath     : \src\components\drag\draggable\src\core\renderHelper.js
 * Author       : 杨欣欣
 * Date         : 2023-09-02 09:27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-05-17 16:09
 * Description  :
 * CodeIterationRecord:
 */
import { ComponentStructure } from "./componentStructure";
import { isHtmlTag, isTransition } from "../util/tags";
import { resolveComponent, TransitionGroup } from "vue";

function getSlot(slots, key) {
  const slotValue = slots[key];
  return slotValue ? slotValue() : [];
}

function computeNodes({ $slots, realList, getKey, itemDataSet }) {
  let handleItemData = undefined;
  switch (typeof itemDataSet) {
    case "function":
      handleItemData = itemDataSet;
      break;
    case "object":
      handleItemData = () => itemDataSet;
      break;
  }
  const normalizedList = realList || [];
  const [header, footer] = ["header", "footer"].map((name) => getSlot($slots, name));
  const { item } = $slots;
  if (!item) {
    throw new Error("draggable element must have an item slot");
  }
  const defaultNodes = normalizedList.flatMap((element, index) =>
    item({ element, index }).map((node) => {
      node.key = getKey(element);
      const extraProps = handleItemData?.(element);
      if (extraProps) {
        node.props = { ...(node.props || {}), "data-draggable": true, ...extraProps };
      } else {
        node.props = { ...(node.props || {}), "data-draggable": true };
      }
      return node;
    })
  );
  if (defaultNodes.length !== normalizedList.length) {
    throw new Error("Item slot must have only one child");
  }
  return {
    header,
    footer,
    default: defaultNodes
  };
}

function getRootInformation(tag) {
  const transition = isTransition(tag);
  const externalComponent = !isHtmlTag(tag) && !transition;
  return {
    transition,
    externalComponent,
    tag: externalComponent ? resolveComponent(tag) : transition ? TransitionGroup : tag
  };
}

function computeComponentStructure({ $slots, tag, realList, getKey, itemDataSet }) {
  const nodes = computeNodes({ $slots, realList, getKey, itemDataSet });
  const root = getRootInformation(tag);
  return new ComponentStructure({ nodes, root, realList });
}

export { computeComponentStructure };
