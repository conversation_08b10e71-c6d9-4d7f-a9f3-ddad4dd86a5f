<!--
 * FilePath     : \src\components\selector\employeeSelector.vue
 * Author       : 张现忠
 * Date         : 2023-08-24 16:17
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-03 09:57
 * Description  : 员工选择下拉组件
 * CodeIterationRecord:
-->
<template>
  <div class="employee-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="employeeIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="remote ? `请输入关键字` : `请选择${label}`"
      :remote-method="remote ? fuzzySearchEmployee : undefined"
      :remote="remote"
      :loading="loading"
      loading-text="加载中……"
      :collapse-tags="multiCollapse"
      :collapse-tags-tooltip="multiCollapse"
      :allow-create="allowCreate"
      :reserve-keyword="false"
      @change="change"
    >
      <el-option v-for="(item, index) in employeeOptions" :key="index" :label="item.label" :value="item.value">
        <span style="float: left">{{ item.label }}</span>
        <span style="float: right; color: #8492a6">{{ item.suffix }}</span>
      </el-option>
    </el-select>
  </div>
</template>
<script setup lang="ts">
//#region 引入
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
//#endregion

//#region props参数
const props = defineProps({
  label: {
    type: String,
    default: "人员"
  },
  modelValue: {
    type: [String, Array<string>]
  },
  departmentID: {
    type: Number
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 200
  },
  remote: {
    type: Boolean,
    default: false
  },
  multiCollapse: {
    type: Boolean,
    default: true
  },
  showAll: {
    type: Boolean,
    default: false
  },
  // 是否可创建新项目，为true时filterable也必须为true
  allowCreate: {
    type: Boolean,
    default: false
  }
});
//#endregion

//#region 监听
watch(
  () => props.departmentID,
  (newValue) => {
    if (newValue) {
      initData();
    }
  }
);
//#endregion

//#region 变量定义
let employeeOptions = ref<Array<Record<any, any>>>([]);
const loading = ref(false);
//#endregion

//#region 业务逻辑
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let employeeIDs = useVModel(props, "modelValue", emits);
let { getEmployeeData, getEmployeeDataByName, getEmployeeDataByIDs } = useDictionaryData();
let { list } = toRefs(props);
// 初始化
onMounted(() => {
  props.remote && !list?.value ? getEmployeeDataByIDs(employeeIDs.value).then((res) => (employeeOptions.value = res)) : initData();
});
watchEffect(() => {
  props.remote && !list?.value && props.multiple && getEmployeeDataByIDs(employeeIDs.value).then((res) => (employeeOptions.value = res));
});
// 添加list监听，解决list集合在组件加载完整之后才赋值，导致无法渲染选项的问题
watch(
  () => list?.value,
  () => {
    if (list?.value) {
      initData();
    }
  }
);
const initData = () => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  if (list?.value) {
    employeeOptions.value = list.value;
  } else {
    getEmployeeData(props.showAll, props.departmentID, Math.random()).then((data) => (employeeOptions.value = data));
  }
};
/**
 * @description: 增加防抖
 * @return
 */
const debouncedGetEmployee = useDebounceFn(async (query) => {
  return await getEmployeeDataByName(query);
}, 500);
/**
 * @description: 模糊搜索员工
 */
const fuzzySearchEmployee = (query: string) => {
  if (!query) {
    return [];
  }
  loading.value = true;
  debouncedGetEmployee(query).then((res: any) => {
    employeeOptions.value = res;
    loading.value = false;
  });
};

/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = async (value: string | Array<string>) => {
  exposeChange(value, emits);
  if (props.multiple && props.remote) {
    // 根据已选择项，重新拉取候选项
    employeeOptions.value = await getEmployeeDataByIDs(value);
    emits("select", employeeOptions.value);
  } else {
    exposeSelect(value, employeeOptions.value, "value", props.multiple, emits);
  }
};
//#endregion
</script>

<style lang="scss">
.employee-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
