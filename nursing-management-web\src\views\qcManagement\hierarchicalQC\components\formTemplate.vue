<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\formTemplate.vue
 * Author       : 苏军志
 * Date         : 2024-04-13 14:06
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 11:33
 * Description  : 质控模板调整组件
 * CodeIterationRecord:
 -->
<template>
  <div class="form-template">
    <zhy-form-designer
      ref="formDesignerRef"
      defaultSourceType="HierarchicalQCAssessList"
      mergeGroup
      :componentGroups="componentGroups"
      :formData="formData"
      :uploadOptions="uploadOptions"
      :batchAddComponentParams="batchAddComponentParams"
      :saveMethod="formParams.saveFormMethod"
    >
      <template #formBeforeExtendProps>
        <el-form-item prop="formLevel" label="质控级别" :rules="[{ message: '请选择质控级别', required: true, trigger: 'change' }]">
          <level-selector
            v-if="levelOptions.length"
            v-model="formData.props.formLevel"
            :disabled="formParams.subjectPropDisabled"
            :width="260"
            :list="levelOptions"
          ></level-selector>
        </el-form-item>
        <el-form-item prop="qcFormType" label="质控类别" :rules="[{ message: '请选择质控类别', required: true, trigger: 'change' }]">
          <form-type-selector
            v-if="formTypeOptions.length"
            label=""
            v-model="formData.props.qcFormType"
            :qcLevel="formData.props.formLevel"
            :disabled="formParams.subjectPropDisabled"
            :width="260"
            :list="formTypeOptions"
          ></form-type-selector>
        </el-form-item>
        <el-form-item prop="departmentID" label="适用部门" :rules="[{ message: '请选择适用部门', required: true, trigger: 'change' }]">
          <department-selector
            v-if="departmentOptions.length"
            v-model="formData.props.departmentID"
            label=""
            :width="240"
            :list="departmentOptions"
          />
        </el-form-item>
      </template>
    </zhy-form-designer>
  </div>
</template>
<script setup lang="ts">
import type { batchAddComponentParam, dynamicFormData, uploadOption, searchParam, selectOptionsView } from "zhytech-ui";
import { componentType } from "zhytech-ui";
import formTypeSelector from "../components/qcFormTypeSelector.vue";
import levelSelector from "../components/qcLevelSelector.vue";
import type { formParam } from "../types/qcFormTemplate";
const props = defineProps({
  formParams: {
    type: Object as PropType<formParam>,
    required: true
  },
  refresh: {
    type: Number
  },
  // 组织架构类型
  organizationType: {
    type: String,
    default: "1"
  }
});
watch(
  () => props.refresh,
  async () => await initFormTemplate()
);
const { sessionStore, userStore } = useStore();
// 动态表单动的上传文件参数
const uploadOptions: uploadOption = {
  serverApi: `${common.session("serverUrl")}/file/uploadFile`,
  headers: {
    "Management-Token": sessionStore.token
  },
  autoUpload: false
};
// 批量添加组件参数
const batchAddComponentParams = ref<batchAddComponentParam>();
const formData = ref<dynamicFormData<Record<string, any>>>({
  datas: {},
  components: [],
  props: {}
});
let formSetting: Record<string, any>[] = [];
let commonTypeSpeciaListType: Record<string, string> | undefined = undefined;
let specialistTypeSetting: Record<string, string> | undefined = undefined;
const componentGroups = ref<Record<string, any>[]>([]);

onMounted(async () => {
  await getFormSetting();
  // await getQCAssessList();
  await getEmployeeList();
  await getPostList();
  await getFormLevelList();
  await getFormTypeList();
  await getDepartmentCascaderList();
  await initFormTemplate();
});

// const dictionary = ref<dictionaryData>({} as dictionaryData);
// const sourceTypeMap = new Map<string, string>([["HierarchicalQCAssessList", "质控评估项"]]);
// /**
//  * @description: 获取质控项目字典
//  */
// const getQCAssessList = () => {
//   if (dictionary.value.dictionaryItems?.length) {
//     return;
//   }
//   hierarchicalQCService.getQCAssessList().then((result: any) => {
//     if (result) {
//       dictionary.value = {
//         dictionaryItems: result,
//         sourceTypeMap: sourceTypeMap
//       };
//     }
//   });
// };

// #region 获取初始化参数
/**
 * @description: 获取质控分类组件配置
 */
const getFormSetting = async () => {
  const params = {
    settingType: "HierarchicalQC",
    settingTypeCode: "NormalWorkingComponentListID"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    formSetting = datas ?? [];
    if (formSetting.length > 0) {
      commonTypeSpeciaListType = formSetting.find((setting: any) => setting.value === "commonType");
      specialistTypeSetting = formSetting.find((setting: any) => setting.value === "specialistType");
    }
  });
};
const { getEmployeeData, getEmployeeDataByName, getPostDictByDepartmentID, getDepartmentCascaderData } = useDictionaryData();
let employeeList: Record<string, string>[] = [];
/**
 * @description: 获取部门人员清单
 */
const getEmployeeList = async () => {
  employeeList = [];
  await getEmployeeData(false, userStore.departmentID, Math.random()).then((data) => (employeeList = data));
  let params = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCSpecialListID",
    settingTypeValue: "973"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((data: any) => {
    if (data?.length) {
      employeeList = [...employeeList, ...data];
    }
  });
};
let postList: Record<string, string>[] = [];
/**
 * @description: 获取部门岗位清单
 */
const getPostList = async () => {
  postList = [];
  await getPostDictByDepartmentID(userStore.departmentID).then((data) => (postList = data));
  let params = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCSpecialListID",
    settingTypeValue: "972"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((data: any) => {
    if (data) {
      postList = [...postList, ...data];
    }
  });
};
let levelOptions = ref<selectOptionsView[]>([]);
/**
 * @description: 获取质控级别
 */
const getFormLevelList = async () => {
  // 字典数据
  const param: SettingDictionaryParams = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCForm",
    settingTypeValue: "HierarchicalQCFormLevel"
  };
  settingDictionaryService.getSettingDictionaryDict(param).then((datas: any) => {
    levelOptions.value = datas;
  });
};
let formTypeOptions = ref<selectOptionsView[]>([]);
/**
 * @description: 获取表单类型
 */
const getFormTypeList = async () => {
  let params = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCFormType"
  };
  await hierarchicalQCService.getQCFormType(params).then((datas: any) => {
    formTypeOptions.value = datas;
  });
};
let departmentOptions = ref<selectOptionsView[]>([]);
/**
 * @description: 获取部门列表
 */
const getDepartmentCascaderList = async () => {
  await getDepartmentCascaderData(props.organizationType, []).then((data) => {
    departmentOptions.value = data;
  });
};
// #endregion

/**
 * @description: 获取筛选参数
 */
const getSearchParams = () => {
  const searchParams: searchParam[] = [
    {
      type: componentType.INPUT,
      label: "模板名称",
      key: "formName"
    },
    {
      type: componentType.SELECTOR,
      label: "质控级别",
      key: "formLevel",
      options: levelOptions.value
    },
    {
      type: componentType.SELECTOR,
      label: "质控类别",
      key: "formType",
      options: formTypeOptions.value
    },
    {
      type: componentType.CASCADER_SELECTOR,
      label: "适用部门",
      key: "addDepartmentID",
      options: departmentOptions.value
    }
  ];
  return searchParams;
};

/**
 * @description: 初始化模板
 */
const initFormTemplate = async () => {
  componentGroups.value = [
    {
      name: "应用组件",
      expand: true,
      components: [
        {
          type: "employee",
          allowRepeat: false,
          fixedItemID: 973,
          options: employeeList,
          searchEmployeeMethod: (searchContent: string) => searchEmployee(searchContent)
        },
        {
          type: "post",
          allowRepeat: false,
          fixedItemID: 972,
          options: postList
        }
      ]
    },
    {
      name: "质控组件",
      expand: true,
      components: [
        {
          allowRepeat: false,
          allowModify: false,
          fixedItemID: 974,
          type: "radio",
          name: commonTypeSpeciaListType?.label ?? "公共质量分类",
          id: commonTypeSpeciaListType?.value ?? "974",
          itemId: commonTypeSpeciaListType?.value ?? "974",
          itemSourceType: "HierarchicalQCAssessList",
          props: {
            label: commonTypeSpeciaListType?.label ?? "公共质量分类",
            disabled: false,
            options: [
              { label: "患者身份识别护理质量", value: "5570", itemSourceType: "HierarchicalQCAssessList" },
              { label: "护理查对（医嘱、标本采集）质量", value: "5571", itemSourceType: "HierarchicalQCAssessList" },
              { label: "病区安全用药质量", value: "5572", itemSourceType: "HierarchicalQCAssessList" },
              { label: "关键流程转科交接质", value: "5573", itemSourceType: "HierarchicalQCAssessList" },
              { label: "输血管理质量", value: "5574", itemSourceType: "HierarchicalQCAssessList" },
              { label: "住院患者身体约束护理质量", value: "5575", itemSourceType: "HierarchicalQCAssessList" },
              { label: "抢救药械质量", value: "5576", itemSourceType: "HierarchicalQCAssessList" },
              { label: "护理文书质量", value: "5577", itemSourceType: "HierarchicalQCAssessList" },
              { label: "分级护理质量", value: "5578", itemSourceType: "HierarchicalQCAssessList" },
              { label: "危重患者护理质量", value: "5579", itemSourceType: "HierarchicalQCAssessList" },
              { label: "责任护士整体护理评估质量", value: "5580", itemSourceType: "HierarchicalQCAssessList" },
              { label: "病区环境管理质量", value: "5581", itemSourceType: "HierarchicalQCAssessList" },
              { label: "病区消毒隔离质量", value: "5582", itemSourceType: "HierarchicalQCAssessList" },
              { label: "健康教育护理质量", value: "5583", itemSourceType: "HierarchicalQCAssessList" },
              { label: "护理服务行为质量", value: "5584", itemSourceType: "HierarchicalQCAssessList" },
              { label: "6S+库房管理质量", value: "5585", itemSourceType: "HierarchicalQCAssessList" },
              { label: "患者费用管理质量", value: "5586", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者跌倒/坠床质量", value: "5587", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者压力性损伤质量", value: "5588", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者导管非计划拔管质量", value: "5589", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者呼吸机相关性肺炎质量", value: "5590", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者导尿管相关尿路感染质量", value: "5591", itemSourceType: "HierarchicalQCAssessList" },
              { label: "预防住院患者中心静脉导管相关血流感染护理质量", value: "5592", itemSourceType: "HierarchicalQCAssessList" }
            ]
          }
        },
        {
          allowRepeat: false,
          allowModify: true,
          fixedItemID: 975,
          type: "radio",
          name: specialistTypeSetting?.label ?? "专科质量分类",
          id: specialistTypeSetting?.value ?? "975",
          itemId: specialistTypeSetting?.value ?? "975",
          itemSourceType: "HierarchicalQCAssessList",
          props: {
            label: specialistTypeSetting?.label ?? "专科质量分类",
            options: [{ label: "自定义", value: "temp_0002", itemSourceType: "HierarchicalQCAssessList" }]
          }
        }
      ]
    }
  ];
  const searchParams = getSearchParams();
  if (props.formParams.formList) {
    batchAddComponentParams.value = {
      formList: props.formParams.formList,
      getFormTemplate: (formID: string) => getFormTemplate(formID),
      searchParams: searchParams
    };
  }
  if (props.formParams.formID) {
    formData.value = await getFormTemplate(props.formParams.formID);
    nextTick(() => {
      formData.value.props.formID = props.formParams.formID;
      formData.value.props.formLevel = props.formParams.formLevel;
      formData.value.props.formType = "1";
      formData.value.props.qcFormType = props.formParams.qcFormType;
      formData.value.props.formName = props.formParams.formName;
      formData.value.props.departmentID = props.formParams.departmentID;
    });
  }
};
/**
 * @description: 查询质控模板
 * @param dynamicFormRecordID
 * @return
 */
const getFormTemplate = async (dynamicFormRecordID: string) => {
  let data = {
    datas: {},
    components: [],
    props: {}
  };
  let params = {
    dynamicFormRecordID
  };
  await dynamicFormService.getFormTemplateByRecordID(params).then((res: any) => {
    if (res) {
      data = res;
    }
  });
  return data;
};
/**
 * @description: 根据姓名查找人员
 * @param searchContent
 * @return
 */
const searchEmployee = async (searchContent: string) => {
  return await getEmployeeDataByName(searchContent);
};
</script>
<style lang="scss">
.form-template {
  height: 100%;
}
</style>
