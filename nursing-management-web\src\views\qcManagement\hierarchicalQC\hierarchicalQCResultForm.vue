<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hierarchicalQCResultForm.vue
 * Author       : 陈超然
 * Date         : 2024-09-25 16:01
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-15 09:48
 * Description  :
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="hierarchical-qc-resultForm">
    <qcForm
      v-if="formData?.props"
      v-model="saveView"
      ref="qcFormDom"
      :formData="formData"
      :disabledExamineDate="disabledExamineDate"
      @getDetails="getDetails"
    ></qcForm>
  </base-layout>
</template>

<script lang="ts" setup>
import qcForm from "./components/qcForm.vue";
import type { dynamicFormData, formAttribute } from "zhytech-ui";
const formData = ref<dynamicFormData<formAttribute>>();
const disabledExamineDate = ref<string>();
const route = useRoute();
onMounted(() => {
  getDetails();
});
const saveView = ref({
  qcDate: route.params.qcDate,
  currentDate: datetimeUtil.getNowDate("yyyy-MM-dd")
});
const getDetails = () => {
  let params = {
    templateCode: route.query.templateCode || "",
    careMainID: route.query.careMainID || ""
  };

  hierarchicalQCService.getQCAssessView(params).then((res: any) => {
    if (res) {
      formData.value = res;
    }
  });
};
</script>
