<!--
 * FilePath     : \src\views\login.vue
 * Author       : 苏军志
 * Date         : 2023-06-04 08:42
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-29 10:01
 * Description  : 系统登录画面
 * CodeIterationRecord:
-->

<template>
  <div class="login">
    <div class="system-logo-name">
      <img class="system-logo" src="/static/images/system-logo.png" />
      <div class="system-name">{{ i18nText.systemName }}</div>
      <div class="system-version" v-if="commonStore.hospital.systemVersion">{{ `V ${commonStore.hospital.systemVersion}` }}</div>
    </div>
    <div class="login-warp">
      <el-carousel class="left-image-carousel" indicator-position="none" arrow="never">
        <el-carousel-item v-for="(image, index) in leftImages" :key="index">
          <img class="left-image" :src="image" />
        </el-carousel-item>
      </el-carousel>
      <div class="login-wrap-content">
        <el-dropdown trigger="click" @command="selectLanguage">
          <div class="select-language">
            <span v-if="commonStore.language.language">
              {{ commonStore.language.languageName }}
            </span>
            <i class="iconfont icon-arrow-down"></i>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="select-hospital-dropdown">
              <el-dropdown-item v-for="(language, index) in languageList" :key="index" :command="language" divided>
                {{ language.languageName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <div class="login-tabs">
          <div class="login-pane oa" v-if="loginType == 'oa'">
            <el-input
              v-model="oaUserID"
              :placeholder="i18nText.oaUserPlaceholder"
              class="login-input"
              @keydown.enter="oaPasswordInput?.focus()"
            >
              <template #prepend>
                <i class="iconfont icon-login-user"></i>
              </template>
            </el-input>
            <el-input
              ref="oaPasswordInput"
              v-model="oaPassword"
              type="password"
              :placeholder="i18nText.oaPasswordPlaceholder"
              class="login-input"
              @keydown.enter="login"
            >
              <template #prepend>
                <i class="iconfont icon-login-password"></i>
              </template>
            </el-input>
            <el-button type="primary" @click="login" class="login-button">{{ i18nText.loginBtn }}</el-button>
          </div>
          <div class="login-pane his" v-if="loginType == 'his'">
            <el-input
              v-model="hisUserID"
              :placeholder="i18nText.hisUserPlaceholder"
              class="login-input"
              @keydown.enter="hisPasswordInput?.focus()"
            >
              <template #prepend>
                <i class="iconfont icon-login-user"></i>
              </template>
            </el-input>
            <el-input
              ref="hisPasswordInput"
              v-model="hisPassword"
              type="password"
              :placeholder="i18nText.hisPasswordPlaceholder"
              class="login-input"
              @keydown.enter="login"
            >
              <template #prepend>
                <i class="iconfont icon-login-password"></i>
              </template>
            </el-input>
            <el-button type="primary" @click="login" class="login-button">{{ i18nText.loginBtn }}</el-button>
          </div>
          <!-- <div class="login-pane wechat" v-if="loginType == 'wechat'">抱歉，尚未开发……</div> -->
          <el-divider>登录方式</el-divider>
          <div :class="['login-type oa', { selected: loginType == 'oa' }]" @click="loginType = 'oa'">OA</div>
          <div :class="['login-type his', { selected: loginType == 'his' }]" @click="loginType = 'his'">HIS</div>
          <!-- <div :class="['login-type wechat', { selected: loginType == 'wechat' }]" @click="loginType = 'wechat'">微信</div> -->
        </div>
        <!-- 暂不考虑多医院 -->
        <div class="authorized-hospital">
          <span class="label">{{ i18nText.authorizedBy }}：</span>
          <el-dropdown trigger="click" :disabled="hospitalList.length === 1" @command="selectHospital">
            <div class="authorized-by">
              <span v-if="commonStore.hospital.hospitalID" :style="{ color: commonStore.hospital.themeColor }">
                {{ commonStore.hospital.hospitalName }}
              </span>
              <span class="select-hospital" v-else>请选择医院</span>
              <i v-if="hospitalList.length !== 1" class="iconfont icon-arrow-down"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu class="select-hospital-dropdown">
                <el-dropdown-item v-for="(hospital, index) in hospitalList" :key="index" :command="hospital" divided>
                  {{ hospital.hospitalName }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <span class="clear-cache" @click="clearCache">清除缓存</span>
    <img class="company-logo" src="/static/images/company-logo.png" />
  </div>
</template>

<script setup lang="ts">
const { proxy } = getCurrentInstance() as any;
const { sessionStore, userStore, commonStore } = useStore();
const router = useRouter();
const route = useRoute();
const url = route.query.url as string;
const token = route.query.token as string;
const leftImages: string[] = [
  "/static/images/login/leftImage/image1.jpg",
  "/static/images/login/leftImage/image2.jpg",
  "/static/images/login/leftImage/image3.jpg",
  "/static/images/login/leftImage/image4.jpg"
];
let loginType = ref("his");
let hisUserID = ref("");
let hisPassword = ref("");
let oaUserID = ref("");
let oaPassword = ref("");
let oaPasswordInput = ref<HTMLElement>();
let hisPasswordInput = ref<HTMLElement>();
let hospitalList = ref<Record<string, any>[]>([]);
let languageList = ref<Record<string, any>[]>([]);
// 多语言处理
const i18nText = computed(() => {
  return {
    systemName: proxy.$t("label.systemName"),
    hisUserPlaceholder: proxy.$t("login.hisUserPlaceholder"),
    oaUserPlaceholder: proxy.$t("login.oaUserPlaceholder"),
    oaPasswordPlaceholder: proxy.$t("login.oaPasswordPlaceholder"),
    hisPasswordPlaceholder: proxy.$t("login.hisPasswordPlaceholder"),
    loginBtn: proxy.$t("login.loginBtn"),
    hisErrorTip: proxy.$t("login.hisErrorTip"),
    oaErrorTip: proxy.$t("login.oaErrorTip"),
    authorizedBy: proxy.$t("login.authorizedBy"),
    selectHospitalTip: proxy.$t("login.selectHospitalTip")
  };
});
// 根据语言设置浏览器显示标题
watch(
  () => commonStore.language,
  () => (document.title = i18nText.value.systemName)
);
// 设置浏览器显示标题
document.title = i18nText.value.systemName;
onMounted(async () => {
  // 先重置缓存
  sessionStore.$reset();
  userStore.$reset();
  common.session("logger", { loggerList: [], loggerSteps: 0 });
  if (!common.session("serverUrl")) {
    await common.wait(100);
  }
  getServerDateTime();
  getHospitalList();
  getLanguageList();
  // 如果传了token,自动登录
  if (token) {
    await getSession(token);
  }
});

// 键盘快捷键
const { command, ctrl, alt, f, c } = useMagicKeys();
watch([() => command.value, () => ctrl.value, () => alt.value, () => f.value, () => c.value], () => {
  if (ctrl.value && alt.value && f.value) {
    fillTestAccount(true);
    return;
  }
  if ((command.value || ctrl.value) && alt.value && c.value) {
    fillTestAccount(false);
  }
});

/**
 * @description: 填充测试账号，快捷键 填充：ctrl + alt + f，清空：ctrl + alt + c
 * @param flag
 */
const fillTestAccount = (flag: boolean) => {
  if (flag) {
    hisUserID.value = "9856";
    hisPassword.value = "9856";
    oaUserID.value = "oaTest";
    oaPassword.value = "123456";
  } else {
    hisUserID.value = "";
    hisPassword.value = "";
    oaUserID.value = "";
    oaPassword.value = "";
  }
};
/**
 * @description: 获取系统时间
 */
const getServerDateTime = () => {
  userLoginService.getServerDateTime().then((data: any) => {
    let timeDiff = 0;
    if (data) {
      // 将本地和服务器时间的时间差存到session中
      // 预估api固定响应时间50毫秒
      // Date._localDate()为封装的获取本地时间方法
      timeDiff = new Date(data).getTime() + 50 - (Date as any).localDate().getTime();
    }
    common.session("timeDiff", timeDiff);
  });
};
/**
 * @description: 获取医院列表
 */
const getHospitalList = () => {
  dictionaryService.getHospitalList().then((result: any) => {
    hospitalList.value = result ?? [];
    if (hospitalList.value.length === 1) {
      selectHospital(hospitalList.value[0]);
    }
  });
};
/**
 * @description: 获取语言列表
 */
const getLanguageList = () => {
  settingDictionaryService.getLanguageList().then((result: any) => {
    languageList.value = result ?? [];
    commonStore.languageList = languageList.value;
  });
};
/**
 * @description: 选择语言
 * @param language
 */
const selectLanguage = (language: Record<string, any>) => {
  commonStore.language = language;
  proxy.$i18n.locale = language.languageCode;
};
/**
 * @description: 选择医院
 * @param hospital
 */
const selectHospital = (hospital: Record<string, any>) => {
  commonStore.hospital = hospital;
};
/**
 * @description: 登录接口
 */
const login = async () => {
  if (loginType.value === "his") {
    if (!hisUserID.value || !hisPassword.value) {
      showMessage("error", i18nText.value.hisErrorTip);
      return;
    }
    userLogin(hisUserID.value, hisPassword.value);
    return;
  }
  if (loginType.value === "oa") {
    if (!oaUserID.value || !oaPassword.value) {
      showMessage("error", i18nText.value.oaErrorTip);
      return;
    }
    userLogin(oaUserID.value, oaPassword.value);
    return;
  }
  if (loginType.value === "wechat") {
    wechatLogin();
    return;
  }
};

/**
 * @description: 账号登录
 * @param userID
 * @param password
 */
const userLogin = async (userID: string, password: string) => {
  if (!commonStore.hospital.hospitalID) {
    showMessage("error", i18nText.value.selectHospitalTip);
    return;
  }
  let params = {
    loginType: loginType.value,
    userID: userID.trim(),
    password: password.trim(),
    hospitalID: commonStore.hospital.hospitalID,
    language: commonStore.language.language || 1,
    clientType: 1
  };
  await userLoginService.login(params).then((data: any) => {
    if (data) {
      sessionStore.isLogin = true;
      sessionStore.token = data.token;
      // 组装登录参数，为系统更新后自动重新登录准备
      sessionStore.loginParams = params;
      userStore.employeeID = data.employeeID;
      userStore.oaUserID = data.oaUserID;
      userStore.hisUserID = data.hisUserID;
      userStore.userName = data.userName;
      userStore.roles = data.roles;
      userStore.departmentID = data.departmentID;
      router.replace({ path: "/home" });
    }
  });
};

/**
 * @description: 微信登录
 */
const wechatLogin = () => {
  showMessage("warning", "未开发……");
};

/**
 * @description: 根据token获取当前登录相关信息
 * @param token
 */
const getSession = async (token?: string) => {
  // 更新token
  token && (sessionStore.token = token);
  await userLoginService.getSessionByToken().then((data: any) => {
    if (data) {
      token && (sessionStore.isLogin = true);
      userStore.employeeID = data.hisUserID;
      userStore.oaUserID = data.oaUserID;
      userStore.hisUserID = data.hisUserID;
      userStore.userName = data.userName;
      userStore.departmentID = data.departmentID;
    }
    if (url && url.length > 0) {
      router.replace(url);
    } else {
      router.replace({ path: "/home" });
    }
  });
};
const clearCache = () => {
  sessionStorage.clear();
  localStorage.clear();
  location.pathname = "login";
};
</script>

<style lang="scss">
$placeholder-color: #faa8a8;
$main-color: #108bf7;
.login {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: url("/static/images/login/login_back.jpg");
  background-size: 100% 100%;
  height: 100%;
  .system-logo-name {
    display: flex;
    position: absolute;
    left: 20px;
    top: 20px;
    height: 84px;
    line-height: 84px;
    font-weight: bold;
    .system-logo {
      width: 84px;
    }
    .system-name {
      font-size: 32px;
      color: $main-color;
      margin-left: 20px;
      letter-spacing: 2px;
    }
    .system-version {
      font-size: 14px;
      color: #ff0000;
      margin: 4px 0 0 10px;
    }
  }
  .login-warp {
    position: absolute;
    display: flex;
    width: 66%;
    height: 500px;
    top: calc(50% - 280px);
    left: 17%;
    box-shadow: 10px 10px 30px 10px rgba(0, 0, 0, 0.2);
    .left-image-carousel {
      height: 100%;
      width: 62%;
      .el-carousel__container {
        height: 100%;
        .left-image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .login-wrap-content {
      height: 100%;
      width: 45%;
      padding: 16px 40px;
      box-sizing: border-box;
      background-color: #ffffff;
      .select-language {
        // 目前还用不到，先隐藏
        display: none;
        position: relative;
        top: -5px;
        left: 340px;
        color: #000000;
        font-size: 16px;
        cursor: pointer;
        z-index: 1000;
        &:focus {
          outline: 0;
        }
        i {
          margin-left: 10px;
          color: $border-color;
          font-size: 14px;
        }
      }
      .login-tabs {
        padding-top: 10px;
        .login-pane {
          height: 220px;
          padding: 10px;
          box-sizing: border-box;
          .login-label {
            display: block;
            margin: 0 0 10px 5%;
            font-size: 20px;
          }
          .login-input {
            width: 90%;
            height: 46px !important;
            margin: 0 5% 26px 5%;
            .el-input-group__prepend {
              color: #000000;
            }
            .el-input__inner {
              font-size: 14px;
              height: 32px;
              padding: 0 8px;
              &::placeholder {
                color: $placeholder-color;
              }
            }
          }
          .login-button {
            width: 90%;
            margin: 5px 0 0 5%;
            font-size: 24px;
            height: 42px;
            background-color: $main-color;
          }
        }
        .login-type {
          margin-top: 20px;
          display: inline-block;
          width: 56px;
          height: 56px;
          line-height: 56px;
          margin-left: 108px;
          text-align: center;
          font-weight: bold;
          font-size: 20px;
          border-radius: 50px;
          color: #ffffff;
          cursor: pointer;
          box-shadow: 2px 3px 6px 4px rgba(0, 0, 0, 0.1);
          &.oa {
            background-color: #fc8720;
          }
          &.his {
            background-color: $base-color;
          }
          &:hover:not(.selected) {
            opacity: 0.7;
          }
          &.selected {
            background-color: $main-color;
          }
        }
      }
      .authorized-hospital {
        position: absolute;
        right: 30px;
        bottom: 20px;
        display: flex;
        align-items: baseline;
        font-weight: bold;
        cursor: pointer;
        .label {
          color: $main-color;
          font-size: 20px;
          cursor: default;
        }
        .authorized-by {
          font-size: 20px;
          &:focus {
            outline: 0;
          }
          .select-hospital {
            color: $placeholder-color;
          }
          i {
            margin-left: 10px;
            color: $border-color;
            font-size: 16px;
          }
        }
      }
    }
  }
  .clear-cache {
    position: absolute;
    right: 130px;
    bottom: 45px;
    color: #ffffff;
    text-decoration: underline;
    letter-spacing: 2px;
    cursor: pointer;
  }
  .company-logo {
    position: absolute;
    right: 20px;
    bottom: 20px;
    height: 84px;
    width: 84px;
    opacity: 0.5;
  }
}
</style>
