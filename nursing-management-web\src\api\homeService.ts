/*
 * FilePath     : \nursing-management-web\src\api\homeService.ts
 * Author       : 张现忠
 * Date         : 2024-07-13 16:50
 * LastEditors  : 张现忠
 * LastEditTime : 2024-12-29 17:42
 * Description  : 主页相关接口请求service
 * CodeIterationRecord:
 */
import http from "@/utils/http";
export class homeService {
  private static getToDoListApi: string = "/Home/GetToDoList";
  private static getUnExecAnnualScheduleApi: string = "/Home/GetUnExecAnnualSchedule";
  private static getHomeMessageListApi: string = "/Home/GetHomeMessageList";
  /**
   * @description 获取待办列表
   * @param params
   * @returns
   */
  public static getToDoList(params?: any) {
    return http.get(this.getToDoListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description 获取未执行年度计划
   * @param params
   * @returns
   */
  public static getUnExecAnnualSchedule(params: any) {
    return http.get(this.getUnExecAnnualScheduleApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description 获取主页中呈现的消息通知
   * @param params
   * @returns
   */
  public static getHomeMessageList(params?: any) {
    return http.get(this.getHomeMessageListApi, params, { loadingText: Loading.LOAD });
  }
}
