/*
 * FilePath     : \src\types\tableView.ts
 * Author       : 苏军志
 * Date         : 2023-10-07 19:02
 * LastEditors  : 苏军志
 * LastEditTime : 2024-06-27 19:46
 * Description  : 动态表格数据格式
 * CodeIterationRecord:
 */
/* eslint-disable */
/**
 * 表格
 */
declare interface TableView {
  /**
   * 列
   */
  columns: TableColumn[];
  /**
   * 行
   */
  rows: Record<string, any>[];
}

/**
 * 表格的列
 */
declare interface TableColumn {
  /**
   * 列序号
   */
  index: number;
  /**
   * 名称
   */
  name: string;
  /**
   * 值
   */
  value: any;
  /**
   * key, 用于关联行数据
   */
  key: string;
  /**
   * 宽度
   */
  width: string;
  /**
   * 特殊提示内容
   */
  specialContent?: string;
  /**
   * 周日标记
   */
  isSunday?: boolean;
  /**
   * 列排序
   */
  sort?: String | number;
  /**
   * 对齐方式
   */
  align: string;
  /**
   * 列冻结
   */
  fixed: "left" | "right" | true | false | undefined;
  /**
   * 合并列标记
   */
  mergeFlag?: boolean;
  /**
   * 呈现组件类型：T：输入框、TN：数字输入框
   */
  componentType?: string;
  /**
   * 子列
   */
  childColumns?: TableColumn[];
}
