<!--
 * FilePath     : \src\components\drag\index.vue
 * Author       : 郭鹏超
 * Date         : 2023-08-27 17:06
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 15:14
 * Description  : 拖拽组件
 * CodeIterationRecord:
 * 组件引用案例：
      参考页面 views\qcManagement\hierarchicalQC\hierarchicalQCSubject.vue
      引用示例 <drag
                v-if="subjecAssignDepartmentList.length"
                :draggableOption="{ itemKey: 'departmentID', groupName: 'department', put: false }"
                :multiFlag="true"
                @scrollLoad="departmentLoad"
                @remove="departmentRemove"
                v-model="subjecAssignDepartmentList"
              >
                <template #content="{ element }">
                  <tag v-if="element" :tagName="element.departmentContent" :tagTitle="'部门名称：' + element.departmentContent + ' ，部门ID：' + element.departmentID" class="tag department-tag">
                  </tag>
                </template>
              </drag>
-->
<template>
  <div class="drag-wrap">
    <draggable
      ref="draggableRef"
      class="drag"
      @change="change"
      :move="moveCheck"
      :style="{ minHeight: drugMinHeight + 'px' }"
      :itemKey="draggableOption.itemKey"
      :group="{ name: draggableOption.groupName, put: draggableOption.put, pull: draggableOption.pull }"
      :sort="sortable"
      :tag="tag"
      :handle="draggableOption.handle"
      :filter="draggableOption.filter"
      v-bind="bindAttrs"
      @update:modelValue="(val:any) => (draggableList = val)"
      animation="300"
      :multi-drag="multiFlag"
      :selected-class="selectedClass"
      v-infinite-scroll="scrollLoad"
      :infinite-scroll-distance="10"
      :infinite-scroll-immediate="true"
      v-fixCellHeight:100="'drag'"
      :componentData="draggableOption.componentData"
      :itemDataSet="draggableOption.itemDataSet"
      :drag-class="dragClass"
      :chosen-class="chosenClass"
      :ghost-class="draggableOption.ghostClass"
      :disabled="disabled"
      @select="onSelect"
      @deselect="onDeselect"
      @start="onStart"
      @contextmenu="rightClick"
    >
      <template #item="{ element, index }">
        <div :class="draggableViewClass">
          <slot name="content" :element="element" :index="index"> </slot>
        </div>
      </template>
      <template #footer>
        <slot name="footer">
          <span v-if="showTipText && tipText && !(list || draggableList)?.length" class="tip-text">{{ tipText }}</span>
        </slot>
      </template>
    </draggable>
  </div>
</template>

<script setup lang="ts">
import draggable from "@/components/drag/draggable/src/vuedraggable.js";
const props = defineProps({
  modelValue: {
    type: Array<any>,
    default: undefined
  },
  tag: {
    type: String,
    default: "div"
  },
  list: {
    type: Array<any>,
    default: undefined
  },
  draggableOption: {
    type: Object,
    default: () => {
      return {
        // 拖拽唯一标识
        itemKey: "" as string,
        // 根据groupName来判断个组件之间是否可相互拖动
        groupName: undefined as String | undefined,
        // 拖进属性
        put: undefined as Boolean | String[] | String | undefined,
        // 拖出属性
        pull: undefined as Boolean | String[] | String | undefined,
        // 希望绑定到组件根DOM上的数据
        componentData: {} as Record<string, any> | undefined,
        // 希望绑定到可拖拽元素上的数据，支持绑定返回一个对象的函数
        itemDataSet: undefined as Record<string, any> | Function | undefined,
        // class类名，用于过滤掉不需要拖拽的元素
        filter: undefined as String | Function | undefined,
        handle: undefined as String | undefined,
        ghostClass: undefined as String | undefined
      };
    }
  },
  // 批量拖拽开关
  multiFlag: {
    type: Boolean,
    default: false
  },
  // 批量拖拽选中样式
  selectedClass: {
    type: String,
    default: "selected"
  },
  // 调整拖拽内容长宽
  draggableViewClass: {
    type: String,
    default: "draggable-view"
  },
  sortable: {
    type: Boolean,
    default: true
  },
  showTipText: {
    type: Boolean,
    default: false
  },
  tipText: {
    type: String,
    default: "请拖拽到此处"
  },
  // 拖拽组件最小高度
  drugMinHeight: {
    type: Number,
    default: 0
  },
  // 是否可拖拽判断方法
  moveCheck: {
    type: Function,
    default: undefined
  },
  height: {
    type: String,
    default: "auto"
  },
  dragClass: {
    type: String,
    default: "sortable-drag"
  },
  chosenClass: {
    type: String,
    default: "sortable-chosen"
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
if (props.list && props.modelValue) {
  showMessage("error", "drag组件的modelValue 和 list props 不可同时使用! ");
}
const draggableRef = ref();
const emit = defineEmits(["update:modelValue", "added", "moved", "remove", "scrollLoad", "select", "deselect", "right-click", "start"]);
const draggableList = useVModel(props, "modelValue", emit);
const bindAttrs = computed(() => {
  return props.list ? { list: props.list } : { modelValue: draggableList.value };
});
/**
 * @description: 当绑定列表数据不为null且数组因拖放操作而发生改变时，触发事件
 * @param added.newIndex 新增元素的索引
 * @param added.element 新增元素的值
 * @param added.from 新增元素来源DOM对象
 * @param added.to 新增元素当前所处DOM对象
 * @param moved.oldIndex 移动前元素的索引
 * @param moved.newIndex 移动后元素的索引
 * @param moved.element 被移动元素的值
 * @param removed.oldIndex 删除前元素的索引
 * @param removed.element 删除元素的值
 * @param removed.from 删除元素来源DOM对象
 * @param removed.to 删除元素当前所处DOM对象
 * @return
 */
const change = ({ added, moved, removed }: any) => {
  if (added) {
    emit("added", added);
    return;
  }
  if (moved) {
    emit("moved", moved);
    return;
  }
  if (removed) {
    emit("remove", removed);
  }
};
/**
 * description: 无限加载事件
 * return {*}
 */
const scrollLoad = () => {
  emit("scrollLoad");
};
const onStart = (evt: Event) => emit("start", evt);
// 当前组件实例状态
const dragState: Record<string, any> = {
  groupName: props.draggableOption.groupName,
  dragID: common.guid(),
  selectedItems: new Set<any>()
};
/**
 * @description: 选择事件透传
 * @param evt 事件
 * @return
 */
const onSelect = (evt: Record<string, any>) => {
  const element = evt.item.__draggable_context.element;
  dragState.selectedItems.add(element);
  emit("select", element);
};
/**
 * @description: 取消选择事件透传
 * @param evt 事件
 * @return
 */
const onDeselect = (evt: Record<string, any>) => {
  const element = evt.item.__draggable_context.element;
  dragState.selectedItems.delete(element);
  emit("deselect", element);
};

/**
 * description:根据条件判断是否可拖拽
 * param {*} value
 * return {*}
 */
const moveCheck = (value: any) => {
  if (!props.moveCheck) {
    return true;
  }
  return props.moveCheck(value);
};
/**
 * @description: 右键回调
 */
const rightClick = () => {
  emit("right-click", dragState);
};
defineExpose({
  /**
   * @description: 多选
   * @param el 待选择元素DOM | 父元素DOM
   * @param element 可选，待操作元素，非空时为单个操作，反之批量操作
   * @return
   */
  toggleSelection: (type: "select" | "deselect", el: HTMLElement, element?: Record<string, any>) => {
    if (element) {
      draggableRef.value?.[type](el);
      dragState.selectedItems[type === "select" ? "add" : "remove"](element);
    } else {
      el.querySelectorAll(`.${props.draggableViewClass}`)?.forEach((itemElement) => {
        draggableRef.value?.[type](itemElement);
      });
      type === "select" ? props.modelValue?.forEach((item) => dragState.selectedItems.add(item)) : dragState.selectedItems.clear();
      // 发射事件
      emit(type);
    }
  },
  dragState
});
</script>
<style lang="scss">
.drag-wrap {
  height: v-bind(height);
  .tip-text {
    display: flex;
    justify-content: center;
    position: relative;
    top: 50%;
    margin: auto;
    z-index: 99;
    color: #999999;
    font-size: large;
  }
  .drag {
    height: 100%;
    overflow-y: auto;
    padding-bottom: 28px;
    .draggable-view {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      &.selected {
        > * {
          @include select-style();
        }
      }
    }
    &.target .draggable-view > * {
      @include select-style();
    }
  }
}
</style>
