<!--
 * FilePath     : \src\views\annualPlan\maintain\components\projectEditDialog.vue
 * Author       : 杨欣欣
 * Date         : 2023-10-11 08:39
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 14:47
 * Description  : 目标任务编辑弹窗
 * CodeIterationRecord:
 -->
<template>
  <el-drawer class="project-edit" destroy-on-close v-model="maintainStore.dialogVisible" :title="maintainStore.dialogTitle" size="100%">
    <base-layout header-height="auto">
      <template #header>
        <el-form ref="modifyForm" class="project-form" :model="projectDetail" :rules="projectRules" label-width="auto" inline>
          <el-form-item label="项目内容：" prop="content">
            <auto-complete
              type="textarea"
              label=""
              v-model="projectDetail.content"
              :options="autoCompleteOptions"
              matchMode="random"
            ></auto-complete>
          </el-form-item>
          <el-form-item label="注记：">
            <el-radio-group v-model="projectDetail.markID">
              <el-radio value="">无（去年达标持续关注）</el-radio>
              <el-radio
                v-for="projectMark in markSettings.filter((markSetting) => markSetting.groupID === 'project')"
                :key="projectMark.administrationIconID"
                :value="projectMark.administrationIconID.toString()"
              >
                <span v-html="getHtmlMark(projectMark, true)" />
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-button v-if="!readOnly" v-permission:B="1" type="primary" @click="projectInterventionTable?.addRow()" class="add-button"
          >新增</el-button
        >
      </template>
      <!-- 分解目标任务表格 -->
      <annual-intervention-maintain
        ref="projectInterventionTable"
        :projectDetailID="maintainStore.editingDetail?.detailID"
        :mainGoalID="maintainStore.editingDetail?.mainGoalID"
        :interventionList="interventionList"
      />
    </base-layout>
    <template #footer>
      <el-button @click="maintainStore.closeDialog">取消</el-button>
      <el-button v-if="!readOnly" v-permission:B="2" type="primary" @click="saveDetail">保存</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { planProject } from "../../types/annualPlanMain";
import { useDetailIcon } from "../../hooks/useDetailIcon";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";

onMounted(() => {
  getInterventionList();
  getSuperiorProjectContent();
});
const isAdd = computed(() => projectDetail.value.detailID.startsWith("temp_"));
const { annualPlanMainID, departmentID, readOnly } = storeToRefs(usePlanManagementStore());
const maintainStore = useAnnualPlanMaintainStore();
// 创建一个计算属性，确保类型为 planProject
const projectDetail = computed<planProject>({
  get: () => maintainStore.editingDetail as planProject,
  set: (value: planProject) => Object.assign(maintainStore.editingDetail!, value)
});
//#region 推荐项
watch(
  () => projectDetail.value.mainGoalID,
  async (newValue) => {
    if (!newValue) {
      return;
    }
    await getSuperiorProjectContent();
  }
);
const autoCompleteOptions = ref<OptionView[]>([]);
/**
 * @description: 获取上级目标任务描述项
 */
const getSuperiorProjectContent = async () => {
  const params = {
    year: usePlanTime().getPlanAnnual(),
    mainGoalID: projectDetail.value.mainGoalID,
    departmentID: departmentID.value
  };
  autoCompleteOptions.value = await annualPlanMainService.getSuperiorProjectDetail(params);
};
//#endregion

//#region 目标任务表单
const { markSettings, getHtmlMark } = useDetailIcon();
const projectRules = ref({
  content: [{ required: true, message: "项目内容不可为空", trigger: "blur" }]
});
const modifyForm = ref<Record<string, any>>();
/**
 * @description: 保存
 * @return
 */
const saveDetail = async () => {
  const validateResult = await useForm().validateRule(modifyForm);
  if (!validateResult) {
    showMessage("error", "请检查表单是否填写完整");
    return;
  }
  const saveProjectResult = isAdd.value
    ? await maintainStore.addNewPlanProjectToPlanGroup(projectDetail.value)
    : await maintainStore.updatePlanProject(projectDetail.value);
  if (!saveProjectResult) {
    showMessage("error", "保存目标任务失败");
  }
  maintainStore.updateOriginalDetail(projectDetail.value);
  // 项目明细保存后，保存分解目标任务
  const saveInterventions = projectInterventionTable.value?.getSaveInterventions() ?? [];
  if (!saveInterventions.length) {
    maintainStore.closeDialog();
    return;
  }
  const params = {
    annualPlanMainID: annualPlanMainID.value,
    aPInterventions: saveInterventions
  };
  const saveAnnualPlanInterventionResult = await annualPlanInterventionService.saveAnnualInterventions(params);
  if (!saveAnnualPlanInterventionResult) {
    showMessage("error", "保存分解目标任务失败");
  }
  maintainStore.closeDialog();
};
//#endregion

//#region 分解目标任务表格
watch(departmentID, async () => {
  await getInterventionList();
});
const projectInterventionTable = ref<Record<string, any>>();
const interventionList = ref<Record<string, any>[]>([]);
/**
 * @description: 获取分解目标任务字典
 */
const getInterventionList = async () => {
  const result = await annualPlanSettingService.getAnnualInterventionList({
    departmentID: departmentID.value,
    showUpperIntervention: true
  });
  interventionList.value = Object.freeze(result) as Record<string, any>[];
};
//#endregion
</script>

<style lang="scss">
.project-edit {
  .base-header {
    display: flex;
    justify-content: space-between;
  }
  .project-form {
    z-index: 999;
    .el-form-item {
      margin-bottom: 6px;
    }
  }
  .add-button {
    position: relative;
    margin-top: -35px;
    z-index: 1000;
  }
}
</style>
