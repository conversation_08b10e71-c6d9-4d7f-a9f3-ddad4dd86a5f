/*
 * FilePath     : \src\views\scheduling\hooks\useSchedulingPrint.ts
 * Author       : 马超
 * Date         : 2024-04-14 19:08
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-22 20:54
 * Description  :
 * CodeIterationRecord:
 */
export function useSchedulingPrint() {
  /**
   * @description: 获取排班岗位名称
   * @param employeeID 人员序号
   * @param post 排班岗位
   * @param columnKey 列主键
   * @param restMap 休假天数map
   * @param restPostType 休假岗位类型
   * @return
   */
  const getPostName = (
    employeeID: string,
    post: Record<string, any>,
    columnKey: string,
    restMap: Map<string, Map<string, Record<string, number>>>,
    restPostType: string
  ) => {
    let postName: string = post.departmentPostName;
    if (post.postType === restPostType && restMap?.size) {
      let restDay: number | string = "";
      if (restMap?.size) {
        const map = restMap.get(employeeID);
        if (map?.size) {
          const counts: Record<string, number> = map.get(columnKey) || {};
          restDay = counts.maternityCount || counts.annualCount || counts.count || "";
        }
        postName += String(restDay);
      }
    }
    return postName;
  };
  /**
   * @description: 筛选表格数据
   */
  const filterTableDatas = (
    source: Record<string, any>,
    item: Record<string, any>,
    criteriaKey: string,
    criteriaValue: number | undefined,
    filterKey: string
  ): Record<string, any> => {
    let result: Record<string, any> = {};
    for (const key in source) {
      const criteriaObject = item[key]?.[criteriaKey];
      if (criteriaObject) {
        const matchFound = Object.values(criteriaObject).some((subItem: any) => subItem[filterKey] === criteriaValue);
        if (matchFound) {
          result[key] = source[key];
        }
      }
    }
    return result;
  };
  /**
   * @description: 打印排班表
   * @param shiftSchedulingTable 排班表数据
   * @return
   */
  const printShiftSchedulingTable = (
    shiftSchedulingTable: TableView,
    restMap: Map<string, Map<string, Record<string, number>>>,
    restPostType: string
  ) => {
    let rowKey: Array<string> = [];
    let prinTableTitles: Array<Array<Record<string, any>>> = [];
    let oneLevelTitle: Array<Record<string, any>> = [];
    let twoLevelTitle: Array<Record<string, any>> = [];
    shiftSchedulingTable.columns.forEach((column, index) => {
      if (column.key === "statisticsColumn") {
        return;
      }
      if (index !== 1) {
        oneLevelTitle.push({
          title: column.name,
          colspan: index === 0 ? 2 : 1,
          style: { "min-width": "35px" }
        });
      }
      if (column.childColumns?.length) {
        column.childColumns.forEach((childColumn) => {
          twoLevelTitle.push({
            title: childColumn.name,
            colspan: 1,
            style: { "min-width": childColumn.key === "employeeName" ? "60px" : "35px" }
          });
          rowKey.push(childColumn.key);
        });
      }
    });
    prinTableTitles.push(oneLevelTitle);
    prinTableTitles.push(twoLevelTitle);
    let rowDatas: Array<Array<string>> = [];
    shiftSchedulingTable.rows.forEach((row) => {
      let rowData: Array<string> = [];
      for (const key of rowKey) {
        const data = row[key];
        if (["capabilityLevel", "employeeName"].includes(key)) {
          rowData.push(data || "");
        } else if (data?.noonPost) {
          let value: string = "";
          if (data.noonPost["1"]) {
            value = getPostName(row.employee.employeeID, data.noonPost["1"], key, restMap, restPostType);
          }
          if (data.noonPost["2"]) {
            const postName = getPostName(row.employee.employeeID, data.noonPost["2"], key, restMap, restPostType);
            if (postName !== value) {
              value += `<br/>${postName}`;
            }
          }
          let markValue: string = "";
          if (data.markList) {
            data.markList.forEach((mark: Record<string, any>) => {
              markValue += mark.icon;
            });
          }
          rowData.push(value + markValue);
        } else {
          rowData.push("");
        }
      }
      rowDatas.push(rowData);
    });
    return {
      prinTableTitles: prinTableTitles,
      tableData: rowDatas
    };
  };
  return { filterTableDatas, printShiftSchedulingTable };
}
