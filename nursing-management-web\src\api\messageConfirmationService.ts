import http from "@/utils/http";
import qs from "qs";
export class messageConfirmationService {
  private static routePrefix: string = "/MessageConfirmation";

  private static saveMessageConfirmationApi: string = this.routePrefix + "/SaveMessageConfirmation";
  private static getPendingConfirmationMessagesApi: string = this.routePrefix + "/GetPendingConfirmationMessages";

  /**
   * @description: 确认消息已读
   * @param params 
   * @returns 
   */
  public static confirmMessage(params: any) {
    return http.post(this.saveMessageConfirmationApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }

  /**
   * @description: 获取待确认消息信息
   * @param params 
   * @returns 
   */
  public static getPendingConfirmationMessage(params?: any) {
    return http.get(this.getPendingConfirmationMessagesApi, params, { loadingText: Loading.LOAD });
  }
}
