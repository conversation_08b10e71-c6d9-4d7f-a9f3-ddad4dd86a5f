/*
 * relative     : \src\views\qcManagement\hierarchicalQC\setting\hierarchicalQCSubject.ts
 * Author       : 郭鹏超
 * Date         : 2024-10-31 08:35
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-11-05 11:45
 * Description  : 主题维护差异配置
 * CodeIterationRecord:
 */
let { userStore } = useStore();
class hierarchicalQCSubjectOption {
  qcLevel: string;
  showImplementationDateFlag: boolean;
  departmentID?: number = undefined;
  formType: string | undefined;
  formDisabled: boolean;
  qcType: "nodeQCFormType" | "normalWorkingFormType" | undefined;
  constructor(props: Record<string, any>) {
    this.qcType = props?.qcType ?? "nodeQCFormType";
    this.qcLevel = props.routerQcLevel ?? props?.qcLevel;
    // 有参数用参数 参数为空 当质控级别为1时默认用户部门
    props.routerQcLevel === "1" && (props.departmentID ??= userStore.departmentID);
    this.departmentID = props?.departmentID;
    this.formType = props?.formType;
    this.formDisabled = Boolean(props?.formType);
    this.showImplementationDateFlag = false;
    this.getImplementationDateFlag();
  }
  /**
   * @description: 是否显示开始实施日期
   */
  getImplementationDateFlag() {
    let params: SettingDictionaryParams = {
      settingType: "HierarchicalQC",
      settingTypeCode: "SystemSwitch",
      settingTypeValue: "GetRecordByImplementationStartDate",
      index: Math.random()
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      if (respBool) {
        this.showImplementationDateFlag = true;
      }
    });
  }
}

export default hierarchicalQCSubjectOption;
