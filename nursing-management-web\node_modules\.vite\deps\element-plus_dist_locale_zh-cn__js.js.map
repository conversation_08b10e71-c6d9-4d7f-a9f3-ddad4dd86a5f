{"version": 3, "sources": ["../../element-plus/dist/locale/zh-cn.js"], "sourcesContent": ["/*! Element Plus v2.6.1 */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleZhCn = factory());\n})(this, (function () { 'use strict';\n\n  var zhCn = {\n    name: \"zh-cn\",\n    el: {\n      colorpicker: {\n        confirm: \"\\u786E\\u5B9A\",\n        clear: \"\\u6E05\\u7A7A\"\n      },\n      datepicker: {\n        now: \"\\u6B64\\u523B\",\n        today: \"\\u4ECA\\u5929\",\n        cancel: \"\\u53D6\\u6D88\",\n        clear: \"\\u6E05\\u7A7A\",\n        confirm: \"\\u786E\\u5B9A\",\n        selectDate: \"\\u9009\\u62E9\\u65E5\\u671F\",\n        selectTime: \"\\u9009\\u62E9\\u65F6\\u95F4\",\n        startDate: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n        startTime: \"\\u5F00\\u59CB\\u65F6\\u95F4\",\n        endDate: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n        endTime: \"\\u7ED3\\u675F\\u65F6\\u95F4\",\n        prevYear: \"\\u524D\\u4E00\\u5E74\",\n        nextYear: \"\\u540E\\u4E00\\u5E74\",\n        prevMonth: \"\\u4E0A\\u4E2A\\u6708\",\n        nextMonth: \"\\u4E0B\\u4E2A\\u6708\",\n        year: \"\\u5E74\",\n        month1: \"1 \\u6708\",\n        month2: \"2 \\u6708\",\n        month3: \"3 \\u6708\",\n        month4: \"4 \\u6708\",\n        month5: \"5 \\u6708\",\n        month6: \"6 \\u6708\",\n        month7: \"7 \\u6708\",\n        month8: \"8 \\u6708\",\n        month9: \"9 \\u6708\",\n        month10: \"10 \\u6708\",\n        month11: \"11 \\u6708\",\n        month12: \"12 \\u6708\",\n        weeks: {\n          sun: \"\\u65E5\",\n          mon: \"\\u4E00\",\n          tue: \"\\u4E8C\",\n          wed: \"\\u4E09\",\n          thu: \"\\u56DB\",\n          fri: \"\\u4E94\",\n          sat: \"\\u516D\"\n        },\n        months: {\n          jan: \"\\u4E00\\u6708\",\n          feb: \"\\u4E8C\\u6708\",\n          mar: \"\\u4E09\\u6708\",\n          apr: \"\\u56DB\\u6708\",\n          may: \"\\u4E94\\u6708\",\n          jun: \"\\u516D\\u6708\",\n          jul: \"\\u4E03\\u6708\",\n          aug: \"\\u516B\\u6708\",\n          sep: \"\\u4E5D\\u6708\",\n          oct: \"\\u5341\\u6708\",\n          nov: \"\\u5341\\u4E00\\u6708\",\n          dec: \"\\u5341\\u4E8C\\u6708\"\n        }\n      },\n      select: {\n        loading: \"\\u52A0\\u8F7D\\u4E2D\",\n        noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n        noData: \"\\u65E0\\u6570\\u636E\",\n        placeholder: \"\\u8BF7\\u9009\\u62E9\"\n      },\n      cascader: {\n        noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n        loading: \"\\u52A0\\u8F7D\\u4E2D\",\n        placeholder: \"\\u8BF7\\u9009\\u62E9\",\n        noData: \"\\u6682\\u65E0\\u6570\\u636E\"\n      },\n      pagination: {\n        goto: \"\\u524D\\u5F80\",\n        pagesize: \"\\u6761/\\u9875\",\n        total: \"\\u5171 {total} \\u6761\",\n        pageClassifier: \"\\u9875\",\n        page: \"\\u9875\",\n        prev: \"\\u4E0A\\u4E00\\u9875\",\n        next: \"\\u4E0B\\u4E00\\u9875\",\n        currentPage: \"\\u7B2C {pager} \\u9875\",\n        prevPages: \"\\u5411\\u524D {pager} \\u9875\",\n        nextPages: \"\\u5411\\u540E {pager} \\u9875\",\n        deprecationWarning: \"\\u4F60\\u4F7F\\u7528\\u4E86\\u4E00\\u4E9B\\u5DF2\\u88AB\\u5E9F\\u5F03\\u7684\\u7528\\u6CD5\\uFF0C\\u8BF7\\u53C2\\u8003 el-pagination \\u7684\\u5B98\\u65B9\\u6587\\u6863\"\n      },\n      messagebox: {\n        title: \"\\u63D0\\u793A\",\n        confirm: \"\\u786E\\u5B9A\",\n        cancel: \"\\u53D6\\u6D88\",\n        error: \"\\u8F93\\u5165\\u7684\\u6570\\u636E\\u4E0D\\u5408\\u6CD5!\"\n      },\n      upload: {\n        deleteTip: \"\\u6309 delete \\u952E\\u53EF\\u5220\\u9664\",\n        delete: \"\\u5220\\u9664\",\n        preview: \"\\u67E5\\u770B\\u56FE\\u7247\",\n        continue: \"\\u7EE7\\u7EED\\u4E0A\\u4F20\"\n      },\n      table: {\n        emptyText: \"\\u6682\\u65E0\\u6570\\u636E\",\n        confirmFilter: \"\\u7B5B\\u9009\",\n        resetFilter: \"\\u91CD\\u7F6E\",\n        clearFilter: \"\\u5168\\u90E8\",\n        sumText: \"\\u5408\\u8BA1\"\n      },\n      tour: {\n        next: \"\\u4E0B\\u4E00\\u6B65\",\n        previous: \"\\u4E0A\\u4E00\\u6B65\",\n        finish: \"\\u7ED3\\u675F\\u5BFC\\u89C8\"\n      },\n      tree: {\n        emptyText: \"\\u6682\\u65E0\\u6570\\u636E\"\n      },\n      transfer: {\n        noMatch: \"\\u65E0\\u5339\\u914D\\u6570\\u636E\",\n        noData: \"\\u65E0\\u6570\\u636E\",\n        titles: [\"\\u5217\\u8868 1\", \"\\u5217\\u8868 2\"],\n        filterPlaceholder: \"\\u8BF7\\u8F93\\u5165\\u641C\\u7D22\\u5185\\u5BB9\",\n        noCheckedFormat: \"\\u5171 {total} \\u9879\",\n        hasCheckedFormat: \"\\u5DF2\\u9009 {checked}/{total} \\u9879\"\n      },\n      image: {\n        error: \"\\u52A0\\u8F7D\\u5931\\u8D25\"\n      },\n      pageHeader: {\n        title: \"\\u8FD4\\u56DE\"\n      },\n      popconfirm: {\n        confirmButtonText: \"\\u786E\\u5B9A\",\n        cancelButtonText: \"\\u53D6\\u6D88\"\n      },\n      carousel: {\n        leftArrow: \"\\u4E0A\\u4E00\\u5F20\\u5E7B\\u706F\\u7247\",\n        rightArrow: \"\\u4E0B\\u4E00\\u5F20\\u5E7B\\u706F\\u7247\",\n        indicator: \"\\u5E7B\\u706F\\u7247\\u5207\\u6362\\u81F3\\u7D22\\u5F15 {index}\"\n      }\n    }\n  };\n\n  return zhCn;\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,KAAC,SAAU,QAAQ,SAAS;AAC1B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,OAAO,UAAU,QAAQ,IACxF,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,OAAO,KAC1D,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,OAAO,wBAAwB,QAAQ;AAAA,IACpH,GAAG,SAAO,WAAY;AAAE;AAEtB,UAAI,OAAO;AAAA,QACT,MAAM;AAAA,QACN,IAAI;AAAA,UACF,aAAa;AAAA,YACX,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,KAAK;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,WAAW;AAAA,YACX,SAAS;AAAA,YACT,SAAS;AAAA,YACT,UAAU;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,YACX,WAAW;AAAA,YACX,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,YACA,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,aAAa;AAAA,UACf;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,SAAS;AAAA,YACT,aAAa;AAAA,YACb,QAAQ;AAAA,UACV;AAAA,UACA,YAAY;AAAA,YACV,MAAM;AAAA,YACN,UAAU;AAAA,YACV,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,YACX,oBAAoB;AAAA,UACtB;AAAA,UACA,YAAY;AAAA,YACV,OAAO;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,UAAU;AAAA,UACZ;AAAA,UACA,OAAO;AAAA,YACL,WAAW;AAAA,YACX,eAAe;AAAA,YACf,aAAa;AAAA,YACb,aAAa;AAAA,YACb,SAAS;AAAA,UACX;AAAA,UACA,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,UACA,MAAM;AAAA,YACJ,WAAW;AAAA,UACb;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ,CAAC,QAAkB,MAAgB;AAAA,YAC3C,mBAAmB;AAAA,YACnB,iBAAiB;AAAA,YACjB,kBAAkB;AAAA,UACpB;AAAA,UACA,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,mBAAmB;AAAA,YACnB,kBAAkB;AAAA,UACpB;AAAA,UACA,UAAU;AAAA,YACR,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IAET,CAAE;AAAA;AAAA;", "names": []}