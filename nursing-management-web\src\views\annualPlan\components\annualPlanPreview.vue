<!--
 * FilePath     : /src/views/annualPlan/components/annualPlanPreview.vue
 * Author       : 杨欣欣
 * Date         : 2025-04-06 15:59
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:54
 * Description  : 年度计划预览组件
 * CodeIterationRecord:
-->
<template>
  <base-layout class="annual-plan-preview">
    <template #header>
      <slot></slot>
    </template>
    <div class="body" v-if="isDataReady">
      <div class="content" ref="leftWrapper">
        <template v-for="(planType, index) in annualPlanData?.planTypes" :key="planType.typeId">
          <div class="type-title" :id="`type-${planType.typeId}`">
            <span>分类{{ numberToChinese(index + 1) + "、" + getTypeContent(planType.typeId) }}</span>
          </div>
          <template v-for="(planGoal, goalIndex) in planType.planGoals" :key="goalIndex">
            <div class="goal-title">目标{{ numberToChinese(planGoal.sort) }}：{{ getGoalContent(planGoal.goalId) }}</div>
            <el-table class="preview-table" :data="planGoal.planGroups" border stripe>
              <el-table-column label="工作指标" :min-width="convertPX(200)">
                <template #default="{ row: planGroup }">
                  <div class="plan-indicator" v-for="(planIndicator, indicatorIndex) in planGroup.planIndicators" :key="indicatorIndex">
                    {{
                      planIndicator.sort +
                      ". " +
                      planIndicator.localShowName +
                      (planIndicator.operator === "=" || !planIndicator.operator ? "" : planIndicator.operator) +
                      (planIndicator.referenceValue ?? "") +
                      (planIndicator.unit ?? "")
                    }}
                    <span v-if="planIndicator.markId" v-html="getHtmlMark(Number(planIndicator.markId))" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="工作项目" :min-width="convertPX(200)">
                <template #default="{ row: planGroup }">
                  <div class="plan-project" v-for="(projectDetail, projectIndex) in planGroup.planProjects" :key="projectIndex">
                    {{ projectDetail.sort + ". " + projectDetail.localShowName }}
                    <span v-if="projectDetail.markId" v-html="getHtmlMark(Number(projectDetail.markId))" />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="负责部门" :width="convertPX(180)">
                <template #default="{ row: planGroup }">
                  <div
                    class="responsible-department"
                    v-for="(responsibleDepartment, index) in planGroup.responsibleDepartments"
                    :key="index"
                  >
                    {{ responsibleDepartment }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </template>
      </div>
      <div class="anchor">
        <el-anchor :container="leftWrapper">
          <el-anchor-link v-for="(anchorItem, index) in anchorData" :key="index" :href="`#${anchorItem.id}`" :title="anchorItem.title" />
        </el-anchor>
      </div>
    </div>
  </base-layout>
</template>

<script setup lang="ts">
import type { previewAnnualPlan, previewPlanType, previewPlanGoal } from "../quarterPlan/types/annualPlanPreview";
import { useDetailIcon } from "../hooks/useDetailIcon";
import { useAnchor } from "../hooks/useAnchor";

const convertPX = inject("convertPX") as (px: number) => number;
const { annualPlanMainId } = defineProps<{
  annualPlanMainId: string;
}>();
const annualPlanData = ref<previewAnnualPlan | undefined>();
const goalList = ref<Record<string, any>[]>([]);
const goalListIds = computed<number[] | undefined>(() =>
  annualPlanData.value?.planTypes
    .map<number[]>((type: previewPlanType) => type.planGoals.map<number>((goal: previewPlanGoal) => goal.goalId))
    .flat()
);
const typeListIds = computed<number[] | undefined>(() => annualPlanData.value?.planTypes.map<number>((planType) => planType.typeId).flat());

// 使用锚点组合式函数
const { leftWrapper, getTypeList, getTypeContent, createAnchorData } = useAnchor();
const anchorData = computed(() => createAnchorData(annualPlanData.value?.planTypes));

const { getHtmlMark } = useDetailIcon();
const isDataReady = ref<boolean>(false);
onMounted(async () => {
  // 启用浏览器默认的右键菜单
  document.oncontextmenu = (event) => event.stopPropagation();
  annualPlanData.value = await annualPlanOverviewService.getAnnualPlanPreview({ annualPlanMainId });
  // 并行获取类型和目标数据
  await Promise.all([getTypeList(typeListIds.value), getGoalList()]);
  isDataReady.value = true;
});

const numberToChinese = common.numberToChinese;

/**
 * @description: 组件卸载之前重新禁用右键菜单
 */
onBeforeUnmount(() => {
  document.oncontextmenu = (event) => event.preventDefault();
});

/**
 * @description: 获取目标字典
 */
const getGoalList = async () => {
  const params = {
    goalIds: goalListIds.value
  };
  goalList.value = await annualPlanSettingService.getGoalList(params);
};

/**
 * 获取年度计划目标名称
 * @param goalId 年度计划目标ID
 * @returns 年度计划目标名称
 */
const getGoalContent = (goalId: number) => {
  const goal = goalList.value.find((goal) => goal.annualGoalID === goalId);
  return goal ? goal.goalContent : "";
};
</script>

<style lang="scss" scoped>
.annual-plan-preview {
  height: 100%;
  .body {
    display: flex;
    height: 100%;
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      .type-title {
        font-size: 24px;
        color: #ff0000;
        font-weight: bold;
        margin: 4px 0;
      }
      .goal-title {
        font-size: 20px;
        font-weight: bold;
        margin: 4px 0;
      }
      .preview-table {
        margin-bottom: 8px;
        :deep(.el-table__cell) {
          vertical-align: top;
        }
        .plan-indicator,
        .plan-project,
        .responsible-department {
          font-size: 18px;
          margin: 5px 0;
          line-height: 1.6;
        }
      }
    }
    .anchor {
      background-color: #ffffff;
      width: 200px;
      padding: 24px 0 0 24px;
      :deep(.el-anchor) {
        .el-anchor__link {
          font-size: 18px;
          letter-spacing: 1px;
          line-height: 1.6;
        }
      }
    }
  }
}
</style>
