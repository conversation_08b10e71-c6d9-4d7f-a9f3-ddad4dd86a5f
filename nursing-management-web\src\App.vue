<!--
 * FilePath     : \src\App.vue
 * Author       : 苏军志
 * Date         : 2023-06-04 08:42
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-09 09:09
 * Description  : 系统入口
 * CodeIterationRecord:
-->

<template>
  <router-view></router-view>
</template>
<script setup lang="ts">
const { sessionStore } = useStore();

// 初始化调试模式快捷键
if (import.meta.env.DEV) {
  sessionStore.setupDebugModeHotkey();
}

// 禁止拖拽打开新页面
document.body.ondragover = (event) => {
  event.preventDefault();
  event.stopPropagation();
};
// 禁用浏览器默认的右键菜单
document.oncontextmenu = function (event) {
  event.preventDefault();
};
// 禁止ctrl 加 ‘+’ ‘-’ 缩放页面
document.addEventListener(
  "keydown",
  function (event) {
    if (
      (event.ctrlKey === true || event.metaKey === true) &&
      (event.which === 61 ||
        event.which === 107 ||
        event.which === 173 ||
        event.which === 109 ||
        event.which === 187 ||
        event.which === 189)
    ) {
      event.preventDefault();
    }
  },
  false
);
// 禁止ctrl+鼠标滚轮 缩放页面
window.addEventListener(
  "mousewheel",
  function (event: any) {
    if (event.ctrlKey === true || event.metaKey) {
      event.preventDefault();
    }
  },
  { passive: false }
);
// 监听页面隐藏/打开
window.addEventListener("visibilitychange", () => {
  if (document.hidden) {
    logger.setLogger({
      title: "离开系统",
      content: "浏览器最小化或其他窗口遮挡了浏览器"
    });
  } else {
    logger.setLogger({
      title: "恢复使用系统",
      content: "浏览器恢复最大化或其他遮挡浏览器的窗口关闭/最小化"
    });
  }
});

// 监听用户鼠标点击行为
window.addEventListener("mouseup", (event: any) => {
  const tagRegex = /^<(button|span|i)[^>]*>/i;
  if (tagRegex.test(event.srcElement.outerHTML)) {
    logger.setLogger({
      title: "用户点击",
      content: event.srcElement.outerHTML
    });
  }
});

// 监听报错信息
window.addEventListener("error", (err: any) => {
  logger.setLogger({
    title: "系统报错",
    content: err.message
  });
});

// 差了一点，后续再调整 取的是上次的值
window.addEventListener(
  "resize",
  () => {
    sessionStore.currRemSize = (common.session("session") || {}).currRemSize;
  },
  false
);
// 向所有子路由传递变量
provide("convertPX", (pixel: number, remFlag?: boolean) => {
  let rem = pixel / 192;
  if (remFlag) {
    return rem;
  }
  return rem * sessionStore.currRemSize;
});
</script>
