/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanGoalActions.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-27 14:33
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-05 16:00
 * Description  : 年度计划策略目标状态与行为
 * CodeIterationRecord:
 */
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";

export default function useAnnualPlanGoalActions(resetGoalsSort: () => void, convertToObject: <S extends { sort: number; }>(items: { [key: string]: S[]; } | S[], itemKey: keyof S) => Record<string, number>) {
  const planManagementStore = usePlanManagementStore();
  const maintainStore = useAnnualPlanMaintainStore();
  const expandPlanGoalIDs = ref<string[]>([]);
  const expandedPlanGoalIDs = ref<Set<string>>(new Set());

  return {
    expandPlanGoalIDs,
    expandedPlanGoalIDs,
    /**
     * @description: 记录已展开的目标面板
     * @param activePlanGoalIDs 当前展开的目标
     * @return 
     */
    addUnExpandedPlanGoalIDs: (activePlanGoalIDs: string[]) => {
      const currentExpandPlanGoalIDs = activePlanGoalIDs.filter((planGoalID) => !expandedPlanGoalIDs.value.has(planGoalID));
      if (!currentExpandPlanGoalIDs.length) {
        return;
      }
      currentExpandPlanGoalIDs.forEach((planGoalID) => expandedPlanGoalIDs.value.add(planGoalID));
      return currentExpandPlanGoalIDs;
    },
    resetPlanGoalsSort: async ({ element: draggedPlanGoal, to }: Record<string, any>) => {
      if (to) {
        draggedPlanGoal.typeID = to.dataset.typeId;
      }
      resetGoalsSort();
      const params = {
        mainID: planManagementStore.annualPlanMainID,
        draggedPlanGoalIDAndTypeID: {
          [draggedPlanGoal.mainGoalID]: draggedPlanGoal.typeID
        },
        planGoalIDAndSort: convertToObject(maintainStore.goalsByTypeID, "mainGoalID"),
        planGroupIDAndSort: convertToObject(maintainStore.planGroups, "groupID"),
        planIndicatorIDAndSort: convertToObject(maintainStore.indicatorsByGroupID, "detailID"),
        planProjectIDAndSort: convertToObject(maintainStore.projectsByGroupID, "detailID")
      };
      await annualPlanMainService.resetAnnualPlanGoalsSort(params);
    }
  };
}
