/*
 * FilePath     : \src\views\approveManagement\approveProcess\types\approveProcess.ts
 * Author       : 杨欣欣
 * Date         : 2023-09-12 10:20
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-05 10:07
 * Description  : 审批流程数据类型
 * CodeIterationRecord:
 */
export interface approveProcess {
  /**
   * 审批流程ID
   */
  approveProcessID?: string;
  /**
   * 审批名称
   */
  processName: string;
  /**
   * 审批描述
   */
  processDescription: string;
  /**
   * 流程状态（未启用、已启用、已停用）
   */
  statusCode: number;
  /**
   * 适用科室
   */
  departmentIDs: number[];
  /**
   * 适用科室名称
   */
  departmentNames: string;
  /**
   * 审批流程分类
   */
  proveCategory: string;
  /**
   * 制订者
   */
  addEmployeeName?: string;
  /**
   * 制订时间
   */
  addDateTime?: string;
  /**
   * 申请模板
   */
  contentTemplate: string;
}
