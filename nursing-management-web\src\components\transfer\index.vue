<!--
 * FilePath     : /src/components/transfer/index.vue
 * Author       : 杨欣欣
 * Date         : 2024-04-03 10:51
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 19:04
 * Description  : 可拖放的穿梭框组件
 * 参数说明：
  * @modelValue: 右侧框中的数据对应的key值
  * @data: 数据源
  * @titles: 穿梭框的标题
  * @filterable: 是否显示过滤框
  * @filterMethod: 过滤方法
  * @props: 对象，组件默认识别选项中的key、label字段，此配置可以更改识别的字段
 * 插槽说明：
  * left-header: 左侧头部插槽
  * right-header: 右侧头部插槽
 * CodeIterationRecord:
 -->
<template>
  <div class="transfer">
    <!-- 源数据 -->
    <transfer-panel
      :data="sourceData"
      :props="props.props"
      :title="titles[0]"
      :filterable="filterable"
      :filterMethod="filterMethod"
      :dragOption="{
        put: false,
        multiple: true
      }"
    >
      <template #header v-if="$slots['left-header']">
        <slot name="left-header" />
      </template>
    </transfer-panel>
    <!-- 目标数据 -->
    <transfer-panel
      v-model:data="targetData"
      :props="props.props"
      :title="titles[1]"
      :filterMethod="filterMethod"
      @added="addTargetKey"
      @remove="removeTargetKey"
      :dragOption="{
        pull: false,
        closeable: true,
        sortable: true
      }"
    >
      <template #header v-if="$slots['right-header']">
        <slot name="right-header" />
      </template>
    </transfer-panel>
  </div>
</template>
<script setup lang="ts">
import { usePropsAlias } from "./hooks/usePropsAlias";
import { computed, defineModel } from "vue";
import type { PropType } from "vue";

interface TransferItem {
  [key: string]: any;
  key?: string | number;
  label?: string;
  disabled?: boolean;
}

interface TransferProps {
  key?: string;
  label?: string;
  disabled?: string;
}

const props = defineProps({
  data: {
    type: Array as PropType<TransferItem[]>,
    default: () => []
  },
  titles: {
    type: Array as PropType<string[]>,
    default: () => ["Source", "Target"]
  },
  props: {
    type: Object as PropType<TransferProps>,
    default: () => ({ key: "key", label: "label", disabled: "disabled" })
  },
  filterable: {
    type: Boolean,
    default: false
  },
  filterMethod: {
    type: Function as PropType<(query: string, item: TransferItem) => boolean>,
    default: undefined
  }
});
const propsAlias = usePropsAlias(props);
const sourceData = computed(() => {
  return props.data.filter((item) => !targetKeys.value.includes(item[propsAlias.value.key]));
});
const targetData = computed({
  get: () => {
    return props.data
      .filter((item) => targetKeys.value.includes(item[propsAlias.value.key]))
      .sort((a, b) => {
        const indexA = targetKeys.value.indexOf(a[propsAlias.value.key]);
        const indexB = targetKeys.value.indexOf(b[propsAlias.value.key]);
        return indexA - indexB;
      });
  },
  set: (value: TransferItem[]) => {
    targetKeys.value = value.map((item) => item[propsAlias.value.key]);
  }
});

const targetKeys = defineModel<string[]>({
  required: true
});
/**
 * @description: 新增目标数据
 * @param value 被添加的元素
 * @return
 */
const addTargetKey = (value: TransferItem | TransferItem[]) => {
  if (Array.isArray(value)) {
    value.forEach((element) => {
      if (targetKeys.value.includes(element[propsAlias.value.key])) {
        return;
      }
      targetKeys.value.push(element[propsAlias.value.key]);
    });
    return;
  }
  if (targetKeys.value.includes(value[propsAlias.value.key])) {
    return;
  }
  targetKeys.value.push(value[propsAlias.value.key]);
};
/**
 * @description: 删除目标数据
 * @param value 被删除的元素
 * @return
 */
const removeTargetKey = (value: TransferItem | TransferItem[]) => {
  if (Array.isArray(value)) {
    value.forEach((element) => {
      targetKeys.value.splice(targetKeys.value.indexOf(element[propsAlias.value.key]), 1);
    });
    return;
  }
  targetKeys.value.splice(targetKeys.value.indexOf(value[propsAlias.value.key]), 1);
};
</script>
<style scoped lang="scss">
.transfer {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .transfer-buttons {
    display: inline-block;
    vertical-align: middle;
    padding: 0 30px;
    flex: 0 0 auto;
  }
}
</style>
