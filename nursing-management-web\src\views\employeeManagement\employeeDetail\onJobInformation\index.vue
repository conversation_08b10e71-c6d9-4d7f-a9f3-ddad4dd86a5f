<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\onJobInformation\index.vue
 * Author       : 张现忠
 * Date         : 2023-07-27 11:08
 * LastEditors  : 马超
 * LastEditTime : 2025-01-15 09:48
 * Description  : 人员在职信息显示
 * CodeIterationRecord:
  2023-07-31 3661-作为护理管理人员，我需要护士在职信息档案，以便查看护士相关档案信息 -zxz
-->
<template>
  <base-layout class="on-job-information" :drawerOptions="drawerOptions" :headerHeight="'0px'">
    <template #header><div></div> </template>
    <el-collapse v-model="collapse">
      <el-collapse-item v-for="(format, index) in jobFormatData" :key="index">
        <template #title>
          <span> {{ format["title"] }}</span>
          <el-tooltip content="编辑" v-if="format.key === 'employeeCapabilityLevel'">
            <i v-permission:B="3" class="iconfont icon-edit" @click.stop="edit(onJobDatas[format.key], format.key, format)"></i>
          </el-tooltip>
        </template>
        <!--  根据用户相关记录条数，循环显示内容-->
        <template v-if="onJobDatas[format.key]?.length">
          <template v-if="format.tabsFlag === true">
            <el-tabs>
              <el-tab-pane v-for="(tab, index) in onJobDatas[format.key]" :key="index">
                <template #label>
                  <span>{{ format.props.find((m) => m.key === tab.title)?.title }}</span>
                  <el-tooltip content="编辑" v-if="tab.title != 'formalPosition'">
                    <i
                      v-permission:B="3"
                      class="iconfont icon-edit"
                      @click.stop="edit(tab.children, format.key, format.props.find((m) => m.key === tab.title)!)"
                      style="margin-left: 8px; cursor: pointer"
                    ></i>
                  </el-tooltip>
                </template>
                <el-descriptions :column="2" :border="true" v-for="tabChildren in tab.children" :key="tabChildren">
                  <el-descriptions-item
                    v-for="(prop, i) in format.props[index]['prop']"
                    :key="i"
                    :label="prop.label"
                    label-class-name="description-item-label"
                    class-name="description-item-value"
                  >
                    <span v-if="prop.alias">{{ tabChildren[prop.alias] }}</span>
                    <span
                      v-else-if="prop.type == 'SD' || prop.type == 'ED'"
                      v-formatTime="{ value: tabChildren[prop.field], type: 'date', format: 'yyyy-MM-dd' }"
                    ></span>
                    <span v-else>{{ tabChildren[prop.field] }}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
            </el-tabs>
          </template>
          <template v-if="format.tabsFlag === false">
            <el-descriptions :column="2" :border="true" v-for="data in onJobDatas[format.key]" :key="data" class="description-table">
              <!-- 遍历所有的呈现项目并呈现 form形式 -->
              <el-descriptions-item
                v-for="(prop, i) in format['prop']"
                :key="i"
                :label="prop.label"
                label-class-name="description-item-label"
                class-name="description-item-value"
              >
                <span v-if="prop.alias">{{ data[prop.alias] }}</span>
                <span v-else-if="prop.type == 'D'" v-formatTime="{ value: data[prop.field], type: 'date', format: 'yyyy-MM-dd' }"></span>
                <span v-else>{{ data[prop.field] }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </template>
      </el-collapse-item>
    </el-collapse>
    <!-- 新增和修改 -->
    <template #drawerContent>
      <!-- 岗位层级 -->
      <div v-if="employeeOnDuty === 'employeeCapabilityLevel'">
        <div class="add-button-container">
          <el-button v-permission:B="3" class="add-button" @click="addRecord">新增</el-button>
        </div>
        <el-table :data="drawerDatas" style="width: 100%" border>
          <template v-for="prop in drawerFormat['prop']" :key="prop.field">
            <el-table-column :prop="prop.field" :label="prop.label">
              <template v-slot="scope">
                <div class="cell-content">
                  <capability-level-selector
                    v-model="scope.row.capabilityLevelID"
                    label=""
                    v-if="prop.type === 'capLevel'"
                  ></capability-level-selector>
                </div>
                <div class="cell-content">
                  <el-date-picker
                    v-if="prop.type == 'D'"
                    v-model="scope.row.promotionDate"
                    type="date"
                    placeholder="请选择日期"
                    value-format="YYYY-MM-DD"
                    clearable
                  ></el-date-picker>
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <!-- 任职记录 -->
      <div v-else-if="employeeOnDuty === 'employeeEmploymentRecord'">
        <div class="add-button-container">
          <el-button v-permission:B="3" class="add-button" @click="addRecord">新增</el-button>
        </div>
        <el-table :data="drawerDatas" style="width: 100%" border>
          <template v-for="prop in drawerFormat['prop']" :key="prop.field">
            <el-table-column :prop="prop.field" :label="prop.label">
              <template v-slot="scope">
                <div class="cell-content">
                  <post-selector label="" v-if="prop.type == 'Post'" v-model="scope.row.post" :width="280"></post-selector>
                </div>
                <div class="cell-content">
                  <department-selector
                    v-model="scope.row.departmentID"
                    v-if="prop.type == 'dept'"
                    label=""
                    :width="280"
                  ></department-selector>
                </div>
                <div class="cell-content">
                  <part-time-selector
                    v-model="scope.row.title"
                    v-if="prop.type == 'titleSelector'"
                    label=""
                    :width="280"
                  ></part-time-selector>
                </div>
                <div class="cell-content">
                  <part-time-selector
                    v-model="scope.row.title"
                    v-if="prop.type == 'partTimeSelector'"
                    label=""
                    :width="280"
                  ></part-time-selector>
                </div>
                <div class="cell-content">
                  <el-date-picker
                    v-if="prop.type == 'SD'"
                    v-model="scope.row.startDate"
                    type="date"
                    placeholder="请选择日期"
                    value-format="YYYY-MM-DD"
                    clearable
                    width="20px"
                  ></el-date-picker>
                </div>
                <div class="cell-content">
                  <el-date-picker
                    v-if="prop.type == 'ED'"
                    v-model="scope.row.endDate"
                    type="date"
                    placeholder="请选择日期"
                    value-format="YYYY-MM-DD"
                    clearable
                  ></el-date-picker>
                </div>
                <div class="cell-content">
                  <el-input v-if="prop.type == 'text'" v-model="scope.row.titleName"></el-input>
                </div>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" width="50px" align="center">
            <template v-slot="scope">
              <el-tooltip content="删除">
                <i v-permission:B="3" class="iconfont icon-delete" @click="deletePosition(scope.row.employeeEmploymentRecordID)"></i>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 奖惩记录 -->
      <div v-for="item in drawerDatas" :key="item" v-else>
        <el-form ref="submitRefs" label-width="120px" label-position="right" :model="item" class="form-style">
          <template v-for="prop in drawerFormat['prop']" :key="prop.label">
            <el-form-item :label="prop.label" :prop="prop.field" :required="prop.required">
              <template #error>
                <span class="errorMessage">{{ `${prop.label}为必选项` }}</span>
              </template>
              <el-input type="text" v-if="prop.type == 'T'" v-model="item[prop.field]" clearable class="form-input"></el-input>
              <el-date-picker
                v-if="prop.type == 'D'"
                v-model="item[prop.field]"
                type="date"
                placeholder="请选择日期"
                value-format="YYYY-MM-DD"
                clearable
              />
              <post-selector label="" v-if="prop.type == 'Post'" v-model="item[prop.field]" :width="386"></post-selector>
              <department-selector v-model="item[prop.field]" v-if="prop.type == 'dept'" label="" :width="386"></department-selector>
              <job-category-selector
                v-model="item[prop.field]"
                v-if="prop.field == 'jobCategoryCode'"
                label=""
                :width="386"
              ></job-category-selector>
              <title-selector v-model="item[prop.field]" v-if="prop.type == 'titleSelector'" label="" :width="386"></title-selector>
              <!-- 奖惩类型选择 -->
              <el-select v-model="item[prop.field]" v-if="prop.type == 'rewardLevel'" class="reward-select">
                <template v-for="({ label, value }, index) in rewardLevelDict" :key="index">
                  <el-option :label="label" :value="value"></el-option>
                </template>
              </el-select>
              <!-- 奖惩类型选择 -->
              <el-select v-model="item[prop.field]" v-if="prop.type == 'rewardType'" class="reward-select">
                <template v-for="({ label, value }, index) in rewardTypeDict" :key="index">
                  <el-option :label="label" :value="value"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </template>
        </el-form>
      </div>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import { useFormatData } from "./hooks/useFormatData";
import { useSelectDict } from "./hooks/useSelectDict";
import type { jobFormat } from "./types/jobFormat";
const route = useRoute();
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
const refreshEmployeeHeader: any = inject("refreshEmployeeHeader");
let rewardLevelDict = ref<Record<any, any>[]>([]);
let rewardTypeDict = ref<Record<any, any>[]>([]);
let { getRewardTypes, getRewardLevels } = useSelectDict();
let { jobFormatData } = useFormatData();
// #region 响应式变量声明
const submitRefs = ref<any>({});
// 在职信息
let onJobDatas = ref<Record<string, any>>({});
// 默认展开
let collapse = ref<string[]>([]);
jobFormatData.forEach((jobForm) => collapse.value.push(jobForm.key));
let drawerFormat = ref<any>({} as jobFormat);
let drawerDatas = ref<any>([]);
let drawerSaveClass = ref<any>("");
let employeeOnDuty = ref<String>("");
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerDirection: "rtl",
  drawerSize: "50%",
  confirm: async () => {
    await save(toRefs(submitRefs.value[0]));
  }
});
// #endregion
// 多语言处理
onMounted(async () => {
  await getDatas();
});

// #region 后端交互逻辑
/**
 * @description: 获取在职信息数据
 * @return {*}
 */
const getDatas = async () => {
  // 获取在职信息 | 用于数据呈现
  let params = {
    employeeID: route?.query?.employeeID ?? userStore.employeeID
  };
  employeeService.getOnJobInfo(params).then((respDatas: any) => {
    toggleDrawer(false);
    if (respDatas) {
      onJobDatas.value = respDatas;
    }
  });
};
/**
 * @description: 保存修改操作的数据
 * @return {*}
 */
const save = async (refs: any) => {
  if (employeeOnDuty.value === "employeeCapabilityLevel") {
    let message = hasEmptyObject();
    if (message.length > 0) {
      showMessage("warning", message);
      return;
    }
  } else if (employeeOnDuty.value === "employeeEmploymentRecord") {
    for (const data of drawerDatas.value) {
      if (!data.startDate) {
        showMessage("warning", "请填写开始时间!");
        return;
      }
      if (data.startDate && data.endDate && data.startDate > data.endDate) {
        showMessage("warning", "开始时间不能大于结束时间!");
        return;
      }
    }
  } else {
    let { validateRule } = useForm();
    if (!(await validateRule(refs))) {
      return;
    }
  }
  // 保存修改的信息
  let params = { employeeID: route?.query?.employeeID ?? userStore.employeeID } as Record<string, any>;
  if (drawerSaveClass.value === "employeeEmploymentRecord") {
    params[drawerSaveClass.value] = [{ title: drawerFormat.value.key, children: drawerDatas.value }];
  } else {
    params[drawerSaveClass.value] = Array.isArray(drawerDatas.value) ? drawerDatas.value : [drawerDatas.value];
  }
  // 请求后端获取在职信息
  await employeeService.saveOnJobInfo(params).then((result: any) => {
    if (result) {
      refreshEmployeeHeader();
      // 重新加载数据
      getDatas();
    }
  });
};
/**
 * description:数据校验
 * param {*}
 * return {*}
 */
const hasEmptyObject = () => {
  // 遍历 drawerDatas 数组
  for (let i = 0; i < drawerDatas.value.length; i++) {
    const data = drawerDatas.value[i];
    if (Object.keys(data).length === 0) {
      return "存在空行，请填写数据!";
    }
    if (!data.capabilityLevelID) {
      return "请填写护理层级!";
    }
    if (!data.promotionDate) {
      return "请填写晋级时间!";
    }
  }
  // 如果所有行数据都不是空对象，则返回 false
  return "";
};
// #endregion

// #region 前端修改新增的处理逻辑
/**
 * @description: 新增记录
 * @return {*}
 */
const addRecord = () => {
  drawerDatas.value.push({});
};
/**
 * @description: 事件-编辑
 * @return {*}
 * @param {*} data:显示的数据
 * @param {*} editFormat:JobFormat 格式
 */
const edit = async (data: [], parentTitle: string, editFormat: jobFormat) => {
  drawerFormat.value = editFormat;
  // 克隆数据，防止在抽屉中修改会一起改变页面上的数据呈现|在未保存抽屉中的数据之前
  drawerDatas.value = common.clone(data);
  drawerSaveClass.value = parentTitle;
  employeeOnDuty.value = parentTitle;
  drawerOptions.value.drawerTitle = `编辑 -${editFormat["title"]}`;
  if (editFormat.key === "employeeReward") {
    rewardTypeDict.value = await getRewardTypes();
    rewardLevelDict.value = await getRewardLevels();
  }
  toggleDrawer(true);
};
/**
 * @description: 打开/关闭抽屉
 * @param {Boolean} flag:开关
 * @return
 */
const toggleDrawer = (flag: Boolean) => {
  drawerOptions.value.showDrawer = flag;
};
const contentWidth = computed(() => `${convertPX(386)}px`);
/**
 * @description: 删除任职记录
 * @param {string} rowID:记录ID
 * @return {*}
 */
const deletePosition = (rowID: string) => {
  let params = {
    employeeEmploymentRecordID: rowID
  };
  employeeService.deleteEmployeePosition(params).then((result: any) => {
    if (result) {
      showMessage("success", "删除成功!");
      getDatas();
    }
  });
};
// #endregion
</script>

<style lang="scss">
.on-job-information {
  .icon-edit {
    margin-left: 16px;
  }
  .description-item-label {
    width: 20%;
  }
  .description-item-value {
    width: 30%;
  }
  .add-button {
    margin-right: 16px;
  }
  .form-style {
    .form-input,
    .reward-select {
      width: v-bind(contentWidth);
    }
    .el-date-editor {
      --el-date-editor-width: v-bind(contentWidth);
      .el-input__wrapper {
        width: v-bind(contentWidth);
      }
    }
    .job-category-selector {
      margin-right: 0;
    }
    .errorMessage {
      color: var(--el-color-danger);
      line-height: 1;
      position: absolute;
      top: 100%;
      left: 0;
    }
  }
  .add-button-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px; /* 为了保持与表格之间的间距 */
  }
  .cell-content {
    text-align: center;
  }
}
</style>
