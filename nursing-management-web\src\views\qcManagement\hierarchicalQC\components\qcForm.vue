<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\qcForm.vue
 * Author       : 郭鹏超
 * Date         : 2024-05-13 16:04
 * LastEditors  : 苏军志
 * LastEditTime : 2025-02-03 16:22
 * Description  :考核组件
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="qc-form" :showFooter="true" headerHeight="50px" footerHeight="auto">
    <template #header v-if="saveView.showDatePickFlag ?? true">
      <label>考核日期：</label>
      <el-date-picker
        :clearable="false"
        class="qc-date"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        v-model="saveView.qcDate"
        type="date"
        placeholder=""
        :disabled-date="disabledDate"
      ></el-date-picker>
    </template>
    <zhy-form-renderer
      v-if="formData"
      ref="rendererForm"
      :formData="formData"
      :uploadOptions="uploadOptions"
      watching
      @change="getDetails"
      :disabled="saveView.disableForm ?? false"
    />
    <template #footer>
      <div class="textarea-display" v-if="saveView.guidanceShowFlag">
        <label class="textarea-label">{{ saveView.guidanceLabel ?? "考核评价：" }}</label>
        <el-input
          type="textarea"
          v-model="saveView.guidance"
          show-word-limit
          :autosize="{ minRows: 2 }"
          :disabled="saveView.disableForm ?? false"
        ></el-input>
      </div>
      <div class="textarea-display" v-if="saveView.improvementShowFlag">
        <label class="textarea-label">{{ saveView.improvementLabel ?? "改进措施：" }}</label>
        <el-input
          type="textarea"
          v-model="saveView.improvement"
          show-word-limit
          :autosize="{ minRows: 2 }"
          :disabled="saveView.disableForm ?? false"
        ></el-input>
      </div>
    </template>
  </base-layout>
</template>

<script lang="ts" setup>
import type { dynamicFormData, formAttribute, uploadOption } from "zhytech-ui";
const { sessionStore } = useStore();
const props = defineProps({
  modelValue: {
    type: Object as () => Record<string, any>,
    default: () => {
      return {};
    }
  },
  formData: {
    type: Object as () => dynamicFormData<formAttribute>,
    default: () => {
      return undefined;
    }
  },
  disabledExamineDateStart: {
    type: String,
    default: ""
  },
  disabledExamineDateEnd: {
    type: String,
    default: ""
  }
});
// 动态表单动的上传文件参数
const uploadOptions: uploadOption = {
  serverApi: `${common.session("serverUrl")}/file/uploadFile`,
  headers: {
    "Management-Token": sessionStore.token
  },
  autoUpload: false
};
const rendererForm = ref<any>();
const emit = defineEmits(["update:modelValue", "getDetails"]);
const saveView = useVModel(props, "modelValue", emit);
const disabledDate = (date: Date) => {
  if (!props.disabledExamineDateStart && !props.disabledExamineDateEnd) {
    return false;
  }
  return (
    datetimeUtil.formatDate(date, "yyyy-MM-dd") > datetimeUtil.formatDate(props.disabledExamineDateEnd, "yyyy-MM-dd") ||
    datetimeUtil.formatDate(date, "yyyy-MM-dd") < datetimeUtil.formatDate(props.disabledExamineDateStart, "yyyy-MM-dd")
  );
};
/**
 * @description: 返回勾选内容
 * @param data
 * @return
 */
const getDetails = (data: Record<string, any>, fileList: Record<string, any>[]) => {
  emit("getDetails", data, fileList);
};
defineExpose({
  rendererForm
});
</script>
