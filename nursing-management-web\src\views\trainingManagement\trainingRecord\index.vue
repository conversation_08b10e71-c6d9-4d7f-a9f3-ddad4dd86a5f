<!--
 * FilePath     : \src\views\trainingManagement\trainingRecord\index.vue
 * Author       : 来江禹
 * Date         : 2024-06-02 16:45
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 16:09
 * Description  : 培训清单页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="training-record" :drawerOptions="drawerOptions">
    <template #header>
      <department-selector
        v-model="departmentIDs"
        label="培训部门"
        :props="{ expandTrigger: 'hover' }"
        clearable
        @change="getTrainingRecord"
      />
      <employee-selector
        label="培训讲师"
        v-model="filterTrainingLecturer"
        :showAll="true"
        :departmentID="departmentIDs"
        :filterable="true"
        :allowCreate="true"
        :clearable="true"
        @change="filterRecordByLecturer"
      />
      <el-button class="add-button" v-permission:B="1" @click="addRecord()">新增</el-button>
    </template>
    <el-table :data="trainingRecord" border stripe class="training-table">
      <el-table-column :width="convertPX(300)" label="培训地点" prop="trainingLocationName"></el-table-column>
      <el-table-column :width="convertPX(120)" label="培训方式" prop="trainingMethodName" align="center"></el-table-column>
      <el-table-column :min-width="convertPX(300)" label="培训内容" prop="trainingContent"></el-table-column>
      <el-table-column :width="convertPX(200)" label="培训目标" prop="trainingTarget"></el-table-column>
      <el-table-column :width="convertPX(120)" label="培训讲师" prop="trainingLecturerName" align="center"></el-table-column>
      <el-table-column :width="convertPX(150)" label="开始时间" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.startDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(150)" label="结束时间" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.endDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column :width="convertPX(120)" label="状态" prop="statusName" align="center"></el-table-column>
      <el-table-column label="操作" :width="convertPX(160)" align="center">
        <template #default="scope">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" v-permission:B="2" @click="addRecord(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="发布">
            <i class="iconfont icon-publish" @click="addRecord(scope.row, true)"></i>
          </el-tooltip>
          <el-tooltip content="附件查看">
            <i class="iconfont icon-preview" @click="showFilePreview(scope.row.fileInfoList)"></i>
          </el-tooltip>
          <el-tooltip content="签到码">
            <i
              v-visibilityHidden="scope.row.signInFlag && scope.row.statusCode > 1"
              class="iconfont icon-sign-in-code"
              @click="generateQRCode(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="培训记录">
            <i class="iconfont icon-employee-training-records" @click="navigateToTrainingLearnerPage(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteRecord(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <base-layout
        v-if="drawerOptions.drawerName === 'addModifyTrainingRecord'"
        class="training-drawer"
        :showHeader="false"
        :drawerOptions="childDrawerOptions"
      >
        <el-form ref="submitRefs" class="drawer-from" :label-width="convertPX(200)" :model="drawerData" :rules="rules">
          <template v-if="!isPublished">
            <el-form-item label="新增科室：">
              <department-selector v-model="drawerData.departmentID" :label="''" :width="convertPX(300)" clearable />
            </el-form-item>
            <el-form-item label="培训群组：" prop="trainingClassMainID">
              <training-class-selector
                v-model="drawerData.trainingClassMainID"
                :isMultiple="true"
                :width="convertPX(300)"
                :label="''"
                filterable
                @change="changeTrainingClass"
              ></training-class-selector>
            </el-form-item>
            <el-form-item v-if="!drawerData.trainingClassMainID" label="培训课程：" prop="courseSettingID">
              <course-setting-selector
                v-model="drawerData.courseSettingIDArr"
                :isMultiple="true"
                :width="convertPX(300)"
                :labe="''"
              ></course-setting-selector>
            </el-form-item>
            <el-form-item label="培训地点：" prop="trainingLocation">
              <el-select clearable v-model="drawerData.trainingLocation" class="from-select">
                <el-option v-for="(item, index) in trainingLocationSetting" :key="index" :label="item.value" :value="item.key" />
              </el-select>
            </el-form-item>
            <el-form-item label="培训方式：" prop="trainingMethod">
              <el-select clearable v-model="drawerData.trainingMethod" class="from-select">
                <el-option v-for="(item, index) in trainingMethodSetting" :key="index" :label="item.value" :value="item.key" />
              </el-select>
            </el-form-item>
            <el-form-item label="培训内容：" prop="content">
              <el-input type="textarea" rows="4" v-model="drawerData.trainingContent" placeholder="请输入培训内容"></el-input>
            </el-form-item>
            <el-form-item label="培训目标：" prop="trainingTarget">
              <el-input v-model="drawerData.trainingTarget" placeholder="请输入培训目标"></el-input>
            </el-form-item>
            <el-form-item label="培训讲师：" prop="trainingLecturer">
              <employee-selector
                label=""
                v-model="drawerData.trainingLecturer"
                :departmentID="drawerData.departmentID"
                :showAll="true"
                :filterable="true"
                :allowCreate="true"
                :clearable="true"
              />
            </el-form-item>
            <el-form-item label="培训主持人：" prop="trainingHost">
              <employee-selector
                label=""
                v-model="drawerData.trainingHost"
                :departmentID="drawerData.departmentID"
                :filterable="true"
                :allowCreate="true"
                :clearable="true"
                :showAll="true"
              />
            </el-form-item>
            <el-form-item label="培训评价模版：" prop="evaluationID">
              <el-button class="add-button" @click="evaluationMaintenance">维护评价模版</el-button>
            </el-form-item>
            <el-form-item label="护士长培训评价模版：" prop="headNurseEvaluationID">
              <el-button class="add-button" @click="headNurseEvaluationMaintenance">维护评价模版</el-button>
            </el-form-item>
            <el-form-item label="上传附件：" prop="files">
              <upload-file :autoUpload="false" :fileInfo="drawerData.fileInfoList" :useBtn="true" @change="getFileList"></upload-file>
            </el-form-item>
            <!-- 编辑时，不显示立即发布 -->
            <el-form-item v-if="addFlag" label="立即发布：" prop="immediateRelease">
              <el-switch v-model="drawerData.immediateRelease" />
            </el-form-item>
          </template>
          <!-- 已发布或者（新增且立即发布）的情况下显示开始结束时间 -->
          <template v-if="isPublished || (addFlag && drawerData.immediateRelease)">
            <el-form-item label="开始时间：" prop="startDateTime">
              <el-date-picker
                v-model="drawerData.startDateTime"
                class="from-select"
                type="datetime"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                placeholder="选择时间"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间：" prop="endDateTime">
              <el-date-picker
                v-model="drawerData.endDateTime"
                class="from-select"
                type="datetime"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                placeholder="选择时间"
              ></el-date-picker>
            </el-form-item>
          </template>
          <!-- 未发布、未选择培训群组时，显示报名条件 -->
          <el-form-item v-if="!isPublished && !existingTrainingClass" label="报名条件：" prop="signUpConditionExpression">
            <condition-input
              :title="'设置报名条件'"
              showStyle
              clearable
              :content="drawerData.signUpConditionContent"
              :defaultValue="drawerData.signUpConditions"
              :selectComponent="selectComponent"
              @result="setConditionData($event)"
              @clear="clearConditionData()"
            ></condition-input>
          </el-form-item>
          <template v-if="!isPublished">
            <el-form-item label="扫码签到：" prop="signInFlag">
              <el-checkbox v-model="drawerData.signInFlag" />
            </el-form-item>
            <el-form-item v-if="drawerData.signInFlag" label="签到码刷新时间（秒)：" prop="qrCodeRefreshTime">
              <el-input-number v-model="drawerData.qrCodeRefreshTime" :step="1" :min="0" />
              <div class="form-item-hint">0为不刷新</div>
            </el-form-item>
          </template>
        </el-form>
        <template #drawerContent>
          <evaluation-form :formParams="formParams"></evaluation-form>
        </template>
      </base-layout>
      <zhy-file-preview
        v-if="drawerOptions.drawerName === 'filePreview'"
        :fileList="previewFileList"
        :option="previewOptions"
      ></zhy-file-preview>
    </template>
    <el-dialog class="training-code-dialog" v-model="dialogFlag" destroy-on-close draggable title="签到码">
      <qr-code type="trainingRecord" :params="qrCodeParams" />
    </el-dialog>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入
import conditionInput from "@/components/conditionInput.vue";
import type { dynamicFormData, fileView, previewOption } from "zhytech-ui";
import type { trainingRecordView } from "../types/trainingRecord";
import type { formParam } from "../types/evaluationFormTemplate";
import { zhyFilePreview } from "zhytech-ui";
import { useDrawerToggle } from "../hooks/useDrawerToggle";
// #endif

//#region 变量定义
const { proxy } = getCurrentInstance() as any;
let { userStore } = useStore();
const router = useRouter();
const convertPX: any = inject("convertPX");
const submitRefs = ref<any>();
let departmentIDs = ref<number>(userStore.departmentID);
const trainingRecord = ref<Array<trainingRecordView>>([]);
const copyTrainingRecord = ref<Array<trainingRecordView>>([]);
const trainingLocationSetting = ref<Array<any>>([]);
const trainingMethodSetting = ref<Array<any>>([]);
const formParams = ref<formParam>({} as formParam);
const filterTrainingLecturer = ref<string>("");
const isPublished = ref<Boolean>(false);
let { getSettingDictionaryByCodeValue } = useDictionaryData();
// 报名条件设计器相关数据
let selectComponent = reactive<Record<string, any>>({});
const drawerData = ref<Partial<trainingRecordView> & { immediateRelease?: boolean }>({});
// 是否选择了培训群组
const existingTrainingClass = ref<boolean>(false);
const addFlag = ref<boolean>(false);
let { trainingClassMainID: queryTrainingClassMainID } = (useRoute().query as Record<string, any>) || {};
// 二维码参数
const qrCodeParams = ref<Record<string, any>>({});
// 弹窗是否打开
const dialogFlag = ref<boolean>(false);

const previewOptions = ref<previewOption>({
  defaultOpenFileIndex: 0,
  autoplayAudio: true,
  autoplayVideo: true
});
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  drawerSize: "40%",
  showDrawer: false,
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    // 培训群组和报名条件至少选择一个
    if (!drawerData.value.signUpConditionContent && !drawerData.value.trainingClassMainID) {
      showMessage("warning", "请选择培训群组或设置报名条件");
      return;
    }
    saveRecord(drawerData.value);
  },
  cancel: () => {
    restoreDrawerOptions();
    drawerData.value = {};
    queryTrainingClassMainID = undefined;
  }
});
const { restoreDrawerOptions, toggleDrawer } = useDrawerToggle(drawerOptions);
const childDrawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  drawerSize: "100%",
  showDrawer: false,
  confirm: () => {
    childDrawerOptions.value.showDrawer = false;
  }
});

const rules = reactive({
  courseSettingIDArr: [
    {
      required: true,
      message: "请选择课程",
      trigger: "change"
    }
  ],
  departmentID: [
    {
      required: true,
      message: "请选择新增科室",
      trigger: "change"
    }
  ],
  evaluationID: [
    {
      required: true,
      message: "请维护培训模版",
      trigger: "change"
    }
  ],
  headNurseEvaluationID: [
    {
      required: true,
      message: "请维护护士长培训模版",
      trigger: "change"
    }
  ]
});
// #endif

//#region 初始化
onMounted(async () => {
  getConditionSetting();
  getLocationAndMethodSetting();
  await getTrainingRecord();
  if (queryTrainingClassMainID) {
    // 跳转培训发布页面
    addRecord(undefined, false);
  }
});
// #endif

//#region
/**
 * @description: 新增、编辑、培训记录发布方法
 * @param row
 * @return
 */
const addRecord = (row?: trainingRecordView, published?: Boolean) => {
  isPublished.value = false;
  if (published) {
    isPublished.value = true;
  }
  const defaultRecord = {
    trainingRecordID: "",
    courseSettingIDArr: [],
    courseSettingName: "",
    trainingLocation: "",
    trainingLocationName: "",
    trainingMethod: "",
    trainingMethodName: "",
    trainingContent: "",
    trainingTarget: "",
    trainingLecturer: "",
    trainingLecturerName: "",
    trainingHost: "",
    trainingHostName: "",
    statusCode: "1",
    statusName: "未发布",
    examinationRecordID: "",
    evaluationID: "",
    headNurseEvaluationID: "",
    departmentID: userStore.departmentID,
    hospitalID: "",
    modifyEmployeeID: "",
    signUpConditionContent: "",
    signUpConditionExpression: "",
    signUpConditions: [],
    immediateRelease: false,
    // 培训群组跳转到当前页面后 新增弹窗中，默认培训群组的值
    trainingClassMainID: queryTrainingClassMainID,
    signInFlag: false,
    qrCodeRefreshTime: 0
  };
  // 判断是否是新增
  addFlag.value = !row;
  // 判断是否已经选择培训群组
  existingTrainingClass.value = Boolean(row?.trainingClassMainID);
  // 设置弹窗内容
  drawerData.value = common.clone(row) || defaultRecord;
  // 编辑时，设置发布状态
  row && row.statusCode === "2" && (drawerData.value.immediateRelease = true);
  // 打开新增、编辑弹窗
  toggleDrawer("addModifyTrainingRecord", true, "40%", row ? "编辑培训记录" : "新增培训记录", true, true);
};
/**
 * @description: 点击按钮维护培训模版
 */
const evaluationMaintenance = () => {
  childDrawerOptions.value.drawerTitle = "培训模版维护";
  childDrawerOptions.value.drawerName = "evaluationForm";
  formParams.value.formID = drawerData.value.evaluationID || undefined;
  formParams.value.saveFormMethod = saveEvaluationForm;
  childDrawerOptions.value.showDrawer = true;
  childDrawerOptions.value.showCancel = false;
  childDrawerOptions.value.showConfirm = false;
};
/**
 * @description: 点击按钮维护护士长培训模版
 */
const headNurseEvaluationMaintenance = () => {
  childDrawerOptions.value.drawerTitle = "护士长培训模版维护";
  childDrawerOptions.value.drawerName = "headNurseEvaluationForm";
  formParams.value.formID = drawerData.value.headNurseEvaluationID || undefined;
  formParams.value.saveFormMethod = saveEvaluationForm;
  childDrawerOptions.value.showDrawer = true;
  childDrawerOptions.value.showCancel = false;
  childDrawerOptions.value.showConfirm = false;
};
/**
 * @description: 保存模版方法
 * @param saveData
 */
const saveEvaluationForm = (saveData: dynamicFormData<Record<string, any>>) => {
  trainingService.saveEvaluationForm(saveData).then((res: any) => {
    if (res) {
      childDrawerOptions.value.showDrawer = false;
      childDrawerOptions.value.drawerName === "evaluationForm"
        ? (drawerData.value.evaluationID = res)
        : (drawerData.value.headNurseEvaluationID = res);
      showMessage("success", "保存成功！");
      nextTick(() => {
        proxy.$refs.submitRefs.validateField();
      });
    }
  });
};
/**
 * @description: 获取培训记录数据
 */
const getTrainingRecord = async () => {
  let params = {
    departmentIDs: typeof departmentIDs.value === "number" ? [departmentIDs.value] : departmentIDs.value
  };
  await trainingService.GetTrainingRecord(params).then((res: any) => {
    if (res) {
      trainingRecord.value = res;
      copyTrainingRecord.value = res;
    }
  });
};
/**
 * @description: 删除数据
 * @param row
 * @return
 */
const deleteRecord = (row: trainingRecordView) => {
  confirmBox("是否删除培训清单数据？", "删除培训清单数据", (flag: Boolean) => {
    if (flag) {
      if (row.trainingRecordID) {
        let params = {
          trainingRecordID: row.trainingRecordID
        };
        trainingService.deleteTrainingRecord(params).then((res: any) => {
          if (res) {
            showMessage("success", "删除成功");
            getTrainingRecord();
          } else {
            showMessage("error", "删除失败");
          }
        });
      }
    }
  });
};
/**
 * @description: 保存
 * @param data
 * @return
 */
const saveRecord = async (data: Partial<trainingRecordView> & { immediateRelease?: boolean }) => {
  // 设置发布状态
  if (isPublished.value || data.immediateRelease) {
    data.statusCode = "2";
  }
  let params = common.convertObjectToFormData(data);
  await trainingService.saveTrainingRecord(params).then((res: any) => {
    if (res) {
      showMessage("success", "保存成功");
      drawerOptions.value.showDrawer = false;
      isPublished.value = false;
      getTrainingRecord();
    }
  });
};
/**
 * @description: 根据培训讲师获取数据
 */
const filterRecordByLecturer = () => {
  if (filterTrainingLecturer.value) {
    trainingRecord.value = copyTrainingRecord.value.filter(
      (record: trainingRecordView) => record.trainingLecturer === filterTrainingLecturer.value
    );
  } else {
    trainingRecord.value = copyTrainingRecord.value;
  }
};
/**
 * @description: 设置好的条件赋值给组件属性
 * @param filterData
 * @return
 */
const setConditionData = (filterData: Record<string, any>) => {
  drawerData.value.signUpConditions = filterData.filterConditions;
  drawerData.value.signUpConditionContent = filterData.filterConditionContent;
  drawerData.value.signUpConditionExpression = filterData.filterConditionExpression;
};
/**
 * @description: 清空组件属性中的条件
 */
const clearConditionData = () => {
  drawerData.value.signUpConditions = [];
  drawerData.value.signUpConditionContent = "";
  drawerData.value.signUpConditionExpression = "";
};
const getConditionSetting = async () => {
  let params = {
    type: "TrainingRecord"
  };
  await conditionService.getConditionSelectComponent(params).then((res: any) => {
    selectComponent = res;
  });
};
/**
 * @description: 获取培训地点、方式配置
 */
const getLocationAndMethodSetting = () => {
  let params = {
    settingTypeCode: "TrainingRecord",
    settingTypeValues: ["TrainingLocation", "TrainingMethod"]
  };
  getSettingDictionaryByCodeValue(params).then((res: any) => {
    trainingLocationSetting.value = res.TrainingLocation;
    trainingMethodSetting.value = res.TrainingMethod;
  });
};
/**
 * @description: 获取文件内容
 * @param value
 * @return
 */
const getFileList = (value: any) => {
  // 过滤掉已上传的文件 已上传文件信息在fileInfoList字段中
  drawerData.value.files = value.filter(
    (fileItem: Record<string, any> | File) => !("status" in fileItem && "fileID" in fileItem && fileItem.status && fileItem.fileID)
  );
};

/**
 * @description: 跳转到人员培训记录管理页面
 * @returns
 */
const navigateToTrainingLearnerPage = (row: Record<string, any>) => {
  router.push({
    path: "/trainingLearner",
    query: {
      trainingRecordID: row.trainingRecordID
    }
  });
};
/**
 * @description: 培训群组选择改变（用来判断群组是否已经被选中，如果被选中，清除报名条件信息,以及课程选择信息）
 * @param trainingClassMainID 选择的培训群组ID
 * @return
 */
const changeTrainingClass = (trainingClassMainID: string) => {
  // 选择培训群组，清除报名条件信息,以及课程选择信息
  trainingClassMainID && (existingTrainingClass.value = true) && clearConditionData();
  // 清除选择的培训群组，修改状态
  trainingClassMainID || (existingTrainingClass.value = false);
  drawerData.value.courseSettingIDArr = [];
};
/**
 * @description: 组装二维码参数
 * @param row 当前行数据
 * @return
 */
const generateQRCode = async (row: any) => {
  qrCodeParams.value = {
    trainingRecordID: row.trainingRecordID,
    qrCodeRefreshTime: row.qrCodeRefreshTime
  };
  dialogFlag.value = true;
};
const previewFileList = ref<fileView[]>([]);
/**
 * @description: 打开文件预览弹窗,处理预览的文件
 * @param fileInfoList 文件列表
 */
const showFilePreview = (fileInfoList: Record<string, string>[] | undefined) => {
  if (!fileInfoList || !fileInfoList.length) {
    showMessage("warning", "没有可以查看的附件");
    return;
  }
  toggleDrawer("filePreview", true, "100%", "文件预览", false, false);
  previewFileList.value =
    fileInfoList.map((fileItem: Record<string, string>) => {
      return {
        file: fileItem.url,
        name: fileItem.fileName,
        extensionName: fileItem.extensionName
      } as fileView;
    }) || [];
};
// #endif
</script>
<style lang="scss">
.training-record {
  height: 100%;
  width: 100%;
  .training-table {
    height: 100%;
  }
  .training-drawer {
    .drawer-from {
      .from-select {
        width: 260px;
      }
      .from-input {
        width: 300px;
      }
      .form-item-hint {
        color: #ff0000;
        font-size: 12px;
        margin-left: 10px;
      }
    }
  }
  .training-code-dialog {
    width: 60%;
  }
}
</style>
