/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\subjectMaintenanceView.ts
 * Author       : 来江禹
 * Date         : 2023-12-04 16:23
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-10-31 16:18
 * Description  : 常态工作过程控制跳转主题维护参数
 * CodeIterationRecord:
 */
export interface skipSubjectView {
  /**
   * 质控类型 NormalWorkingFormType：常态工作控制   NodeQCFormType：节点式督导
   */
  qcType: "nodeQCFormType" | "normalWorkingFormType" | "visitsFormType" | "specialFormType" | undefined;
  /**
   * 质控级别
   */
  qcLevel: string;
  /**
   * 主题类别
   */
  formType: string;
  /**
   * 部门ID
   */
  departmentID: number;
}
