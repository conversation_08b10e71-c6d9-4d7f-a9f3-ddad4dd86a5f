<!--
 * FilePath     : \src\views\annualPlan\generalView\index.vue
 * Author       : 来江禹
 * Date         : 2024-01-13 16:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 17:25
 * Description  : 年度计划计划总览
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="general-view">
    <template #header>
      <annual-plan-header :year="year" @change="getTableData" />
    </template>
    <div>
      <el-table v-if="generalView.length > 0" :data="generalView" :span-method="tableRowSpanMethod" height="100%" border>
        <el-table-column prop="mainGoalContent" label="策略目标" :width="convertPX(400)"></el-table-column>
        <el-table-column prop="localShowName" label="分解目标任务" :min-width="convertPX(300)"></el-table-column>
        <el-table-column v-for="{ value, label } in monthSetting" :label="label" :key="value" align="center" :width="convertPX(60)">
          <template #default="{ row }">
            <span :data-column-identify="value" class="table-plan-month">
              {{ Boolean(row.planMonths?.find((planMonth: number) => planMonth == value)) ? "○" : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="principalName" align="center" label="负责人" :width="convertPX(180)"></el-table-column>
      </el-table>
      <div v-else class="empty-state">
        <div class="empty-content">
          <div class="iconfont icon-info11"></div>
          <div class="empty-title">暂无年度计划</div>
          <div class="empty-description">
            请至
            <strong>年度计划维护</strong>
            页面进行维护
          </div>
        </div>
      </div>
    </div>
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";

let monthSetting: Record<string, string | number>[] = [];
const generalView = ref<Array<Record<string, any>>>([]);
const convertPX: any = inject("convertPX");
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
const annualPlanStore = usePlanManagementStore();
const { departmentID } = storeToRefs(annualPlanStore);
const year = usePlanTime().getPlanAnnual();
onMounted(async () => {
  // 月份字典获取
  const params: SettingDictionaryParams = {
    settingType: "Common",
    settingTypeCode: "TimePeriods",
    settingTypeValue: "Monthly"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((result: any) => (monthSetting = result));
  await getTableData();
});
watch(departmentID, async () => {
  await getTableData();
});

//#region 业务逻辑
/**
 * @description: 获取数据
 */
const getTableData = async () => {
  let params = {
    year,
    departmentID: departmentID.value
  };
  generalView.value = await annualPlanInterventionService.getAnnualPlanGeneralView(params) as Array<Record<string, any>>;
  setTableRowSpanArr(generalView.value, ["mainGoalContent"]);
};
//#endregion
</script>
<style lang="scss">
.general-view {
  .base-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .table-plan-month {
    font-size: 45px;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    .empty-content {
      text-align: center;
      color: #999;
      .icon-info11 {
        font-size: 32px;
        margin-bottom: 16px;
        opacity: 0.6;
      }
      .empty-title {
        font-size: 18px;
        font-weight: 500;
        color: #666;
        margin-bottom: 8px;
      }
      .empty-description {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
      }
    }
  }
}
</style>
