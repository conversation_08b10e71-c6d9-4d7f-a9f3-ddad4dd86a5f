/*
 * FilePath     : \src\hooks\useLogout.ts
 * Author       : 杨欣欣
 * Date         : 2024-07-11 18:25
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-02 11:54
 * Description  : 注销模块
 * CodeIterationRecord:
 */
export function useLogout() {
  return {
    /**
     * @description: 注销
     */
    async loginOut() {
      let session = common.session("session");
      if (session.token) {
        await logger.setLogger({ title: "退出登录" });
        await logger.commitServer();
        userLoginService.logOut();
      }
      // 清空前端缓存
      sessionStorage.clear();
      // 这种写法会清空所有session
      location.pathname = "login";
    }
  };
}
