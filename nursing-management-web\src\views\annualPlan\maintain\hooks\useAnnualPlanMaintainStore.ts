/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanMaintainStore.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-24 15:01
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-08 20:01
 * Description  : 年度计划维护状态与行为聚合
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";
import useAnnualPlanTypeActions from "./useAnnualPlanTypeActions";
import useAnnualPlanGoalActions from "./useAnnualPlanGoalActions";
import useAnnualPlanGroupActions from "./useAnnualPlanGroupActions";
import useAnnualPlanIndicatorActions from "./useAnnualPlanIndicatorActions";
import useAnnualPlanProjectActions from "./useAnnualPlanProjectActions";
import { useAnnualPlanEditDialog } from "./useAnnualPlanEditDialog";
import type { annualPlanMaintainView, planGroup } from "../../types/annualPlanMain";

export const useAnnualPlanMaintainStore = defineStore("annualPlanMaintain", () => {
  const planTypes = ref<annualPlanMaintainView["planTypes"]>([]);
  const goalsByTypeID = ref<annualPlanMaintainView["goalsByTypeID"]>({});
  const planGroups = ref<annualPlanMaintainView["planGroups"]>([]);
  const indicatorsByGroupID = ref<annualPlanMaintainView["indicatorsByGroupID"]>({});
  const projectsByGroupID = ref<annualPlanMaintainView["projectsByGroupID"]>({});
  const { dialogTitle, dialogVisible, originalDetail, editingDetail, editingDetailType, openDialog, closeDialog, updateOriginalDetail } = useAnnualPlanEditDialog();

  //#region 私有方法
  /**
   * @description: 从分类起，逐层进行Sort重算
   */
  const resetGoalsSort = () => {
    const maintainStore = useAnnualPlanMaintainStore();
    let goalSort = 1;
    let groupSort = 1;
    // 分类、目标发生改变，会连带着分组的物理顺序也要发生改变
    const newGroups:planGroup[] = [];
    let indicatorSort = 1;
    let projectSort = 1;
    // 依照当前分类、策略目标、目标分组、策略指标、目标任务的实际物理位置进行Sort赋值
    maintainStore.planTypes.forEach((planType) => {
      const typePlanGoals = maintainStore.goalsByTypeID[planType.typeID];
      typePlanGoals.forEach((planGoal) => {
        planGoal.sort = goalSort++;
        const relationGroups = maintainStore.planGroups.filter((planGroup) => planGroup.mainGoalID === planGoal.mainGoalID);
        newGroups.push(...relationGroups);
        relationGroups?.forEach((planGroup) => {
          planGroup.sort = groupSort++;
          maintainStore.indicatorsByGroupID[planGroup.groupID]?.forEach((planIndicator) => {
            planIndicator.sort = indicatorSort++;
          });
          maintainStore.projectsByGroupID[planGroup.groupID]?.forEach((planProject) => {
            planProject.sort = projectSort++;
          });
        });
      });
    });
    maintainStore.planGroups = newGroups;
  };
  /**
   * @description: 转换为主键序号关系对象
   * @param items 要转换的对象集合
   * @param itemKey 要作为对象键的键名，取值使用
   */
  const convertToObject = <S extends { sort: number }>(items: { [key: string]: S[] } | S[], itemKey: keyof S): Record<string, number> => {
    const itemsArray = Array.isArray(items) ? items : Object.values(items).flat();
    return itemsArray.reduce((acc, obj) => {
      const key = obj[itemKey];
      acc[key as string] = obj.sort;
      return acc;
    }, {} as Record<string, number>);
  };

  /**
   * @description: 计算每个分组下新元素的序号
   * @param array 集合，按照父ID分组
   * @param parentItems 父级元素，因为计算元素是按父ID分组的
   * @param groupKey 分组的key名称
   */
  const calculateNewSortByParent = <T, P extends { sort: number }>(
    array: { [key: string]: T[] } | T[],
    parentItems: MaybeRefOrGetter<P[]>,
    groupKey: keyof P & keyof T
  ) => {
    const map = new Map<string, number>();
    let previousCount = 0;

    toValue(parentItems)
      .sort((a, b) => a.sort - b.sort)
      .forEach((item) => {
        const groupId = String(item[groupKey]);
        const currentGroupCount =
          (Array.isArray(array) ? array.filter((item) => item[groupKey] === groupId)?.length : array[groupId]?.length) ?? 0;
        // 新元素的序号 = 前面所有分组的元素数量 + 当前分组的元素数量 + 1
        const newSort = previousCount + currentGroupCount + 1;
        map.set(groupId, newSort);
        // 累加当前分组的元素数量到总数中
        previousCount += currentGroupCount;
      });

    return map;
  };
  //#endregion

  const { expandPlanGoalIDs, expandedPlanGoalIDs, addUnExpandedPlanGoalIDs, resetPlanGoalsSort } = useAnnualPlanGoalActions(resetGoalsSort, convertToObject);
  const { recommendations, addPlanGroupToPlanGoal, resetPlanGroupsSort, deletePlanGroup, savePlanGroup } = useAnnualPlanGroupActions(
    calculateNewSortByParent,
    convertToObject
  );

  const storeState = {
    planTypes,
    goalsByTypeID,
    expandedPlanGoalIDs,
    expandPlanGoalIDs,
    planGroups,
    recommendations,
    indicatorsByGroupID,
    projectsByGroupID,
    dialogTitle,
    dialogVisible,
    originalDetail,
    editingDetail,
    editingDetailType
  };
  const storeActions = {
    // 分类
    ...useAnnualPlanTypeActions(resetGoalsSort, convertToObject),
    // 策略目标
    addUnExpandedPlanGoalIDs,
    resetPlanGoalsSort,
    // 目标分组
    resetPlanGroupsSort,
    addPlanGroupToPlanGoal,
    deletePlanGroup,
    savePlanGroup,
    // 策略指标
    ...useAnnualPlanIndicatorActions(
      calculateNewSortByParent,
      convertToObject
    ),
    // 目标任务
    ...useAnnualPlanProjectActions(
      calculateNewSortByParent,
      convertToObject
    ),
    // 弹窗
    openDialog,
    closeDialog,
    updateOriginalDetail
  };
  return {
    ...storeState,
    ...storeActions
  };
});
