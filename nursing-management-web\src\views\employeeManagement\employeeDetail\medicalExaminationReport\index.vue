<!--
 * FilePath     : \src\views\employeeManagement\employeeDetail\medicalExaminationReport\index.vue
 * Author       : 胡长攀
 * Date         : 2024-08-29 08:25
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-06-23 15:46
 * Description  : 体检报告
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="medical-examination-report" :showHeader="false">
    <el-collapse v-model="activeNames" @change="openCollapse">
      <el-collapse-item
        v-for="(item, index) in employeePhyExamList"
        :key="index"
        :name="item.visitID"
        :disabled="item.resultStatus !== '9'"
      >
        <template #title>
          <div class="title">
            <span class="title-label">
              体检次数：<span class="title-label-count">{{ item.visitID }}</span>
            </span>
            <span class="title-label">
              体检日期：<span v-formatTime="{ value: item.phyExamDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
            </span>
            <span class="title-label"> 报告状态：{{ item.resultStatus === "9" ? "已出" : "未出" }} </span>
          </div>
        </template>
        <template v-for="(record, index) in examFormatData" :key="index">
          <div :class="{ detail: record.key !== 'phyExamInfo' }" v-if="record.key !== 'phyExamReport'">
            <div class="detail-title">{{ record.title }}</div>
            <el-table :data="item.employeePhyExamRecord?.[record.key]" border stripe>
              <el-table-column
                v-for="(recordColumn, index) in record.prop"
                :type="recordColumn.type"
                :key="index"
                :label="recordColumn.label"
                :prop="recordColumn.field"
                :width="!recordColumn.minWidthFlag ? convertPX(recordColumn.tableColumnWidth) : ''"
                :min-width="recordColumn.minWidthFlag ? convertPX(recordColumn.tableColumnWidth) : ''"
                :align="recordColumn.align"
                :index="indexMethod"
              >
                <template #default="scope">
                  <span v-if="recordColumn.field === 'phyExamProposal'">{{ scope.row.itemName }}：{{ scope.row.content }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div :class="[{ detail: record.key !== 'phyExamInfo' }]" v-else>
            <template v-for="(examDetail, index) in item.employeePhyExamRecord?.[record.key]" :key="index">
              <div class="detail-title">{{ examDetail.itemAssemName }}</div>
              <el-table :data="examDetail.detailItems" border stripe>
                <el-table-column
                  v-for="(recordColumn, index) in record.prop"
                  :type="recordColumn.type"
                  :key="index"
                  :label="recordColumn.label"
                  :prop="recordColumn.field"
                  :width="!recordColumn.minWidthFlag ? convertPX(recordColumn.tableColumnWidth) : ''"
                  :min-width="recordColumn.minWidthFlag ? convertPX(recordColumn.tableColumnWidth) : ''"
                  :align="recordColumn.align"
                  :index="indexMethod"
                >
                  <template v-if="recordColumn.field === 'abnormalIndicator'" #default="scope">
                    <span :class="recordColumn.className">{{
                      scope.row[recordColumn.field] === "H" ? "高" : scope.row[recordColumn.field] === "L" ? "低" : ""
                    }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </div>
        </template>
      </el-collapse-item>
    </el-collapse>
  </base-layout>
</template>
<script setup lang="ts">
import { useExamFormatData } from "./hooks/useExamFormatData";
const route = useRoute();
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
const employeePhyExamList = ref<Record<string, any>>([]);
const activeNames = ref<string[]>([]);
const { examFormatData } = useExamFormatData();
onMounted(async () => {
  await getPhyExamList();
});
/**
 * @description: 表格序号
 * @param index
 * @return
 */
const indexMethod = (index: number) => {
  return index + 1;
};
/**
 * @description: 获取员工体检报告列表
 */
const getPhyExamList = async () => {
  let params = {
    employeeID: route?.query?.employeeID ?? userStore.employeeID
  };
  employeeService.getPhyExamList(params).then((respDatas: any) => {
    if (respDatas) {
      if (respDatas.item1) {
        employeePhyExamList.value = respDatas.item2;
        return;
      }
      employeePhyExamList.value = [];
      respDatas.item2 && showMessage("warning", respDatas.item2);
    }
  });
};
/**
 * @description: 折叠面板展开
 **/
const openCollapse = async (items: any) => {
  if (items.length <= 0) {
    return;
  }
  // 获取体检次数
  let visitID = items[items.length - 1];
  let employeePhyExam = employeePhyExamList.value.find((phyExam: any) => {
    return phyExam.visitID === visitID;
  });
  // 已经获取过不再进行获取
  if (!employeePhyExam || employeePhyExam.employeePhyExamRecord) {
    return;
  }
  let params = {
    phyExamID: employeePhyExam.chartNO,
    visitID
  };
  await employeeService.getPhyExamRecord(params).then((respDatas: any) => {
    if (respDatas) {
      employeePhyExam.employeePhyExamRecord = respDatas;
    }
  });
};
</script>
<style lang="scss">
.medical-examination-report {
  height: 100%;
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title-label {
      display: flex;
      align-items: center;
      margin-right: 30px;
      .title-label-count {
        width: 30px;
        text-align: right;
      }
    }
  }
  .detail {
    margin-top: 30px;
  }
  .detail-title {
    margin: 20px 0 7px 0;
    font-weight: bold;
    color: #fc8720;
    &:first-child {
      margin: 0 0 7px 0;
    }
  }
  .status {
    font-weight: bold;
    color: red;
  }
}
</style>
