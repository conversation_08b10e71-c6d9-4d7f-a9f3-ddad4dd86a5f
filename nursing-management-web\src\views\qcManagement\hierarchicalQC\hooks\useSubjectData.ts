/*
 * FilePath     : \nursing-management-web\src\views\qcManagement\hierarchicalQC\hooks\useSubjectData.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-08 14:39
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-08 15:23
 * Description  :主题指派的页面数据钩子
 * CodeIterationRecord:
 */
/**
 * description: 主题维护初始化数据
 * return {*}
 */
import { subjectClass } from "../types/hierarchicalQCSubjectView";
export const subjectUseData = () => {
  const searchSubjectView = reactive({
    searchSubjectLevel: "3",
    searchFormID: undefined,
    searchSubjectDate: datetimeUtil.getNowDate("yyyy-MM"),
    qcFormList: new subjectClass().qcFormList
  });
  const subjectDataList = ref<Array<any>>([]);
  return {
    /**
     * 主题搜索View
     */
    searchSubjectView,
    /**
     * 主题表格数据View
     */
    subjectDataList,
    /**
     * description: 主题数据获取
     * return {*}
     */
    getSubjectData() {
      let params = {
        level: searchSubjectView.searchSubjectLevel,
        formID: searchSubjectView.searchFormID,
        yearMonth: searchSubjectView.searchSubjectDate
      };
      hierarchicalQCService.getSubjectTableView(params).then((res: any) => {
        subjectDataList.value = res;
      });
    }
  };
};
/**
 * description: 主题指派初始化数据
 * return {*}
 */
import { subjectAssignClass } from "../types/hierarchicalQCSubjectView";
export const subjectAssignUseData = () => {
  let subjectAssignTableView = reactive(new subjectAssignClass());
  let subjectAssignDepartmentList = ref<any[]>([]);
  let subjectAssignCopyDepartmentList = ref<any[]>([]);
  let assignDepartmentSearchInputData = ref("");
  let selectedDepartmentID = ref<number | undefined>();
  let formLevel = ref<string>("");
  let currEmployeeID = ref<string>();
  let upperDepartmentID = ref<number | never>();
  /**
   * description: 初始化人员
   * return {*}
   */
  let subjectAssignEmployeeList = ref<any[]>([]);
  let subjectAssignCopyEmployeeList = ref<any[]>([]);
  let assignEmployeeSearchInputData = ref("");

  /**
   * @description: 人员搜索过滤器
   * @param {Record<string, any>} item
   * @return {Boolean} 是否匹配
   */
  const employeeFilter = (item: Record<string, any>) =>
    item.employeeName.includes(assignEmployeeSearchInputData.value) ||
    item.employeeID.includes(assignEmployeeSearchInputData.value) ||
    item.namePinyin?.startsWith(assignEmployeeSearchInputData.value);
  /**
   * description: 部门无限加载
   * return {*}
   */
  const departmentLoad = () => {
    if (
      assignDepartmentSearchInputData.value ||
      subjectAssignCopyDepartmentList.value.length === subjectAssignDepartmentList.value.length
    ) {
      return;
    }
    let loadData = subjectAssignCopyDepartmentList.value.slice(
      subjectAssignDepartmentList.value.length,
      subjectAssignDepartmentList.value.length + 50
    );
    if (loadData.length === 0) {
      return;
    }
    subjectAssignDepartmentList.value = [...subjectAssignDepartmentList.value, ...loadData];
  };
  /**
   * description: 人员无限加载
   * return {*}
   */
  const employeeLoad = () => {
    // 搜索数据不加载数据
    if (assignEmployeeSearchInputData.value || subjectAssignCopyEmployeeList.value.length === subjectAssignEmployeeList.value.length) {
      return;
    }
    let loadData = subjectAssignCopyEmployeeList.value.slice(
      subjectAssignEmployeeList.value.length,
      subjectAssignEmployeeList.value.length + 50
    );
    if (loadData.length === 0) {
      return;
    }
    subjectAssignEmployeeList.value = [...subjectAssignEmployeeList.value, ...loadData];
  };
  return {
    /**
     * 当前操作人员
     */
    currEmployeeID,
    /**
     * 质控等级
     */
    formLevel,
    /**
     * 人员筛选时选择的部门ID
     */
    selectedDepartmentID,
    /**
     * 主题指派表格数据View
     */
    subjectAssignTableView,
    /**
     * 考核病区View
     */
    subjectAssignDepartmentList,
    /**
     * Copy考核病区View 搜索使用
     */
    subjectAssignCopyDepartmentList,
    /**
     * 考核病区搜索输入框数据
     */
    assignDepartmentSearchInputData,
    /**
     * 考核人员View
     */
    subjectAssignEmployeeList,
    /**
     * 考核人员搜索输入框数据
     */
    assignEmployeeSearchInputData,
    /**
     * 选择的片区ID
     */
    upperDepartmentID,
    /**
     * description: 人员无限加载
     * return {*}
     */
    employeeLoad,
    /**
     * description: 部门无限加载
     * return {*}
     */
    departmentLoad,
    /**
     * @description: 获取考核部门
     * @return {*}
     */
    getAssignDepartment() {
      hierarchicalQCService
        .getAssignDepartmentList({
          subjectID: subjectAssignTableView.hierarchicalQCSubjectID,
          formLevel: formLevel.value
        })
        .then((res: any) => {
          subjectAssignCopyDepartmentList.value = res;
          departmentLoad();
        });
    },
    /**
     * description: 部门搜索
     * return {*}
     */
    searchAssignDepartment() {
      if (assignDepartmentSearchInputData.value || upperDepartmentID.value) {
        subjectAssignDepartmentList.value = subjectAssignCopyDepartmentList.value.filter((item) => {
          if (assignDepartmentSearchInputData.value && upperDepartmentID.value) {
            return (
              item.department.includes(assignDepartmentSearchInputData.value) &&
              item.upperLevelDepartmentID === upperDepartmentID.value
            );
          }
          if (assignDepartmentSearchInputData.value && !upperDepartmentID.value) {
            return item.department.includes(assignDepartmentSearchInputData.value);
          }
          return item.upperLevelDepartmentID === upperDepartmentID.value;
        });
      } else {
        departmentLoad();
      }
    },
    /**
     * description: 获取考核人员
     * return {*}
     */
    async getAssignEmployee() {
      await hierarchicalQCService.getAssignEmployeeList({ formLevel: formLevel.value }).then((res: any) => {
        subjectAssignCopyEmployeeList.value = res;
        employeeLoad();
      });
    },
    /**
     * @description: 人员搜索
     * @return {*}
     */
    searchAssignEmployee() {
      if (!selectedDepartmentID.value && !assignEmployeeSearchInputData.value) {
        employeeLoad();
        return;
      }
      // 只筛选部门
      if (selectedDepartmentID.value && !assignEmployeeSearchInputData.value) {
        subjectAssignEmployeeList.value = subjectAssignCopyEmployeeList.value.filter((item) => {
          return item.departmentID === selectedDepartmentID.value;
        });
        return;
      }
      // 只筛选人员
      if (!selectedDepartmentID.value && assignEmployeeSearchInputData.value) {
        subjectAssignEmployeeList.value = subjectAssignCopyEmployeeList.value.filter((item) => {
          return employeeFilter(item);
        });
        return;
      }
      // 筛选部门和人员
      subjectAssignEmployeeList.value = subjectAssignCopyEmployeeList.value.filter((item: Record<string, any>) => {
        return item.departmentID === selectedDepartmentID.value && employeeFilter(item);
      });
    },

    /**
     * description: 获取指派初表格始化数据
     * param {String} subjectID
     * return {*}
     */
    getSubjectAssignView(subjectID: String) {
      hierarchicalQCService.getSubjectAssignView({ subjectID }).then((res: any) => {
        Object.assign(subjectAssignTableView, res);
      });
    }
  };
};
