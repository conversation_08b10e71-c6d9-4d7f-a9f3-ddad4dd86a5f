/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\medicalExaminationReport\hooks\useExamFormatData.ts
 * Author       : 胡长攀
 * Date         : 2024-08-29 15:51
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:16
 * Description  : 检验报告格式化数据
 * CodeIterationRecord:
 */
export function useExamFormatData() {
  const examFormatData: Record<string, any> = [
    {
      title: "基本信息",
      key: "phyExamInfo",
      prop: [
        {
          chartNO: "ID号",
          field: "chartNO",
          minWidthFlag: false,
          tableColumnWidth: 350,
          align: "center"
        },
        {
          label: "体检次数",
          field: "visitID",
          minWidthFlag: true,
          tableColumnWidth: 100,
          align: "center"
        },
        {
          label: "体检日期",
          field: "phyExamDate",
          minWidthFlag: true,
          tableColumnWidth: 100,
          align: "center"
        },
        {
          label: "结果日期",
          field: "resultDate",
          minWidthFlag: true,
          tableColumnWidth: 100,
          align: "center"
        },
        {
          label: "登记人",
          field: "registrant",
          minWidthFlag: true,
          tableColumnWidth: 100,
          align: "center"
        }
      ]
    },
    {
      title: "体检结果",
      key: "phyExamReport",
      prop: [
        {
          label: "体检项目",
          field: "peItemName",
          minWidthFlag: false,
          tableColumnWidth: 350
        },
        {
          label: "结果",
          field: "peResult",
          minWidthFlag: true,
          tableColumnWidth: 100
        },
        {
          label: "正常范围",
          field: "printContext",
          minWidthFlag: true,
          tableColumnWidth: 100
        },
        {
          label: "单位",
          field: "units",
          minWidthFlag: true,
          tableColumnWidth: 100
        },
        {
          label: "状态",
          field: "abnormalIndicator",
          minWidthFlag: true,
          tableColumnWidth: 100,
          className: "medical-examination-report-status",
          align: "center"
        }
      ]
    },
    {
      title: "科室小结",
      key: "phyExamDeptResult",
      prop: [
        {
          label: "体检项目",
          field: "itemName",
          minWidthFlag: false,
          tableColumnWidth: 350
        },
        {
          label: "科室小结",
          field: "content",
          minWidthFlag: true,
          tableColumnWidth: 300
        }
      ]
    },
    {
      title: "结果汇总",
      key: "phyExamResult",
      prop: [
        {
          label: "体检项目",
          field: "itemName",
          minWidthFlag: false,
          tableColumnWidth: 350
        },
        {
          label: "结果汇总",
          field: "content",
          minWidthFlag: true,
          tableColumnWidth: 300
        }
      ]
    },
    {
      title: "体检建议",
      key: "phyExamProposal",
      prop: [
        {
          label: "序号",
          field: "",
          minWidthFlag: false,
          tableColumnWidth: 100,
          type: "index",
          align: "center"
        },
        {
          label: "体检建议",
          field: "phyExamProposal",
          minWidthFlag: true,
          tableColumnWidth: 300
        }
      ]
    }
  ];
  return {
    /**
     * 表格格式内容
     */
    examFormatData
  };
}
