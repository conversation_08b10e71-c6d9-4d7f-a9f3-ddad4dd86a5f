<!--
 * FilePath     : \src\components\selector\proveCategorySelector.vue
 * Author       : 杨欣欣
 * Date         : 2023-12-02 14:54
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-12-07 09:40
 * Description  : 审批分类级联选择器
 * CodeIterationRecord:
 -->
<template>
  <div class="prove-category-selector">
    <span v-if="label">{{ label }}：</span>
    <el-cascader v-model="selectCategories" v-bind="attrs" class="cascader-component" @change="onSelectOptionsChange" />
  </div>
</template>

<script lang="ts" setup>
// #region props
const props = defineProps({
  modelValue: {
    type: [String, Array<String>]
  },
  label: {
    type: String,
    default: "分类"
  },
  clearable: {
    type: Boolean,
    default: true
  },
  showAllLevels: {
    type: Boolean,
    default: false
  },
  props: {
    type: Object,
    default: () => {
      return {
        expandTrigger: "hover",
        emitPath: false
      };
    }
  },
  width: {
    type: Number,
    default: 200
  }
});
// 获取父组件传来的其余属性
const otherAttrs = useAttrs();
// el-cascader组件的props属性部分属性默认值
const defaultCascaderProps = {
  expandTrigger: "hover",
  emitPath: false
};
// 组装、透传props与事件给el-cascader组件
const attrs = computed(() => ({
  ...otherAttrs,
  clearable: props.clearable,
  "show-all-levels": props.showAllLevels,
  props: {
    ...defaultCascaderProps,
    ...props.props
  },
  // 组件固定参数，覆盖外部传递值
  options: options.value,
  placeholder: `请选择${props.label}`,
  "collapse-tags": true,
  "collapse-tags-tooltip": true
}));
// #endregion

const options = ref<CascaderList<string>[]>([]);
const { getCascaderFullValue } = useCascaderFullValue();
const isMultiple = Boolean(props.props.multiple);
const emits = defineEmits(["update:modelValue", "change", "select"]);
const modelCategory = useVModel(props, "modelValue", emits);
// 内部维护el-cascader组件的双向绑定值
const selectCategories = ref<string[] | string[][]>([]);

/**
 * @description: 在选择项发生变化时触发
 * @param value 选中的节点的值
 * @return
 */
const onSelectOptionsChange = (value: string | string[]) => {
  modelCategory.value = value;
  const [, selectItems] = getCascaderFullValue(value, options.value, "value", "children", isMultiple);
  emits("select", selectItems);
  emits("change", value);
};

// 初始化时获取候选项
onMounted(() => {
  useDictionaryData()
    .getProveCategoryCascaderData()
    .then((data) => {
      options.value = data;
      // 因el-cascader组件绑定的值需要是由该节点所在各级菜单的值组成的数组，所以需要对传入的modelValue进行转换
      const result = getCascaderFullValue(props.modelValue, options.value, "value", "children", isMultiple);
      if (isMultiple) {
        [selectCategories.value] = result as [string[][], Record<string, any>[]];
      } else {
        [selectCategories.value] = result as [string[], Record<string, any>];
      }
    });
});

const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const cascaderWidth = computed(() => `${convertPX(width.value)}px`);
</script>
<style lang="scss">
.prove-category-selector {
  display: inline-block;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include cascader-component-style(v-bind(cascaderWidth));
}
</style>
