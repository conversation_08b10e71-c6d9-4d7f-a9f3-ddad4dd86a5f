/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\personalInformation\components\table\tableHeader.ts
 * Author       : 来江禹
 * Date         : 2023-08-01 09:41
 * LastEditors  : 马超
 * LastEditTime : 2024-09-24 20:29
 * Description  : 个人信息表格组件使用表格列TS配置
 * CodeIterationRecord:3671-作为护理管理人员，我需要护士个人信息档案，以便查看护士相关档案信息
 */
import type { tableHeaders } from "@/views/employeeManagement/employeeDetail/personalInformation/types/tableView";
export default {
  contactHeader: [
    {
      prop: "contactWay",
      label: "联系方式",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "contactContent",
      label: "联系内容",
      minWidth: 160,
      align: "left"
    }
  ],
  educationalHeader: [
    {
      prop: "educationName",
      label: "学历",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "graduateSchool",
      label: "毕业院校",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "graduationMajor",
      label: "毕业专业",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "entryDate",
      label: "入学日期",
      width: 140,
      align: "left"
    },
    {
      prop: "graduationDate",
      label: "毕业日期",
      width: 140,
      align: "left"
    },
    {
      prop: "educationStatus",
      label: "状态",
      width: 100,
      align: "left"
    },
    {
      prop: "educationType",
      label: "学历类型",
      width: 100,
      align: "left"
    },
    {
      prop: "remark",
      label: "备注",
      minWidth: 350,
      align: "left"
    }
  ],
  workExperienceHeader: [
    {
      prop: "startDate",
      label: "开始时间",
      width: 100,
      align: "left"
    },
    {
      prop: "endDate",
      label: "结束时间",
      width: 100,
      align: "left"
    },
    {
      prop: "company",
      label: "公司名称",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "department",
      label: "所在部门",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "post",
      label: "职务",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "remark",
      label: "备注",
      minWidth: 350,
      align: "left"
    }
  ],
  skillHeader: [
    {
      prop: "skillClassification",
      label: "技能分类",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "skill",
      label: "技能名称",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "proficiencyLevel",
      label: "熟练程度",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "certificate",
      label: "证书",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "remark",
      label: "备注",
      minWidth: 350,
      align: "left"
    }
  ],
  positionHeader: [
    {
      prop: "professionalType",
      label: "专业分类",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "professionalLevel",
      label: "专业等级",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "obtainingDate",
      label: "获得职称时间",
      width: 140,
      align: "left"
    },
    {
      prop: "certificateNo",
      label: "证书编号",
      minWidth: 120,
      align: "left"
    }
  ],
  relativesHeader: [
    {
      prop: "relationship",
      label: "关系",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "relativesName",
      label: "姓名",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "relativesPhoneNumber",
      label: "手机号",
      width: 120,
      align: "left"
    },
    {
      prop: "relativesIDCardNo",
      label: "身份证号",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "relativesNativePlace",
      label: "籍贯",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "relativesEducation",
      label: "学历",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "relativesSchool",
      label: "学校",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "relativesCompany",
      label: "任职单位",
      minWidth: 160,
      align: "left"
    },
    {
      prop: "relativesPost",
      label: "担任职务",
      minWidth: 120,
      align: "left"
    }
  ],
  strongPointHeader: [
    {
      prop: "strengthName",
      label: "个人特长",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "honor",
      label: "相关荣誉",
      minWidth: 120,
      align: "left"
    },
    {
      prop: "controls",
      label: "操作",
      minWidth: 15,
      align: "center"
    }
  ]
} as tableHeaders;
