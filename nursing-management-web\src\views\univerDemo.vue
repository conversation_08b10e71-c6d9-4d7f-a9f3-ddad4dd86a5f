<!--
 * FilePath     : \src\views\univerDemo.vue
 * Author       : 苏军志
 * Date         : 2024-09-28 11:08
 * LastEditors  : 苏军志
 * LastEditTime : 2024-09-28 15:03
 * Description  :
 * CodeIterationRecord:
 -->
<template>
  <div class="univer-demo">
    <univer-sheet :data="data"></univer-sheet>
  </div>
</template>
<script setup lang="ts">
const data = ref({
  sheetOrder: ["sheet1"],
  sheets: {
    sheet1: {
      id: "sheet1",
      name: "工作表 1",
      rowCount: 19,
      columnCount: 31,
      defaultColumnWidth: 30,
      defaultRowHeight: 30,
      mergeData: [],
      cellData: {
        "1": {
          "0": {
            v: 1
          },
          "1": {
            v: 2
          },
          "12": {
            v: 2
          },
          "3": {
            v: 2
          },
          "4": {
            v: 2
          },
          "5": {
            v: 2
          },
          "6": {
            v: 2
          },
          "7": {
            v: 2
          }
        }
      },
      rowData: [],
      columnData: [],
      rowHeader: { width: 20 },
      columnHeader: { height: 20 }
    }
  }
});
</script>
<style lang="scss">
.univer-demo {
  height: 100%;
  width: 100%;
}
</style>
