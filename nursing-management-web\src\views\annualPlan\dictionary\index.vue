<!--
 * FilePath     : \src\views\annualPlan\dictionary\index.vue
 * Author       : 苏军志
 * Date         : 2024-02-04 16:58
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-01-04 15:44
 * Description  : 年度计划字典维护相关
 * CodeIterationRecord:
-->
<template>
  <top-menu class="ap-dict-top-menu" :menuList="tabMenus" ref="childPage"></top-menu>
</template>

<script setup lang="ts">
const { sessionStore } = useStore();
const route = useRoute();
let childPage = ref<any>();
let parentRouterName = route.matched[route.matched.length - 1]?.name as string;
const tabMenus = ref(sessionStore.pageTopMenus[parentRouterName]);
// 处理按F5刷新
if (!tabMenus.value) {
  parentRouterName = route.matched[route.matched.length - 2]?.name as string;
  tabMenus.value = sessionStore.pageTopMenus[parentRouterName];
}

// 暴漏给父路由
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    nextTick(() => {
      if (childPage?.value) {
        childPage?.value.refreshData();
      }
    });
  }
});
</script>
<style lang="scss">
.ap-dict-top-menu {
  .base-content .base-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .annual-plan-header {
      padding-top: 8px;
    }
  }
}
</style>
