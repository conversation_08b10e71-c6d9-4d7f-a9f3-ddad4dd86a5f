/*
 * FilePath     : \src\types\advancedEmployeeSelectorTypes.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-30 16:29
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 18:15
 * Description  : 高级用户选择器相关类型定义，用于支持用户和用户组的复杂选择场景
 * CodeIterationRecord: 
 */

/**
 * 初始化用户信息
 */
export interface initialEmployee {
  /**
   * 用户唯一标识ID
   */
  id: string;
}

/**
 * 初始化用户组信息
 */
export interface initialEmployeeGroup {
  /**
   * 用户组唯一标识ID
   */
  id: number;
  /**
   * 用户组名称
   */
  name: string;
  /**
   * 用户组成员列表
   */
  members: initialEmployee[];
}

/**
 * 已选择的用户或用户组
 */
export interface selectedEmployeeOrGroup {
  /**
   * 用户组ID，个人时为空
   */
  groupID?: number;
  /**
   * 用户组名称，个人时为空
   */
  groupName?: string;
  /**
   * 用户ID
   */
  employeeID: string;
  /**
   * 用户姓名
   */
  employeeName: string;
}