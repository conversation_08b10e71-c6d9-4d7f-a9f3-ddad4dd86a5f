<!--
 * FilePath     : \src\views\annualPlan\dictionary\annualPlanGoalList.vue
 * Author       : 胡长攀
 * Date         : 2024-01-28 11:55
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-05 11:20
 * Description  : 年度计划目标字典维护
 -->
<template>
  <base-layout class="annual-plan-goal-list" :drawerOptions="drawerOptions">
    <template #header>
      <annual-plan-header :year="year" />
      <el-button v-permission:B="1" @click="addAnnualPlanMainGoal" class="add-button">{{ i18nText.add }}</el-button>
    </template>
    <el-table class="annual-plan-goal-list-table" :data="annualPlanGoalList" stripe border>
      <el-table-column prop="annualPlanTypeContent" :label="i18nText.annualPlanTypeContent" :min-width="convertPX(150)" />
      <el-table-column prop="annualPlanGoalContent" :label="i18nText.annualPlanGoalContent" :min-width="convertPX(250)" />
      <el-table-column prop="addEmployeeName" :label="i18nText.addPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.addDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" :label="i18nText.modifyPerson" :width="convertPX(120)" align="center" />
      <el-table-column :label="i18nText.modifyDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column :label="i18nText.operation" :width="convertPX(80)" align="center">
        <template #default="{ row }">
          <el-tooltip :content="i18nText.edit">
            <i v-permission:B="3" @click="editAnnualPlanMainGoal(row)" class="iconfont icon-edit" />
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i v-permission:B="4" @click="deleteAnnualPlanMainGoal(row)" class="iconfont icon-delete" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="editAnnualPlanMainGoalForm" :model="editingAnnualPlanMainGoal" :rules="rules" label-position="left" label-width="auto">
        <el-form-item :label="i18nText.annualPlanTypeContent" prop="annualPlanTypeID">
          <el-select v-model="editingAnnualPlanMainGoal.annualPlanTypeID" placeholder="请选择分类" clearable>
            <el-option
              v-for="(item, index) in annualPlanTypeList"
              :key="index"
              :label="item.annualPlanTypeContent"
              :value="item.annualPlanTypeID"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="i18nText.annualPlanGoalContent" prop="annualPlanGoalContent">
          <el-input
            :placeholder="i18nText.annualPlanGoalContent"
            v-model="editingAnnualPlanMainGoal.annualPlanGoalContent"
            :rows="3"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
const { proxy } = getCurrentInstance() as any;
const convertPX: any = inject("convertPX");
const year = usePlanTime().getPlanAnnual();
const annualPlanGoalList = ref<Record<string, any>>([]);
const annualPlanTypeList = ref<Record<string, any>>([]);
const managementStore = usePlanManagementStore();

onMounted(async () => await init());
watch(
  () => managementStore.departmentID,
  async () => {
    await init();
  }
);
/**
 * @description: 数据初始化
 */
const init = async () => {
  await Promise.all([getAnnualPlanMainGoalList(), getAnnualPlanTypeListByDepartment()]);
};
/**
 * @description: 获取年度计划指标字典
 */
const getAnnualPlanMainGoalList = async () => {
  const params = {
    departmentID: managementStore.departmentID
  };
  annualPlanGoalList.value = await annualPlanMainService.getAnnualPlanMainGoalList(params);
};
/**
 * @description: 获取年度计划分类（本部门及上级、间接上级部门分类）
 */
const getAnnualPlanTypeListByDepartment = async () => {
  annualPlanTypeList.value = await annualPlanSettingService.getAnnualPlanTypeListByDepartment({
    departmentID: managementStore.departmentID
  });
};

const editingAnnualPlanMainGoal = ref<Record<string, any>>({});
const editAnnualPlanMainGoalForm = ref<any>();
const currentRow = ref<Record<string, any>>({});

//#region 弹窗
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  cancel: () => closeDrawer(),
  confirm: async () => await saveAnnualPlanMainGoal()
});
/**
 * @description: 新增指标字典
 */
const addAnnualPlanMainGoal = () => {
  drawerOptions.value.drawerTitle = i18nText.value.add;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 编辑指标字典
 * @param row 当前行
 * @return
 */
const editAnnualPlanMainGoal = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  drawerOptions.value.drawerTitle = i18nText.value.edit;
  drawerOptions.value.showDrawer = true;
  editingAnnualPlanMainGoal.value = common.clone(row);
  currentRow.value = common.clone(row);
};
/**
 * @description: 关闭弹窗
 */
const closeDrawer = () => {
  drawerOptions.value.showDrawer = false;
  editingAnnualPlanMainGoal.value.annualPlanMainGoalID = "";
  editingAnnualPlanMainGoal.value.annualPlanTypeID = undefined;
  editingAnnualPlanMainGoal.value.annualPlanGoalID = undefined;
  editingAnnualPlanMainGoal.value.annualPlanGoalContent = "";
  editingAnnualPlanMainGoal.value.departmentID = undefined;
  currentRow.value = {};
};
//#endregion

//#region 表单
const rules = ref({
  annualPlanTypeID: [{ required: true, message: "请选择分类", trigger: "change" }],
  annualPlanGoalContent: [{ required: true, message: "请输入目标名称", trigger: "blur" }]
});
/**
 * @description: 保存分类目标
 */
const saveAnnualPlanMainGoal = async () => {
  let { validateRule } = useForm();
  if (!(await validateRule(editAnnualPlanMainGoalForm.value))) {
    showMessage("warning", "请填写必填项");
    return;
  }
  if (
    currentRow.value.annualPlanMainGoalID &&
    currentRow.value.departmentID !== managementStore.departmentID &&
    currentRow.value.annualPlanGoalContent !== editingAnnualPlanMainGoal.value.annualPlanGoalContent
  ) {
    showMessage("warning", "非本部门制定的目标不可修改目标名称！");
    return;
  }
  const params = {
    annualPlanMainID: editingAnnualPlanMainGoal.value.annualPlanMainID,
    annualPlanMainGoalID: editingAnnualPlanMainGoal.value.annualPlanMainGoalID,
    annualPlanTypeID: editingAnnualPlanMainGoal.value.annualPlanTypeID,
    annualPlanGoalID: editingAnnualPlanMainGoal.value.annualPlanGoalID,
    annualPlanGoalContent: editingAnnualPlanMainGoal.value.annualPlanGoalContent,
    departmentID: managementStore.departmentID,
    year
  };
  annualPlanSettingService.saveAnnualPlanMainGoal(params).then(() => {
    drawerOptions.value.showDrawer = false;
    showMessage("success", "保存成功");
    closeDrawer();
    getAnnualPlanMainGoalList();
  });
};
//#endregion

/**
 * @description: 删除分类字典
 * @param row 当前行
 * @return
 */
const deleteAnnualPlanMainGoal = async (row: Record<string, any>) => {
  if (!row?.annualPlanMainGoalID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }

  confirmBox("确定删除？", proxy.$t("tip.systemTip"), async (flag: boolean) => {
    if (!flag) {
      return;
    }
    // 添加检核逻辑
    if (!(await checkAnnualPlanMainGoal(row))) {
      return;
    }
    await annualPlanSettingService.deleteAnnualPlanMainGoal(row).then(() => {
      showMessage("success", "删除成功");
      getAnnualPlanMainGoalList();
    });
  });
};

/**
 * @description: 检核该目标是否可删除
 * @param row 当前行
 * @return {*}
 */
const checkAnnualPlanMainGoal = async (row: Record<string, any>) => {
  let flag = false;
  await annualPlanSettingService
    .checkAnnualPlanMainGoal({
      annualPlanMainGoalID: row.annualPlanMainGoalID,
      departmentID: managementStore.departmentID
    })
    .then((res) => {
      if (res) {
        flag = true;
      }
    });
  return flag;
};

/**
 * @description: 多语言处理
 */
const i18nText = computed(() => {
  return {
    sort: proxy.$t("label.sort"),
    add: proxy.$t("button.add"),
    delete: proxy.$t("tip.delete"),
    edit: proxy.$t("tip.edit"),
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    buttonSave: proxy.$t("button.save"),
    cancel: proxy.$t("button.cancel"),
    annualPlanTypeContent: proxy.$t("annualPlanGoalList.annualPlanTypeContent"),
    annualPlanGoalContent: proxy.$t("annualPlanGoalList.annualPlanGoalContent"),
    addPerson: proxy.$t("annualPlanGoalList.addPerson"),
    addDateTime: proxy.$t("annualPlanGoalList.addDateTime"),
    modifyPerson: proxy.$t("annualPlanGoalList.modifyPerson"),
    modifyDateTime: proxy.$t("annualPlanGoalList.modifyDateTime")
  };
});
</script>
<style lang="scss">
.annual-plan-goal-list {
  .el-select {
    width: 160px;
  }
  .annual-plan-goal-list-table {
    height: 100%;
  }
}
</style>
