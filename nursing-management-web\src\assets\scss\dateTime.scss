.el-input__prefix {
  color: $base-color !important;
}

.el-picker-panel {
  .el-date-picker__header,
  .el-date-range-picker__header {
    background-color: $base-color;
    margin: 0;
    padding: 5px 10px;
    > div,
    .el-date-picker__header-label {
      color: #ffffff;
      font-weight: bold;
      margin-top: 4px;
      padding: 0;
    }
    .el-picker-panel__icon-btn {
      font-size: 20px;
      margin-top: 0px;
      color: #ffffff;
    }
  }
  .el-year-table,
  .el-month-table,
  .el-date-table {
    font-size: 14px;
    th {
      padding-bottom: 12px;
    }
    td {
      &.current {
        &:not(.disabled) {
          span {
            background-color: #ffc600;
          }
        }
      }
      &.available {
        &:hover {
          color: #ffc600;
        }
        &.in-range .el-date-table-cell {
          color: $base-color;
          font-weight: bold;
        }
        &.start-date .el-date-table-cell__text,
        &.end-date .el-date-table-cell__text {
          background-color: #ffc600;
        }
      }
      &.today {
        span {
          color: $base-color;
        }
      }
    }
  }
  .el-date-table {
    td {
      padding: 0;
      height: 24px;
      width: 20px;
      span {
        height: 24px;
        width: 24px;
        line-height: 24px;
      }
    }
  }
}

/* 时间 */
.el-time-spinner__item.is-active:not(.is-disabled),
.el-time-spinner__item:hover {
  color: $base-color !important;
}

/* 日期范围 */
.el-date-editor {
  .el-range__icon {
    color: $base-color;
    font-size: 20px;
    margin-right: 5px;
  }
  .el-range__close-icon {
    display: none;
  }
}
