<!--
 * FilePath     : \src\views\post\departmentPostToCapabilityLevel.vue
 * Author       : 张现忠
 * Date         : 2023-08-20 10:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:36
 * Description  : 部门岗位能级对照关系配置页面
 * CodeIterationRecord:
-->
<template>
  <div class="department-post-to-capability-level">
    <base-layout :drawerOptions="drawerOptions">
      <template #header>
        <File class="right-file" :fileOption="fileOption" @getExcelData="getExcelData"></File>
        <el-button @click="addRecord" class="add-button" v-permission:B="1">新增</el-button>
      </template>
      <el-table :data="dataList" element-loading-text="Loading" border fit highlight-current-row :span-method="tableRowSpanMethod">
        <el-table-column :label="i18nText.postName" :min-width="convertPX(150)" align="center" prop="postName"> </el-table-column>
        <el-table-column label="条件" :min-width="convertPX(90)" align="center" prop="condition"> </el-table-column>
        <el-table-column label="护理能级" :min-width="convertPX(150)" align="center" prop="capabilityLevel"> </el-table-column>
        <el-table-column :label="i18nText.startDate" :min-width="convertPX(150)" align="center">
          <template v-slot="scope">
            <span v-formatTime="{ value: scope.row.startDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column :label="i18nText.endDate" :min-width="convertPX(150)" align="center">
          <template v-slot="scope">
            <span v-formatTime="{ value: scope.row.endDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column :label="i18nText.status" :width="convertPX(100)" align="center">
          <template v-slot="scope">
            <el-tag :type="getUseStatusTag(scope.row.statusCode)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="i18nText.modifyPerson" prop="modifyPerson" :min-width="convertPX(150)" align="center"></el-table-column>
        <el-table-column :label="i18nText.modifyDateTime" :min-width="convertPX(200)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
        <el-table-column :label="i18nText.operation" :width="convertPX(80)">
          <template v-slot="scope">
            <el-tooltip content="修改" placement="top" :enterable="false">
              <i class="iconfont icon-edit" @click="updateRecord(scope.row)" v-permission:B="3"></i>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" :enterable="false">
              <i class="iconfont icon-delete" @click="deleteRecord(scope.row)" v-permission:B="4"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- 修改和新增弹窗 -->
      <template #drawerContent>
        <el-form ref="submitRefs" :model="currRow" :label-width="convertPX(170)" class="form-style" :rules="rules">
          <el-form-item label="部门：" prop="departmentID">
            <department-selector v-model="currRow.departmentID" :disabled="disabledEditFlag" label="" :width="386"></department-selector>
          </el-form-item>
          <el-form-item label="岗位名称：" prop="postID">
            <post-selector label="" v-model="currRow.postID" :width="386"></post-selector>
          </el-form-item>
          <el-form-item label="条件：" prop="condition">
            <el-radio-group v-model="currRow.condition">
              <el-radio label=">=" value=">=" />
              <el-radio label="<=" value="<=" />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="护理能级：" prop="capabilityLevelID">
            <capability-level-selector label="" v-model="currRow.capabilityLevelID" :width="386"></capability-level-selector>
          </el-form-item>
          <el-form-item label="开始时间：" prop="startDate">
            <el-date-picker v-model="currRow.startDate" value-format="YYYY-MM-DD" :disabled-date="disabledStartDate" />
          </el-form-item>
          <el-form-item label="结束时间：" prop="endDate">
            <el-date-picker v-model="currRow.endDate" value-format="YYYY-MM-DD" :disabled-date="disabledEndDate" />
          </el-form-item>
          <el-form-item label="状态：" prop="statusCode">
            <el-radio-group v-model="currRow.statusCode">
              <template v-for="(radio, index) in options" :key="index">
                <el-radio :label="radio.value">{{ radio.label }}</el-radio>
              </template>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </template>
    </base-layout>
  </div>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const { userStore } = useStore();
const { getUseStatusTag } = useStatusTag();
const { setTableRowSpanArr, tableRowSpanMethod } = useTable();
// #region 响应式变量
const submitRefs = ref({}) as any;
const currRow = ref<{}>() as any;
const options = ref<Record<any, any>[]>([]);
// 编辑，主键不可修改
const disabledEditFlag = ref(false);
let dataList = ref<Record<any, any>[]>([]);
let rules = reactive({
  departmentID: [{ required: true, message: "请选择部门科室", trigger: "change" }],
  postID: [{ required: true, message: "请选择岗位", trigger: "change" }],
  capabilityLevelID: [{ required: true, message: "请选择能级", trigger: "change" }],
  startDate: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endDate: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  statusCode: [{ required: true, message: "请选择状态", trigger: "change" }],
  condition: [{ required: true, message: "请选择条件", trigger: "change" }]
});
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    await saveRecord(currRow.value);
  }
});
let dictionData = ref<any>([]);
let dictionLevelData = ref<any>([]);
let departMentData = ref<any>([]);
// #endregion

// #region 事件触发响应式数据处理
// 挂载时获取列表数据
onMounted(() => {
  getDepartmentPostToCapabilityLevels();
});
// 通过hooks从数据库获取数据
let { getPostData } = useDictionaryData();
// 通过hooks从数据库获取能级
let { getCapabilityLevelData } = useDictionaryData();
// 通过hooks从数据库获取科室字典
let { getDepartmentCascaderData } = useDictionaryData();
/**
 * @description: 获取岗位列表
 */
getPostData().then((datas) => {
  datas.forEach((post) => {
    dictionData.value.push({
      postID: post.value,
      postName: post.label
    });
  });
});
getCapabilityLevelData().then((datas) => {
  datas.forEach((capabilityLevel) => {
    dictionLevelData.value.push({
      capabilityLevelID: capabilityLevel.value,
      capabilityLevel: capabilityLevel.label
    });
  });
  sortByKeys(dictionLevelData.value, ["capabilityLevelID"]);
});

getDepartmentCascaderData("1", []).then((datas) => {
  datas.forEach((departmentCasCader) => {
    departmentCasCader.children.forEach((child) => {
      departMentData.value.push({
        departMentID: child.value,
        departMentName: child.label
      });
    });
    departMentData.value.push({
      departMentID: departmentCasCader.value,
      departMentName: departmentCasCader.label
    });
  });
});
// #region 事件触发响应式数据处理
// 挂载时获取列表数据
onMounted(() => {
  getDepartmentPostToCapabilityLevels();
});
const disabledEndDate = (date: any) => {
  let startDate = currRow.value.startDate;
  return startDate ? date < new Date(startDate) : false;
};
const disabledStartDate = (date: any) => {
  let endDate = currRow.value.endDate;
  return endDate ? date > new Date(endDate) : false;
};

/**
 * @description: 新增按钮处理
 */
const addRecord = () => {
  disabledEditFlag.value = false;
  // 新增内容默认启用状态
  currRow.value = { statusCode: "1", departmentID: userStore.departmentID };
  drawerOptions.value.drawerTitle = "新增";
  toggleDrawer(true);
};

let exportExcelData = ref<any>([
  {
    departmentName: ""
  }
]);
let templateColumn = ref<{}>({
  departmentID: "部门编码",
  postID: "岗位编码",
  condition: "条件('>='或者'<='')",
  capabilityLevelID: "能级编码",
  startDate: "开始日期",
  endDate: "结束日期",
  statusCode: "状态(0暂停,1启用)"
});
// Excel 导出参数
const exportExcelOption = reactive<ExportExcelView[]>([
  {
    buttonName: "导出模板",
    fileName: "部门岗位能级对照",
    sheetName: "部门岗位能级对照",
    columnData: templateColumn,
    tableData: exportExcelData.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位能级对照",
    sheetName: "部门字典",
    columnData: {
      departMentID: "部门编码",
      departMentName: "部门名称"
    },
    tableData: departMentData.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位能级对照",
    sheetName: "岗位字典",
    columnData: {
      postID: "岗位编码",
      postName: "部门岗位名称"
    },
    tableData: dictionData.value
  },
  {
    buttonName: "导出模板",
    fileName: "部门岗位能级对照",
    sheetName: "能级字典",
    columnData: {
      capabilityLevelID: "能级编码",
      capabilityLevel: "能级名称"
    },
    tableData: dictionLevelData.value
  }
]);
/**
 * @description: Excel 导入参数
 */
const importExcelOption = reactive<ImportExcelView>({
  columnData: templateColumn,
  buttonName: "导入数据"
});
/**
 * @description: 修改按钮 点击事件处理
 * @param row
 */
const updateRecord = (row: any) => {
  disabledEditFlag.value = true;
  currRow.value = common.clone(row);
  drawerOptions.value.drawerTitle = "修改";
  toggleDrawer(true);
};
/**
 * @description: file组件参数
 */
const fileOption = reactive<FilePropsView>({
  typeArr: ["exportExcel", "importExcel"],
  exportExcelOption,
  importExcelOption
});
/**
 * @description: 打开\关闭抽屉-编辑或新增窗口
 */
const toggleDrawer = (switchFlag: boolean) => {
  // 获取字典数据
  const param: SettingDictionaryParams = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCForm",
    settingTypeValue: "StatusCode"
  };
  settingDictionaryService.getSettingDictionaryDict(param).then((datas: any) => {
    options.value = datas;
  });
  drawerOptions.value.showDrawer = switchFlag;
};
// #endregion

// #region 后端交互逻辑
/**
 * @description: 删除按钮 点击事件处理
 * @param  row
 */
const deleteRecord = async (row: any) => {
  if (!row?.postID || !row?.departmentID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  await deleteConfirm(i18nText.value.deleteConfirm, (confirm: boolean) => {
    if (!confirm) {
      return;
    }
    let params = {
      postID: row.postID,
      departmentID: row.departmentID,
      capabilityLevelID: row.capabilityLevelID
    };
    postService.deleteDepartmentPostToCapabilityLevel(params).then((respDatas: any) => {
      if (respDatas) {
        showMessage("success", "删除成功");
        getDepartmentPostToCapabilityLevels();
      }
    });
  });
};
/**
 * @description: Excel导入数据处理
 * @param importData
 * @return
 */
const getExcelData = async (importData: any) => {
  if (importData.length === 0) {
    return;
  }
  let params: any[] = [];
  importData.forEach((importItem: any) => {
    let param: any = {
      capabilityLevelID: importItem.capabilityLevelID,
      condition: importItem.condition,
      departmentID: importItem.departmentID,
      endDate: datetimeUtil.formatDate(importItem.endDate, "yyyy-MM-dd hh:mm"),
      postID: importItem.postID,
      startDate: datetimeUtil.formatDate(importItem.startDate, "yyyy-MM-dd hh:mm"),
      statusCode: String(importItem.statusCode)
    };
    params.push(param);
  });

  if (params.length === 0) {
    return;
  }
  await postService.saveDepartmentPostToCapabilityLevelList(params).then((res: any) => {
    if (res) {
      showMessage("success", "保存成功");
      getDepartmentPostToCapabilityLevels();
    }
  });
};

/**
 * @description: 获取列表数据 - 部门岗位与能级关系
 */
const getDepartmentPostToCapabilityLevels = async (deptID: any = undefined) => {
  let params = {
    departmentID: deptID ?? userStore.departmentID
  };
  await postService.getDepartmentPostToCapabilityLevels(params).then((respData: any) => {
    dataList.value = respData;
    // 合并行
    setTableRowSpanArr(dataList.value, ["postName"]);
  });
};

/**
 * @description: 保存记录
 * @param params
 */
const saveRecord = async (params: any) => {
  await postService.saveDepartmentPostToCapabilityLevel(params).then((respFlag: any) => {
    if (respFlag) {
      getDepartmentPostToCapabilityLevels(currRow.value.departmentID);
      showMessage("success", "保存成功");
    } else {
      showMessage("error", "保存部门岗位/能级对照记录失败");
    }
    toggleDrawer(false);
  });
};
// #endregion

// #region 多语言处理
const i18nText = computed(() => {
  return {
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    department: proxy.$t("label.department"),
    cancel: proxy.$t("button.cancel"),
    postName: proxy.$t("departmentPostToCapabilityLevel.postName"),
    startDate: proxy.$t("departmentPostToCapabilityLevel.startDate"),
    status: proxy.$t("departmentPostToCapabilityLevel.status"),
    endDate: proxy.$t("departmentPostToCapabilityLevel.endDate"),
    modifyPerson: proxy.$t("departmentPostToCapabilityLevel.modifyPerson"),
    modifyDateTime: proxy.$t("departmentPostToCapabilityLevel.modifyDateTime")
  };
});
// #endregion
// 用于调整时间选择器的宽度与其他选择器宽度相同
const datePickerWidth = computed(() => `${convertPX(386)}px`);
</script>
<style lang="scss">
.department-post-to-capability-level {
  height: 100%;
  width: 100%;
  // form表单中的所有选择组件对齐
  .form-style {
    margin: 0;
    padding-top: 2%;
    .el-date-editor {
      .el-input__wrapper {
        width: v-bind(datePickerWidth);
      }
    }
  }
}
.right-file {
  display: block;
  float: right;
}
</style>
