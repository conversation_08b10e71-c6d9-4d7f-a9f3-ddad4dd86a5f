/*
 * FilePath     : \src\hooks\useUtils.ts
 * Author       : 马超
 * Date         : 2025-03-02 11:13
 * LastEditors  : 马超
 * LastEditTime : 2025-03-02 11:15
 * Description  :
 * CodeIterationRecord:
 */
type MType = keyof typeof MessageType;
import common from "@/utils/common";
import datetimeUtil from "@/utils/datetimeUtil";
export function useUtils() {
  return {
    ...common,
    ...datetimeUtil,
    /*
     * @description: 提示信息
     * @param type 类型
     * @param message 消息内容
     * @return
     */
    showAlert(type: MType, message: string, title?: string, btnName?: string) {
      let confirmButtonText = btnName == "是" ? "是" : "确定";
      let tipTitle = title || "系统提示";
      return ElMessageBox.alert(message, tipTitle, {
        type: type,
        confirmButtonText: confirmButtonText,
        dangerouslyUseHTMLString: true
      });
    },
    /**
     * @description: 当前登录画面是否为护士长
     * @return
     */
    isHeadNurse() {
      const roles = useStore().userStore.roles as number[];
      return roles.includes(40);
    }
  };
}
