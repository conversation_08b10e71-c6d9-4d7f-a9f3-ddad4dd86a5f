/*
 * FilePath     : /src/api/employeeService.ts
 * Author       : 来江禹
 * Date         : 2023-08-01 17:31
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-04 16:54
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class employeeService {
  private static getEmployeePersonalDataAPi: string = "/Employee/GetEmployeePersonalData";
  private static getOnJobInfoApi: string = "/Employee/GetOnJobInfo";
  private static saveOnJobInfoApi: string = "/Employee/SaveOnJobInfo";
  private static getPersonalFileHeaderInfoApi: string = "/Employee/GetPersonalFileHeaderInfo";
  private static getEmployeeListApi: string = "/Employee/GetEmployeeList";
  private static getEmployeeSecondmentRecordListApi: string = "/Employee/GetEmployeeSecondmentRecordList";
  private static deleteEmployeeSecondmentRecordApi = "/Employee/DeleteEmployeeSecondmentRecord";
  private static saveEmployeeSecondmentRecordApi = "/Employee/SaveEmployeeSecondmentRecord";
  private static getEmployeeTeachingRelationViewApi: string = "/Employee/GetEmployeeTeachingRelationView";
  private static saveTeachRelationApi: string = "/Employee/SaveTeachRelation";
  private static bachSaveTeachRelationApi: string = "/Employee/BachSaveTeachRelation";
  private static stopTeachRelationApi: string = "/Employee/StopTeachRelation";
  private static deleteTeachRelationApi: string = "/Employee/DeleteTeachRelation";
  private static getDepartmentChangeRequestEmployeeByDepartmentIDApi: string = "/Employee/GetDepartmentChangeRequestEmployeeByDepartmentID";
  private static saveEmployeeDepartmentChangeRequestApi: string = "/Employee/SaveEmployeeDepartmentChangeRequest";
  private static getEmployeeDepartmentChangeRequestViewApi: string = "/Employee/GetEmployeeDepartmentChangeRequestView";
  private static deleteEmployeeDepartmentChangeRequestApi: string = "/Employee/DeleteEmployeeDepartmentChangeRequest";
  private static saveEmployeeClothingSizesApi: string = "/Employee/SaveEmployeeClothingSizes";
  private static getEmployeeToDepartmentsApi: string = "/Employee/GetEmployeeToDepartments";
  private static getEmployeeDepartmentsByEmployeeIDApi: string = "/Employee/GetEmployeeDepartmentsByEmployeeID";
  private static deleteEmployeeToDepartmentByEmployeeIDApi: string = "/Employee/DeleteEmployeeToDepartmentByEmployeeID";
  private static saveEmployeeToDepartmentsApi: string = "/Employee/SaveEmployeeToDepartments";
  private static saveEmployeeStrengthsApi: string = "/Employee/saveEmployeeStrengths";
  private static getEmployeeToDeptByEmployeeIDAndOrganizationTypeApi: string = "/Employee/GetEmployeeToDeptByEmployeeIDAndOrganizationType";
  private static getPhyExamListApi: string = "/Employee/GetPhyExamList";
  private static getPhyExamRecordApi: string = "/Employee/GetPhyExamRecord";
  private static deleteEmployeeStrengthsApi: string = "/Employee/DeleteEmployeeStrengths";
  private static deleteEmployeePositionApi: string = "/Employee/DeleteEmployeePosition";
  private static getEmployeeToRoleApi: string = "/Employee/GetEmployeeToRole";
  private static getEmployeeToDepartmentApi: string = "/Employee/GetEmployeeToDepartment";
  private static getEmployeeDepartmentIDAndNamesApi: string = "/Employee/GetEmployeeDepartmentIDAndNames";
  private static getEmployeesInformationApi: string = "/Employee/GetEmployeesInformation";
  private static getEmployeeResignationListApi: string = "/Employee/GetEmployeeResignationList";
  private static getEmployeesAsOptionsApi: string = "/Employee/GetEmployeesAsOptions";
  /**
   * description: 获取用户基本信息
   * return {*}
   */
  public static getEmployeePersonalData(params?: any) {
    return http.get(this.getEmployeePersonalDataAPi, params, { loadingText: Loading.LOAD });
  }
  /**
   * description: 获取用户在职相关信息
   * return {*}
   */
  public static getOnJobInfo(params: any) {
    return http.get(this.getOnJobInfoApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * description: 保存用户在职相关信息
   * return {*}
   */
  public static saveOnJobInfo(params: any) {
    return http.post(this.saveOnJobInfoApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * description: 获取用户个人档案头信息
   * return {*}
   */
  public static getPersonalFileHeaderInfo(params: any) {
    return http.get(this.getPersonalFileHeaderInfoApi, params);
  }
  /**
   * description: 获取人员清单
   * return {*}
   */
  public static getEmployeeList(params: any) {
    return http.post(this.getEmployeeListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取人员借调数据
   * @param params
   * @return
   */
  public static getEmployeeSecondmentRecordList(params: any) {
    return http.get(this.getEmployeeSecondmentRecordListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除人员借调数据
   * @param params
   * @return
   */
  public static deleteEmployeeSecondmentRecord(params: any) {
    return http.post(this.deleteEmployeeSecondmentRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存人员借调数据
   * @param params
   * @return
   */
  public static saveEmployeeSecondmentRecord(params: any) {
    return http.post(this.saveEmployeeSecondmentRecordApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存人员借调数据
   * @param params
   * @return
   */
  public static getEmployeeTeachingRelationView(params: any) {
    return http.get(this.getEmployeeTeachingRelationViewApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存无证护士带教关系
   * @param params
   * @return
   */
  public static saveTeachRelation(params: any) {
    return http.post(this.saveTeachRelationApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 批量保存无证护士带教关系
   * @param params
   * @return
   */
  public static bachSaveTeachRelation(params: any) {
    return http.post(this.bachSaveTeachRelationApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 停止无证护士带教关系
   * @param params
   * @return
   */
  public static stopTeachRelation(params: any) {
    return http.post(this.stopTeachRelationApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除无证护士带教关系
   * @param params
   * @return
   */
  public static deleteTeachRelation(params: any) {
    return http.post(this.deleteTeachRelationApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 根据部门ID获取部门可调整部门人员
   */
  public static getDepartmentChangeRequestEmployeeByDepartmentID(params: any) {
    return http.get(this.getDepartmentChangeRequestEmployeeByDepartmentIDApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存人员部门变更申请
   */
  public static saveEmployeeDepartmentChangeRequest(params: any) {
    return http.post(this.saveEmployeeDepartmentChangeRequestApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取人员部门变更申请记录
   */
  public static getEmployeeDepartmentChangeRequestView(params: any) {
    return http.get(this.getEmployeeDepartmentChangeRequestViewApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除人员部门变更申请
   */
  public static deleteEmployeeDepartmentChangeRequest(params: any) {
    return http.post(this.deleteEmployeeDepartmentChangeRequestApi, params, { loadingText: Loading.DELETE });
  }
  /**
   * @description: 保存员工服饰尺码信息
   */
  public static saveEmployeeClothingSizes(params: any) {
    return http.post(this.saveEmployeeClothingSizesApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取员工多组织架构部门列表
   */
  public static getEmployeeToDepartments(params?: any) {
    return http.get(this.getEmployeeToDepartmentsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  }
  /**
   * @description: 获取某人员所属部门
   */
  public static getEmployeeDepartmentsByEmployeeID(params?: any) {
    return http.get(this.getEmployeeDepartmentsByEmployeeIDApi, params) as Promise<Record<string, any>[]>;
  }
  /**
   * @description: 删除某人员所属部门
   */
  public static deleteEmployeeToDepartmentByEmployeeID(params?: any) {
    return http.get(this.deleteEmployeeToDepartmentByEmployeeIDApi, params);
  }
  /**
   * @description: 保存人员所属部门
   */
  public static saveEmployeeToDepartments(params?: any) {
    return http.post(this.saveEmployeeToDepartmentsApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 保存员工特长
   */
  public static saveEmployeeStrengths(params: any) {
    return http.post(this.saveEmployeeStrengthsApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取某人员所属某组织架构的部门列表
   * @param params {employeeID: number, organizationType: string}
   */
  public static getEmployeeToDeptByEmployeeIDAndOrganizationType(params: any) {
    return http.get(this.getEmployeeToDeptByEmployeeIDAndOrganizationTypeApi, params);
  }
  /**
   * @description: 获取员工体检次数列表
   * @param params
   */
  public static getPhyExamList(params: any) {
    return http.get(this.getPhyExamListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取体检明细数据
   * @param params
   */
  public static getPhyExamRecord(params: any) {
    return http.get(this.getPhyExamRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除员工特长
   */
  public static deleteEmployeeStrengths(params: any) {
    return http.post(this.deleteEmployeeStrengthsApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 删除员工任职记录
   */
  public static deleteEmployeePosition(params: any) {
    return http.post(this.deleteEmployeePositionApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 获取所属科室
   */
  public static getEmployeeToRole(params: any) {
    return http.get(this.getEmployeeToRoleApi, params);
  }
  /**
   * @description: 获取所属科室ID和名称
   */
  public static getEmployeeDepartmentIDAndNames(params: any) {
    return http.get(this.getEmployeeDepartmentIDAndNamesApi, params);
  }
  /**
   * @description: 获取人员信息
   */
  public static getEmployeesInformation() {
    return http.get(this.getEmployeesInformationApi) as Promise<EmployeeInformation[]>;
  }
  /**
   * @description: 获取离职员工列表
   */
  public static getEmployeeResignationList(params: any) {
    return http.post(this.getEmployeeResignationListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取人员信息下拉选项
   */
  public static getEmployeesAsOptions() {
    return http.get(this.getEmployeesAsOptionsApi) as Promise<EmployeeKeyPairs>;
  }
}
