/*
 * FilePath     : \src\views\annualPlan\types\annualScheduleMainView.ts
 * Author       : 张现忠
 * Date         : 2024-01-02 15:42
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-07 15:56
 * Description  : 年度计划执行-任务View
 * CodeIterationRecord:
 */
/**
 * 年度计划呈现
 */
export interface taskMainView {
  /**
   * 主键ID
   */
  annualScheduleMainID: string;
  /**
   * 状态
   */
  statusCode?: number;
  /**
   * 所属分类名称
   */
  typeName: string;
  /**
   * 任务内容
   */
  taskContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 计划执行时间
   */
  scheduleDateTime: Date | string;
  /**
   * 实际执行时间
   */
  performDateTime: Date | string;
  /**
   * 延迟执行的原因类型
   */
  delayReason?: number;
  /**
   * 备注
   */
  remark: string;
}
