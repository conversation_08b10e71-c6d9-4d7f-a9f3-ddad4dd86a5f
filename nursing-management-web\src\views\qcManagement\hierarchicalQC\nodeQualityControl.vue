<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\nodeQualityControl.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:32
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-30 19:23
 * Description  : 三级质控节点式质控
 * CodeIterationRecord:
-->
<template>
  <top-menu ref="childPage" :menuList="tabMenus"></top-menu>
</template>
<script setup lang="ts">
import topMenu from "@/components/topMenu.vue";
const { sessionStore } = useStore();
const route = useRoute();
type topMenuType = InstanceType<typeof topMenu>;
let childPage = useTemplateRef<topMenuType | undefined>("childPage");
const tabMenus = ref<topMenuType["$props"]["menuList"]>([]);
// #region  解决 路由带参数时，切换路由不刷新问题
let params = route.params;
import { isEqual } from "lodash-es";
watch(
  () => route.params,
  (val) => {
    let parentRouterName = route.matched[route.matched.length - 1]?.name as string;
    tabMenus.value = sessionStore.pageTopMenus[parentRouterName];
    // 处理按F5刷新
    if (!tabMenus.value) {
      parentRouterName = route.matched[route.matched.length - 2]?.name as string;
      tabMenus.value = sessionStore.pageTopMenus[parentRouterName];
    }
    if (Reflect.ownKeys(val).length && !isEqual(params, val)) {
      params = val;
      refreshData();
    }
  },
  { immediate: true, deep: true }
);
//#endregion
/**
 * description: 系统顶部刷新按钮触发
 */
const refreshData = () => {
  if (childPage?.value) {
    // 刷新前取消未完成的请求
    http.cancelPageRequest();
    childPage?.value.refreshData();
  }
};
// 暴漏给父路由
defineExpose({
  refreshData
});
</script>
