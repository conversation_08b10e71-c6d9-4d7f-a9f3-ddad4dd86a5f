/*
 * FilePath     : \nursing-management-web\src\types\cascaderData.ts
 * Author       : 来江禹
 * Date         : 2023-09-17 11:34
 * LastEditors  : 胡长攀
 * LastEditTime : 2023-12-14 15:03
 * Description  : 级联选择器数据格式
 * CodeIterationRecord:
 */
/* eslint-disable */
/**
 * 级联选择器数据格式
 */
declare interface CascaderList<T> {
  /**
   * 标题
   */
  label: string;
  /**
   * value值
   */
  value: T;
  /**
   * 子集
   */
  children: CascaderList<T>[];
  /**
   * 节点是否禁用，如果是节点存在子级，则子级也会被禁用
   */
  disabled?: boolean;
}
