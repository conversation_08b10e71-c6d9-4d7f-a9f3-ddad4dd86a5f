<!--
 * FilePath     : \src\components\selector\questionBankSelector.vue
 * Author       : 张现忠
 * Date         : 2024-11-06 18:07
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-17 14:45
 * Description  : 题库选择器组件
 * CodeIterationRecord:
 -->

<template>
  <div class="question-bank-selector">
    <span v-if="label">{{ label }}：</span>
    <el-cascader
      ref="cascaderRef"
      class="cascader-component"
      v-model="questionBankIDs"
      :placeholder="`请选择${label}`"
      :options="cascaderOptions"
      :show-all-levels="showAllLevels"
      :props="cascaderProps"
      :disabled="disabled"
      :clearable="clearable"
      :collapse-tags="multiCollapse"
      :filterable="filterable"
      popper-class="question-bank-popper-panel"
    >
      <template #default="{ node, data }" v-if="!isMultiple">
        <span class="cascader-component-label" @click="nodeClick(node)">{{ data.label }}</span>
      </template>
    </el-cascader>
  </div>
</template>

<script lang="ts" setup>
// #region props
const props = defineProps({
  modelValue: {
    type: [String, Array<String>]
  },
  // label内容
  label: {
    type: String,
    default: "题库"
  },
  // 清空属性，默认关闭
  clearable: {
    type: Boolean,
    default: false
  },
  // 输入框中是否显示选中值的完整路径，默认不显示
  showAllLevels: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 级联选择器配置选项
  props: {
    type: Object,
    default: () => {
      return {
        expandTrigger: "hover"
      };
    }
  },
  multiCollapse: {
    type: Boolean,
    default: true
  },
  // 组件宽度
  width: {
    type: Number,
    default: 200
  },
  list: {
    type: Array as PropType<cascaderType[]>,
    default: () => undefined
  },
  showDefaultValues: {
    type: Boolean,
    default: true
  },
  // 是否是获取实操类题库，true获取实操类，false获取非实操类，空获取所有
  isPractical: {
    type: Boolean,
    default: undefined
  },
  filterable: {
    type: Boolean,
    default: false
  }
});

// #endregion
type cascaderType = CascaderList<string> & { isPractical: boolean };
const cascaderOptions = ref<cascaderType[]>([]);
const { getCascaderFullValue } = useCascaderFullValue();
let questionBankIDs = ref<string[] | Array<string[]>>([]);
const defaultQuestionBankIDs = ref<string[]>([]);
const isMultiple = Boolean(props.props.multiple);
const cascaderProps = computed(() => {
  return { ...props.props, expandTrigger: "hover" as const, checkStrictly: true };
});
const cascaderRef = shallowRef();
onMounted(async () => {
  await init();
});
watch(
  () => props.modelValue,
  () => {
    initDefaultValue();
  }
);
let { list } = toRefs(props);
/**
 * @description: 初始化
 * @param employeeID
 * @return
 */
const init = async () => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  if (list?.value) {
    cascaderOptions.value = list.value;
  } else {
    await getCascaderData();
  }
  initDefaultValue();
  let value: any = questionBankIDs.value;
  if (!isMultiple) {
    value =
      Array.isArray(questionBankIDs.value) && questionBankIDs.value.length > 0
        ? questionBankIDs.value[questionBankIDs.value.length - 1]
        : undefined;
  }
  emits("afterInitChange", value);
};
/**
 * @description: 初始化默认值
 */
const initDefaultValue = () => {
  // 如果没有传值，就使用默认值(根据条件是否需要显示默认值来处理)
  if (!props.modelValue) {
    ignoreUpdates(() => (questionBankIDs.value = defaultQuestionBankIDs.value));
    return;
  }
  /**
   * 使用递归函数，构造返回默认数据显示默认
   * 如：入参2，返回(ID以及上级ID集合)ret[0]: [70,50,2]
   *   ret[1] CascaderList类型集合
   */
  const defaultValue = getCascaderFullValue(
    props.modelValue,
    cascaderOptions.value,
    "value",
    "children",
    typeof props.modelValue !== "string"
  ) as any;
  ignoreUpdates(
    () =>
      (questionBankIDs.value = isMultiple ? (typeof props.modelValue !== "string" ? defaultValue[0] : [defaultValue[0]]) : defaultValue[0])
  );
};
/**
 * @description: 获取级联选择器数据
 * @return
 */
const getCascaderData = async () => {
  await examineService.getQuestionBankCascader({ isPractical: props.isPractical, index: Math.random() }).then((data: any) => {
    cascaderOptions.value = data;
    if (props.showDefaultValues && !props.modelValue && data && data.length > 0) {
      defaultQuestionBankIDs.value = [data[0].value];
      emits("update:modelValue", defaultQuestionBankIDs.value[0]);
      emits("select", data[0]);
    }
  });
};
let cascaderItems: cascaderType[] | Array<cascaderType[]>[];
const emits = defineEmits(["update:modelValue", "change", "select", "afterInitChange"]);
const { ignoreUpdates } = watchIgnorable(
  () => questionBankIDs.value,
  (newValue) => {
    if (!newValue || newValue.length <= 0) {
      emits("update:modelValue", undefined);
      emits("change", undefined);
      emits("select", undefined);
      return;
    }
    let chooseValues: string | string[] = [];
    if (isMultiple) {
      // 如果是多选类型，返回每一个数组ID数据最后一个参数
      newValue.forEach((value: any) => (chooseValues as string[]).push(value[value.length - 1]));
    } else {
      // 如果是单选类型，返回部门ID数据最后一个参数
      chooseValues = newValue[newValue.length - 1] as string;
    }
    // 处理首次进入监听，组件默认值情况，传入的值newValue不是一个数组，是几个数值，在遍历时chooseValues数组均为undefine，此时取数据最后一个参数
    if (isMultiple && !(chooseValues as string[])[0] && newValue) {
      chooseValues = newValue[newValue.length - 1] as string;
    }
    // 数据清空时，直接返回
    if (!chooseValues) {
      return;
    }
    // 根据选择的值，获取到对象的位置及对象数据值
    const ret = getCascaderFullValue(chooseValues, cascaderOptions.value, "value", "children", typeof chooseValues !== "string") as any;
    emits("update:modelValue", chooseValues);
    emits("change", chooseValues);
    cascaderItems = ret[1];
    emits("select", cascaderItems);
  },
  { deep: true }
);
/**
 * @description: 级联选择选项点击事件
 * @param node 级联选择器中每一个节点的数据类型对象
 * @return
 */
const nodeClick = (node: any) => {
  if (!cascaderRef.value) {
    return;
  }
  if (isMultiple) {
    let parentNode = node.parent;
    // 选择子节点 取消父节点的选中(互斥)
    while (questionBankIDs.value && parentNode) {
      for (let index = 0; index < questionBankIDs.value.length; index++) {
        const innerQuestionBankIDs = questionBankIDs.value[index];
        if (innerQuestionBankIDs.indexOf(parentNode.value) > -1) {
          handleCheckChange(parentNode, false);
          break;
        }
      }
      parentNode = parentNode.parent;
    }
    // 选择父节点，清理子节点(互斥)
    let selectedNodes = cascaderRef.value.getCheckedNodes(false);
    let childNodes = node.children;
    for (let index = 0; index < childNodes.length; index++) {
      const childNode = childNodes[index];
      selectedNodes.find((m: any) => m.uid === childNode.uid) && handleCheckChange(childNode, false);
    }
  }
  // 选中当前节点
  handleCheckChange(node, true);
  // 非多选关闭弹窗
  node.config.multiple || cascaderRef.value.togglePopperVisible(false);
};
/**
 * @description:
 * @param node 级联选择器节点
 * @param flag 是否选择当前节点 true:选择,false:取消选择节点
 * @return
 */
const handleCheckChange = (node: any, flag: Boolean) => {
  cascaderRef.value.cascaderPanelRef.handleCheckChange(node, flag);
  // emits("select", node);
};

//#endregion
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const cascaderWidth = computed(() => (width.value === 0 ? "100%" : `${convertPX(width.value)}px`));
</script>

<style lang="scss">
.question-bank-selector {
  display: inline-block;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include cascader-component-style(v-bind(cascaderWidth));
  .cascader-component {
    &--label {
      display: block;
    }
  }
}
// 去掉部门选择器->级联选择器组件中单选时的单选按钮样式
.question-bank-popper-panel {
  .el-cascader-panel {
    .el-cascader-node {
      > .el-radio {
        display: none;
      }
    }
    // 实现点击label空白区域也可以触发事件
    .cascader-component-label {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
