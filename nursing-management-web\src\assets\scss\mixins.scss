/**
* 设置线性渐变背景色
* $direction：线性渐变方向
* $colors：渐变颜色，可变长度
*/
@mixin l-gradient-bg($direction, $colors...) {
  background: linear-gradient(to $direction, $colors);
}
/**
* 选择背景色
*/
@mixin select-style() {
  @include l-gradient-bg(right, #fc8720, #f9d582);
  color: #ffffff !important;
}
@mixin animation-background() {
  @keyframes masked-animation {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: -100%, 0;
    }
  }
  background: linear-gradient(
    to right,
    #ffffff,
    #ff0000 6.25%,
    #ff7d00 12.5%,
    #ffff00 18.75%,
    #00ff00 25%,
    #00ffff 31.25%,
    #0000ff 37.5%,
    #ff00ff 43.75%,
    #ffff00 50%,
    #ff0000 56.25%,
    #ff7d00 62.5%,
    #ffff00 68.75%,
    #00ff00 75%,
    #00ffff 81.25%,
    #0000ff 87.5%,
    #ff00ff 93.75%,
    #ffff00 100%
  );
  background-clip: text;
  background-size: 200% 100%;
  animation: masked-animation 2s infinite linear;
}

/**
* 超出多少行溢出显示省略号
* $line：行数，默认1
*/
@mixin show-overflow($line: 1) {
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: $line;
  overflow: hidden;
  display: -webkit-box;
}

/**
* 字典下拉框组件样式
* $width：宽度
*/
@mixin selector-component-style($width: 120px) {
  .selector-component {
    width: $width;
    margin-right: 10px;
    .el-select__tags {
      max-width: calc($width - 20px) !important;
      .el-select__tags-text {
        display: initial;
        word-break: initial;
      }
    }
  }
}

/**
* 级联选择器组件样式
* $width：宽度
*/
@mixin cascader-component-style($width: 200px) {
  .cascader-component {
    width: $width;
    margin-right: 10px;
    .el-cascader__tags {
      max-width: calc($width - 20px) !important;
      .el-tag__content {
        display: initial;
        word-break: initial;
      }
    }
  }
}

@mixin flex-aline($direction: row, $aline: start) {
  display: flex;
  align-items: center;
  flex-direction: $direction;
  justify-content: $aline;
  gap: 10px;
  flex-wrap: wrap;
}
