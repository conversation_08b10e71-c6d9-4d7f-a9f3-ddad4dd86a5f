/*
 * FilePath     : \src\api\messageManagementService.ts
 * Author       : 张现忠
 * Date         : 2024-10-19 14:09
 * LastEditors  : 苏军志
 * LastEditTime : 2025-01-13 18:02
 * Description  : 消息发布相关接口api
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";
export class messageManagementService {
  private static baseApi: string = "/messageManagement";

  private static getMessageListApi: string = `${this.baseApi}/GetMessageList`;
  private static getMessageDetailApi: string = `${this.baseApi}/GetMessageDetail`;
  private static addMessageApi: string = `${this.baseApi}/AddMessage`;
  private static updateMessageApi: string = `${this.baseApi}/UpdateMessage`;
  private static deleteMessageApi: string = `${this.baseApi}/DeleteMessage`;
  private static publishMessageApi: string = `${this.baseApi}/PublishMessage`;
  private static recallMessageApi: string = `${this.baseApi}/RecallMessage`;
  private static sendSystemPreUpdateMessageApi: string = `${this.baseApi}/SendSystemPreUpdateMessage`;
  private static getLastSystemUpdateRecordApi: string = `${this.baseApi}/GetLastSystemUpdateRecord`;

  /**
   * @description: 获取消息列表
   * @param params
   * @return
   */
  public static getMessageList(params: any) {
    return http.get(this.getMessageListApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取消息详情
   * @param params
   * @return
   */
  public static getMessageDetail(params: any) {
    return http.get(this.getMessageDetailApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 新增消息
   * @param params  新增的消息记录
   * @return
   */
  public static addMessage(params: any) {
    return http.post(this.addMessageApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:更新消息
   * @param params 修改的消息记录
   * @return
   */
  public static updateMessage(params: any) {
    return http.post(this.updateMessageApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除消息
   * @param params 删除的消息记录主键ID
   * @return
   */
  public static deleteMessage(params: any) {
    return http.post(this.deleteMessageApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 发布消息
   * @param params
   * @return
   */
  public static publishMessage(params: any) {
    return http.post(this.publishMessageApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 撤回消息
   * @param params
   * @return
   */
  public static recallMessage(params: any) {
    return http.post(this.recallMessageApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 发送系统通知消息
   * @param params
   * @returns
   */
  public static sendSystemPreUpdateMessage(params: any) {
    return http.post(this.sendSystemPreUpdateMessageApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取最后一条系统更新记录
   * @param params
   * @returns
   */
  public static getLastSystemUpdateRecord(params?: any) {
    return http.get(this.getLastSystemUpdateRecordApi, params, { loadingText: Loading.LOAD });
  }
}
