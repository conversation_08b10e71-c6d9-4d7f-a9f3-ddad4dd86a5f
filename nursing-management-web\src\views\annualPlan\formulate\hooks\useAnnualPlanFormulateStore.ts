/*
* FilePath     : \src\views\annualPlan\formulate\hooks\useAnnualPlanFormulateStore.ts
 * Author       : 杨欣欣
 * Date         : 2025-05-11 16:24
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-11 16:26
 * Description  : 年度计划制定状态与行为
 * CodeIterationRecord: 
*/
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";

export const useAnnualPlanFormulateStore = defineStore("annualPlanFormulate", () => {
  const usedInterventionIDs = ref<number[]>([]);
  const getUsedInterventionIDs = async () => {
    const params = {
      mainID: usePlanManagementStore().annualPlanMainID
    };
    usedInterventionIDs.value = await annualPlanInterventionService.getUsedInterventionIDs(params);
  };
  const addUsedInterventionID = (interventionID: number) => {
    usedInterventionIDs.value.push(interventionID);
  };
  const removeUsedInterventionID = (interventionID: number) => {
    usedInterventionIDs.value = usedInterventionIDs.value.filter((id) => id !== interventionID);
  };
  return {
    usedInterventionIDs,
    getUsedInterventionIDs,
    addUsedInterventionID,
    removeUsedInterventionID
  };
});
