<!--
 * FilePath     : /src/views/annualPlan/components/workQuoteDialog.vue
 * Author       : 杨欣欣
 * Date         : 2024-12-29 11:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:58
 * Description  : 工作引用组件
 * CodeIterationRecord:
-->
<template>
  <el-dialog v-model="visible" title="参考工作" class="work-quote-dialog">
    <div class="work-quote">
      <!-- 左侧执行项目 -->
      <div class="left-wrapper" ref="workSelectorContainer">
        <!-- 按分类分区 -->
        <section v-for="type in activePlanGroup?.children" :key="type.typeID" :id="type.typeName" class="type-section">
          <div class="left-type-title">{{ type.typeName }}</div>
          <div class="work-list">
            <!-- 工作描述，卡片呈现 -->
            <work-card
              v-for="(work, index) in type.children"
              :content="work.workContent"
              :tip="work.requirement"
              :key="index"
              @click="selectWork(work)"
            />
          </div>
        </section>
      </div>
      <div class="right-wrapper">
        <!-- 可能有多个计划，供选择 -->
        <h3 class="plan-title" v-if="quickRefWorks.length === 1">{{ quickRefWorks[0].planName }}</h3>
        <el-select v-else v-model="currentPlanIndex" class="plan-select" fit-input-width>
          <el-option v-for="(planGroup, index) in quickRefWorks" :key="index" :label="planGroup.planName" :value="index" />
        </el-select>
        <!-- 右侧锚点 -->
        <el-anchor class="type-anchor" :container="workSelectorContainer">
          <el-anchor-link
            v-for="typeGroup in activePlanGroup?.children"
            :key="typeGroup.typeID"
            :title="typeGroup.typeName"
            :href="`#${typeGroup.typeName}`"
          />
        </el-anchor>
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import type { planWorkImportVo } from "../types/planWorkImportVo";

type work = planWorkImportVo["children"][number]["children"][number];

const { quickRefWorks } = defineProps<{
  quickRefWorks: planWorkImportVo[];
}>();
// eslint-disable-next-line no-spaced-func
const emit = defineEmits<{
  (e: "confirm", work: work): void;
}>();
const visible = defineModel({
  required: true
});
const workSelectorContainer = ref<Record<string, any>>();
const currentPlanIndex = ref<number>(0);
const activePlanGroup = computed(() => (quickRefWorks.length ? quickRefWorks[currentPlanIndex.value] : undefined));

/**
 * @description: 选择工作
 * @param work 工作
 */
const selectWork = (work: work) => {
  emit("confirm", work);
  visible.value = false;
};
</script>
<style scoped lang="scss">
/*:deep(.work-quote-dialog) {
  .el-dialog__body {
    padding: 0;
    height: 600px;
  }
}*/

.work-quote {
  padding: 16px;
  box-sizing: border-box;
  height: 100%;
  width: 100%;
  display: flex;
  gap: 24px;
  .left-wrapper {
    width: 85%;
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none;
    padding-right: 12px;
    .type-section {
      margin-bottom: 20px;
      .quarter-work-card:hover {
        transform: translateY(-8px);
      }
    }
    .left-type-title {
      font-size: 32px;
      font-weight: 600;
      margin: 16px 0;
    }
    .work-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-right: -4px;
      .work-item {
        flex: 0 0 calc(33.33% - 16px);
        margin: 4px;
        box-sizing: border-box;
        padding: 8px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        background: #ffffff;
        position: relative;
      }
      .work-item:hover {
        transform: translateY(-8px);
      }
    }
  }
  .right-wrapper {
    flex: 1;
    width: 0;
    .plan-select {
      margin-bottom: 8px;
    }
    .el-anchor__link {
      font-size: 18px;
      letter-spacing: 1px;
      line-height: 1.6;
      &:not(.is-active) {
        color: #707070;
      }
    }
  }
}
</style>
