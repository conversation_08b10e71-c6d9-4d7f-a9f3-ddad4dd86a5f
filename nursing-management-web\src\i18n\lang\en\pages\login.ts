/*
 * FilePath     : \src\i18n\lang\en\pages\login.ts
 * Author       : 苏军志
 * Date         : 2023-06-08 09:08
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:51
 * Description  : Login screen English language pack
 * CodeIterationRecord:
 */
export default {
  login: {
    hisUserPlaceholder: "Please enter HIS account",
    oaUserPlaceholder: "Please enter OA account",
    oaPasswordPlaceholder: "Please enter OA password",
    hisPasswordPlaceholder: "Please enter HIS password",
    loginBtn: "Log in",
    bindBtn: "Bind",
    hisErrorTip: "HIS Account or password cannot be empty!",
    oaErrorTip: "OA Account or password cannot be empty!",
    wechat<PERSON><PERSON>l: "wechat",
    authorizedBy: "Authorized by",
    selectHospitalTip: "Please select a hospital"
  }
};
