<!--
 * FilePath     : \src\views\mainLayout\components\navMenu.vue
 * Author       : 苏军志
 * Date         : 2023-06-17 09:01
 * LastEditors  : 苏军志
 * LastEditTime : 2023-12-02 19:49
 * Description  : 菜单封装--支持递归
 * CodeIterationRecord:
-->
<template>
  <template v-for="navMenu in navMenus" :key="navMenu[fieldMap.menuID]">
    <el-sub-menu v-if="navMenu[fieldMap.children]?.length" :index="String(navMenu[fieldMap.menuID])" :popper-offset="0" :teleported="true">
      <template #title>
        <i :class="navMenu[fieldMap.iconName]" v-if="!navMenu[fieldMap.parentID]"></i>
        <span class="item-title">{{ navMenu[fieldMap.menuName] }}</span>
      </template>
      <nav-menu :navMenus="navMenu[fieldMap.children]" :fieldMap="fieldMap"></nav-menu>
    </el-sub-menu>
    <el-menu-item v-else :index="navMenu[fieldMap.router]">
      <!-- 只有根菜单才显示图标 -->
      <i :class="navMenu[fieldMap.iconName]" v-if="!navMenu[fieldMap.parentID]"></i>
      <span class="item-title">{{ navMenu[fieldMap.menuName] }}</span>
    </el-menu-item>
  </template>
</template>
<script setup lang="ts">
defineProps({
  navMenus: {
    require: true,
    type: Array<any>
  },
  fieldMap: {
    type: Object,
    default: () => {
      return {
        menuID: "menuID",
        parentID: "parentID",
        menuName: "menuName",
        router: "router",
        iconName: "iconName",
        sort: "sort",
        children: "children"
      };
    }
  }
});
</script>
<style lang="scss">
.el-menu {
  $active-bg-color: #ffffff;
  /* 重新给el-menu组件内部的scss变量赋值 */
  --el-menu-text-color: #ffffff;
  --el-menu-hover-bg-color: #ffffff;
  --el-menu-item-height: 32px;
  --el-menu-sub-item-height: 24px;
  border-right: 0;
  height: 100%;
  overflow-y: auto;
  @include l-gradient-bg(right, darken($base-color, 10%), lighten($base-color, 20%));
  &.el-menu--collapse {
    min-width: 64px !important;
    max-width: 86px !important;
  }
  &:not(.el-menu--collapse) {
    min-width: 180px;
    max-width: 260px;
  }
  &::-webkit-scrollbar {
    display: none;
  }
  .el-menu-item [class^="iconfont"],
  .el-sub-menu > .el-sub-menu__title [class^="iconfont"],
  .el-menu-item-group > ul > .el-sub-menu > .el-sub-menu__title [class^="iconfont"] {
    margin: 0 5px;
    font-size: 18px;
  }
  .el-sub-menu,
  .el-menu-item {
    border-bottom: 1px dashed #ffffff;
    &:last-child {
      border: 0;
    }
  }
  [aria-expanded="true"] .el-sub-menu__title,
  el-sub-menu .el-menu-item:hover,
  .el-sub-menu.is-active .el-sub-menu__title,
  .el-sub-menu__title:hover,
  .el-menu-item:hover,
  .el-menu-item.is-active {
    background-color: $active-bg-color !important;
    color: $base-color;
    font-weight: bold;
  }
  .el-sub-menu__icon-arrow {
    font-weight: bold;
    font-size: 14px;
  }
  &.el-menu--collapse > {
    .el-menu-item-group > ul > .el-sub-menu > .el-sub-menu__title,
    .el-menu-item,
    .el-sub-menu > .el-sub-menu__title {
      --el-menu-item-height: 60px;
      flex-direction: column;
      padding: 0;
      & [class^="iconfont"] {
        line-height: 40px;
        font-size: 24px;
      }
      & > span {
        line-height: 12px;
        height: 20px;
        width: 60px;
        visibility: visible;
        display: block;
        text-align: center;
      }
    }
    .el-sub-menu.is-active .el-sub-menu__title,
    .el-sub-menu.is-opened .el-sub-menu__title {
      background-color: $active-bg-color !important;
      color: $base-color;
      font-weight: bold;
    }
  }
}
// 非一级菜单调整
.el-menu--vertical.el-menu--popup-container {
  box-shadow: 2px 0 12px 0 rgba(0, 0, 0, 0.2);
  .el-menu--popup-right-start {
    min-width: 140px;
    width: 140px;
  }
}
</style>
