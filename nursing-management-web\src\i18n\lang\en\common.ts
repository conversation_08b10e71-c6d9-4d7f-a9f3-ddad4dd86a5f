/*
 * FilePath     : \src\i18n\lang\en\common.ts
 * Author       : 苏军志
 * Date         : 2021-11-01 11:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:53
 * Description  : Public part English language pack
 */
export default {
  // 按钮显示文字
  button: {
    add: "Add",
    modify: "Modify",
    query: "Query",
    save: "Save",
    delete: "Delete",
    print: "Print",
    edit: "Edit",
    stop: "Stop",
    export: "Export",
    cancel: "No",
    confirm: "Yes",
    back: "Back",
    revoke: "Revoke"
  },
  // loading提示文字
  loadingText: {
    load: "Loading……",
    save: "Saving……",
    delete: "Deleting……",
    saveSuccess: "Save succeed!",
    deleteSuccess: "Delete succeed!",
    updateSuccess: "Update succeed!"
  },
  // tooltip显示文字
  tip: {
    modify: "Modify",
    query: "Query",
    save: "Save",
    delete: "Delete",
    print: "Print",
    edit: "Edit",
    stop: "Stop",
    systemTip: "System message",
    dataEmpty: "No Data",
    operationAuthority: "No permission only if the data owner!",
    deleteConfirm: "Are you sure you want to delete data?",
    fullscreen: "Full screen",
    exitFullscreen: "Exit full screen"
  },
  // 选择框、输入框、日期时间等组件提示文字
  placeholder: {
    date: "Selecting date",
    time: "Selecting time",
    department: "Selecting department"
  },
  // 标签显示文字
  label: {
    systemName: "Intelligent Nursing Management System",
    date: "Date:",
    time: "Time:",
    operation: "Operating",
    station: "Station:",
    department: "department:"
  }
};
