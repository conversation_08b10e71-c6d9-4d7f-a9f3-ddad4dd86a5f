<!--
 * FilePath     : \nursing-management-web\src\views\trainingManagement\courseSetting\index.vue
 * Author       : 张现忠
 * Date         : 2024-04-07 09:09
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-18 08:44
 * Description  : 培训课程库
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="course-setting" headerHeight="60px" :drawerOptions="drawerOptions">
    <template #header>
      <course-type-selector v-model="courseTypeID"></course-type-selector>
      <div class="course-dictionary-search">
        <span>课程名称：</span>
        <el-input v-model="searchQuery" clearable placeholder="请输入搜索内容" @keyup.enter="searchCourseSetting(searchQuery)">
          <template #append>
            <i class="iconfont icon-search" @click="searchCourseSetting(searchQuery)" />
          </template>
        </el-input>
      </div>
      <el-button type="primary" class="add-button" v-permission:B="1" @click="openDrawer(undefined)">新增课程</el-button>
      <el-button type="primary" class="right-button" @click="classifyCourse">课程分类</el-button>
    </template>
    <el-table
      ref="courseTableRef"
      :data="courseList"
      :row-key="(row :courseSetting) => row.courseSettingID"
      :expand-row-keys="expandRowKeys"
      highlight-current-row
      border
      stripe
      :lazy="true"
      @row-click="handleRowClick"
    >
      <el-table-column type="expand" :width="convertPX(40)">
        <template #default="{ row }">
          <el-table :data="row.childCourses" border stripe highlight-current-row :show-header="false" :lazy="true">
            <el-table-column type="hidden" :width="convertPX(40)" />
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              :width="convertPX(column.width)"
              :min-width="convertPX(column.minWidth)"
              :align="column.align"
            >
              <template v-if="column.isDateColumn" #default="{ row }">
                <span v-formatTime="{ value: row[column.prop!], type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-tooltip content="编辑">
                  <i class="iconfont icon-edit" v-permission:B="3" @click="openDrawer(row)"></i>
                </el-tooltip>
                <el-tooltip content="附件预览">
                  <i class="iconfont icon-preview" v-permission:B="3" @click="openDrawer(row)"></i>
                </el-tooltip>
                <el-tooltip content="删除">
                  <i class="iconfont icon-delete" v-permission:B="4" @click="deleteCourse(row)"></i>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(column, index) in columns"
        :key="index"
        :prop="column.prop"
        :label="column.label"
        :width="convertPX(column.width)"
        :min-width="convertPX(column.minWidth)"
        :align="column.align"
      >
        <template v-if="column.isDateColumn" #default="{ row }">
          <span v-formatTime="{ value: row[column.prop!], type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" type="text" v-permission:B="3" @click.stop="openDrawer(row)"></i>
          </el-tooltip>
          <el-tooltip content="新增子课程">
            <i class="iconfont icon-add" v-permission:B="1" @click.stop="openDrawer(row, true)"></i>
          </el-tooltip>
          <el-tooltip content="附件预览">
            <i class="iconfont icon-preview" v-permission:B="3" @click.stop="showFilePreview(row.fileInfoList)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" type="text" v-permission:B="4" @click.stop="deleteCourse(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName === 'addModifyCourse'"
        ref="courseFormRef"
        :rules="courseRules"
        :model="currRow"
        label-width="100px"
      >
        <el-form-item label="课程分类：" prop="courseTypeID">
          <setting-dictionary-type-selector
            v-model="currRow!.courseTypeID"
            label=""
            :settingTypeCode="settingTypeCode"
            :settingType="settingType"
          ></setting-dictionary-type-selector>
        </el-form-item>
        <el-form-item label="课程名称：" prop="courseName">
          <el-input v-model="currRow!.courseName" clearable placeholder="请输入课程名称" />
        </el-form-item>
        <el-form-item label="课程简介：" prop="courseIntroduction">
          <el-input type="textarea" rows="4" v-model="currRow!.courseIntroduction" clearable placeholder="请输入课程简介" />
        </el-form-item>
        <el-form-item label="上传附件：" prop="files">
          <upload-file
            :autoUpload="false"
            :fileInfo="currRow?.fileInfoList"
            :fileAssociations="{}"
            :useBtn="true"
            @change="getFileList"
          ></upload-file>
        </el-form-item>
        <el-form-item label="年份：" prop="year">
          <el-date-picker
            v-model="year"
            type="year"
            placeholder="请选择年份"
            :clearable="false"
            @change="currRow!.year = year.getFullYear()"
          />
        </el-form-item>
      </el-form>
      <setting-dictionary-maintain
        v-if="drawerOptions.drawerName === 'classifyCourse'"
        :settingTypeCode="settingTypeCode"
        :settingType="settingType"
      ></setting-dictionary-maintain>
      <zhy-file-preview
        v-if="drawerOptions.drawerName === 'filePreview'"
        :fileList="previewFileList"
        :option="previewOptions"
      ></zhy-file-preview>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import type { courseSetting } from "../types/courseSetting";
import { useCourseLogic } from "../hooks/useCourse";
import type { fileView, previewOption } from "zhytech-ui";
import { zhyFilePreview } from "zhytech-ui";
import { useDrawerToggle } from "../hooks/useDrawerToggle";
//#region 类型定义
const { courseTypeID, courseList, saveCourseSetting, deleteCourseSetting, getCourseSettingList, searchCourseSetting } = useCourseLogic();
const convertPX: any = inject("convertPX");
let { validateRule } = useForm();
const { userStore } = useStore() as any;
const currRow = ref<courseSetting>();
const year = ref(new Date());
const searchQuery = ref("");
const courseFormRef = shallowRef();
const courseTableRef = shallowRef();
// 记录当前行点击状态( 记录当前行的courseSettingID 和 子树的展开状态)
const rowClickCourse = ref<Array<string | undefined | boolean>>([undefined, false]);
const columns = [
  { label: "年份", prop: "year", width: 60, align: "center" },
  { label: "课程名称", prop: "courseName", minWidth: 100 },
  { label: "课程简介", prop: "courseIntroduction", minWidth: 120 },
  { label: "课程分类", prop: "courseTypeName", minWidth: 120 },
  { label: "新增人员", prop: "addEmployeeName", width: 120 },
  { label: "添加时间", prop: "addDateTime", width: 210, align: "center", isDateColumn: true },
  { label: "修改人员", prop: "modifyEmployeeName", width: 120 },
  { label: "修改时间", prop: "modifyDateTime", width: 210, align: "center", isDateColumn: true }
];
// 表单javascript验证规则
const courseRules = {
  courseName: [
    { required: true, message: "请输入课程名称", trigger: "blur" },
    { min: 1, max: 25, message: "课程名称长度在 1 到 25 个字符", trigger: "blur" }
  ],
  courseIntroduction: [
    { required: true, message: "请输入课程简介", trigger: "blur" },
    { min: 1, max: 500, message: "课程简介长度在 1 到 500 个字符", trigger: "blur" }
  ],
  year: [{ required: true, message: "请选择制定年份", trigger: "blur" }],
  courseTypeID: [{ required: true, message: "请选择课程分类", trigger: "blur" }]
};
const previewOptions = ref<previewOption>({
  defaultOpenFileIndex: 0,
  autoplayAudio: true,
  autoplayVideo: true
});
//#endregion

//#region vue 生命周期
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "编辑课程",
  drawerSize: "60%",
  showDrawer: false,
  cancel: () => restoreDrawerOptions() && (drawerOptions.value.showDrawer = false),
  confirm: async () => {
    if (await validateRule(courseFormRef)) {
      await saveCourseSetting(currRow.value!);
      drawerOptions.value.showDrawer = false;
    }
  }
});
const { restoreDrawerOptions, toggleDrawer } = useDrawerToggle(drawerOptions);
const settingTypeCode = ref<string>("TrainingClassification");
const settingType = ref<string>("TrainingManagement");
// 监听courseTypeID变化，重新获取课程列表
watch(courseTypeID, async () => {
  searchQuery.value = "";
  rowClickCourse.value = [undefined, false];
  await getCourseSettingList();
});
// 展开行
const expandRowKeys = computed(() => {
  return courseList.value.filter((m: courseSetting) => m.expandTree).map((item: courseSetting) => item.courseSettingID);
});
//#endregion

//#region 事件交互函数
/**
 * @description: 打开编辑功能弹窗
 * @param row ：当前行课程数据或者父课程数据
 * @param addChildCourseFlag：是否为新增子课程
 * @return
 */
const openDrawer = (row?: courseSetting, addChildCourseFlag: boolean = false) => {
  if (row && addChildCourseFlag) {
    currRow.value = { parentID: row.courseSettingID, level: (row.level || 0) + 1 } as courseSetting;
  } else {
    currRow.value = (row && common.clone(row)) || ({ level: 1 } as courseSetting);
  }
  toggleDrawer("addModifyCourse", true, "60%", "编辑课程");
  if (!row) {
    // 新增记录时，默认当前部门
    currRow.value.addDepartmentID = userStore.departmentID;
    year.value = new Date();
    currRow.value.year = year.value.getFullYear();
    return;
  }
  year.value = new Date(currRow.value.year, 0);
};
/**
 * @description: 删除课程
 * @param row 表格当前行记录
 * @return
 */
const deleteCourse = (row: courseSetting) => {
  deleteCourseSetting(row.courseSettingID);
};
/**
 * @description: 获取文件内容
 * @param value
 * @return
 */
const getFileList = (value: any) => {
  if (Array.isArray(value)) {
    currRow.value!.fileInfoList = value.filter((item: any) => item.status === "success" && item.url);
    currRow.value!.files = value.filter((item: any) => item instanceof File);
  } else {
    currRow.value!.files = [value];
  }
};
/**
 * @description: 课程分类功能跳转
 */
const classifyCourse = () => {
  toggleDrawer("classifyCourse", true, "100%", "课程分类维护", false, false);
};
/**
 * @description:处理行点击事件
 * @param {courseSetting} currentRow - 当前行数据对象
 * @return
 */
const handleRowClick = (currentRow: courseSetting) => {
  if (!currentRow) {
    return;
  }
  let [selectedCourseSettingID, expand] = rowClickCourse.value;
  // 判断当前子树的接下来的展开状态
  expand = selectedCourseSettingID === currentRow.courseSettingID ? !expand : true;
  // 存储当前课程展出状态
  rowClickCourse.value = [currentRow.courseSettingID, expand];
  // 切换展开状态
  courseTableRef.value.toggleRowExpansion(currentRow, expand);
};
const previewFileList = ref<fileView[]>([]);
/**
 * @description: 打开文件预览弹窗,处理预览的文件
 * @param fileInfoList 文件列表
 */
const showFilePreview = (fileInfoList: Record<string, string>[] | undefined) => {
  if (!fileInfoList || !fileInfoList.length) {
    showMessage("warning", "没有可以查看的附件");
    return;
  }
  previewFileList.value =
    fileInfoList.map((fileItem: Record<string, string>) => {
      return {
        file: fileItem.url,
        name: fileItem.fileName,
        extensionName: fileItem.extensionName
      } as fileView;
    }) || [];
  toggleDrawer("filePreview", true, "100%", "文件预览", false, false);
};

//#endregion
</script>

<style lang="scss">
.course-setting {
  .course-dictionary-search {
    margin: 0 10px;
    display: inline-flex;
    align-items: center;
    // 解决span标签换行问题
    span {
      white-space: nowrap;
      margin-right: 2px;
    }
    .el-input__inner {
      width: 200px;
    }
  }
  .el-drawer {
    .el-form {
      .el-form-item {
        min-width: 400px;
        max-width: 800px;
      }
    }
  }
}
</style>
