/*
 * FilePath     : \src\views\scheduling\hooks\useSchedulingStatistics.ts
 * Author       : 苏军志
 * Date         : 2024-02-25 10:38
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-13 17:24
 * Description  : 排班统计相关逻辑
 * CodeIterationRecord:
 */
export function useSchedulingStatistics() {
  /**
   * 休假岗
   */
  const restPostType: string = "4";
  /**
   * 年休假岗
   */
  const annualRestPostID: number = 186;
  /**
   * 下夜班岗
   */
  const nightShiftOffPostID: number = 999;
  /**
   * @description: 排班岗位月统计逻辑
   * @param month 月份
   * @param type 统计类型，1：统计出勤，2：统计中午值班，3：统计休假，4：统计年休假，5：统计指定岗位
   * @param row 行数据
   * @param departmentPostID 要统计的岗位编号
   * @param noonDutyMarkID 中午值班标记ID
   */
  const getMonthlyStatistics = (
    month: string,
    columns: TableColumn[],
    type: number,
    row: Record<string, any>,
    departmentPostID?: number,
    noonDutyMarkID?: number
  ) => {
    let count = 0;
    columns.forEach((column, index) => {
      let cellData = row[column.key];
      // 将不是本月的数据排除
      if (index > 0 && cellData?.noonPost && month === datetimeUtil.formatDate(column.value, "M")) {
        let noonPosts: Record<string, any>[] = Object.values(cellData.noonPost);
        // 中午值班
        if (type === 2) {
          if (cellData?.markList?.length) {
            cellData.markList.forEach((mark: Record<string, any>) => {
              if (mark.markID === noonDutyMarkID) {
                count += 1;
              }
            });
          }
        } else {
          noonPosts.forEach((noonType: Record<string, any>) => {
            // 统计出勤
            if (type === 1 && noonType.postType !== restPostType && noonType.departmentPostID !== nightShiftOffPostID) {
              count += noonType.attendanceDays / 2;
            }
            // 统计休假
            if (type === 3 && noonType.postType === restPostType && noonType.departmentPostID !== annualRestPostID) {
              count += 0.5;
            }
            // 统计年休假
            if (type === 4 && noonType.postType === restPostType && noonType.departmentPostID === annualRestPostID) {
              count += 0.5;
            }
            // 正常岗位统计
            if (type === 5 && noonType.departmentPostID === departmentPostID) {
              count += 0.5;
            }
          });
        }
      }
    });
    return count;
  };
  return {
    /**
     * @description: 统计排班岗位月数据
     */
    getStatisticsMonthlyData(
      month: string,
      shiftSchedulingTable: TableView | undefined,
      employeeRemainingRestDaysDict: Record<string, number | undefined>,
      statisticsPostColumns: Record<string, any>[],
      noonDutyMarkID?: number
    ) {
      let monthlyStatisticsData: Record<string, any>[] = [];
      if (!shiftSchedulingTable?.rows?.length) {
        return monthlyStatisticsData;
      }
      shiftSchedulingTable.rows.forEach((row) => {
        let monthlyStatisticsRow: Record<string, any> = {
          employeeName: row.employee.employeeName,
          employeeID: row.employee.employeeID
        };
        // 可休假天数
        monthlyStatisticsRow.employeeRemainingRestDays = employeeRemainingRestDaysDict[row.employee.employeeID];
        // 出勤天数
        monthlyStatisticsRow.attendanceDays = getMonthlyStatistics(month, shiftSchedulingTable.columns, 1, row);
        // 中午值班天数
        monthlyStatisticsRow.noonDutyDays = getMonthlyStatistics(month, shiftSchedulingTable.columns, 2, row, undefined, noonDutyMarkID);
        // 休假天数
        monthlyStatisticsRow.restDays = getMonthlyStatistics(month, shiftSchedulingTable.columns, 3, row);
        // 年休假天数
        monthlyStatisticsRow.annualRestDays = getMonthlyStatistics(month, shiftSchedulingTable.columns, 4, row);
        statisticsPostColumns.forEach((condition: Record<string, any>) => {
          monthlyStatisticsRow[condition.conditionValue] = getMonthlyStatistics(
            month,
            shiftSchedulingTable.columns,
            5,
            row,
            condition.conditionValue
          );
        });
        monthlyStatisticsData.push(monthlyStatisticsRow);
      });
      return monthlyStatisticsData;
    },
    getStatisticsMonthlyColumn(monthlyStatisticsPostCondition: Record<string, any>[]) {
      let monthlyStatisticsColumn: Record<string, any>[] = [];
      monthlyStatisticsColumn.push({
        name: "可休天数",
        key: "employeeRemainingRestDays",
        width: 40
      });
      monthlyStatisticsColumn.push({
        name: "出勤",
        key: "attendanceDays",
        width: 40
      });
      monthlyStatisticsColumn.push({
        name: "中午值班",
        key: "noonDutyDays",
        width: 40
      });
      monthlyStatisticsColumn.push({
        name: "休",
        key: "restDays",
        width: 35
      });
      monthlyStatisticsColumn.push({
        name: "年休",
        key: "annualRestDays",
        width: 40
      });
      monthlyStatisticsPostCondition.forEach((condition: Record<string, any>) => {
        monthlyStatisticsColumn.push({
          name: condition.conditionKey,
          key: String(condition.conditionValue),
          width: 35
        });
      });
      return monthlyStatisticsColumn;
    }
  };
}
