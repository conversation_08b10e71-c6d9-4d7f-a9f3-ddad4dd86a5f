/*
 * FilePath     : \src\views\scheduling\hooks\useSchedulingControl.ts
 * Author       : 苏军志
 * Date         : 2024-02-25 10:38
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-22 14:20
 * Description  : 排班相关逻辑
 * CodeIterationRecord:
 */
export function useSchedulingControl(shiftSchedulingTable: TableView) {
  // 上午
  const noonTypeAM: String = "1";
  // 下午
  const noonTypePM: String = "2";
  /**
   * @description: 获取借调类型，开始午别，结束午别
   * @param rowIndex
   * @param columnIndex
   * @return tuple[string,string,string]元组,借调/被借，开始午别，结束午别
   */
  const getEmployeeSecondmentNoon = (rowIndex: number, columnIndex: number): [string, string, string] => {
    let startNoonTypeID = "";
    let endNoonTypeID = "";
    const column: TableColumn | undefined = shiftSchedulingTable.columns.find((column) => column.index === columnIndex);
    const row: Record<string, any> = shiftSchedulingTable.rows[rowIndex];
    if (!column || !row) {
      return ["", startNoonTypeID, endNoonTypeID];
    }
    // 本部门借调出去
    if (row.employee.secondedList?.length) {
      const startSeconded: Record<string, string> = row.employee.secondedList.find(
        (seconded: Record<string, string>) => datetimeUtil.formatDate(seconded.startDate, "yyyyMMdd") === column.key
      );
      if (startSeconded) {
        startNoonTypeID = startSeconded.startNoonTypeID;
      }
      const endSeconded: Record<string, string> = row.employee.secondedList.find(
        (seconded: Record<string, string>) => datetimeUtil.formatDate(seconded.endDate, "yyyyMMdd") === column.key
      );
      if (endSeconded) {
        endNoonTypeID = endSeconded.endNoonTypeID;
      }
      return [row[column.key]?.secondedFlag ? "seconded" : "", startNoonTypeID, endNoonTypeID];
    }
    // 借调到本部门
    if (row.employee.secondmentList?.length) {
      const startSecondment: Record<string, string> = row.employee.secondmentList.find(
        (secondment: Record<string, string>) => datetimeUtil.formatDate(secondment.startDate, "yyyyMMdd") === column.key
      );
      if (startSecondment) {
        startNoonTypeID = startSecondment.startNoonTypeID;
      }
      const endSecondment: Record<string, string> = row.employee.secondmentList.find(
        (secondment: Record<string, string>) => datetimeUtil.formatDate(secondment.endDate, "yyyyMMdd") === column.key
      );
      if (endSecondment) {
        endNoonTypeID = endSecondment.endNoonTypeID;
      }
      return [row[column.key]?.secondmentFlag ? "secondment" : "non-secondment-date", startNoonTypeID, endNoonTypeID];
    }
    return ["", startNoonTypeID, endNoonTypeID];
  };
  /**
   * @description: 判断是否可以排班
   * @param employeeSecondmentNoon
   * @param noonTypeID
   * @return
   */
  const canScheduling = (employeeSecondmentNoon: [string, string, string], noonTypeID: string) => {
    let [type, startNoonTypeID, endNoonTypeID] = employeeSecondmentNoon;
    if (
      // 正常人员
      type === "" ||
      (type === "seconded" &&
        // 被借调第一天的上午或最后一天的下午
        ((startNoonTypeID === noonTypePM && noonTypeID !== startNoonTypeID) ||
          (endNoonTypeID === noonTypeAM && noonTypeID !== endNoonTypeID))) ||
      (type === "secondment" &&
        // 借调到本部门的全天或第一天的下午或最后一天的上午
        ((!startNoonTypeID && !endNoonTypeID) ||
          startNoonTypeID === noonTypeAM ||
          (startNoonTypeID !== noonTypePM && endNoonTypeID === noonTypePM) ||
          (startNoonTypeID === noonTypePM && noonTypeID === startNoonTypeID) ||
          (endNoonTypeID === noonTypeAM && noonTypeID === endNoonTypeID)))
    ) {
      return true;
    }
    return false;
  };

  return {
    /**
     * @description: 获取借调类型，开始午别，结束午别
     * @param rowIndex
     * @param columnIndex
     * @return tuple[string,string,string]元组,借调/被借，开始午别，结束午别
     */
    getEmployeeSecondmentNoon,
    /**
     * @description: 判断是否可以排班
     * @param employeeSecondmentNoon
     * @param noonTypeID
     * @return
     */
    canScheduling,
    /**
     * @description: 检核是否为 本部门人员被借调出去的列、借调到本部门的人的非借调列
     * @param rowIndex
     * @param columnIndex
     * @return
     */
    checkSeconded(rowIndex: number, columnIndex: number) {
      const column: TableColumn | undefined = shiftSchedulingTable.columns.find((column) => column.index === columnIndex);
      const row: Record<string, any> = shiftSchedulingTable.rows[rowIndex];
      if (!column || !row) {
        return false;
      }
      //#region  被借调出去人员 拦截判断
      let isIntercept = true;
      // tuple元组解构-取值
      let [type, startNoonTypeID, endNoonTypeID] = getEmployeeSecondmentNoon(rowIndex, columnIndex);
      // 如果开始是下午不拦截 因为上午还应该能排班
      // 如果结束是上午不拦截 因为下午还应该能排班
      if (type === "seconded" && (startNoonTypeID === noonTypePM || endNoonTypeID === noonTypeAM)) {
        isIntercept = false;
      }
      if (row[column.key]?.secondedFlag && isIntercept) {
        return true;
      }
      //#endregion

      //#region 被借调到本部门 拦截判断
      if (row.employee?.secondmentList?.length && !row[column.key]?.secondmentFlag) {
        return true;
      }
      //#endregion
      return false;
    }
  };
}
