<!--
 * FilePath     : /src/views/annualPlan/components/principalSelectorDialog.vue
 * Author       : 杨欣欣
 * Date         : 2025-06-27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 18:59
 * Description  : 负责人选择弹窗组件
 * CodeIterationRecord:
 -->
<template>
  <div class="principal-selector-dialog">
    <el-dialog v-model="visible" title="选择负责人" @close="handleDialogClear">
      <advanced-employee-selector ref="employeeSelector" :data="initialEmployeeAndGroups" />
      <template #footer>
        <el-button @click="handleDialogClear">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import advancedEmployeeSelector from "@/components/selector/advancedEmployeeSelector.vue";
import type { initialEmployee, initialEmployeeGroup, selectedEmployeeOrGroup } from "@/types/advancedEmployeeSelectorTypes";
import type { annualPrincipal } from "../types/common";

const { principals = [] } = defineProps<{
  principals: annualPrincipal[];
}>();
// eslint-disable-next-line no-spaced-func
const emit = defineEmits<{
  (e: "confirm", principals: selectedEmployeeOrGroup[]): void;
}>();

const visible = defineModel<boolean>({
  required: true
});
type selectorType = InstanceType<typeof advancedEmployeeSelector>;
const employeeSelector = useTemplateRef<selectorType>("employeeSelector");
const initialEmployeeAndGroups = ref<(initialEmployee | initialEmployeeGroup)[] | undefined>(undefined);

/**
 * @description: 初始化回显数据
 */
const initSelectorData = () => {
  if (!principals.length) {
    initialEmployeeAndGroups.value = undefined;
    return;
  }

  const seen = new Set<string>();
  initialEmployeeAndGroups.value = [];

  principals.forEach((principal) => {
    const hasGroup = principal.groupID && String(principal.groupID) !== "undefined" && String(principal.groupID) !== "null";
    const key = hasGroup ? `group_${principal.groupID}` : `employee_${principal.employeeID}`;

    if (!seen.has(key)) {
      seen.add(key);

      if (hasGroup) {
        initialEmployeeAndGroups.value!.push({
          id: principal.groupID!,
          name: principal.groupName!,
          members: principals.filter((p) => p.groupID === principal.groupID).map((item) => ({ id: item.employeeID }))
        });
      } else {
        initialEmployeeAndGroups.value!.push({ id: principal.employeeID });
      }
    }
  });
};
whenever(visible, () => initSelectorData());

/**
 * @description: 处理确认操作
 */
const handleConfirm = () => {
  const selectedItems = employeeSelector.value?.getSelectedData() || [];
  emit("confirm", selectedItems);
  visible.value = false;
};

/**
 * 处理取消操作
 */
const handleDialogClear = () => {
  employeeSelector.value?.clearSelect();
  visible.value = false;
};
</script>

<style lang="scss" scoped>
.principal-selector-dialog {
  :deep(.el-dialog) {
    width: 40%;
  }
  :deep(.el-dialog__body) {
    padding: 12px;
  }

  :deep(.el-dialog__footer) {
    padding-top: 8px;
  }
}
</style>
