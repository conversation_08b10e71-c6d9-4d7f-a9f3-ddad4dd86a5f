<!--
 * FilePath     : \nursing-management-web\src\views\trainingManagement\trainingClass\index.vue
 * Author       : 孟昭永
 * Date         : 2024-07-10 09:46
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-26 16:46
 * Description  : 培训组维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="training-class" headerHeight="60px" :drawerOptions="drawerOptions">
    <!-- 头部操作按钮 -->
    <template #header>
      <div class="header">
        <el-button type="primary" class="add-button" v-permission:B="1" @click="openDrawer('training', undefined)">新增</el-button>
      </div>
    </template>
    <!-- 培训班列表 -->
    <div :class="[{ 'content-show-main': !showDetailFlag }]">
      <el-table
        height="100%"
        :data="trainingClassList"
        :row-key="(row :trainingClass) => row.trainingClassMainID"
        highlight-current-row
        border
        stripe
        :lazy="true"
        @row-click="recordRowClick"
      >
        <!-- 表格标题列 -->
        <el-table-column
          v-for="(column, index) in mainTableColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          :width="convertPX(column.width)"
          :min-width="convertPX(column.minWidth)"
          :align="column.align"
        >
          <template #default="{ row }">
            <span
              v-if="column.isDateTimeColumn"
              v-formatTime="{ value: row[column.prop!], type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
            />
            <span v-if="column.isDateColumn" v-formatTime="{ value: row[column.prop!], type: 'date', format: 'yyyy-MM-dd' }" />
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column label="操作" :width="convertPX(150)">
          <template #default="{ row }">
            <el-tooltip content="编辑">
              <i class="iconfont icon-edit" type="text" v-permission:B="3" @click.stop="openDrawer('training', row)"></i>
            </el-tooltip>
            <el-tooltip content="报名">
              <i
                class="iconfont icon-sign-up"
                v-visibilityHidden="!row.signUpRecord.statusCode || row.signUpRecord.statusCode !== '1'"
                type="text"
                @click.stop="saveSignUpRecord(row, false)"
              ></i>
            </el-tooltip>
            <el-tooltip content="取消报名">
              <i
                class="iconfont icon-stop"
                v-visibilityHidden="row.signUpRecord.statusCode === '1'"
                @click.stop="saveSignUpRecord(row, true)"
              ></i>
            </el-tooltip>
            <el-tooltip content="报名审核">
              <i
                class="iconfont icon-approveRecord"
                v-visibilityHidden="userRoles.includes(41)"
                type="text"
                @click.stop="openDrawer('signUpReview', row)"
              ></i>
            </el-tooltip>
            <el-tooltip content="发起培训">
              <i class="iconfont icon-preview" @click.stop="navigateToTrainingRecordPage(row)"></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i class="iconfont icon-delete" type="text" v-permission:B="4" @click.stop="deleteTrainingClass(row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 抽屉表单 -->
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName === 'training'"
        ref="trainingClassFormRef"
        :rules="trainingClassRules"
        :model="currRow"
        label-width="100px"
      >
        <el-form-item label="培训班名称" prop="trainingClassName">
          <el-input v-model="currRow!.trainingClassName" clearable placeholder="请输入培训班名称"
        /></el-form-item>
        <el-form-item label="开班日期" prop="startDate">
          <el-date-picker
            class="form-date"
            v-model="currRow!.startDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择开班日期"
            :clearable="false"
            @change="changeDate()"
            :disabled-date="(val:any)=>pickerOptions(currRow!.endDate,val)"
          />
        </el-form-item>
        <el-form-item label="闭班日期" prop="endDate">
          <el-date-picker
            class="form-date"
            v-model="currRow!.endDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择闭班日期"
            :clearable="false"
            @change="changeDate()"
            :disabled-date="(val:any)=>pickerOptions(val,currRow!.startDate)"
          />
        </el-form-item>
        <el-form-item label="培训时长(周)" prop="trainingDuration">
          <el-input class="training-duration" v-model="currRow!.trainingDuration" disabled placeholder="培训时长" />
        </el-form-item>
        <el-form-item label="结业日期" prop="completeDate">
          <el-date-picker
            class="form-date"
            v-model="currRow!.completeDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择结业日期"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item label="培训课程" prop="courseSettingID">
          <course-setting-selector
            v-model="currRow!.courseSettingIDArr"
            :isMultiple="true"
            :width="convertPX(500)"
          ></course-setting-selector>
        </el-form-item>
      </el-form>
      <sign-up-record
        v-if="drawerOptions.drawerName === 'signUpReview'"
        :signUpType="'2'"
        :sourceID="currRow.trainingClassMainID"
        :disabledSelector="true"
      >
      </sign-up-record>
    </template>
    <!-- 培训群组课程 -->
    <base-layout v-if="showDetailFlag">
      <template #header>
        <span>培训班课程列表</span>
      </template>
      <el-table height="100%" :data="trainingClassCourseList" highlight-current-row border stripe :lazy="true">
        <!-- 子课程列表 -->
        <el-table-column type="expand" :width="convertPX(40)">
          <template #default="{ row }">
            <el-table :data="row.childCourses" border stripe highlight-current-row :show-header="false" :lazy="true">
              <el-table-column type="hidden" :width="convertPX(40)" />
              <el-table-column
                v-for="(column, index) in detailTableColumns"
                :key="index"
                :prop="column.prop"
                :label="column.label"
                :width="convertPX(column.width)"
                :min-width="convertPX(column.minWidth)"
                :align="column.align"
              >
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <!-- 主课程列表 -->
        <el-table-column
          v-for="(column, index) in detailTableColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          :width="convertPX(column.width)"
          :min-width="convertPX(column.minWidth)"
          :align="column.align"
        >
        </el-table-column>
      </el-table>
    </base-layout>
  </base-layout>
</template>
<script setup lang="ts">
import signUpRecord from "@/views/signUpRecord/index.vue";
import { useTrainingClass } from "../hooks/useTrainingClass";
import type { trainingClass, trainingClassCourse } from "../types/trainingClass";
//#region 类型定义
const currRow = ref<trainingClass>({} as trainingClass);
const router = useRouter();
let { validateRule } = useForm();
const { userStore } = useStore();
const trainingClassFormRef = shallowRef();
const { trainingClassList, copyTrainingClassList, saveTrainingClass, deleteTrainingClassByID, getTrainingClassList } = useTrainingClass();
const userRoles = userStore.roles as number[];
const mainTableColumns = [
  { label: "培训班名称", prop: "trainingClassName", minWidth: 100 },
  { label: "开班日期", prop: "startDate", width: 150, align: "center", isDateColumn: true },
  { label: "闭班日期", prop: "endDate", width: 150, align: "center", isDateColumn: true },
  { label: "培训时长(周)", prop: "trainingDuration", width: 120, align: "center" },
  { label: "结业日期", prop: "completeDate", width: 150, align: "center", isDateColumn: true },
  { label: "添加人员", prop: "addEmployeeName", width: 120 },
  { label: "添加时间", prop: "addDateTime", width: 180, align: "center", isDateTimeColumn: true },
  { label: "修改人员", prop: "modifyEmployeeName", width: 120 },
  { label: "修改时间", prop: "modifyDateTime", width: 180, align: "center", isDateTimeColumn: true }
];
const convertPX: any = inject("convertPX");
// 弹窗参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "编辑培训班",
  drawerSize: "40%",
  showDrawer: false,
  drawerName: "",
  cancel: async () => (await restoreDrawerOptions()) && (drawerOptions.value.showDrawer = false),
  confirm: async () => {
    if (await validateRule(trainingClassFormRef)) {
      await saveTrainingClass(currRow.value!);
      drawerOptions.value.showDrawer = false;
    }
  }
});

// 表单javascript验证规则
const trainingClassRules = {
  trainingClassName: [
    { required: true, message: "请输入培训班名称", trigger: "blur" },
    { min: 1, max: 50, message: "培训班名称长度在 1 到 50 个字符", trigger: "blur" }
  ],
  startDate: [{ required: true, message: "请选择开班日期", trigger: "blur" }],
  endDate: [{ required: true, message: "请选择闭班日期", trigger: "blur" }],
  completeDate: [{ required: true, message: "请选择结业日期", trigger: "blur" }]
};
// 显示详情部分
const showDetailFlag = ref<boolean>(false);
const detailTableColumns = [
  { label: "年份", prop: "year", width: 60, align: "center" },
  { label: "课程名称", prop: "courseName", minWidth: 100 },
  { label: "课程简介", prop: "courseIntroduction", minWidth: 120 },
  { label: "课程分类", prop: "courseTypeName", minWidth: 120 }
];
const trainingClassCourseList = ref<trainingClassCourse[]>([]);
//#endregion

//#region 事件交互函数
const openDrawer = (drawerName: string, row?: trainingClass) => {
  drawerOptions.value.drawerName = drawerName;
  currRow.value = common.clone(row) ?? ({} as trainingClass);
  if (drawerName === "training") {
    let diffDate = datetimeUtil.getTimeDifference(currRow.value.startDate, currRow.value.endDate, undefined, "D");
    currRow.value.trainingDuration = diffDate ? Math.ceil((Number(diffDate) + 1) / 7) : 0;
  } else if (drawerName === "signUpReview") {
    drawerOptions.value.drawerTitle = "报名审核";
    drawerOptions.value.drawerSize = "100%";
    drawerOptions.value.showCancel = false;
    drawerOptions.value.showConfirm = false;
  }
  drawerOptions.value.showDrawer = true;
};

/**
 * 还原初始弹窗配置
 */
const restoreDrawerOptions = async () => {
  drawerOptions.value.showDrawer = false;
  drawerOptions.value.drawerTitle = "编辑培训班";
  drawerOptions.value.drawerSize = "40%";
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
  currRow.value = {} as trainingClass;
  if (drawerOptions.value.drawerName === "signUpReview") {
    // 审核后刷新培训群组列表数据
    await getTrainingClassList();
  }
  return true;
};

/**
 * @description: 删除培训群组
 * @param row 表格当前行记录
 * @return
 */
const deleteTrainingClass = async (row: trainingClass) => {
  await deleteTrainingClassByID(row.trainingClassMainID);
};
/**
 * @description: 开班日期和闭班日期改变培训时长
 * @return
 */
const changeDate = () => {
  if (currRow.value) {
    let diffDate = datetimeUtil.getTimeDifference(currRow.value.startDate, currRow.value.endDate, undefined, "D");
    currRow.value.trainingDuration = diffDate ? Math.ceil((Number(diffDate) + 1) / 7) : 0;
  }
};
/**
 * @description: 开班日期不能晚于闭班日期
 * @param startDate 开班日期
 * @param endDate 闭班日期
 */
const pickerOptions = (endDate: any, startDate: any) => {
  if (!endDate || !startDate) {
    return;
  }
  let endDateDateTime = datetimeUtil.formatDate(endDate, "yyyy-MM-dd");
  let startDateTime = datetimeUtil.formatDate(startDate, "yyyy-MM-dd");
  return endDateDateTime < startDateTime;
};
/**
 * @description: 行点击事件
 * @param row
 * @return
 */
const recordRowClick = async (row: trainingClass) => {
  showDetailFlag.value = !showDetailFlag.value;
  if (showDetailFlag.value) {
    trainingClassList.value = [row];
    await trainingClassService.getTrainingClassCourseListByMainID({ trainingClassMainID: row.trainingClassMainID }).then((resp: any) => {
      if (resp) {
        trainingClassCourseList.value = resp;
      }
    });
  } else {
    trainingClassList.value = copyTrainingClassList.value;
  }
};
/**
 * @description: 保存报名记录
 * @param row 当前培训群组信息
 * @return
 */
const saveSignUpRecord = async (row: any, cancelFlag: boolean) => {
  if (!row) {
    showMessage("warning", "未找到群组信息，请刷新后重试！");
    return;
  }
  // 存在报名记录且报名被拒绝，进行提示
  if (row.signUpRecord && row.signUpRecord.statusCode === "0") {
    showMessage("warning", "您已报名且报名被拒绝,不可重复报名！");
    return;
  }
  let param = row.signUpRecord;
  param.sourceType ??= "2";
  param.sourceID ??= row.trainingClassMainID;
  param.statusCode = cancelFlag ? "2" : "1";
  param.signUpType ??= "1";
  param.employeeID ??= userStore.employeeID;
  await signUpRecordService.saveSignUpRecord(param).then((res: any) => {
    if (res) {
      showMessage("success", cancelFlag ? "取消报名成功！" : "报名成功！");
      return;
    }
    showMessage("error", cancelFlag ? "取消报名失败！" : "报名失败！");
  });
  getTrainingClassList();
};
/**
 * @description: 跳转到培训发布页面
 */
const navigateToTrainingRecordPage = (row: any) => {
  router.push({ name: "trainingRecord", query: { trainingClassMainID: row.trainingClassMainID } });
};
//#endregion
</script>

<style lang="scss">
.training-class {
  height: 100%;
  width: 100%;
  .form-date {
    width: 200px;
  }
  .training-duration {
    width: 100px;
  }
  .content-show-main {
    height: calc(100% - 5px);
  }
}
</style>
