/*
 * FilePath     : \src\views\signUpRecord\types\signUpRecordView.ts
 * Author       : 张现忠
 * Date         : 2024-07-18 16:38
 * LastEditors  : 张现忠
 * LastEditTime : 2024-07-20 17:21
 * Description  : 报名记录视图模型
 * CodeIterationRecord: 
 */

/**
 * 培训报名视图模型
 */
export interface signUpRecordView {
  /**
   * 报名记录ID
   */
  signUpRecordID: string;

  /**
   * 来源ID
   */
  sourceID: string;

  /**
   * 来源类别
   */
  sourceType: string;
    /**
   * 来源类别名称
   */
    sourceTypeDescription: string;

  /**
   * 状态(0:不同意，1：同意)
   */
  statusCode: string;

  /**
   * 状态描述
   */
  statusDescription: string;

  /**
   * 报名类型(SelfSignUp：自主报名；TrainingChoose:系统自动报名、系统自动补报名）
   */
  signUpType: string;

  /**
   * 自主报名,新增培训选中
   */
  signUpTypeDescription: string;

  /**
   * 人员工号
   */
  employeeID: string;
/**
 * 人员姓名
 */
  employeeName: string;

  /**
   * 新增人员姓名
   */
  addEmployeeName: string;
  /**
   * 新增时间
   */
  addDateTime: string;
}
