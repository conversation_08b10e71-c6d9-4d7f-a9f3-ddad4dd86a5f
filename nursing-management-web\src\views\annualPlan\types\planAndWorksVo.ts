/*
 * FilePath     : \src\views\annualPlan\types\planAndWorksVo.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-28 07:28
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 19:38
 * Description  : 计划工作视图对象
 * CodeIterationRecord: 
 */
import type { annualPrincipal, workType } from "./common";

/**
 * 计划工作视图对象
 */
export interface planAndWorksVo {
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 分类名称
   */
  typeName: string;
  /**
   * 工作集合
   */
  children: planWork[];
}

/**
 * 季度计划工作项
 */
export interface planWork {
  /**
   * 主键
   */
  quarterPlanDetailID: string | undefined;
  monthlyPlanDetailID: string | undefined;
  /**
   * 参考执行项目ID
   */
  apInterventionID?: number;
  /**
   * 参考执行项目名称
   */
  apInterventionLocalShowName: string;
  /**
   * 分类字典ID
   */
  typeID: number;
  /**
   * 序号
   */
  sort?: number;
  /**
   * 季度工作内容
   */
  workContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作类型
   */
  workType: workType;
  /**
   * 临时性工作标记
   */
  isTemp: boolean;
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人详细信息
   */
  principals: annualPrincipal[];
  /**
   * 计划执行月份
   */
  planMonths: number[];
}

