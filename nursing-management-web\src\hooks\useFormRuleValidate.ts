/*
 * FilePath     : \src\hooks\useFormRuleValidate.ts
 * Author       : 张现忠
 * Date         : 2023-09-18 16:31
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-01-25 14:32
 * Description  : 表单公共方法hooks
 * CodeIterationRecord:
 */

export function useForm() {
  return {
    /**
     * 保存前，根据rules规则验证表单数据
     */
    /**
     * @description: 保存前，根据rules规则验证表单数据
     * @return {Boolean} 是否验证成功
     * @param {Ref} formRefs el-form的对应ref
     */
    async validateRule(formRefs: Ref<any> | any): Promise<boolean> {
      const form = toValue(formRefs);
      if (!form) {
        return false;
      }
      let validResult = true;
      // 表单校验
      await form.validate((valid: any) => {
        validResult = valid;
      });
      return validResult;
    }
  };
}
