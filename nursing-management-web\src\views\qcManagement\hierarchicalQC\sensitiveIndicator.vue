<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\sensitiveIndicator.vue
 * Author       : 马超
 * Date         : 2025-01-26 15:04
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-04-13 09:33
 * Description  : 敏感指标督导记录页面
 * CodeIterationRecord:
-->
<template>
  <base-layout class="patient-supervision" headerHeight="60" :drawerOptions="drawerOptions">
    <template #header>
      <div class="filter-condition">
        <div class="left">
          <el-radio-group v-model="searchView.supervisionType">
            <el-radio-button
              v-for="(type, index) in patientTypeList"
              :key="index"
              :label="type.label"
              :value="type.value"
              @change="getDepartmentList()"
            />
          </el-radio-group>
          <span>日期:</span>
          <el-date-picker
            v-model="searchView.rangeDate"
            type="daterange"
            range-separator="-"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="date-picker"
            :clearable="false"
            @change="handleDateChange"
          />

          <el-radio-group v-model="searchView.supervisionFlag" @change="filterSupervisionList">
            <el-radio :value="false">未督导</el-radio>
            <el-radio :value="true">已督导</el-radio>
          </el-radio-group>
        </div>
        <div class="right">
          <export-excel :exportExcelOption="exportExcelOption">
            <el-button class="print-button" @click="createExportExcelParam">导出数据</el-button>
          </export-excel>
        </div>
      </div>
    </template>
    <department-check-box
      v-model:selectedDistrict="districtValue"
      v-model:checkedDepartments="departmentCheckList"
      :district-options="districtOptions"
      :department-groups="departmentGroups"
      :count-field="'noSupervisionCount'"
      :disabled="deptOptionFlag"
      @district-change="changeDistrict"
    />
    <record-and-main-layout class="supervision-main-record" v-model="switchArr" :recordHiddenHeight="135" headerHeight="5px">
      <template #recordContent>
        <div class="patient-view">
          <dynamic-table
            v-model="recordTableView.tableData"
            :headerList="recordTableView.tableHeader"
            @rowClick="recordRowClick"
            :row-class-name="getRowClassName"
          >
            <template #operate="scope">
              <span v-if="isHeaderNurse">
                <el-tooltip content="新增">
                  <i class="iconfont icon-add" v-permission:B="1" @click.stop="addSupervision(scope.row, true)"></i>
                </el-tooltip>
                <el-tooltip v-if="searchView.supervisionFlag" content="删除">
                  <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteAllRecord(scope.row)"></i>
                </el-tooltip>
              </span>
            </template>
            <template #riskContent="scope">
              <span class="risk-level" :style="{ backgroundColor: scope.row.riskColor }">
                {{ scope.row.riskContent }}
              </span>
            </template>
          </dynamic-table>
        </div>
      </template>
      <template #mainHeader>
        <div class="qc-record">
          <span class="qc-record-title">督导记录</span>
          <span v-if="isHeaderNurse">
            <el-button class="add-button" @click="addSupervision(currentRecord, true)">新增</el-button>
          </span>
        </div>
      </template>
      <template #mainContent>
        <dynamic-table v-model="qcRecordTableView.tableData" :headerList="qcRecordTableView.tableHeader">
          <template #operate="scope">
            <span v-if="isHeaderNurse">
              <el-tooltip content="修改">
                <i class="iconfont icon-edit" v-permission:B="3" @click.stop="modifySupervision(scope.row)"></i>
              </el-tooltip>
              <el-tooltip content="删除">
                <i class="iconfont icon-delete" v-permission:B="4" @click.stop="deleteRecord(scope.row)"></i>
              </el-tooltip>
            </span>
          </template>
          <template #riskContent="scope">
            <span class="risk-level" :style="{ backgroundColor: scope.row.riskColor }">
              {{ scope.row.riskContent }}
            </span>
          </template>
        </dynamic-table>
      </template>
    </record-and-main-layout>
    <template #drawerContent>
      <qcForm v-model="saveView" ref="qcFormDom" :formData="formData" @getDetails="getDetails"></qcForm>
    </template>
    <template #drawerOtherFooter>
      <div>
        <span v-if="isHeaderNurse">
          <el-button @click="drawerOptions.showDrawer = false">取消</el-button>
          <el-button v-permission:B="8" class="print-button" @click="saveSupervision('T')">暂存</el-button>
          <el-button v-permission:B="2" type="primary" @click="saveSupervision('S')">保存</el-button>
        </span>
      </div>
    </template>
  </base-layout>
</template>

<script lang="ts" setup>
import { useVisitsAndSupervisionOperations } from "./hooks/useVisitsAndSupervisionOperations";
const { userStore } = useStore();
const isHeaderNurse = ref<boolean>(true);

onMounted(async () => {
  await getNurseRole();
  await getSupervisionPatientType();
  await getDepartmentList();
});

// 组件显示隐藏控制开关
const switchArr = ref<boolean[]>([true, false]);
/**
 * @description: 获取督导类型
 */
const getSupervisionPatientType = async () => {
  let params: SettingDictionaryParams = {
    settingType: "Medical",
    settingTypeCode: "PatientProfileRecord",
    settingTypeValue: "ProfileID_SupervisionType"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
    patientTypeList.value = datas || [];
    patientTypeList.value.length !== 0 && (searchView.value.supervisionType = datas[0].value);
  });
};
const districtResponse = ref<Record<string, any>[]>([]);
const districtOptions = ref<any>([]);
const districtValue = ref<any>();
const departmentGroups = ref<any>([]);
const searchViewRes = ref<any>();
let recordTableView = ref<Record<string, Record<string, any>[]>>({});
let copyRecordTableView = ref<Record<string, Record<string, any>[]>>({});
const patientTypeList = ref<Record<string, string>[]>([]);
const departmentCheckList = ref<any[]>([]);
const searchView = ref<Record<string, any>>({
  supervisionFlag: false,
  rangeDate: [
    datetimeUtil.formatDate(datetimeUtil.addDate(new Date(), -14), "yyyy-MM-dd"),
    datetimeUtil.formatDate(new Date(), "yyyy-MM-dd")
  ],
  supervisionType: "",
  stationIDs: []
});
/**
 * @description: 获取督导患者信息
 */
const getSupervisionRecord = async () => {
  searchView.value.stationIDs = departmentCheckList.value;
  searchView.value.startDate = searchView.value.rangeDate[0];
  searchView.value.endDate = searchView.value.rangeDate[1];
  await hierarchicalQCService.getSupervisionRecord(searchView.value).then((res: any) => {
    res && (searchViewRes.value = res);
    copyRecordTableView.value = common.clone(searchViewRes.value);
    filterSupervisionList();
  });
  getNoSupervisionCount();
};
/**
 * @description: 获取未督导数量
 */
const getNoSupervisionCount = () => {
  departmentGroups.value.forEach((item: any) => {
    let currentData = searchViewRes.value?.tableData.filter((data: any) => data.nmDepartmentID === item.value);
    if (!currentData || currentData.length === 0) {
      item.noSupervisionCount = "0/0";
      return; // 跳过空数据的情况
    }
    let filterData = currentData.filter((data: any) => !data.isSensitiveFlag);
    item.noSupervisionCount = `${filterData.length}/${currentData.length}`; // 动态计算未督导数量
  });
};
/**
 * @description: 监听筛选条件
 * @param searchView
 * @return
 */
watch(departmentCheckList, async () => {
  recordTableView.value.tableData = [];
  if (switchArr.value[1]) {
    switchArr.value = [true, false];
  }
  departmentCheckList.value.forEach((element) => {
    if (searchView.value.supervisionFlag) {
      let currentRowData: any = searchViewRes.value.tableData.filter(
        (item: any) => item.nmDepartmentID === element && item.isSensitiveFlag
      );
      if (currentRowData.length > 0) {
        currentRowData.forEach((item: any) => {
          recordTableView.value.tableData.push(item);
        });
      }
    } else {
      let currentRowData: any = searchViewRes.value.tableData.filter(
        (item: any) => item.nmDepartmentID === element && !item.isSensitiveFlag
      );
      currentRowData.forEach((item: any) => {
        recordTableView.value.tableData.push(item);
      });
    }
  });
});
/**
 * @description: 筛选督导患者信息
 * @return
 */
const filterSupervisionList = () => {
  let result: any[] = [];
  if (searchView.value.supervisionFlag) {
    departmentCheckList.value.forEach((element) => {
      const filteredData = searchViewRes.value.tableData.filter((item: any) => item.nmDepartmentID === element && item.isSensitiveFlag);
      result = result.concat(filteredData);
    });
  } else {
    if (switchArr.value[1]) {
      switchArr.value = [true, false];
    }
    departmentCheckList.value.forEach((element) => {
      const filteredData = searchViewRes.value.tableData.filter((item: any) => item.nmDepartmentID === element && !item.isSensitiveFlag);
      result = result.concat(filteredData);
    });
  }
  recordTableView.value.tableData = result;
};

let currentRecord = ref<Record<string, any>>();
/**
 * @description:督导患者信息行点击
 * @param row
 * @return
 */
const recordRowClick = (row: any) => {
  // 未督导 无督导维护记录查看
  if (!searchView.value.supervisionFlag) {
    switchArr.value[1] = false;
    return;
  }
  switchArr.value[1] = !switchArr.value[1];
  currentRecord.value = switchArr.value[1] ? row : undefined;
  recordTableView.value.tableData = switchArr.value[1] ? [row] : common.clone(searchViewRes.value.tableData);
  filterSupervisionList();
  // 根据督导信息获取督导记录
  switchArr.value[1] && getSensitiveQcRecord();
};
let qcRecordTableView = ref<Record<string, Record<string, any>[]>>({});
/**
 * @description: 获取督导记录
 */
const getSensitiveQcRecord = () => {
  let params = {
    sourceID: currentRecord.value?.sourceID,
    sourceType: currentRecord.value?.sourceType,
    supervisionType: searchView.value?.supervisionType,
    templateCode: currentRecord.value?.templateCode
  };
  hierarchicalQCService.getSensitiveQcRecord(params).then((res: any) => {
    res && (qcRecordTableView.value = res);
  });
};
/**
 * @description: 抽屉控制参数
 */
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false
});
import { saveClass } from "./types/hierarchicalSaveView";
const saveView = reactive(new saveClass());
saveView.guidanceLabel = "存在问题及指导：";
saveView.improvementShowFlag = false;
let currentMaintenance = ref<Record<string, any>>();
const { record, details, formData, getDetails, save, add, modify, initFormData } = useVisitsAndSupervisionOperations(
  saveView,
  currentRecord,
  currentMaintenance
);
/**
 * @description:督导记录新增
 * @param row
 * @param addFlag
 * @return
 */
const addSupervision = (row: Record<string, any> | undefined, addFlag: boolean) => {
  if (!row) {
    return;
  }
  let { nmDepartmentName, bedNumber, patientName } = row;
  drawerOptions.value.drawerTitle = nmDepartmentName + "-" + bedNumber + "床-" + patientName;
  drawerOptions.value.showDrawer = true;
  let params = {
    profileID: searchView.value.supervisionType,
    qcMainID: saveView.hierarchicalQCMainID
  };
  add(row, addFlag, searchView.value.supervisionType);
  hierarchicalQCService.getSensitiveQcAssessView(params).then((res) => {
    res && (formData.value = res);
  });
};
/**
 * @description: 督导记录修改
 * @param row
 * @return
 */
const modifySupervision = (row: Record<string, any>) => {
  currentMaintenance.value = row;
  modify(row, () => {
    let params = {
      profileID: searchView.value.supervisionType,
      qcMainID: saveView.hierarchicalQCMainID
    };
    return hierarchicalQCService.getSensitiveQcAssessView(params);
  });
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 保存督导记录
 * @param saveType
 * @return
 */
const saveSupervision = async (saveType: string) => {
  save(saveType, (qcMainAndDetail) => {
    let params = {
      supervisionFormID: currentRecord.value?.superVisionFormID,
      upNMDepartmentID: currentRecord.value?.upNmDepartmentID,
      supervisionRecord: record.value,
      supervisionDetails: details.value,
      nurseEmployeeID: currentRecord.value?.nurseEmployeeID,
      qcMainAndDetail
    };
    hierarchicalQCService.saveSensitiveRecord(params).then(async (res) => {
      res && showMessage("success", "保存成功");
      drawerOptions.value.showDrawer = false;
      currentMaintenance.value = undefined;
      switchArr.value[1] ? getSensitiveQcRecord() : await getSupervisionRecord();
    });
  });
};

/**
 * @description: 删除所有督导记录
 * @param row
 * @return
 */
const deleteAllRecord = (row: Record<string, any>) => {
  const { sourceType, sourceID } = row;
  let params = {
    sourceType,
    sourceID,
    relatedTableName: "HierarchicalQCRecord"
  };
  confirmBox("确定要删除所有督导记录么？", "督导记录删除", (flag: Boolean) => {
    if (flag) {
      hierarchicalQCService.deleteAllSupervisionRecord(params).then((res) => {
        if (res) {
          showMessage("success", "删除成功");
          switchArr.value[1] = false;
          getSupervisionRecord();
        }
      });
    }
  });
};
/**
 * @description: 删除单条督导记录
 * @param row
 * @return
 */
const deleteRecord = (row: Record<string, any>) => {
  let params = {
    patientProfileRecordID: row.patientProfileRecordID
  };
  confirmBox("确定要删除督导记录么？", "督导记录删除", (flag: Boolean) => {
    if (flag) {
      hierarchicalQCService.deleteSensitiveRecord(params).then((res) => {
        if (res) {
          showMessage("success", "删除成功");
          switchArr.value[1] = false;
          getSupervisionRecord();
        }
      });
    }
  });
};
const deptOptionFlag = ref<boolean>(false);
/**
 * @description: 获取病区列表
 * @return
 */
const getDepartmentList = async () => {
  let params = {
    employeeID: userStore.employeeID
  };
  await dictionaryService.getVisitsDeptOptions(params).then((res: any) => {
    districtResponse.value = res;
    districtResponse.value.forEach((item: any) => {
      districtOptions.value.push({
        label: item.label,
        value: item.value
      });
    });
    if (districtOptions.value.length === 1) {
      deptOptionFlag.value = true;
    }
    districtValue.value = districtOptions.value[0].value;
  });
  await changeDistrict();
};
/**
 * @description: 片区改变
 * @param value
 * @return
 */
const changeDistrict = async () => {
  departmentGroups.value = [];
  departmentCheckList.value = [];
  let currentDistrict: any = districtResponse.value.find((item) => item.value === districtValue.value);
  if (currentDistrict.children.length <= 0) {
    return;
  }
  departmentGroups.value = currentDistrict.children;
  searchView.value.stationIDs = currentDistrict.children.map((item: any) => item.value);
  searchView.value.startDate = searchView.value.rangeDate[0];
  searchView.value.endDate = searchView.value.rangeDate[1];
  await hierarchicalQCService.getSupervisionRecord(searchView.value).then((res: any) => {
    res && (searchViewRes.value = res);
    recordTableView.value.tableHeader = searchViewRes.value.tableHeader;
    recordTableView.value.tableData = [];
    copyRecordTableView.value = common.clone(recordTableView.value);
  });
  getNoSupervisionCount();
};
/**
 * @description: 获取行样式
 * @param row 行数据
 * @return
 */
const getRowClassName = ({ row }: { row: any }) => {
  if (row.dischargeDateTime) {
    return "discharge-row";
  }
  if (
    row.startDateTime &&
    !row.isSensitiveFlag &&
    datetimeUtil.addDate(row.startDateTime, 3, "yyyy-MM-dd") < datetimeUtil.getNowDate("yyyy-MM-dd")
  ) {
    return "no-evaluate-row";
  }
  return "";
};
// 导出的Excel参数
const exportExcelOption = ref<ExportExcelView[]>([]);
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(recordTableView.value.tableData);
  const columns = recordTableView.value.tableHeader.reduce((acc, item) => {
    if (item.prop) {
      acc[item.prop] = item.label;
    }
    return acc;
  }, {});
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: "敏感指标督导记录",
    sheetName: "敏感指标督导记录",
    columnData: columns,
    tableData: cloneData
  });
};

// 添加日期变化处理函数
const handleDateChange = (val: string[]) => {
  if (val) {
    searchView.value.startDate = val[0];
    searchView.value.endDate = val[1];
    getDepartmentList();
  }
};
const getNurseRole = async () => {
  let params = {
    employeeID: userStore.employeeID
  };
  await employeeService.getEmployeeToRole(params).then((res: any) => {
    isHeaderNurse.value = res;
    searchView.value.supervisionFlag = !isHeaderNurse.value;
  });
};
</script>

<style lang="scss">
.patient-supervision {
  height: 100%;
  .qc-record {
    width: 100%;
  }
  .add-button {
    float: right;
    margin-top: 8px;
  }
  .patient-view {
    width: 100%;
    height: 100%;
  }
  .bottom-warp {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    .el-checkbox {
      flex: 1;
    }
  }
  .el-checkbox-group {
    line-height: normal;
    font-size: unset;
  }
  .el-radio-group {
    font-size: unset;
  }
  .checkbox-group-warp {
    border-bottom: 1px solid #ddd;
    width: 100%;
    @include flex-aline(row, space-between);
    .count {
      width: 65px;
      text-align: center;
      height: 100%;
      font-size: 20px;
      background-color: #e6a23c;
      border-radius: 20px;
      color: #fff;
    }
  }
  .checkbox-group:last-child {
    border-bottom: none;
  }
  .el-checkbox {
    width: 100%;
    overflow: hidden;
    --el-border: none !important;
    .el-checkbox__inner {
      border: none !important;
      box-shadow: none !important;
    }
  }
  .el-checkbox__label {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    box-sizing: border-box;
    color: $base-color;
    margin-bottom: 0px;
  }
  .filter-condition {
    .left {
      @include flex-aline(row, start);
      .el-radio-group {
        margin-right: 10px;
      }
    }
    @include flex-aline(row, space-between);
    margin-bottom: 0px;
    .date-picker {
      width: 300px;
      flex-grow: 0;
    }
  }
  .checkbox-selected {
    width: 85%;
  }
  .supervision-main-record {
    .base-header {
      @include flex-aline(row, start);
      margin-bottom: 0px;
      .el-switch {
        margin-left: 10px;
      }
      .date {
        width: 200px !important;
      }
      .el-radio--large {
        margin-right: 10px !important;
      }
    }
  }
  .dynamic-table {
    & .el-table--striped tr.el-table__row--striped,
    & .el-table__body tr.el-table__row.discharge-row {
      background-color: #fdf6ec !important;
    }
    & .el-table--striped tr.el-table__row--striped,
    & .el-table__body tr.el-table__row.no-evaluate-row {
      .cell {
        color: #ff0000 !important;
      }
    }
  }
  .risk-level {
    color: #fff;
    padding: 5px;
  }
}
</style>
