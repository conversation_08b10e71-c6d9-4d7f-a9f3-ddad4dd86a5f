<!--
 * FilePath     : \src\views\approveManagement\approveProcess\components\flowDiagram.vue
 * Author       : 杨欣欣
 * Date         : 2023-09-11 17:52
 * LastEditors  : 杨欣欣
 * LastEditTime : 2024-03-28 15:11
 * Description  : 审批流程图
 * CodeIterationRecord:
 -->
<template>
  <div class="flow-diagram">
    <div class="zoom">
      <el-input-number v-model="scalePercent" :min="5" :max="200" :step="5" />
    </div>
    <el-scrollbar view-class="nodes">
      <div>
        <div class="start-node">流程开始</div>
        <div class="add-node-btn-box">
          <el-button class="add-node-btn iconfont icon-add" @click="addNode()" />
        </div>
      </div>
      <div v-for="(approveProcessNode, index) in displayNodes" :key="index" class="dynamic-node">
        <div class="node-box approver-box-node">
          <div class="header">
            <div class="title">
              <i class="iconfont icon-user" />
              <el-input
                class="edit-name-input"
                v-if="(approveProcessNode as any).isInput"
                @blur="(approveProcessNode as any).isInput = false"
                @keydown.enter.stop.prevent="(approveProcessNode as any).isInput = false"
                v-model="approveProcessNode.approveNodeName"
                v-focus
              />
              <span v-else @click="editNodeName(approveProcessNode)" class="content">{{ approveProcessNode.approveNodeName }}</span>
            </div>
            <i class="actions iconfont icon-del-img" @click="deleteNode(approveProcessNode.approveNodeID, index)" />
          </div>
          <div class="body" @click="editNode(approveProcessNode)">
            <span v-if="approveProcessNode.nodeDetails?.length" v-text="getProcessNodeDetailStr(approveProcessNode)" />
            <span v-else class="placeholder">请选择审批人员</span>
          </div>
        </div>
        <div class="add-node-btn-box">
          <el-button class="add-node-btn iconfont icon-add" @click="addNode(approveProcessNode, index)" />
        </div>
      </div>
      <div class="end-node">流程结束</div>
    </el-scrollbar>
    <!-- 编辑审批节点 -->
    <edit-approve-node-dialog ref="editDialog" v-model="dialogVisible" v-model:node="cloneModifyNode" @submit="saveNode" @cancel="dialogVisible = false"/>
  </div>
</template>
<script setup lang="ts">
import type { approveProcessNode } from "../types/approveProcessNode";
const props = defineProps({
  nodes: {
    type: Array as () => approveProcessNode[],
    default: () => []
  }
});
const dialogVisible = ref(false);
// 双向绑定
const emits = defineEmits(["update:nodes"]);
const nodes = useVModel(props, "nodes", emits);
const displayNodes = computed(() => props.nodes.filter((node) => !node.isDeleted));

/**
 * @description: 获取节点展示文字
 * @param node
 * @return
 */
const getProcessNodeDetailStr = (node: approveProcessNode) => {
  return (node.approveModel === "3" ? "【或签】\n" : "") + node.nodeDetails.map((nodeDetail) => nodeDetail.name).join("，");
};
/**
 * @description: 编辑节点名称
 * @param node 节点
 * @return
 */
const editNodeName = (node: Record<string, any>) => {
  node.isInput = true;
};
// #region 节点操作
/**
 * @description: 添加节点
 * @param previousNode 前一个节点，为空说明是startNode
 * @param previousIndex 前一个节点的索引，为0说明是startNode
 * @return
 */
const addNode = (previousNode?: approveProcessNode, previousIndex?: number) => {
  const nextNode = previousNode ? nodes.value?.[(previousIndex as number) + 1] : nodes.value?.[0];
  const partialNewNode: Partial<approveProcessNode> = {
    approveNodeID: `temp_${common.guid()}`,
    approveNodeName: "审批人",
    approveModel: "1",
    // 默认使用NextNode.ID，如果NetNode是endNode则置为空串
    nextNodeID: nextNode?.approveNodeID ?? "",
    approveTimeLimit: 0,
    nodeDetails: []
  };
  const newNode = partialNewNode as approveProcessNode;
  // nextNode为空说明是endNode
  if (nextNode) {
    previousNode ? nodes.value.splice((previousIndex as number) + 1, 0, newNode) : nodes.value.unshift(newNode);
  } else {
    nodes.value.push(newNode);
  }
  // 添加完毕，修改previousNode的nextNodeID为新添加的node
  previousNode && (previousNode.nextNodeID = newNode.approveNodeID);
};
const modifyNode = ref<approveProcessNode>({} as approveProcessNode);
/**
 * @description: 删除节点
 * @param node 节点
 * @param index 节点索引
 * @return
 */
const deleteNode = (nodeID: string, index: number) => {
  nodeID.includes("temp_") ? nodes.value.splice(index, 1) : (nodes.value[index].isDeleted = true);
  const previousNode = nodes.value?.[index - 1];
  const nextNode = nodes.value?.[index + 1];
  previousNode && (previousNode.nextNodeID = nextNode?.approveNodeID ?? "");
};
const cloneModifyNode = ref<approveProcessNode>({} as approveProcessNode);
/**
 * @description: 编辑节点
 * @param node 审批节点
 * @return
 */
const editNode = (node: approveProcessNode) => {
  dialogVisible.value = true;
  modifyNode.value = node;
  cloneModifyNode.value = common.clone(toRaw(modifyNode.value));
};
const editDialog = ref();
/**
 * @description: 保存节点
 * @return
 */
const saveNode = () => {
  Object.assign(modifyNode.value, cloneModifyNode.value);
  dialogVisible.value = false;
};

// #endregion

// #region 缩放逻辑
const scalePercent = ref(100);
const scaleNumber = computed(() => scalePercent.value / 100);
// #endregion
</script>

<style lang="scss">
.flow-diagram {
  height: 100%;
  .nodes {
    transform: scale(v-bind(scaleNumber));
    transform-origin: top;
  }
  .start-node,
  .end-node,
  .dynamic-node {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  // 圆点
  .start-node::after,
  .end-node::before {
    content: "";
    width: 10px;
    height: 10px;
    margin: auto;
    border: none;
    border-radius: 50%;
    background: #dbdcdc;
  }
  // 箭头
  .dynamic-node::before {
    position: relative;
    left: 6px;
    content: "";
    transform: translateX(-50%);
    border-style: solid;
    border-width: 8px 6px 4px;
    border-color: #cacaca transparent transparent;
  }
  // 新增按钮块
  .add-node-btn-box {
    width: 220px;
    height: 72px;
    position: relative;
    padding-top: 40px;
    margin: auto;
    display: flex;
    justify-content: center;
    .add-node-btn {
      width: 32px;
      height: 32px;
      border-radius: 15px;
      color: #ffffff;
      background-color: #3296fa;
      border-color: transparent;
      transition: transform 0.5s, -webkit-transform 0.5s;
      opacity: 1;
      margin: 0 auto;
    }
    .add-node-btn:hover {
      transform: scale(1.2);
      box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    }
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      margin: auto;
      width: 2px;
      height: 100%;
      background-color: #cacaca;
    }
  }
  // 动态生成节点卡片
  .node-box {
    display: flex;
    flex-direction: column;
    width: 220px;
    min-height: 82px;
    font-size: 12px;
    border-radius: 4px;
    text-align: left;
    cursor: pointer;
    overflow: hidden;
    box-sizing: border-box;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
    background: #ffffff;
    .header {
      height: 24px;
      line-height: 24px;
      color: #ffffff;
      position: relative;
    }
    .body {
      flex-grow: 1;
      height: calc(100% - 24px);
    }
  }
  .node-box .header .title {
    width: 190px;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    i {
      margin-top: 0;
    }
    .content {
      vertical-align: top;
    }
    .content:hover {
      border-bottom: 1px dashed #ffffff;
    }
    .edit-name-input {
      vertical-align: top;
      width: 80px;
    }
  }
  .node-box .header .actions {
    position: absolute;
    right: 0;
    top: 0;
    visibility: hidden;
    margin: 0 4px 0 0;
    color: #ffffff;
  }
  .node-box:hover .actions {
    visibility: visible;
  }
  // 审批人卡片
  .approver-box-node .header {
    background-color: #ff9431;
  }
  .approver-box-node:hover {
    box-shadow: 0 0 0 2px #ff9431, 0 0 5px 4px rgba(0, 0, 0, 0.2);
  }
}
</style>
