/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useNormalWorkingAppeal.ts
 * Author       : 张现忠
 * Date         : 2023-12-10 09:34
 * LastEditors  : 马超
 * LastEditTime : 2025-03-02 11:07
 * Description  : 常态工作过程控制-申诉相关api
 * CodeIterationRecord:
 */
import { useUtils } from "../../../../hooks/useUtils";
const { showAlert } = useUtils();
export const useNormalWorkingAppeal = () => {
  const { userStore } = useStore() as any;
  const getContent = (row: any) => {
    return `问题发生人${row.examineEmployee} 对记录人${row.qcEmployeeName}于${datetimeUtil.formatDate(
      row.examineDate,
      "yyyy-MM-dd"
    )}记录的${row.subjectName} 问题 提出申诉，申诉原因：${row.appealReason}。`;
  };
  return {
    /**
     * @description 提交审批
     * @param row 质控维护记录
     */
    async submitForApproval(row: any) {
      let param = {
        sourceID: row.hierarchicalQCMainID,
        departmentID: userStore.departmentID,
        addEmployeeID: userStore.employeeID,
        // 常态工作过程状态审批 查看配置：select * from AdministrationDictionary where SettingTypeCode='ProveCategory'
        proveCategory: "MG-077",
        content: getContent(row),
        reason: row.appealReason
      };
      await hierarchicalQCService.submitForApproval(param).then((res: any) => {
        if (!res.recordSaveFlag) {
          return;
        } else if (!res.approveSaveFlag) {
          showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
        } else {
          showMessage("success", "提交申诉成功");
        }
      });
    },
    /**
     * @description 停止未开始的审批
     * @param row 质控维护记录
     */
    async stopApproval(row: any) {
      let param = {
        hierarchicalMainID: row.hierarchicalQCMainID
      };
      await hierarchicalQCService.stopHierarchicalQCApproval(param).then((respData: any) => {
        // 提交审批之后返回结果
        if (respData) {
          showMessage("success", "停止申诉成功");
        }
      });
    }
  };
};
