<!--
 * FilePath     : \nursing-management-web\src\views\trainingManagement\components\trainingClassSelector.vue
 * Author       : 张现忠
 * Date         : 2024-10-14 11:03
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-26 15:42
 * Description  : 培训群组选择组件
 * CodeIterationRecord:
 -->
<template>
  <div class="training-class-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="trainingClassMainID"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="filterable ? `请输入关键字` : `请选择${label}`"
      :loading="loading"
      loading-text="加载中……"
      @change="change"
    >
      <el-option v-for="(item, index) in classOptions" :key="index" :label="item.label" :value="item.value">
        <slot name="option" :index="index" :label="item.label" :suffix="item.suffix">
          <span style="float: left">{{ item.label }}</span>
          <span style="float: right; color: #8492a6">{{ item.suffix }}</span>
        </slot>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
//#region 引入
import { useExposeSelectorEvent } from "@/components/selector/hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
//#endregion

//#region props参数
const props = defineProps({
  label: {
    type: String,
    default: "培训群组"
  },
  modelValue: {
    type: String
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 200
  }
});
//#endregion

//#region 变量定义
let classOptions = ref<Array<Record<any, any>>>([]);
const loading = ref(false);

//#endregion

//#region 业务逻辑
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
const trainingClassMainID = useVModel(props, "modelValue", emits);
// 初始化
onMounted(() => {
  initData();
});
/**
 * @description: 初始化获取下拉选项
 */
const initData = () => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  trainingClassService.getTrainClassOptions().then((classItemOptions: any) => (classOptions.value = classItemOptions));
};

/**
 * 选择数据异动时暴露事件
 * @param value 异动数据
 */
const change = async (value: string) => {
  exposeChange(value, emits);
  exposeSelect(value, classOptions.value, "value", false, emits);
};
//#endregion
</script>

<style lang="scss">
.training-class-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
