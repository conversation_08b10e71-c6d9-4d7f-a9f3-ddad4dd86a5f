/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useQcCommonMethod.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-08 11:02
 * LastEditors  : 郭鹏超
 * LastEditTime : 2025-06-12 11:51
 * Description  : 三级质控公共方法hooks
 * CodeIterationRecord:
 */
let { userStore } = useStore();
import type { dynamicFormData, formAttribute } from "zhytech-ui";
export const useQcCommonMethod = () => {
  /**
   * @description: 质控业务控制按钮权限
   * @param employeeID
   * @param role
   * @return
   */
  const getButtonAuthority = (employeeID: string | string[], role: number | undefined = 99) => {
    // 管理员显示
    let roles = userStore.roles as number[];
    if (roles.includes(role)) {
      return true;
    }
    if (typeof employeeID === "object") {
      return employeeID.includes(userStore.employeeID);
    }
    return userStore.employeeID === employeeID;
  };
  /**
   * @description:是否为管理员
   */
  const getIsAdministrators = () => {
    // 管理员显示
    let roles = userStore.roles as number[];
    if ((roles.filter((role) => [70, 99].includes(role)) ?? []).length > 0) {
      return true;
    }
    return false;
  };
  /**
   * @description: 删除质控维护记录
   * @param hierarchicalQCMainID
   * @param getTableData
   * @return
   */
  const deleteQcMain = async (hierarchicalQCMainID: string, getTableData: Function) => {
    if (!hierarchicalQCMainID) {
      return;
    }
    confirmBox("是否删除当前考核结果维护记录？", "删除考核结果维护记录", (flag: Boolean) => {
      if (flag) {
        let params = {
          mainID: hierarchicalQCMainID
        };
        hierarchicalQCService.deleteHierarchicalQCMain(params).then((res) => {
          if (res) {
            getTableData();
            showMessage("success", "删除成功");
          } else {
            showMessage("error", "删除失败");
          }
        });
      }
    });
  };

  /**
   * @description:节点式督导及追踪考核内容保存
   * @param saveView
   * @param qcFormDom
   * @param saveFlag
   * @param isSaveMethod
   * @param currentRecord
   * @return
   */
  const saveQcMainAndDetails = async (saveView: Record<string, any>, qcFormDom: any, saveFlag: boolean, isSaveMethod: Function) => {
    if (!(await checkRequireCondition(saveView, qcFormDom, saveFlag))) {
      return;
    }
    let params = {
      // 1.缺少字段，存储记录状态：暂存还是保存
      qcMain: {
        status: saveFlag ? "S" : "T",
        hierarchicalQCRecordID: saveView.hierarchicalQCRecordID,
        point: saveView.point,
        guidance: saveView.guidance,
        improvement: saveView.improvement,
        hierarchicalQCMainID: saveView.hierarchicalQCMainID,
        examineDate: saveView.qcDate
      },
      qcDetails: saveView.details,
      qcSubjectID: saveView?.hierarchicalQCSubjectID,
      fileList: saveView.templateFileList
    };
    let formDatas = common.convertObjectToFormData(params);
    await hierarchicalQCService.saveHierarchicalQCMainAndDetails(formDatas).then(async (respBool: any) => {
      if (respBool) {
        await isSaveMethod();
      }
    });
  };

  /**
   * @description:保存检核
   * @param saveView
   * @param qcFormDom
   * @return
   */
  const checkRequireCondition = async (saveView: Record<string, any>, qcFormDom: any, saveFlag: boolean) => {
    if ((saveView?.details ?? []).length === 0) {
      showMessage("warning", "没有数据变动，无法保存数据");
      return false;
    }
    // 暂存不检核必填
    if (!saveFlag) {
      return true;
    }
    if (!saveView.details.find((item: any) => item.value !== "-1")) {
      showMessage("warning", "全部选择不考评无法保存！");
      return false;
    }
    if (!saveFlag) {
      return true;
    }
    let checkFlag: boolean = true;
    await qcFormDom.validate((flag: boolean) => {
      checkFlag = flag;
    });
    return checkFlag;
  };
  /**
   * @description: 组装明细
   * @param saveView
   * @return
   */
  const setSaveDetails = (saveView: Record<string, any>) => {
    const { templateDetails } = saveView;
    if ((templateDetails?.length ?? 0) === 0) {
      saveView.details = [];
      return;
    }
    saveView.details = templateDetails.map((item: Record<string, any>) => {
      return {
        itemID: item.id,
        groupID: item?.groupID,
        parentID: item?.parentID,
        value: item?.value ? item.value?.score ?? JSON.stringify(item.value) : "",
        problem: item.value?.problem,
        brightSpot: item.value?.brightSpot,
        remark: item.value?.remark
      };
    });
  };
  // 总分
  let totalPoint: number = 0;
  /**
   * @description: 计算得分
   * @param saveView
   * @param formData
   * @return
   */
  const setSavePoint = (saveView: Record<string, any>, formData: dynamicFormData<formAttribute>) => {
    const { templateDetails } = saveView;
    saveView.point = 0;
    if ((templateDetails?.length ?? 0) === 0) {
      return;
    }
    // 计算得分
    let allPoint: number = getCurrentPoint(templateDetails);
    // 计算总分分母
    let notHaveItemIDArr: number[] = templateDetails
      .filter((item: Record<string, any>) => item.value?.score && item.value.score === "-1")
      .map((item: Record<string, any>) => item.id);
    totalPoint = 0;
    getTotalPoint(notHaveItemIDArr, formData.components);
    // 百分制换算 分数赋值
    saveView.point = allPoint ? ((allPoint / totalPoint) * 100).toFixed(1) : "0";
  };
  /**
   * @description: 计算得分
   * @param templateDetails
   * @param any
   * @return
   */
  const getCurrentPoint = (templateDetails: Record<string, any>[]) => {
    // 获取含有单项否决的分组GroupID集合 最外层含有单项否决 groupID为undefined
    const vetoGroupIDArr = Array.from(
      new Set(
        templateDetails
          .filter((detail: Record<string, any>) => detail?.value?.score === "-2")
          .map((detail: Record<string, any>) => detail.groupID)
      )
    );
    let point = templateDetails
      .filter((item: Record<string, any>) => item.value.score)
      .reduce((acc: number, item: Record<string, any>) => acc + Number(["-1", "-2"].includes(item.value.score) ? 0 : item.value.score), 0);
    // 整个表单无单项否决 正常计算
    if (vetoGroupIDArr.length === 0) {
      return point;
    }
    // 最外层有单项否决 整个返回0分
    if (vetoGroupIDArr.includes(undefined)) {
      return 0;
    }
    // 计算单项否决不需要计算的分数
    let vetoPoint: number = 0;
    // 整个表单有分组
    vetoGroupIDArr.forEach((groupID: any) => {
      vetoPoint += templateDetails
        .filter((item: Record<string, any>) => item.groupID === groupID && !["-1", "-2"].includes(item.value.score))
        .reduce(
          (acc: number, item: Record<string, any>) => acc + Number(["-1", "-2"].includes(item.value.score) ? 0 : item.value.score),
          0
        );
    });
    // 总分减去不需要计算的分数
    return point - vetoPoint;
  };
  /**
   * @description: 递归计算总分
   * @param totalPoint
   * @param notHaveItemIDArr
   * @param components
   * @param any
   * @return
   */
  const getTotalPoint = (notHaveItemIDArr: number[], components: Record<string, any>[]) => {
    if (!components || components.length === 0) {
      return;
    }
    components.forEach((item: Record<string, any>) => {
      if (item.type === "grade" && !notHaveItemIDArr.includes(item.id)) {
        totalPoint = totalPoint + (item.props?.maxScore ?? 0);
      } else if (item.type === "groupLayout" && item.children) {
        getTotalPoint(notHaveItemIDArr, item.children);
      }
    });
  };
  /**
   * @description: 获取质控评估不满分项内容
   * @param detailItem 不满分明细项
   * @param index 序号
   * @return { String } 不满分项内容
   */
  const getNotFullScoreContent = (detailItem: any, index: number): string => {
    let description = "";
    let orderIndex = index + "、";
    switch (detailItem.score) {
      case -1:
        description = "不考评";
        break;
      case -2:
        description = "单项否决";
        break;
      default:
        description = detailItem.score + "分";
    }
    return `${orderIndex}${detailItem.name} (${description})`;
  };
  /**
   * @description: 组装表单备注内容,结果呈现:1.测试 2.测试；
   * @param formDatas
   * @return
   */
  const assemblyRemarksByDetails = (formDatas: Record<string, any>) => {
    if (!formDatas) {
      return undefined;
    }
    let remarks = Object.entries(formDatas)
      .filter(([, value]) => value?.remark && value.remark.trim() !== "")
      .map((item, index) => `${index + 1}.${item[1].remark}`)
      .join(" ");
    return remarks === "" ? remarks : remarks + "；";
  };
  /**
   * @description: 是否为片区主任
   * @param userRoles
   * @return
   */
  const getIsAreaDirector = (userRoles?: number[]) => {
    const roles = userRoles ?? ((userStore?.roles ?? []) as number[]);
    return roles.includes(60);
  };
  return {
    getButtonAuthority,
    getIsAdministrators,
    deleteQcMain,
    saveQcMainAndDetails,
    setSavePoint,
    setSaveDetails,
    getNotFullScoreContent,
    assemblyRemarksByDetails,
    getIsAreaDirector
  };
};
