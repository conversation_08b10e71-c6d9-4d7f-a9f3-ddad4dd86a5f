/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\hierarchicalQCSubjectView.ts
 * Author       : 郭鹏超
 * Date         : 2023-08-28 17:37
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-10-31 16:50
 * Description  :
 * CodeIterationRecord:
 */

let { userStore } = useStore();
/**
 * 主题新增修改View
 */
export class subjectClass {
  hierarchicalQCSubjectID?: string;
  hierarchicalQCFormLevel?: any;
  hierarchicalQCFormID?: any;
  formName?: any;
  templateCode: string;
  yearMonth?: any;
  startDate?: any;
  endDate?: any;
  qcFormList?: qcFormListView[];
  copyFlag?: boolean;
  formType?: any;
  formTypeDisabled?: boolean = false;
  implementationStartDate?: string;
  departmentID?: number;
  minPassingScore?: number;
  qcType: "nodeQCFormType" | "normalWorkingFormType" | "visitsFormType" | "specialFormType" | undefined;
  constructor() {
    this.qcFormList = [];
    this.copyFlag = false;
    this.templateCode = "";
    this.departmentID = userStore.departmentID;
  }
  reset() {
    this.hierarchicalQCSubjectID = undefined;
    this.hierarchicalQCFormLevel = undefined;
    this.hierarchicalQCFormID = undefined;
    this.formName = undefined;
    this.templateCode = "";
    this.yearMonth = datetimeUtil.getNowDate("yyyy-MM");
    this.startDate = "";
    this.endDate = "";
    this.qcFormList = [];
    this.copyFlag = false;
    this.formType = undefined;
    this.formTypeDisabled = false;
  }
}
interface qcFormListView {
  hierarchicalQCFormID: Number;
  formName: String;
}

/**
 * 指派新增修改View
 */
export class subjectAssignClass {
  hierarchicalQCSubjectID?: string;
  assignTableData: AssignTableItem[];
  constructor() {
    this.hierarchicalQCSubjectID = undefined;
    this.assignTableData = [
      {
        departmentViews: [],
        verifierEmployee: [],
        hierarchicalQCEmploy: []
      }
    ];
  }
  reset() {
    this.assignTableData = [
      {
        departmentViews: [],
        verifierEmployee: [],
        hierarchicalQCEmploy: []
      }
    ];
  }
}
/**
 * 指派表格View
 */
interface AssignTableItem {
  departmentViews: AssignDepartmentView[];
  verifierEmployee: AssignEmployeeView[];
  hierarchicalQCEmploy: AssignEmployeeView[];
}
/**
 * 考核病区View
 */
interface AssignDepartmentView {
  departmentCode: String;
  departmentID: Number;
  departmentContent: String;
  checkFlag: Boolean;
}
/**
 * 考核人员View
 */
interface AssignEmployeeView {
  employeeID: String;
  employeeName: String;
}
