/*
 * FilePath     : \src\utils\convertUtil.ts
 * Author       : 苏军志
 * Date         : 2023-08-10 19:51
 * LastEditors  : 苏军志
 * LastEditTime : 2023-08-30 15:20
 * Description  : 单位转换
 * CodeIterationRecord:
 */
const convert = (value: string) => {
  // 如果是auto则直接返回
  if (value == "auto") {
    return "auto";
  }
  if (value.indexOf("%") != -1) {
    return value;
  }
  value = value.replace("px", "");
  if (Number(value) == 0) {
    return "auto";
  }
  return value;
};
export default { convert };
