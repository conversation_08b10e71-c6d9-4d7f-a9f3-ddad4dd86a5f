/*
 * FilePath     : /src/views/annualPlan/hooks/useAnchor.ts
 * Author       : 杨欣欣
 * Date         : 2025-07-03 15:40
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:52
 * Description  : 锚点通用组合式函数
 * CodeIterationRecord:
 */

import type { anchorType } from "../types/anchorType";

/**
 * 年度计划锚点通用组合式函数
 * @description 提供锚点相关的通用逻辑，包括容器引用、锚点数据计算等
 */
export function useAnchor() {
  // 左侧内容容器引用
  const leftWrapper = ref<HTMLElement | undefined>(undefined);

  // 类型字典数据
  const typeList = ref<Record<string, any>[]>([]);

  /**
   * 获取类型字典数据
   * @param typeIds 类型ID数组
   */
  const getTypeList = async (typeIds: number[] | undefined) => {
    if (!typeIds || typeIds.length === 0) {
      return;
    }

    const params = {
      typeIds
    };
    typeList.value = await annualPlanSettingService.getTypeList(params);
  };

  /**
   * 获取年度计划分类名称
   * @param typeId 年度计划分类ID
   * @returns 年度计划分类名称
   */
  const getTypeContent = (typeId: number) => {
    const type = typeList.value.find((type) => type.annualPlanTypeID === typeId);
    return type ? type.annualPlanTypeContent : "";
  };

  /**
   * 创建锚点数据
   * @param planTypes 计划类型数组
   * @returns 锚点数据数组
   */
  const createAnchorData = (planTypes: Array<{ typeId: number }> | undefined): anchorType[] | undefined => {
    return planTypes?.map<anchorType>((planType) => ({
      id: `type-${planType.typeId}`,
      title: getTypeContent(planType.typeId)
    }));
  };

  return {
    leftWrapper,
    typeList,
    getTypeList,
    getTypeContent,
    createAnchorData
  };
}
