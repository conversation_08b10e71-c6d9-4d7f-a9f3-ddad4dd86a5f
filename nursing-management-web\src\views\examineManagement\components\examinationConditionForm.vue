<!--
 * relative     : \nursing-management-web\src\views\examineManagement\components\examinationConditionForm.vue
 * Author       : 张现忠
 * Date         : 2025-03-10 16:11
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-19 14:57
 * Description  : 考核试卷-组卷规则条件表单
 * CodeIterationRecord:
 -->
<template>
  <el-form ref="conditionFormRef" :model="editFormData" label-width="auto" :rules="rules" class="examination-condition-form">
    <el-form-item label="规则名称:" prop="conditionName">
      <el-input
        class="form-input"
        v-model="editFormData.conditionName"
        placeholder="请输入条件名称"
        :disabled="disableNameInput"
      ></el-input>
    </el-form-item>
    <el-form-item label="题型规则:" class="examine-condition-wrapper" prop="countAndScoreConditions">
      <examine-condition
        ref="tableConditionFilterRef"
        v-if="showExamineConditionFlag"
        :defaultValue="editFormData.countAndScoreConditions as any"
        @questionBankChange="questionBankChange"
      ></examine-condition>
    </el-form-item>
    <el-form-item v-if="showExamineConditionFlag" label="难度等级：" prop="difficultyLevelConditions" class="difficulty-level-wrapper">
      <zhy-dynamic-filter
        v-if="selectComponentDict['difficultyLevel']"
        :ref="(el:any) => setDynamicFilterRef(el, 'difficultyLevel')"
        :items="selectComponentDict['difficultyLevel'].items"
        :conditionTypes="selectComponentDict['difficultyLevel'].conditionTypes"
        :conditionProps="selectComponentDict['difficultyLevel'].conditionProps"
        :defaultValue="editFormData.difficultyLevelConditions"
        :allowMultiLevel="false"
        showStyle
      ></zhy-dynamic-filter>
    </el-form-item>
    <el-form-item label="必选题：">
      <el-checkbox v-model="showRequiredChoice"></el-checkbox>
    </el-form-item>
    <el-form-item prop="requiredChoiceConditions" v-if="showRequiredChoice">
      <zhy-dynamic-filter
        v-if="selectComponentDict['requiredChoice']"
        :ref="(el:any) => setDynamicFilterRef(el, 'requiredChoice')"
        showStyle
        :items="selectComponentDict['requiredChoice'].items"
        :conditionTypes="selectComponentDict['requiredChoice'].conditionTypes"
        :conditionProps="selectComponentDict['requiredChoice'].conditionProps"
        :defaultValue="editFormData.requiredChoiceConditions"
        :allowMultiLevel="false"
      ></zhy-dynamic-filter>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { showMessage } from "@/utils/message";
type componentType = "difficultyLevel" | "requiredChoice";
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },
  conditionName: {
    type: String,
    default: ""
  }
});
defineEmits(["update:modelValue"]);
// #region 变量定义
const showRequiredChoice = ref(false);
const selectedQuestionBankIDs = ref<string[]>([]);
const conditionFormRef = ref();
const editFormData = ref<{
  conditionName: string;
  examinationConditionRecordID?: string;
  requiredChoiceConditions: Record<string, any>[];
  difficultyLevelConditions: Record<string, any>[];
  countAndScoreConditions: Record<string, any>[];
  filterConditions: Record<string, any>[];
  score: number;
}>({ score: 0 } as any);

const selectComponentDict = reactive<Record<componentType, Record<string, any>>>({} as any);
const dynamicFilterRef = ref<Record<componentType, any>>({ difficultyLevel: undefined, requiredChoice: undefined } as any);
const tableConditionFilterRef = ref();
const rules = ref({
  conditionName: [{ required: true, message: "请输入规则名称", trigger: "blur" }],
  countAndScoreConditions: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        const filterRules = tableConditionFilterRef.value.getFilterData();
        if (!filterRules || filterRules.length === 0) {
          callback("请选择题型规则");
          return;
        }
        callback();
      }
    }
  ],
  difficultyLevelConditions: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        const { message } = dynamicFilterRef.value["difficultyLevel"].getFilterData(false);
        if (message) {
          callback("请输入题目难度等级");
          return;
        }
        callback();
      }
    }
  ],
  requiredChoiceConditions: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!dynamicFilterRef.value["requiredChoice"]) {
          callback();
          return;
        }
        const { message } = dynamicFilterRef.value["requiredChoice"].getFilterData(false);
        if (message) {
          callback("请输入题目必选题");
          return;
        }
        callback();
      }
    }
  ]
});
const showExamineConditionFlag = ref(false);
const disableNameInput = ref(false);
// #endregion
onBeforeMount(() => {
  getConditionSettings("difficultyLevel", []);
});
onMounted(async () => {
  if (props.modelValue) {
    await getExaminationConditionEditView();
  }
  if (props.conditionName) {
    editFormData.value.conditionName = props.conditionName;
    disableNameInput.value = true;
  }
  showExamineConditionFlag.value = true;
});
watch(
  () => showRequiredChoice.value,
  (newValue) => {
    newValue && getConditionSettings("requiredChoice", selectedQuestionBankIDs.value);
  }
);
// #region 组卷规则条件选择器Setting
/**
 * 条件类型映射字典
 */
let transferTypeDict: Record<string, string> = {
  difficultyLevel: "CompositionPaperByDifficultyLevel",
  requiredChoice: "CompositionPaperByRequiredChoice"
};

/**
 * @description 获取条件选择器组件的配置信息
 * @param type - 条件类型：'difficultyLevel'(难度等级) 或 'requiredChoice'(必选题)
 * @param questionBankIDs - 题库ID数组
 */
const getConditionSettings = (type: "difficultyLevel" | "requiredChoice", questionBankIDs: string[]) => {
  let params = {
    type: transferTypeDict[type],
    filterIDs: questionBankIDs.join(",")
  };
  conditionService.getConditionSelectComponent(params).then((res: any) => {
    if (res) {
      selectComponentDict![type] = res;
    }
  });
};

/**
 * @description 获取组卷规则明细视图数据
 */
const getExaminationConditionEditView = async () => {
  await conditionService.getExaminationConditionEditView({ examinationConditionRecordID: props.modelValue }).then((respData: any) => {
    if (respData) {
      editFormData.value.examinationConditionRecordID = respData.examinationConditionRecordID;
      editFormData.value.conditionName = respData.conditionName;
      editFormData.value.countAndScoreConditions = respData.countAndScoreConditions;
      editFormData.value.requiredChoiceConditions = transformValue(respData.requiredChoiceConditions, false);
      editFormData.value.difficultyLevelConditions = transformValue(respData.difficultyLevelConditions, false);
      if (respData.requiredChoiceConditions && respData.requiredChoiceConditions.length > 0) {
        showRequiredChoice.value = true;
      }
    }
  });
};
/**
 * @description 题库选择变更处理函数
 * @param questionBankIDs - 选中的题库ID数组
 */
const questionBankChange = (questionBankIDs: string[]) => {
  selectedQuestionBankIDs.value = questionBankIDs;
  showRequiredChoice.value && getConditionSettings("requiredChoice", questionBankIDs);
};

/**
 * @description 设置动态过滤器组件引用
 * @param el - 组件实例
 * @param type - 组件类型：'difficultyLevel'(难度等级) 或 'requiredChoice'(必选题)
 */
const setDynamicFilterRef = (el: any, type: componentType) => {
  if (el) {
    dynamicFilterRef.value[type] = el;
  } else {
    delete dynamicFilterRef.value[type];
  }
};

/**
 * @description 检查必选题条件是否有重复选项
 * @param requiredChoiceConditions - 必选题条件数组
 * @returns 是否存在重复选项
 */
const checkRequiredChoice = (requiredChoiceConditions: Record<string, any>[]) => {
  if (!requiredChoiceConditions?.length) {
    return false;
  }
  const valueSet = new Set<string>();
  let isDuplicate = false;
  for (const item of requiredChoiceConditions) {
    if (valueSet.has(item.value)) {
      isDuplicate = true;
      break;
    } else {
      valueSet.add(item.value);
    }
  }
  return isDuplicate;
};
// #endregion

// #region 从组件中获取规则条件数据
/**
 * @description 获取所有过滤条件数据
 * @returns 包含动态过滤器和表格条件的完整过滤条件数组
 */
const getFilterData = (): Record<string, any>[] => {
  let zhyDynamicFilterConditions = getFilterDataFromZhyDynamicFilter();
  let tableConditions = tableConditionFilterRef.value.getFilterData();
  return [...zhyDynamicFilterConditions, ...tableConditions];
};

/**
 * @description 从动态过滤器组件中获取过滤条件数据
 * @returns 动态过滤器的过滤条件数组
 */
const getFilterDataFromZhyDynamicFilter = () => {
  let filterConditions = [];
  for (let key in dynamicFilterRef.value) {
    let filterCompRef = dynamicFilterRef.value[key as componentType];
    if (!filterCompRef) {
      continue;
    }
    const filterData = filterCompRef.getFilterData();
    if (filterData.message) {
      continue;
    }
    transformValue(filterData.filterConditions, true);
    let filterCondition = {
      groupType: transferTypeDict[key],
      conditions: filterData.filterConditions,
      conditionContent: filterData.filterConditionContent,
      conditionExpression: filterData.filterConditionExpression
    };
    filterConditions.push(filterCondition);
  }

  return filterConditions;
};
/**
 * @description: 按照条件转换value的值
 * @param conditions 选择的明细条件
 * @param stringify true:array 转 string / false:string 转 array
 * @return
 */
function transformValue(conditions: any, stringify: boolean) {
  for (const condition of conditions) {
    if (condition.value && ((typeof condition.value === "string" && condition.value.length) || Array.isArray(condition.value))) {
      // 更新顶层的 value
      condition.value = stringify ? JSON.stringify(condition.value) : JSON.parse(condition.value);
    }
    if (condition.children && condition.children.length > 0) {
      // 递归调用以处理子条件
      transformValue(condition.children, stringify);
    }
  }
  return conditions;
}
// #endregion

/**
 * @description 验证表单数据
 * @returns 验证是否通过
 */
const validate = async () => {
  let { validateRule } = useForm();
  if (!(await validateRule(conditionFormRef.value))) {
    return false;
  }
  let filterData = getFilterData();
  editFormData.value.filterConditions = filterData;
  let totalScore = tableConditionFilterRef.value.getTotalScore();
  editFormData.value.score = totalScore;
  if (checkRequiredChoice(editFormData.value.requiredChoiceConditions)) {
    showMessage("warning", "必选题选项不能重复，请重新选择");
    return false;
  }
  return true;
};

/**
 * @description 保存组卷规则
 * @returns 保存成功返回[规则ID, 规则总分]，失败返回false
 */
const save = async () => {
  if (!(await validate())) {
    return [undefined, undefined];
  }
  if (!editFormData.value) {
    return [undefined, undefined];
  }
  let params = editFormData.value;
  params.examinationConditionRecordID = props.modelValue;

  const respData = await conditionService.saveExaminationConditionRecord(params);
  if (respData) {
    // 返回规则ID 以及规则总分
    return [respData, editFormData.value.score];
  }
  showMessage("error", "保存组卷规则失败。");
  return [undefined, undefined];
};

defineExpose({
  save,
  validate
});
</script>

<style lang="scss">
.examination-condition-form {
  .form-input {
    width: 50%;
    min-width: 200px;
  }
  .examine-condition-wrapper {
    max-height: 400px;
  }
  .el-form-item {
    margin-top: 0;
    & ~ .el-form-item {
      margin-top: 15px;
    }
    &.difficulty-level-wrapper {
      margin-top: 8px;
    }
  }
}
</style>
