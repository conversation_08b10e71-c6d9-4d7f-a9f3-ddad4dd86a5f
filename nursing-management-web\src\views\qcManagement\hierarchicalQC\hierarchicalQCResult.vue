<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hierarchicalQCResult.vue
 * Author       : 张现忠
 * Date         : 2023-12-14 17:52
 * LastEditors  : 马超
 * LastEditTime : 2025-06-23 10:03
 * Description  : 质控考核页面
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="hierarchical-qc-result" headerHeight="auto" :drawerOptions="drawerOptions">
    <template #header>
      <label>年月:</label>
      <el-date-picker
        type="monthrange"
        range-separator="至"
        format="YYYY-MM"
        value-format="YYYY-MM"
        v-model="subjectSearch.yearMonthRange"
        class="header-yearMonth-range"
      >
      </el-date-picker>
      <label>维护组织:</label>
      <department-switch-cascader @select="pageOptions.changeFormType(subjectSearch)" v-model="subjectSearch.departmentID">
      </department-switch-cascader>
      <label>类别:</label>
      <form-type-selector label="" :width="150" v-model="subjectSearch.formType" :qcType="subjectSearch.qcType"></form-type-selector>
      <label>主题:</label>
      <el-select clearable v-model="subjectSearch.hierarchicalQCSubjectID" style="width: 180px">
        <el-option v-for="item in pageOption.subjectOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <label>被考核部门:</label>
      <el-select clearable v-model="recordSearch.qcDepartmentID" style="width: 120px">
        <el-option v-for="item in pageOption.qcDepartmentOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <label>考核人:</label>
      <el-select clearable v-model="recordSearch.employeeID" style="width: 120px">
        <el-option v-for="item in pageOption.qcEmployeeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </template>
    <div :class="['content-record', { 'content-show-record': !showMainFlag }, { 'content-show-main': showMainFlag }]">
      <el-table height="100%" :data="recordTableData" stripe border @row-click="recordRowClick">
        <el-table-column prop="formName" label="质控主题" :min-width="convertPX(200)" align="left"></el-table-column>
        <el-table-column prop="examineEmployee" label="考核人" :width="convertPX(140)" align="left"></el-table-column>
        <el-table-column prop="examineObject" label="被考核部门" :width="convertPX(200)" align="left"></el-table-column>
        <el-table-column prop="auditNurse" label="审核人" :width="convertPX(140)" align="left"></el-table-column>
        <el-table-column prop="examineNumbers" label="考核次数" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column prop="firstQCDateTime" label="首次考核日期" :width="convertPX(160)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.firstQCDateTime, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="首次分数" :width="convertPX(120)" align="center">
          <template #default="{ row }">
            <span :class="row.minPassingScore && row.firstPoint < row.minPassingScore ? 'red' : ''">{{ row.firstPoint }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="lastQCDateTime" label="末次考核日期" :width="convertPX(160)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.lastQCDateTime, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="末次分数" :width="convertPX(120)" align="center">
          <template #default="{ row }">
            <span :class="row.minPassingScore && row.lastPoint < row.minPassingScore ? 'red' : ''">{{ row.lastPoint }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="convertPX(100)" align="center">
          <template #default="scope">
            <el-tooltip content="新增">
              <i
                v-permission:B="1"
                v-visibilityHidden="getButtonAuthority(scope.row.examineEmployeeIDList)"
                class="iconfont icon-add"
                @click.stop="showQcDrawer(scope.row, true)"
              ></i>
            </el-tooltip>
            <el-tooltip content="删除">
              <i
                v-permission:B="4"
                v-visibilityHidden="getButtonAuthority(scope.row.examineEmployeeIDList)"
                class="iconfont icon-delete"
                @click.stop="deleteRecord(scope.row)"
              ></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <base-layout headerHeight="40px" class="content-main" v-if="showMainFlag">
      <template #header>
        <div class="main-title">
          <span>考核记录</span>
          <el-button v-if="!isShowReadButton" v-permission:B="1" class="add-button" @click="showQcDrawer(currentRecord, false)"
            >新增
          </el-button>
        </div>
      </template>
      <el-table height="100%" :data="mainTableData" stripe border>
        <el-table-column prop="number" label="考核次数" :width="convertPX(120)" align="center"></el-table-column>
        <el-table-column label="考核日期" :width="convertPX(180)" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.examineDate, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="examineEmployee" label="考核人" :width="convertPX(100)" align="left"> </el-table-column>
        <el-table-column label="分数" :width="convertPX(100)" align="center">
          <template #default="{ row }">
            <span :class="getMainTableClassForScore(row)">{{ row.point }}</span>
          </template>
        </el-table-column>
        <el-table-column label="不满分项" :min-width="convertPX(140)" align="left">
          <template #default="scope">
            <div v-for="(trackItem, index) in scope.row.trackDetails" :key="index">
              {{ getNotFullScoreContent(trackItem, index + 1) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="guidance" label="指导内容" :min-width="convertPX(140)" align="left"></el-table-column>
        <el-table-column prop="improvement" label="改进内容" :min-width="convertPX(140)" align="left"></el-table-column>
        <el-table-column prop="reader" label="阅读" :width="convertPX(100)" align="center"></el-table-column>
        <el-table-column
          prop="submitStatus"
          label="提交"
          :width="convertPX(140)"
          align="center"
          v-if="!pageOption.notShowApprovalContentFlag"
        ></el-table-column>
        <el-table-column label="操作" :width="convertPX(140)">
          <template #default="scope">
            <template v-if="isShowReadButton">
              <el-tooltip content="阅读">
                <i class="iconfont icon-preview" @click="showReadQcDrawer(scope.row)"></i>
              </el-tooltip>
            </template>
            <template v-else>
              <div v-if="hiddenButtonByRevoke(scope.row.auditStatus)"></div>
              <el-tooltip content="修改">
                <i v-permission:B="3" class="iconfont icon-edit" @click="showQcDrawer(scope.row, false)"></i>
              </el-tooltip>
              <template v-if="!pageOption.notShowApprovalContentFlag">
                <!-- <el-tooltip content="提交审批" v-if="scope.row.auditStatus == '0'">
                  <i
                    v-permission:B="3"
                    class="iconfont icon-save"
                    v-visibilityHidden="getButtonAuthority(scope.row.currExamineEmployeeID)"
                    @click.stop="submitApproval(scope.row, currentRecord!)"
                  ></i>
                </el-tooltip> -->
                <el-tooltip content="取消审批" v-if="scope.row.auditStatus == '1'">
                  <i
                    v-permission:B="3"
                    class="iconfont icon-stop"
                    v-visibilityHidden="getButtonAuthority(scope.row.currExamineEmployeeID)"
                    @click.stop="stopSubmit(scope.row)"
                  ></i>
                </el-tooltip>
                <el-tooltip content="撤销">
                  <i
                    v-permission:B="26"
                    v-visibilityHidden="
                      getButtonAuthority(scope.row.currExamineEmployeeID) &&
                      showRevokeButton(scope.row.auditStatus, scope.row.currExamineEmployeeID)
                    "
                    v-if="showRevokeFlag"
                    @click="startRevoke()"
                    class="iconfont"
                  ></i>
                </el-tooltip>
              </template>
            </template>
            <el-tooltip content="删除">
              <i
                v-permission:B="4"
                v-visibilityHidden="getButtonAuthority(currentRecord?.examineEmployeeIDList ?? [])"
                class="iconfont icon-delete"
                @click="deleteMain(scope.row)"
              ></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </base-layout>
    <template #drawerContent>
      <revoke-approval
        v-if="drawerOptions.drawerName === 'revoke'"
        v-model:drawer-options="drawerOptions"
        :revokeFormData="revokeFormData!"
        :id="currentRecord!.hierarchicalQCRecordID"
        :isSource="true"
        @refreshData="getRecord"
      />
      <qcForm
        v-if="drawerOptions.drawerName === 'qcForm'"
        v-model="saveView"
        ref="qcFormDom"
        :formData="formData"
        :disabledExamineDateStart="disabledExamineDateStart"
        :disabledExamineDateEnd="disabledExamineDateEnd"
        @getDetails="getDetails"
      ></qcForm>
    </template>
    <template #drawerOtherFooter v-if="drawerOptions.drawerName !== 'revoke'">
      <div>
        <strong>标准得分：{{ saveView.point }}</strong>
        <el-button @click="drawerOptions.showDrawer = false">取消</el-button>
        <span v-if="!isShowReadButton">
          <el-button
            v-visibilityHidden="getButtonAuthority(currentRecord?.examineEmployeeIDList ?? [])"
            v-permission:B="2"
            class="print-button"
            v-if="disableSelection === 'auto'"
            @click="saveMainAndDetails(false)"
            >暂存</el-button
          >
          <el-button
            v-visibilityHidden="getButtonAuthority(currentRecord?.examineEmployeeIDList ?? [])"
            v-permission:B="2"
            type="primary"
            v-if="disableSelection === 'auto'"
            @click="saveMainAndDetails(true)"
            >保存</el-button
          >
        </span>
        <span v-else>
          <el-button type="primary" @click="updateReadStatus(currentRecord)">阅读</el-button>
        </span>
      </div>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
const convertPX: any = inject("convertPX");
const route = useRoute();
import formTypeSelector from "./components/qcFormTypeSelector.vue";
import revokeApproval from "@/views/approveManagement/approveRecord/components/revokeApproval.vue";
import qcForm from "./components/qcForm.vue";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
import type { recordRow } from "./types/hierarchicalQCResultView";

const { getButtonAuthority, deleteQcMain, saveQcMainAndDetails, setSavePoint, setSaveDetails, getNotFullScoreContent } =
  useQcCommonMethod();

import qcOptions from "./setting/index";
import hierarchicalQCSubject from "./setting/hierarchicalQCResult";

let recordTableData = ref<recordRow[]>([]);
let copyRecordTableData = ref<recordRow[]>([]);
const disabledExamineDateStart = ref<string>();
const disabledExamineDateEnd = ref<string>();
const { showRevokeButton } = useButton();
const recordMinPassingScore = ref<number>();
onMounted(async () => {
  await getRecord();
  pageOption.value.getSearchOptions(recordTableData.value);
});

// 差异配置从初始化
const pageOptions = ref<qcOptions>(
  new qcOptions("hierarchicalQCResult", { routerQcLevel: route.params.qcLevel as string, qcType: "nodeQCFormType" })
);
const pageOption = ref<hierarchicalQCSubject>(pageOptions.value.pageOption as hierarchicalQCSubject);

import type { subjectSearchView } from "./types/subjectSearchView";
import type { recordSearchView } from "./types/recordSearchView";
// 筛选内容初始化
const subjectSearch = ref<subjectSearchView>({
  yearMonthRange: [datetimeUtil.getNowDate("yyyy-MM"), datetimeUtil.getNowDate("yyyy-MM")],
  qcType: pageOptions.value.qcType,
  qcLevel: pageOption.value.qcLevel,
  departmentID: pageOption.value.departmentID,
  formType: pageOption.value.formType,
  hierarchicalQCSubjectID: undefined
});
const recordSearch = ref<recordSearchView>({
  qcDepartmentID: undefined,
  employeeID: undefined
});
watch(subjectSearch.value, async () => {
  await getRecord();
  pageOption.value.getSearchOptions(recordTableData.value);
});
watch(recordSearch.value, () => getRecord());
/**
 * @description: 获取主记录数据
 * @return
 */
const getRecord = async () => {
  showMainFlag.value = false;
  recordTableData.value = [];
  let params = {
    ...subjectSearch.value,
    startYearMonth: subjectSearch.value.yearMonthRange![0],
    endYearMonth: subjectSearch.value.yearMonthRange![1],
    ...recordSearch.value
  };
  await hierarchicalQCService.getHierarchicalQCRecordList(params).then((res: any) => {
    if (res) {
      recordTableData.value = res;
      copyRecordTableData.value = res;
    }
  });
};
let currentRecord = ref<recordRow>();
let showMainFlag = ref<boolean>(false);
/**
 * @description: 行点击事件
 * @param row
 * @return
 */
const recordRowClick = async (row: recordRow) => {
  currentRecord.value = row;
  showMainFlag.value = !showMainFlag.value;
  recordMinPassingScore.value = row.minPassingScore;
  if (showMainFlag.value) {
    currentRecord.value = row;
    recordTableData.value = [row];
    await getMain();
  } else {
    recordTableData.value = copyRecordTableData.value;
    currentRecord.value = undefined;
  }
};

import type { mainRow } from "./types/hierarchicalQCResultView";
let mainTableData = ref<mainRow[]>([]);
/**
 * @description: 获取维护记录数据
 * @param recordID
 * @return
 */
const getMain = async () => {
  if (!currentRecord.value?.hierarchicalQCRecordID) {
    // showAlert("warning", "获取考核记录失败，缺少记录参数");
    return;
  }
  getIsShowReadButton(currentRecord.value.hierarchicalQCRecordID);
  let param = {
    recordID: currentRecord.value.hierarchicalQCRecordID
  };
  mainTableData.value = [];
  await hierarchicalQCService.getHierarchicalQCMainList(param).then((res: any) => {
    if (res) {
      mainTableData.value = res;
    }
  });
};

import { saveClass } from "./types/hierarchicalSaveView";
const saveView = reactive(new saveClass());
let currentMain = ref<mainRow>();
const disableSelection = ref("auto");
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false
});
/**
 * @description: 质控弹窗初始化
 * @param row
 * @return
 */
const showQcDrawer = async (row?: any, recordAddFlag?: boolean) => {
  drawerOptions.value.drawerName = "qcForm";
  drawerOptions.value.drawerTitle = `${row && !recordAddFlag ? "修改" : "新增"}考核（被质控部门：${
    currentRecord.value?.examineObject || ""
  }）`;
  drawerOptions.value.showDrawer = true;
  recordAddFlag && (currentRecord.value = row);
  currentMain.value = undefined;
  row && !recordAddFlag && (currentMain.value = row);
  saveView.guidance = row?.guidance ?? "";
  saveView.improvement = row?.improvement ?? "";
  disableSelection.value = row?.auditStatus === "2" ? "none" : "auto";
  saveView.qcDate = row?.examineDate ?? datetimeUtil.getNowDate("yyyy-MM-dd");
  saveView.templateCode = currentRecord.value?.templateCode ?? "";
  saveView.hierarchicalQCSubjectID = currentRecord.value?.hierarchicalQCSubjectID ?? "";
  saveView.hierarchicalQCRecordID = currentRecord.value?.hierarchicalQCRecordID ?? "";
  saveView.hierarchicalQCMainID = currentMain.value?.hierarchicalQCMainID ?? "";
  saveView.startDate = row?.startDate;
  saveView.endDate = row?.endDate;
  await getFormTemplate(row?.hierarchicalQCMainID);
  if (pageOption.value.restrictExamineDateFlag) {
    disabledExamineDateStart.value = row?.startDate ?? currentRecord.value?.startDate;
    disabledExamineDateEnd.value = row?.endDate;
  }
};
const showReadQcDrawer = async (row?: any) => {
  drawerOptions.value.drawerName = "qcForm";
  drawerOptions.value.drawerTitle = `阅读考核（被质控部门：${currentRecord.value?.examineObject || ""}）`;
  drawerOptions.value.showDrawer = true;
  currentRecord.value = row;
  currentMain.value = undefined;
  row && (currentMain.value = row);
  saveView.guidance = row?.guidance ?? "";
  saveView.improvement = row?.improvement ?? "";
  saveView.qcDate = row?.examineDate ?? datetimeUtil.getNowDate("yyyy-MM-dd");
  saveView.templateCode = currentRecord.value?.templateCode ?? "";
  saveView.hierarchicalQCSubjectID = currentRecord.value?.hierarchicalQCSubjectID ?? "";
  saveView.hierarchicalQCRecordID = currentRecord.value?.hierarchicalQCRecordID ?? "";
  saveView.hierarchicalQCMainID = currentMain.value?.hierarchicalQCMainID ?? "";
  saveView.startDate = row?.startDate;
  saveView.endDate = row?.endDate;
  await getFormTemplate(row?.hierarchicalQCMainID);
  if (pageOption.value.restrictExamineDateFlag) {
    disabledExamineDateStart.value = row?.startDate ?? currentRecord.value?.startDate;
    disabledExamineDateEnd.value = row?.endDate;
  }
};
const qcFormDom = ref<any>();
/**
 * @description: 保存方法
 * @param saveFlag true:保存 false：暂存
 * @return
 */
const saveMainAndDetails = async (saveFlag: boolean) => {
  setSaveDetails(saveView);
  await saveQcMainAndDetails(saveView, qcFormDom.value.rendererForm, saveFlag, isSaveMethod);
};
/**
 * @description: 保存之后执行
 */
const isSaveMethod = async () => {
  drawerOptions.value.showDrawer = false;
  if (showMainFlag.value) {
    await getMain();
    let lastMain = mainTableData.value[mainTableData.value.length - 1];
    if (currentRecord.value) {
      currentRecord.value.lastQCDateTime = lastMain?.examineDate;
      currentRecord.value.lastPoint = lastMain?.point;
      currentRecord.value.examineNumbers = mainTableData.value.length;
      // 获取最新添加的记录
      const latestRecord = mainTableData.value[mainTableData.value.length - 1];
      // 如果记录存在且状态为未提交（auditStatus为0），则自动提交审批
      if (latestRecord && latestRecord.auditStatus === "0") {
        await submitForApproval(latestRecord, currentRecord.value);
      }
    }
  } else {
    await getRecord();
  }
  showMessage("success", "保存成功");
};

/**
 * @description: 删除主记录
 * @param row
 * @return
 */
const deleteRecord = async (row: recordRow) => {
  if (!row?.hierarchicalQCRecordID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  confirmBox("是否删除考核结果主记录？", "删除考核结果主记录", (flag: Boolean) => {
    if (flag) {
      let params = {
        recordID: row.hierarchicalQCRecordID
      };
      hierarchicalQCService.deleteHierarchicalQCRecord(params).then((res: any) => {
        if (res) {
          getRecord();
          showMessage("success", "删除成功");
        } else {
          showMessage("error", "删除失败");
        }
      });
    }
  });
};
/**
 * @description: 删除维护记录
 * @param row
 * @return
 */
const deleteMain = async (row: mainRow) => {
  if (!row?.hierarchicalQCMainID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  // 审核后不能删除
  if (row.auditStatus == "2") {
    showMessage("warning", "审核后不能删除");
    return;
  }
  deleteQcMain(row.hierarchicalQCMainID, getMain);
};

import type { dynamicFormData, formAttribute } from "zhytech-ui";
import { useQcApproval } from "./hooks/useQcApproval";
const formData = ref<dynamicFormData<formAttribute>>();
/**
 * @description: 获取质控评估内容
 * @param careMainID
 * @return
 */
const getFormTemplate = async (careMainID: string | undefined = undefined) => {
  formData.value = undefined;
  let params = {
    templateCode: currentRecord.value?.templateCode,
    careMainID: careMainID
  };
  await hierarchicalQCService.getQCAssessView(params).then((res: any) => {
    if (res) {
      formData.value = res;
    }
  });
};

/**
 * @description: 获取质控内容点选数据
 * @param data
 * @param fileList 文件或图片
 * @return
 */
const getDetails = (data: Record<string, any>[], fileList: Record<string, any>[]) => {
  saveView.templateDetails = data;
  saveView.templateFileList = fileList;
  setSavePoint(saveView, formData.value!);
};
const { showRevokeFlag, hiddenButtonByRevoke } = useApproval(true);
const { submitForApproval, stopApproval } = useQcApproval();
import { useUtils } from "../../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 停止提交审批
 * @param row
 * @return
 */
const stopSubmit = (row: mainRow) => {
  showAlert("warning", "确定停止审批吗？").then(async (value: any) => {
    if (value !== "confirm") {
      return;
    }
    await stopApproval(row);
    await getMain();
  });
};

/**
 * @description: 提交审批
 * @param row
 * @param record
 * @return
 */
const submitApproval = (row: mainRow, record: recordRow) => {
  showAlert("warning", "确定提交审批吗？").then(async (value: any) => {
    if (value !== "confirm") {
      return;
    }
    await submitForApproval(row, record);
    await getMain();
  });
};
const revokeFormData = ref<Record<string, any>>();
/**
 * @description: 开启撤销弹窗
 */
const startRevoke = () => {
  revokeFormData.value = [
    {
      label: "质控主题",
      value: currentRecord.value!.formName
    }
  ];
  drawerOptions.value.drawerName = "revoke";
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 获取考核分数样式
 * @param row 行数据
 * @return0
 */
const getMainTableClassForScore = (row: Record<string, any>): string => {
  const passingScore = recordMinPassingScore.value ?? 100;
  return row.point < passingScore ? "red" : "";
};
const isShowReadButton = ref(false);
const getIsShowReadButton = (hierarchicalQCRecordID: string) => {
  hierarchicalQCService
    .getQcResultShowRead({
      recordID: hierarchicalQCRecordID
    })
    .then((res: any) => {
      if (res) {
        isShowReadButton.value = res;
      }
    });
};
// 修改阅读状态
const updateReadStatus = async (currentRecord: any) => {
  await hierarchicalQCService.updateQcResultReadStatus({
    mainID: currentRecord.hierarchicalQCMainID
  });
  drawerOptions.value.showDrawer = false;
  await getRecord();
};
</script>
<style lang="scss">
.hierarchical-qc-result {
  height: 100%;
  width: 100%;
  .base-header {
    @include flex-aline();
    .header-yearMonth-range {
      flex-grow: unset;
      width: 200px;
    }
  }
  .red {
    color: #ff0000;
  }
  .content-record {
    margin-top: 10px;
  }
  .content-show-record {
    height: calc(100% - 30px);
    margin-bottom: 5px;
  }
  .content-show-main {
    height: 80px;
  }
  .content-main {
    height: calc(100% - 115px);
    .base-header {
      margin: 0;
    }
    .main-title {
      width: 100%;
      height: 40px;
      line-height: 40px;
      @include flex-aline(row, space-between);
    }
    .add-button {
      float: right;
      margin-top: 8px;
    }
  }
  .el-tooltip__trigger {
    margin: 0 5px;
  }
  .textarea-display {
    margin: 5px 5px;
    .textarea-label {
      font-size: 22px;
      display: block;
      padding-bottom: 5px;
    }
  }
}
</style>
