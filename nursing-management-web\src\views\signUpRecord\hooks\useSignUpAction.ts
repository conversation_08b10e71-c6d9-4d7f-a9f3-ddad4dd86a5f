/*
 * FilePath     : \src\views\signUpRecord\hooks\useSignUpAction.ts
 * Author       : 张现忠
 * Date         : 2024-07-14 10:48
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-08-13 15:01
 * Description  : 报名记录数据和相关操作逻辑钩子
 * CodeIterationRecord:
 */

import { signUpRecordService } from "@/api/signUpRecordService";
import type { signUpRecordView } from "../types/signUpRecordView";

/**
 * @description 报名记录数据和相关操作逻辑
 */
export function useSignUpAction() {
  // 报名来源类型
  const sourceType = ref<string>("");
  const signUpList = ref<signUpRecordView[]>([]);
  const filterSignUpList = ref<signUpRecordView[]>([]);
  const sourceDataID = ref<string>("");
  /**
   * @description 获取报名记录列表数据
   * @param courseID - 课程ID
   * @returns {}
   */
  const getSignUpRecordList = async (sourceTypeValue?: string, sourceID?: string) => {
    let params = {
      sourceType: sourceTypeValue ?? sourceType.value,
      sourceID: sourceID ?? sourceDataID.value
    };
    signUpList.value = await signUpRecordService.getSignUpRecordList(params);
    filterSignUpList.value = signUpList.value;
  };

  /**
   * @description 添加报名记录
   * @param signUp - 待添加的报名记录
   * @returns
   */
  const saveSignUpRecord = async (signUp: signUpRecordView) => {
    let params = signUp;
    params.sourceType ??= sourceType.value;
    await signUpRecordService.saveSignUpRecord(params).then((respBool: any) => {
      if (!respBool) {
        showMessage("error", "保存失败！");
        return false;
      }
    });
    showMessage("success", "保存成功!");
    // 保存成功后刷新报名记录列表
    await getSignUpRecordList();
    return true;
  };

  /**
   * @description 删除报名记录
   * @param row - 待删除的报名记录
   * @return
   */
  const deleteSignUpRecord = (row: signUpRecordView) => {
    deleteConfirm("确定要删除么？", (confirmFlag: Boolean) => {
      if (!confirmFlag) {
        return;
      }
      signUpRecordService.deleteSignUpRecord({ signUpRecordID: row.signUpRecordID }).then((res: any) => {
        if (res) {
          showMessage("success", "删除成功!");
          getSignUpRecordList();
          return;
        }
        showMessage("error", "删除失败!");
      });
    });
  };

  // 返回从逻辑中获取的响应式数据和方法
  return {
    // 课程来源类型
    sourceType,
    // 课程来源ID
    sourceDataID,
    // 报名记录列表数据(查询后的初始数据)
    signUpList,
    // 报名记录列表数据过滤
    filterSignUpList,
    /**
     * @description 获取报名记录列表数据
     * @param courseIDValue - 课程ID
     * @returns Promise<void>
     */
    getSignUpRecordList,
    /**
     * @description 保存报名记录
     * @param signUp - 待保存的报名记录
     * @returns Promise<void>
     */
    saveSignUpRecord,
    /**
     * @description 删除报名记录
     * @param signUpRecordID - 待删除的报名记录的ID
     * @return
     */
    deleteSignUpRecord
  };
}
