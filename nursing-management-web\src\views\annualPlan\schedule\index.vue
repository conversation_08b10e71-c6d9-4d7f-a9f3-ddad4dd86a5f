<!--
 * FilePath     : \src\views\annualPlan\schedule\index.vue
 * Author       : 张现忠
 * Date         : 2023-12-27 14:54
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-11 11:53
 * Description  :年度计划执行
 * CodeIterationRecord:
 -->

<template>
  <div class="annual-plan-schedule">
    <base-layout headerHeight="auto" :drawerOptions="drawerOptions">
      <template #header>
        <!-- TODO: 待沟通后决定是否删除 -->
        <!-- <el-button type="primary" @click="addTask()" class="add-button" v-permission:B="1">加入计划任务</el-button> -->
        <el-table v-if="taskStatistics" :data="[taskStatistics]" border stripe class="schedule-data" @cell-click="getTasksByMonth">
          <template v-for="([processedCount, totalCount], index) in taskStatistics" :key="index">
            <el-table-column :label="`${index}月`" label-class-name="header-cell" align="center" :min-width="convertPX(120)">
              <template #default>
                <div :class="['column', { 'is-select': selectedMonth == index }]">
                  {{ processedCount + " / " + totalCount }}
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <el-radio-group v-if="filterTasks" v-model="selectType" @change="filterTasks">
          <el-radio label="全部" border />
          <el-radio v-for="(typeName, index) in typeNames" :key="index" :label="typeName" :value="typeName" border />
        </el-radio-group>
      </template>
      <template #default>
        <el-table :data="filterTasks" v-if="filterTasks" border height="100%">
          <el-table-column prop="scheduleDateTime" label="计划时间" align="center" :width="convertPX(150)">
            <template #default="{ row }">
              <span v-formatTime="{ value: row.scheduleDateTime, type: 'dateTime', format: 'yyyy-MM' }" />
            </template>
          </el-table-column>
          <el-table-column prop="taskContent" label="任务内容" />
          <el-table-column label="要求">
            <template #default="{ row }">
              <span v-html="row.requirement" />
            </template>
          </el-table-column>
          <el-table-column label="执行日期" :width="convertPX(150)" align="center">
            <template #default="{ row }">
              <span v-formatTime="{ value: row.performDateTime, type: 'dateTime', format: 'yyyy-MM-dd' }" />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" />
          <el-table-column label="操作" :width="convertPX(150)" fixed="right">
            <template #default="{ row }">
              <div class="btn-wrapper">
                <el-tooltip :content="`${row.statusCode === 0 ? '执行' : '修改'}任务`">
                  <i :class="['iconfont', `icon-${row.statusCode === 0 ? 'execute' : 'edit'}`]" @click="performTask(row)"></i>
                </el-tooltip>
                <el-tooltip content="查看来源">
                  <i class="iconfont icon-info" @click="showScheduleSource(row)"></i>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #drawerContent>
        <!-- TODO: 待沟通后决定是否删除 -->
        <!-- v-if="drawerOptions.drawerName == 'taskPerform'"  -->
        <el-form :model="performingTask" :rules="rules">
          <el-form-item label="执行时间：">
            <el-date-picker
              type="date"
              v-model="performingTask.performDateTime"
              :clearable="false"
              :disabled-date="getDisableDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="执行方案：" prop="statusCode">
            <el-radio-group v-model="performingTask.statusCode">
              <el-radio :value="1">完成</el-radio>
              <el-radio :value="2">延期</el-radio>
              <el-radio :value="3">放弃</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="延期原因：" v-if="performingTask.statusCode === 2" prop="delayReason">
            <el-radio-group v-model="performingTask.delayReason">
              <el-radio :label="reason.label" :value="Number(reason.value)" v-for="reason in delayReasons" :key="reason.value" />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注：">
            <el-input type="textarea" :rows="5" v-model="performingTask!.remark" />
          </el-form-item>
        </el-form>
        <!-- TODO：增加了季度、月度计划制定，此处应当不再有临时新增的需要了，先暂时屏蔽，后续沟通后决定去留 -->
        <!-- <base-layout v-else>
          <template #header>
            <label>预计执行日期：</label>
            <el-date-picker type="date" v-model="scheduleDate" :clearable="false" format="YYYY-MM" value-format="YYYY-MM-DD" />
          </template>
          <div class="delay-execute">
            <span>计划项目内容：</span>
            <el-input type="textarea" :rows="5" v-model="scheduleContent" class="delayContent"></el-input>
          </div>
        </base-layout> -->
      </template>
    </base-layout>
  </div>
</template>

<script setup lang="ts">
import { uniq } from "lodash-es";
import type { taskMainView } from "../types/annualScheduleMainView";
const year = usePlanTime().getPlanAnnual();
const route = useRoute();
const convertPX: any = inject("convertPX");

onMounted(() => {
  getTaskStatistics();
  // 选中到对应月份
  if (route.query?.month) {
    selectedMonth.value = route.query.month ? Number(route.query.month) : undefined;
    common.isRefValid(selectedMonth) && getAnnualTasks();
  }
});
//#region 统计信息
const selectedMonth = ref<number | undefined>();
const taskStatistics = ref<{
  [key: number]: [number, number];
}>({});
const getTaskStatistics = async () => {
  const params = {
    schedulePerformer: useStore().userStore.employeeID,
    scheduleYear: year
  };
  const scheduleStatistics = await annualScheduleService.getAnnualScheduleStatistics(params);
  taskStatistics.value = scheduleStatistics.reduce((previousValue, currentValue) => {
    previousValue[currentValue.month] = [currentValue.processedCount, currentValue.totalCount];
    return previousValue;
  }, {} as Record<number, [number, number]>);
};
//#endregion

//#region 任务列表
const monthTasks = ref<taskMainView[]>();
const filterTasks = computed(() => {
  if (selectType.value === "全部") {
    return monthTasks.value;
  }
  return monthTasks.value?.filter((task) => task.typeName === selectType.value);
});
const typeNames = ref<string[]>([]);
const selectType = ref<string>("全部");
/**
 * @description: 显示选择月份的计划
 * @param row 行
 * @param column 列
 * @return
 */
const getTasksByMonth = async (row: Record<string, any>, column: any) => {
  selectedMonth.value = column.no + 1;
  common.isRefValid(selectedMonth) && (await getAnnualTasks());
  typeNames.value = uniq(monthTasks.value?.map((monthTask) => monthTask.typeName));
};
/**
 * @description: 获取年度计划执行任务
 * @return
 */
const getAnnualTasks = async () => {
  const params = {
    scheduleYear: year,
    scheduleMonth: selectedMonth.value
  };
  monthTasks.value = await annualScheduleService.getAnnualScheduleMains(params);
};
/**
 * @description: 执行计划
 * @param row：执行的计划记录
 * @return
 */
const performTask = async (row: taskMainView) => {
  await getScheduleDelayPerformReason();
  performingTask.value = common.clone(row);
  isAdd.value = !performingTask.value.performDateTime;
  if (performingTask.value.statusCode === 0) {
    performingTask.value.performDateTime = datetimeUtil.getNow("yyyy-MM-dd");
  }
  // TODO：待沟通后决定是否删除
  // drawerOptions.value.drawerName = "taskPerform";
  drawerOptions.value.drawerTitle = `计划执行：${row.taskContent}`;
  if (row.statusCode !== 0) {
    const params = {
      taskID: row.annualScheduleMainID
    };
    await annualScheduleService.getAnnualScheduleDetails(params);
  }
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 查看排程来源
 * @param row
 * @return
 */
const showScheduleSource = async (row: taskMainView) => {
  const params = {
    annualScheduleMainID: row.annualScheduleMainID
  };
  const result = await annualScheduleService.getAnnualScheduleSource(params);
  result && showMessage("info", result);
};
//#endregion

//#region 表单弹窗
const performingTask = ref<Partial<taskMainView>>({});
const isAdd = ref<boolean>(false);
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "计划执行",
  showDrawer: false,
  drawerSize: "80%",
  confirm: () => {
    // TODO：待沟通后决定是否删除
    // if (drawerOptions.value.drawerName === "addTask") {
    //   addAnnualScheduleManualSave();
    // } else {
    //   showReasonFlag.value ? delayScheduleSave() : ;
    // }
    savePerformingTask();
  }
});
const rules = ref({
  performDateTime: [{ required: true, message: "请选择执行时间", trigger: "change" }],
  statusCode: [{ required: true, message: "请选择执行方案", trigger: "change" }],
  delayReason: [{ required: true, message: "请选择延期原因", trigger: "change" }]
});
const delayReasons = ref<Record<string, number>[]>([]);

/**
 * @description: 禁选今日以前的日期
 * @param date 日期
 * @return
 */
const getDisableDate = (date: any) => date < datetimeUtil.toDate(datetimeUtil.addDate(datetimeUtil.getNow("yyyy-MM-dd"), -1));
/**
 * @description: 获取延迟执行原因配置
 */
const getScheduleDelayPerformReason = async () => {
  if (delayReasons.value.length) {
    return;
  }
  const params: SettingDictionaryParams = {
    settingType: "AnnualPlan",
    settingTypeCode: "AnnualScheduleMain",
    settingTypeValue: "Reason"
  };
  delayReasons.value = await settingDictionaryService.getSettingDictionaryDict(params);
};
/**
 * @description: 执行计划保存
 */
const savePerformingTask = async () => {
  if (!performingTask.value) {
    showMessage("error", "没有获取到计划，请尝试重新加载");
    return;
  }
  const param = {
    annualScheduleMainID: performingTask.value.annualScheduleMainID,
    performDateTime: performingTask.value.performDateTime,
    statusCode: performingTask.value.statusCode,
    delayReason: performingTask.value.statusCode === 2 ? performingTask.value.delayReason : undefined,
    remark: performingTask.value.remark,
    scheduleDetails: []
  };
  const result = await annualScheduleService.performTask(param);
  drawerOptions.value.showDrawer = false;
  if (common.isRefValid(selectedMonth) && result) {
    isAdd.value && taskStatistics.value && (taskStatistics.value[selectedMonth.value][0] += 1);
    await getAnnualTasks();
  }
};
//#endregion

// const addTask = () => {
//   drawerOptions.value.drawerName = "addTask";
//   drawerOptions.value.drawerTitle = "加入计划项目";
//   drawerOptions.value.showDrawer = true;
// };

// /**
//  * @description 手动加入年度计划保存
//  */
// const addAnnualScheduleManualSave = () => {
//   if (!scheduleDate.value) {
//     showMessage("warning", "请选择预计执行日期");
//     return;
//   }
//   if (!scheduleContent.value) {
//     showMessage("warning", "请输入要添加的执行内容");
//     return;
//   }
//   addAnnualScheduleManual(scheduleDate.value, scheduleContent.value, (flag: boolean) => {
//     if (flag) {
//       drawerOptions.value.showDrawer = false;
//       scheduleDate.value = undefined;
//       scheduleContent.value = undefined;
//     }
//   });
// };
</script>

<style lang="scss">
.annual-plan-schedule {
  width: 100%;
  height: 100%;
  .label {
    vertical-align: middle;
  }
  .btn-wrapper {
    display: flex;
    justify-content: center;
    gap: 4px;
    padding: 0 20px;
  }
  .schedule-data {
    td {
      padding: 0;
      .cell > .column {
        cursor: pointer;
        height: 42px;
        line-height: 42px;
        &.is-select {
          background-image: url("/static/images/text-select.png");
          background-position: center center;
          background-repeat: no-repeat;
          background-size: 45px;
        }
      }
    }
  }
}
</style>
