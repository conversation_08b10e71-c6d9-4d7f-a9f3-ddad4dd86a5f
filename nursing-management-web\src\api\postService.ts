/*
 * FilePath     : \src\api\postService.ts
 * Author       : LX
 * Date         : 2023-08-07 08:44
 * LastEditors  : 马超
 * LastEditTime : 2025-03-19 16:45
 * Description  : 岗位管理相关API接口
 */
import http from "@/utils/http";
import qs from "qs";
export class postService {
  private static getDepartmentPostListApi: string = "Post/GetDepartmentPostList";
  private static getPostDescriptionListApi: string = "Post/GetPostDescriptionList";
  private static updateDepartmentPostApi: string = "Post/UpdateDepartmentPost";
  private static getDepartmentPostSettingApi: string = "Post/GetDepartmentPostSetting";
  private static deleteDepartmentPostSettingApi: string = "Post/DeleteDepartmentPostSetting";
  private static saveDepartmentPostSettingApi: string = "Post/SaveDepartmentPostSetting";
  private static getDepartmentPostToCapabilityLevelsApi: string = "Post/GetDepartmentPostToCapabilityLevels";
  private static saveDepartmentPostToCapabilityLevelApi: string = "Post/SaveDepartmentPostToCapabilityLevel";
  private static saveDepartmentPostToCapabilityLevelListApi: string = "Post/SaveDepartmentPostToCapabilityLevelList";
  private static deleteDepartmentPostToCapabilityLevelApi: string = "Post/DeleteDepartmentPostToCapabilityLevel";
  private static saveDepartmentPostApi: string = "Post/SaveDepartmentPost";
  private static batchSaveDepartmentPostApi: string = "Post/BatchSaveDepartmentPost";
  private static batchSaveDepartmentPostSettingApi: string = "Post/BatchSaveDepartmentPostSetting";
  private static deletePostDescriptionApi: string = "Post/DeletePostDescription";
  private static savePostDescriptionApi: string = "Post/SavePostDescription";
  private static batchSavePostDescriptionApi: string = "Post/BatchSavePostDescription";
  private static getPostListApi: string = "Post/GetPostList";
  private static savePostDataApi: string = "Post/SavePostData";
  private static deletePostDataApi: string = "Post/DeletePostData";
  private static activatePostDataApi: string = "Post/ActivatePostData";
  private static getPostWhetherDataApi: string = "Post/GetPostWhetherData";

  public static getDepartmentPostList(params: any) {
    return http.get(this.getDepartmentPostListApi, params, { loadingText: Loading.LOAD });
  }
  public static getPostDescriptionList(params: any) {
    return http.get(this.getPostDescriptionListApi, params, { loadingText: Loading.LOAD });
  }
  public static getDepartmentPostSetting(params: any) {
    return http.get(this.getDepartmentPostSettingApi, params, { loadingText: Loading.LOAD });
  }
  public static deleteDepartmentPostSetting(params: any) {
    return http.post(this.deleteDepartmentPostSettingApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  public static updateDepartmentPost(params: any) {
    return http.post(this.updateDepartmentPostApi, params);
  }
  public static saveDepartmentPostSetting(params: any) {
    return http.post(this.saveDepartmentPostSettingApi, params, { loadingText: Loading.SAVE });
  }
  public static getDepartmentPostToCapabilityLevels(params: any) {
    return http.get(this.getDepartmentPostToCapabilityLevelsApi, params, { loadingText: Loading.LOAD });
  }
  public static saveDepartmentPostToCapabilityLevel(params: any) {
    return http.post(this.saveDepartmentPostToCapabilityLevelApi, params, { loadingText: Loading.SAVE });
  }
  public static saveDepartmentPostToCapabilityLevelList(params: any) {
    return http.post(this.saveDepartmentPostToCapabilityLevelListApi, params, { loadingText: Loading.SAVE });
  }
  public static deleteDepartmentPostToCapabilityLevel(params: any) {
    return http.post(this.deleteDepartmentPostToCapabilityLevelApi, params, { loadingText: Loading.DELETE });
  }
  public static saveDepartmentPost(params: any) {
    return http.post(this.saveDepartmentPostApi, params, { loadingText: Loading.SAVE });
  }
  public static batchSaveDepartmentPost(params: any) {
    return http.post(this.batchSaveDepartmentPostApi, params, { loadingText: Loading.SAVE });
  }
  public static batchSaveDepartmentPostSetting(params: any) {
    return http.post(this.batchSaveDepartmentPostSettingApi, params, { loadingText: Loading.SAVE });
  }
  public static deletePostDescription(params: any) {
    return http.post(this.deletePostDescriptionApi, params, { loadingText: Loading.DELETE });
  }
  public static savePostDescription(params: any) {
    return http.post(this.savePostDescriptionApi, params, { loadingText: Loading.SAVE });
  }
  public static batchSavePostDescription(params: any) {
    return http.post(this.batchSavePostDescriptionApi, params, { loadingText: Loading.SAVE });
  }
  public static getPostList() {
    return http.get(this.getPostListApi, { loadingText: Loading.LOAD });
  }
  public static savePostData(params: any) {
    return http.post(this.savePostDataApi, params, { loadingText: Loading.SAVE });
  }
  public static deletePostData(params: any) {
    return http.post(this.deletePostDataApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  public static activatePostData(params: any) {
    return http.post(this.activatePostDataApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  public static getPostWhetherData(params: any) {
    return http.get(this.getPostWhetherDataApi, params, { loadingText: Loading.LOAD });
  }
}
