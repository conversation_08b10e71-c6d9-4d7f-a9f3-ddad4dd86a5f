/*
 * FilePath     : \src\views\approveManagement\approveProcess\types\approveProcessNode.ts
 * Author       : 杨欣欣
 * Date         : 2023-09-13 15:13
 * LastEditors  : 杨欣欣
 * LastEditTime : 2023-11-18 17:14
 * Description  : 审批流程节点类型
 * CodeIterationRecord:
 */
import type { approveProcessNodeDetail } from "./approveProcessNodeDetail";
export interface approveProcessNode {
  /**
   * 审批节点唯一码，Guid
   */
  approveNodeID: string;
  /**
   * 审批节点名称
   */
  approveNodeName: string;
  /**
   * 节点审批时限
   */
  approveTimeLimit: number;
  /**
   * 下一节点ID
   */
  nextNodeID: string;
  /**
   * 审批模式
   */
  approveModel: string;
  /**
   * 是否标记删除
   */
  isDeleted: boolean;
  /**
   * 节点明细集合
   */
  nodeDetails: approveProcessNodeDetail[];
}
