/*
 * FilePath     : \src\views\trainingManagement\types\courseSetting.ts
 * Author       : 张现忠
 * Date         : 2024-04-07 11:41
 * LastEditors  : 张现忠
 * LastEditTime : 2024-07-17 09:24
 * Description  : 课程设置 接口定义
 * CodeIterationRecord:
 */
/**
 * @description: 课程
 */
export interface courseSetting {
  /**
   * 课程唯一ID
   */
  courseSettingID: string;
  /*
   * 课程名称
   */
  courseName: string;
  /*
   * 课程简介
   */
  courseIntroduction: string;
  /*
   * 课程分类ID
   */
  courseTypeID: string;
  /*
   * 课程分类
   */
  courseTypeName: string;
  /**
   * 课程制定年份
   */
  year: number;
  /*
   * 新增人员
   */
  addEmployeeName: string;
  /*
   * 添加时间
   */
  addDateTime: string;
  /*
   * 修改时间
   */
  modifyEmployeeName: string;
  /*
   * 修改时间
   */
  modifyDateTime: string;
  /*
   * 子课程列表
   */
  childCourses?: courseSetting[];
  /**
   * 课程父节点
   */
  parentID?: string;
  /**
   * 新增部门ID
   */
  addDepartmentID: number;
  /**
   * 是否展开子节点
   */
  expandTree: boolean;
  /**
   * 课程层级
   */
  level: number;
  /**
   * 新增人工号
   */
  addEmployeeID: string;
  /**
   * 文件列表
   */
  files?: Array<Record<string, any>>;
  /**
   * 文件数据
   */
  fileInfoList?: Array<Record<string, any>>;
}
