/*
 * FilePath     : \src\hooks\useApproval.ts
 * Author       : 张现忠
 * Date         : 2024-04-25 11:36
 * LastEditors  : 马超
 * LastEditTime : 2025-03-02 11:04
 * Description  : 审批公共hook
 * CodeIterationRecord:
 */

import { approveRecordService } from "@/api/approveRecordService";

/**
 * @description 审批公共hook
 * 1.审批撤销
 * @param isSourceID 是否为来源ID(发起审批的项目主键ID)
 * @returns
 */
export function useApproval(isSourceID: boolean = false) {
  const showRevokeFlag = ref(false);
  let params: SettingDictionaryParams = {
    settingType: "ApprovalManagement",
    settingTypeCode: "SystemSwitch",
    settingTypeValue: "SponsorQuashAuthority"
  };
  settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
    showRevokeFlag.value = Boolean(respBool);
  });
  /**
   * @description 手动提交审批
   * @param approveType 审批类型
   * @param recordID 审批记录ID
   * @param callBack 回调函数
   */
  const manualSubmissionApprove = (approveType: string, recordID: string, callBack: Function) => {
    let params = {
      approveType: approveType,
      recordID: recordID
    };
    approveRecordService.manualSubmissionApprove(params).then((res: any) => {
      if (res) {
        showMessage("success", "提交成功");
        callBack();
      } else {
        showMessage("error", "审批流程未配置，请联系护士长或护理部！");
      }
    });
  };
  return {
    /**
     * @description 撤销审批
     * @param recordID 审批记录ID（approveRecordID）/审批来源ID（sourceID）
     * @param reason 撤销审批的原因
     * @returns
     */
    revokeApproval: async (recordID: string, reason: string) => {
      await deleteConfirm("确定撤销审批吗？", async (confirm: boolean) => {
        if (!confirm) {
          return;
        }
        let params = isSourceID ? { sourceID: recordID, reason: reason } : { approveRecordID: recordID, reason: reason };
        await approveRecordService.revokeApproval(params).then((respBool: any) => {
          if (!respBool) {
            showMessage("error", "撤销审批失败！");
            return;
          }
          showMessage("success", "撤销审批成功");
        });
      });
    },
    /**
     * @description 是否显示撤销按钮
     */
    showRevokeFlag,
    /**
     * @description: 因记录撤销判断是否隐藏按钮权限
     * @param statusCode
     * @returns
     */
    hiddenButtonByRevoke: (statusCode: string) => {
      // 非撤销状态
      return statusCode !== "4";
    },
    manualSubmissionApprove
  };
}
