<!--
 * FilePath     : \src\views\approveManagement\approveRecord\components\approve.vue
 * Author       : 张现忠
 * Date         : 2023-10-26 09:20
 * LastEditors  : 马超
 * LastEditTime : 2025-04-29 14:26
 * Description  : 审批维护
 * CodeIterationRecord: 3843-作为IT人员，我需要开发审批功能，以利护理管理系统推进
 -->
<template>
  <base-layout class="approve" :show-header="false">
    <div class="approve-content">
      {{ approveViews[0]?.content }}
    </div>
    <div class="approve-diagram">
      <el-steps direction="vertical" class="approval-steps" :space="80">
        <el-step
          v-for="item in approveViews"
          :key="item.approveMainID"
          :status="getStepStatus(item)"
          :class="[item.statusCode == '3' && 'no-line']"
        >
          <template #title>
            <span>{{ item.approveNodeName }}</span>
            <span v-if="item.headFlag">：{{ item.approveDetailViews[0]?.approveEmployeeName }}</span>
          </template>
          <template #description>
            <div class="approval-step-body" v-if="!item.headFlag">
              <template v-if="item.statusCode">
                <div v-for="detail in item.approveDetailViews.filter((detail) => detail.statusCode)" :key="detail.approveDetailID">
                  <span>审批人：</span>
                  <el-tag effect="dark" :type="getApproveStatusTag(detail.statusCode)" class="tag-name">
                    {{ detail.approveEmployeeName || detail.approveEmployeeID }}
                  </el-tag>
                  <div class="approval-suggestion">
                    <el-text v-if="detail.approveSuggestions">审批意见：{{ detail.approveSuggestions }}</el-text>
                  </div>
                </div>
              </template>
              <template v-else>
                <div v-for="detail in item.approveDetailViews.filter((detail) => detail.statusCode)" :key="detail.approveDetailID">
                  <el-tag effect="dark" :type="getApproveStatusTag(detail.statusCode)" class="tag-name">
                    {{
                      detail.approveEmployeeName ||
                      (userStore.employeeID === detail.approveEmployeeID ? userStore.userName : detail.approveEmployeeID)
                    }}
                  </el-tag>
                  <el-text>{{ detail.approveSuggestions }}</el-text>
                </div>
                <div v-for="detail in item.approveDetailViews" :key="detail.approveDetailID">
                  <template v-if="detail.preApproveEmployeeIDs?.includes(employeeID) && !detail.approveDateTime">
                    <p>审批意见：</p>
                    <el-input v-model="detail.approveSuggestions" type="textarea" placeholder="请输入审批意见" :rows="3"></el-input>
                  </template>
                </div>
              </template>
            </div>
          </template>
        </el-step>
        <el-step v-if="approveRecord.approveStatusCode === '4'" status="error">
          <template #title>
            <span>{{ approveRecord.approveStatus }}</span>
          </template>
          <template #description>
            <span>撤销原因：</span><span>{{ approveRecord.revokeReason || "" }}</span>
          </template>
        </el-step>
      </el-steps>
    </div>
  </base-layout>
</template>
<script setup lang="ts">
import { useApproveRecord } from "../hooks/useApproveRecord";
import type { approveDetailView, approveView } from "../types/approveDetailView";
const { userStore } = useStore();
const { getApproveStatusTag } = useStatusTag();
const { getApproveDetailView } = useApproveRecord();
const approveViews = ref<approveView[]>([]);
const currApproveView = ref<approveView>();
const props = defineProps({
  // 审批流程主记录ID
  approveRecordID: {
    type: String,
    required: true
  },
  employeeID: {
    type: String,
    required: true
  },
  approveDecision: {
    type: String,
    default: ""
  },
  approveRecord: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({})
  }
});
const emits = defineEmits(["save-approval"]);
onMounted(async () => {
  if (!props.approveRecordID) {
    return;
  }
  // 获取明细数据
  approveViews.value = await getApproveDetailView(props.approveRecordID);
  // 设置当前待审批节点view信息
  let pendingApprovalViews = approveViews.value.filter((approve) => !approve.headFlag && !approve.statusCode);
  if (pendingApprovalViews?.length > 0) {
    currApproveView.value = pendingApprovalViews[0];
  }
});

// 监听approveRecordID的变化
watch(
  () => props.approveRecordID,
  async (newID) => {
    if (newID) {
      // 获取明细数据
      approveViews.value = await getApproveDetailView(newID);
      // 设置当前待审批节点view信息
      let pendingApprovalViews = approveViews.value.filter((approve) => !approve.headFlag && !approve.statusCode);
      if (pendingApprovalViews?.length > 0) {
        currApproveView.value = pendingApprovalViews[0];
      }
    }
  },
  { immediate: true }
);

// 监听按钮状态
watch(
  () => props.approveDecision,
  () => (props.approveDecision === "1" ? approveItem() : props.approveDecision === "-1" ? rejectItem() : "")
);
/**
 * @description: 同意审批逻辑处理
 */
const approveItem = () => {
  if (!currApproveView.value) {
    return;
  }
  let detail = currApproveView.value.approveDetailViews.filter(
    (approveDetail) => approveDetail.preApproveEmployeeIDs?.includes(props.employeeID) && !approveDetail.approveDateTime
  )[0];
  let item = currApproveView.value || {};
  if (!detail) {
    return;
  }
  // 执行同意审批的逻辑
  detail.statusCode = ApprovalStatus.Completed.toString();
  detail.approveEmployeeID = props.employeeID;
  detail.approveEmployeeName = userStore.userName;
  // 特殊处理会签 -同意
  if (item.approveModel !== "2" || !item.approveDetailViews.some((approveDetail) => !approveDetail.statusCode)) {
    item.statusCode = ApprovalStatus.Completed.toString();
  }
  saveApproval(item, detail);
};
/**
 * @description: 拒绝审批逻辑处理
 */
const rejectItem = () => {
  if (!currApproveView.value) {
    return;
  }
  let detail = currApproveView.value.approveDetailViews.filter(
    (approveDetail) => approveDetail.preApproveEmployeeIDs?.includes(props.employeeID) && !approveDetail.approveDateTime
  )[0];
  let item = currApproveView.value || {};
  // 执行拒绝审批的逻辑
  detail.statusCode = ApprovalStatus.Rejected.toString();
  // 特殊处理或签 -拒绝
  if (item.approveModel !== "3" || !item.approveDetailViews.some((approveDetail) => !approveDetail.statusCode)) {
    item.statusCode = ApprovalStatus.Rejected.toString();
  }
  detail.approveEmployeeID = props.employeeID;
  detail.approveEmployeeName = userStore.userName;
  saveApproval(item, detail);
};
/**
 * @description: 保存当前节点的的审批结果
 * @param item  审批节点view信息
 * @param detail  审批到的明细（一个节点中可能存在多条明细，如：会签和或签）
 */
const saveApproval = (item: approveView, detail: approveDetailView) => {
  let result = {
    approveDetailID: detail.approveDetailID,
    approveRecordID: detail.approveRecordID,
    approveMainID: item.approveMainID,
    approveSuggestions: detail.approveSuggestions,
    statusCode: detail.statusCode
  };
  emits("save-approval", result);
};
/**
 * @description:获取当前节点状态
 * @return : "wait" | "process" | "finish" | "error" | "success"
 */
const getStepStatus = (item: approveView) => {
  let status = item.statusCode === "2" ? "success" : item.statusCode === "3" ? "error" : "process";
  return status;
};
</script>
<style lang="scss">
.approve {
  .approve-content {
    border: 1px solid $border-color;
    padding: 8px 0;
  }
  .approve-diagram {
    // 进度条
    .approval-steps {
      margin-top: 8px;
      .approve-badge {
        transform: none !important;
      }
      .no-line {
        .el-step__line {
          background: transparent;
          .el-step__line-inner {
            border-width: 0 !important;
          }
        }
      }
      .is-success {
        .el-step__line {
          background-color: rgb(103, 194, 58);
        }
      }
      .el-step__description {
        padding-right: 0 !important;
        .approval-step-body {
          margin-top: 5px;
          margin-bottom: 20px;
          padding: 0 5px 10px 10px;
          .tag-name {
            margin-right: 10px;
            margin-top: 10px;
          }
          .approval-suggestion {
            margin-top: 10px;
          }
        }
      }
      .el-step__icon.is-text > div {
        display: none;
      }
    }
  }
}
</style>