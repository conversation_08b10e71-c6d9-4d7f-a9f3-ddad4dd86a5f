<!--
 * FilePath     : \src\views\annualPlan\browse\index.vue
 * Author       : 胡长攀
 * Date         : 2023-12-12 09:08
 * LastEditors  : 马超
 * LastEditTime : 2025-06-21 11:08
 * Description  : 年度计划-计划查询
 -->
<template>
  <base-layout class="annual-plan-browse" :drawerOptions="drawerOptions">
    <template #header>
      <div class="header-container">
        <el-radio-group v-model="planType" @change="getPlanList" class="radio-group">
          <el-radio-button value="annual">年度计划</el-radio-button>
          <el-radio-button value="quarter">季度计划</el-radio-button>
          <el-radio-button value="monthly">月度计划</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <el-table :data="plans" border stripe class="display-table" :row-class-name="setBoldRowClass">
      <el-table-column label="年度" :width="convertPX(96)">
        <template #default> {{ year }} </template>
      </el-table-column>
      <el-table-column v-if="planType === 'quarter'" prop="quarter" label="季度" :width="convertPX(80)"> </el-table-column>
      <el-table-column v-if="planType === 'monthly'" prop="monthly" label="月度" :width="convertPX(80)"> </el-table-column>
      <el-table-column prop="departmentName" label="部门" :filters="departmentOptions" :filter-method="filterDepartment" />
      <el-table-column prop="plannerName" label="制定人" align="center" :width="convertPX(100)" />
      <el-table-column label="修改时间" align="center" :width="convertPX(220)">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime' }" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(125)" fixed="right">
        <template #default="{ row }">
          <el-tooltip content="预览">
            <i v-visibilityHidden="row.statusCode == 1" v-permission:B="7" class="iconfont icon-preview" @click="previewPlan(row)" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <annual-plan-preview v-if="planType === 'annual'" :annualPlanMainId="previewPlanKey">
        <el-button type="primary" class="right-button" @click="exportPlan">导出</el-button>
      </annual-plan-preview>
      <quarter-plan-preview v-if="planType === 'quarter'" v-model="showRoutineWorks" :quarterPlanMainId="previewPlanKey">
        <el-button type="primary" class="right-button" @click="exportPlan">导出</el-button>
      </quarter-plan-preview>
      <monthly-plan-preview v-if="planType === 'monthly'" v-model="showRoutineWorks" :monthlyPlanMainId="previewPlanKey">
        <el-button type="primary" class="right-button" @click="exportPlan">导出</el-button>
      </monthly-plan-preview>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
import { uniqBy } from "lodash-es";
const convertPX: any = inject("convertPX");
const planType = ref<"annual" | "quarter" | "monthly">("annual");
const { getPlanAnnual } = usePlanTime();
const year = getPlanAnnual();

//#region 表格初始化
onMounted(async () => {
  await getPlanList();
});
const plans = ref<Record<string, any>[]>([]);
/**
 * @description: 加载列表
 */
const getPlanList = async () => {
  // 获取年度计划列表（上级、当前用户、下级制订）
  const params = { year };
  switch (planType.value) {
    case "annual":
      plans.value = await annualPlanMainService.getBrowseAPViews(params);
      break;
    case "quarter":
      plans.value = await quarterPlanMaintainService.getBrowseQPViews(params);
      break;
    case "monthly":
      plans.value = await monthlyPlanMaintainService.getBrowseMPViews(params);
      break;
    default:
      break;
  }
};
/**
 * @description: 设置粗体行样式
 * @param row 行数据
 */
const setBoldRowClass = ({ row }: Record<string, any>) => (row.isMainDepartment ? "bold-row" : "");
//#endregion

//#region 筛选
const departmentOptions = computed(() =>
  uniqBy(
    plans.value.map((planMain) => ({ text: planMain.departmentName, value: planMain.departmentID })),
    "value"
  )
);
const filterDepartment = (value: string, row: Record<string, any>) => row.departmentID === value;
//#endregion

//#region 预览
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "年度计划总览",
  showDrawer: false,
  showCancel: false,
  drawerSize: "100%",
  cancel: () => (drawerOptions.value.showDrawer = false)
});
const previewPlanKey = ref<string>("");
const planName = ref<string>("");
const unitTimeChinese = computed(() => {
  if (planType.value === "annual") {
    return "年";
  }
  if (planType.value === "quarter") {
    return "季";
  }
  if (planType.value === "monthly") {
    return "月";
  }
  return "";
});
/**
 * @description: 查看年度计划
 * @param plan 计划实例
 */
const previewPlan = (plan: Record<string, any>) => {
  drawerOptions.value.showDrawer = true;
  previewPlanKey.value = plan.key;
  planName.value = `${plan.departmentName}${unitTimeChinese.value}度计划`;
};
//#endregion

//#region 导出
/**
 * @description: 导出计划
 */
const exportPlan = async () => {
  if (planType.value === "annual") {
    await exportAnnualPlan();
  }
  if (planType.value === "quarter") {
    await exportQuarterPlan();
  }
  if (planType.value === "monthly") {
    await exportMonthlyPlan();
  }
};
/**
 * @description: 导出年度计划
 */
const exportAnnualPlan = async () => {
  const fileUrl = await annualPlanOverviewService.exportAnnualPlan({ mainID: previewPlanKey.value });
  common.downloadFile(fileUrl, `${planName.value}.docx`);
};
const showRoutineWorks = ref<boolean>(false);
/**
 * @description: 导出季度计划
 */
const exportQuarterPlan = async () => {
  const params = { quarterPlanMainID: previewPlanKey.value, showRoutineWorks: showRoutineWorks.value };
  const fileUrl = await annualPlanOverviewService.exportQuarterPlan(params);
  common.downloadFile(fileUrl, `${planName.value}.docx`);
};
/**
 * @description: 导出月度计划
 */
const exportMonthlyPlan = async () => {
  const params = { monthlyPlanMainID: previewPlanKey.value, showRoutineWorks: showRoutineWorks.value };
  const fileUrl = await annualPlanOverviewService.exportMonthlyPlan(params);
  common.downloadFile(fileUrl, `${planName.value}.docx`);
};
//#endregion
</script>

<style lang="scss">
.annual-plan-browse {
  .year-picker {
    width: 140px;
    margin-right: 20px;
  }
  .display-table {
    .bold-row {
      font-weight: bold;
    }
  }
  .el-drawer {
    background-color: #f3f3f3;
    .el-drawer__body {
      padding: 0px;
    }
    .base-header .right-button {
      margin-top: 14px;
    }
    .base-content {
      user-select: text;
    }
  }
  .header-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .radio-group {
      margin-right: 20px;
    }
  }
}
</style>
