<!--
 * FilePath     : \src\views\approveManagement\approveRecord\components\transferPage\transferQcTemplate.vue
 * Author       : 张现忠
 * Date         : 2024-05-20 15:55
 * LastEditors  : 来江禹
 * LastEditTime : 2024-12-09 10:58
 * Description  : 质控内容明细
 * CodeIterationRecord:4435-作为IT人员，我需要重构审批画面，以利符合线上使用（PC端）（21）(军志)
                       4962-作为IT人员，我需要优化护理管理质控部分功能，以利于用户操作（7）-zxz 20240929
 -->
<template>
  <div class="transfer-qc-template">
    <el-table
      ref="qcMainTable"
      class="qc-main-table"
      border
      :data="qcMainViews"
      height="100%"
      highlight-current-row
      @current-change="getQcForm"
    >
      <el-table-column prop="hQcSort" label="考核次数" :width="convertPX(80)" align="center" />
      <el-table-column prop="hQcDate" label="考核日期" :width="convertPX(100)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.hQcDate, type: 'date' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="考核分数" align="center">
        <template #default="{ row }">
          <span :class="getClassForScore(row)">{{ row.hQcScore }}</span>
        </template>
      </el-table-column>
    </el-table>
    <qcForm v-model="saveView" :formData="formData"></qcForm>
  </div>
</template>

<script setup lang="ts">
import type { dynamicFormData, formAttribute } from "zhytech-ui";
const props = defineProps({
  approveCareMainID: {
    type: String,
    required: true
  }
});
const convertPX: any = inject("convertPX");
const careMainID = ref<string>("");
const qcMainTable = ref<any>();
const emits = defineEmits(["row-change"]);
onMounted(async () => {
  careMainID.value = props.approveCareMainID;
  await Promise.all([getQcMainViews(), getFormTemplate(), getGuidanceAndImprovement()]);
});
const formData = ref<dynamicFormData<formAttribute>>();
const saveView = ref<Record<string, any>>({
  guidanceShowFlag: true,
  improvementShowFlag: true,
  disableForm: true,
  showDatePickFlag: false
});

const qcMainViews = ref<Record<string, any>[]>([]);
/**
 * @description: 获取所有考核维护记录
 */
const getQcMainViews = async () => {
  const params = {
    careMainID: props.approveCareMainID
  };
  qcMainViews.value = (await hierarchicalQCService.getQcMainViews(params)) as Record<string, any>[];
  // 选择当前行
  const currentRow = qcMainViews.value.find((qcMainView) => qcMainView.hQcMainId === careMainID.value);
  qcMainTable.value.setCurrentRow(currentRow);
};
/**
 * @description: 获取选择的主题考核记录明细
 * @param hQcMainId 主题考核维护记录ID
 * @return
 */
const getQcForm = ({ hQcMainId }: Record<string, any>) => {
  careMainID.value = hQcMainId;
  getFormTemplate();
  // 通知父组件行数据变化
  emits("row-change", { hQcMainId });
};
/**
 * @description: 获取质控评估内容
 */
const getFormTemplate = async () => {
  formData.value = undefined;
  let params = {
    careMainID: careMainID.value
  };
  await hierarchicalQCService.getQCAssessView(params).then((res: any) => {
    res && (formData.value = res);
  });
};
/**
 * @description: 获取质控记录中的指导内容和改进内容
 */
const getGuidanceAndImprovement = async () => {
  let params = {
    careMainID: careMainID.value
  };
  await hierarchicalQCService.getGuidanceAndImprovement(params).then((res: any) => {
    res && ({ item1: saveView.value.guidance, item2: saveView.value.improvement } = res);
  });
};
/**
 * @description: 获取考核分数样式
 * @param row 行数据
 * @return0
 */
const getClassForScore = (row: Record<string, any>): string => {
  const passingScore = row.minPassingScore ?? 100;
  return row.hQcScore < passingScore ? "red" : "";
};
</script>

<style lang="scss">
.transfer-qc-template {
  width: auto;
  height: 100%;
  display: flex;
  .qc-main-table {
    flex: 1;
    .red {
      color: #ff0000;
    }
  }
  .qc-form {
    flex: 2;
  }
}
</style>
