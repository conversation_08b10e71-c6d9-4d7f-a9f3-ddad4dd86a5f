<!--
 * FilePath     : \src\views\messageManagement\components\messagePreview.vue
 * Author       : 张现忠
 * Date         : 2024-12-13 00:09
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:30
 * Description  : 系统通知弹窗
 * CodeIterationRecord:4969-作为护理人员，我需要消息管理支持发送系统更新的公告，以利临床工作开展（21） zxz 20241213
 -->

<template>
  <el-dialog
    class="system-notice"
    v-model="visible"
    :title="systemMessageNotice?.title"
    destroy-on-close
    :before-close="(done:any) => handleClose(false,done)"
  >
    <zhy-rich-text-editor v-model="systemMessageNotice!.messageContent" :option="{ readOnly: true }"></zhy-rich-text-editor>
    <template #footer v-if="isPendingConfirmMessage">
      <el-button type="primary" @click="confirm(true)" :disabled="disabledBtn">{{ buttonLabel }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useMessageConfirmationHandler } from "../hooks/useMessageConfirmationHandler";
const { systemMessageNotice, completeRead, checkRole } = useMessageConfirmationHandler();
const props = defineProps({
  modelValue: {
    type: Boolean,
    required: false
  },
  params: {
    type: Object as PropType<Record<string, any>>,
    default: () => {}
  },
  isPendingConfirmMessage: {
    type: Boolean,
    default: false
  }
});
const disabledBtn = computed(() => counter.value > 0);
// 按钮倒计时
const counter = ref(10);
const buttonLabel = computed(() => {
  return counter.value <= 0 ? "已知晓 " : `${counter.value}秒后点击`;
});
const emit = defineEmits(["update:modelValue", "confirm"]);
const visible = useVModel(props, "modelValue", emit);
watch(
  () => props.params,
  () => {
    systemMessageNotice.value = props.params;
    props.isPendingConfirmMessage ? startCount() : (counter.value = 0);
  }
);
/**
 * @description 开启倒计时
 */
const startCount = () => {
  counter.value = 10;
  const { pause } = useIntervalFn(() => {
    counter.value -= 1;
    if (counter.value <= 0) {
      pause();
    }
  }, 1000);
};
/**
 * @description 关闭弹窗
 */
const handleClose = async (hasConfirmed: boolean, done: any) => {
  if (hasConfirmed || !checkRole(hasConfirmed)) {
    done(false);
  }
  systemMessageNotice.value && emit("confirm", systemMessageNotice.value.messageRecordID);
};
/**
 * @description 确认按钮点击事件
 * @param hasConfirmed 是否确认
 */
const confirm = async (hasConfirmed: boolean) => {
  if (!hasConfirmed && checkRole(hasConfirmed)) {
    return;
  }
  let success = await completeRead(hasConfirmed);
  success && (visible.value = false);
};
</script>
