<!--
 * FilePath     : \src\views\annualPlan\dictionary\annualIndicatorList.vue
 * Author       : 杨欣欣
 * Date         : 2023-11-04 11:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-04 17:05
 * Description  : 年度计划-指标字典维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="annual-indicator-list" :drawerOptions="drawerOptions">
    <template #header>
      <annual-plan-header :year="year" />
      <el-button v-permission:B="1" @click="addIndicator" class="add-button">{{ i18nText.add }}</el-button>
    </template>
    <el-table class="annual-indicator-list-table" :data="indicatorList" stripe border>
      <el-table-column prop="sort" :label="i18nText.sort" :width="convertPX(70)">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="indicatorContent" :label="i18nText.indicatorContent" :min-width="convertPX(250)" />
      <el-table-column prop="enableYear" :label="i18nText.year" :width="convertPX(120)" align="center" />
      <el-table-column prop="addEmployeeName" :label="i18nText.addPerson" :width="convertPX(100)" align="center" />
      <el-table-column :label="i18nText.addDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column prop="modifyEmployeeName" :label="i18nText.modifyPerson" :width="convertPX(100)" align="center" />
      <el-table-column :label="i18nText.modifyDateTime" :width="convertPX(210)" align="center">
        <template #default="{ row }">
          <span v-formatTime="{ value: row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }" />
        </template>
      </el-table-column>
      <el-table-column :label="i18nText.operation" :width="convertPX(80)" align="center">
        <template #default="{ row }">
          <el-tooltip :content="i18nText.edit">
            <i v-permission:B="3" @click="editIndicator(row)" class="iconfont icon-edit" />
          </el-tooltip>
          <el-tooltip :content="i18nText.delete">
            <i v-permission:B="4" @click="deleteRow(row)" class="iconfont icon-delete" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="editForm" :model="editingIndicator" :rules="rules" class="edit-form" label-position="left" label-width="auto">
        <el-form-item :label="i18nText.year" prop="enableYear">
          <el-date-picker class="form-year-picker" v-model="editingIndicator.enableYear" type="year" value-format="YYYY" disabled />
        </el-form-item>
        <el-form-item :label="i18nText.indicatorContent" prop="indicatorContent">
          <el-input :placeholder="i18nText.indicatorContent" v-model="editingIndicator.indicatorContent" :rows="3" type="textarea" />
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
const convertPX: any = inject("convertPX");
const { proxy } = getCurrentInstance() as any;
const rules = ref({
  enableYear: [{ required: true, message: "请输入启用年度", trigger: "blur" }],
  indicatorContent: [{ required: true, message: "请输入指标名称", trigger: "blur" }]
});
const indicatorList = ref<Record<string, any>[]>([]);
const editingIndicator = ref<Record<string, any>>({});
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  cancel: () => (drawerOptions.value.showDrawer = false),
  confirm: async () => await saveIndicator()
});
const managementStore = usePlanManagementStore();
const year = usePlanTime().getPlanAnnual();
onMounted(async () => await getAnnualIndicatorList());
watch(() => managementStore.departmentID, async () => {
  await getAnnualIndicatorList();
});
/**
 * @description: 获取年度计划指标字典
 */
const getAnnualIndicatorList = async () => {
  const params = {
    departmentID: managementStore.departmentID
  };
  indicatorList.value = await annualPlanSettingService.getAnnualIndicatorList(params);
};
/**
 * @description: 新增指标字典
 */
const addIndicator = () => {
  editingIndicator.value = {
    enableYear: datetimeUtil.getNowDate("yyyy"),
    departmentID: managementStore.departmentID
  };
  drawerOptions.value.drawerTitle = i18nText.value.add;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 编辑指标字典
 * @param row 当前行
 * @return
 */
const editIndicator = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  editingIndicator.value = common.clone(row);
  editingIndicator.value.enableYear = row.enableYear.toString();
  drawerOptions.value.drawerTitle = i18nText.value.edit;
  drawerOptions.value.showDrawer = true;
};
const editForm = ref<any>();
/**
 * @description: 保存指标字典
 */
const saveIndicator = async () => {
  const isValid = await editForm.value?.validate();
  if (!isValid) {
    return;
  }
  const params = toRaw(editingIndicator.value);
  annualPlanSettingService.saveAnnualIndicatorList(params).then(() => {
    getAnnualIndicatorList();
    drawerOptions.value.showDrawer = false;
    showMessage("success", "保存成功");
  });
};
/**
 * @description: 删除指标字典
 * @param row 当前行
 * @return
 */
const deleteRow = (row: Record<string, any>) => {
  if (!row?.annualIndicatorID) {
    showMessage("warning", "没有获取到当前记录数据，请刷新页面");
    return;
  }
  confirmBox("确定删除？", proxy.$t("tip.systemTip"), (flag: boolean) => {
    if (!flag) {
      return;
    }
    const params = {
      annualIndicatorID: row.annualIndicatorID
    };
    annualPlanSettingService.deleteAnnualIndicatorList(params).then(() => {
      showMessage("success", "删除成功");
      getAnnualIndicatorList();
    });
  });
};
// 多语言处理
const i18nText = computed(() => {
  return {
    sort: proxy.$t("label.sort"),
    add: proxy.$t("button.add"),
    delete: proxy.$t("tip.delete"),
    edit: proxy.$t("tip.edit"),
    deleteConfirm: proxy.$t("tip.deleteConfirm"),
    operation: proxy.$t("label.operation"),
    buttonSave: proxy.$t("button.save"),
    cancel: proxy.$t("button.cancel"),
    indicatorContent: proxy.$t("annualIndicatorList.indicatorContent"),
    year: proxy.$t("annualIndicatorList.year"),
    addPerson: proxy.$t("annualIndicatorList.addPerson"),
    addDateTime: proxy.$t("annualIndicatorList.addDateTime"),
    modifyPerson: proxy.$t("annualIndicatorList.modifyPerson"),
    modifyDateTime: proxy.$t("annualIndicatorList.modifyDateTime")
  };
});
</script>
<style lang="scss">
.annual-indicator-list {
  .el-select {
    width: 160px;
  }
  .annual-indicator-list-table {
    height: 100%;
  }
  .edit-form {
    .form-year-picker {
      width: 150px;
    }
  }
}
</style>
