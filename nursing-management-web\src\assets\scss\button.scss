.el-button {
  border-radius: 5px;
  margin-left: 5px;
  &:hover {
    opacity: 0.7;
  }
  & + .el-button {
    margin-left: 5px;
  }

  &.el-button--small {
    padding: 12px 16px;
    border-radius: 4px;
  }
  &:active {
    background: $base-color !important;
    border-color: $base-color !important;
  }
  &.add-button,
  &.add-button:focus,
  &.add-button:active {
    background: #14d8d8 !important;
    border-color: #14d8d8 !important;
    color: #ffffff !important;
  }

  &.edit-button,
  &.edit-button:focus,
  &.edit-button:active {
    background: #108bf7 !important;
    border-color: #108bf7 !important;
    color: #ffffff !important;
  }

  &.print-button,
  &.print-button:focus,
  &.print-button:active {
    background: #ff7400 !important;
    border-color: #ff7400 !important;
    color: #ffffff !important;
  }

  &.query-button,
  &.query-button:focus,
  &.query-button:active {
    background-color: #8cc63e !important;
    border-color: #8cc63e !important;
    color: #ffffff !important;
  }

  &.stop-button,
  &.stop-button:focus,
  &.stop-button:active {
    background: #707070 !important;
    border-color: #707070 !important;
    color: #ffffff !important;
  }
}
