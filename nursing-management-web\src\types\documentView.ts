/*
 * FilePath     : \src\types\documentView.ts
 * Author       : 苏军志
 * Date         : 2024-06-20 09:38
 * LastEditors  : 苏军志
 * LastEditTime : 2024-06-20 11:58
 * Description  : 文档管理中文件上传参数类型
 * CodeIterationRecord:
 */
/* eslint-disable */
/**
 * @description: 文档管理中文件上传参数类型
 */
declare interface DocumentView {
  /**
   * 文档主内容
   */
  documentMain: DocumentMainView;
  /**
   * 文档明细属性数据集合
   */
  documentDetails: DocumentDetailView[];
  /**
   * 文档标签集合
   */
  documentTags: DocumentTagView[];
}
/**
 * @description: 文档主内容
 */
declare interface DocumentMainView {
  /**
   * 来源系统
   */
  sourceSystem: string;
  /**
   * 文档类型序号
   */
  documentTypeID: number;
  /**
   * 上传人员
   */
  userID: string;
  /**
   * 上传人员
   */
  userName: string;
  /**
   * 文档标题
   */
  documentTitle: string;
  /**
   * 文档封面图片地址
   */
  documentCover?: string;
  /**
   * 文档摘要
   */
  documentAbstract?: string;
  /**
   * 文件扩展名
   */
  filenameExtension: string;
}
/**
 * @description: 文档明细属性数据
 */
declare interface DocumentDetailView {
  /**
   * 明细组号
   */
  groupID?: number;
  /**
   * 明细序号
   */
  itemID: number;
  /**
   * 明细值
   */
  value: string;
}
/**
 * @description: 文档标签
 */
declare interface DocumentTagView {
  /**
   * 文档标签序号
   */
  documentTagListID?: number;
  /**
   * 标签类型
   */
  tagType?: string;
  /**
   * 标签内容
   */
  tagContent?: string;
  /**
   * 标签说明
   */
  tagDescription?: string;
  /**
   * 标签颜色
   */
  tagColor?: string;
  /**
   * 自定义标记
   */
  customFlag?: boolean;
}
