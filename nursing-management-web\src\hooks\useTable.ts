/*
 * FilePath     : \src\hooks\useTable.ts
 * Author       : 苏军志
 * Date         : 2023-12-06 18:29
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-13 08:54
 * CodeIterationRecord: table相关
 */

/**
 * table相关
 */
export function useTable() {
  let spanMap: Record<string, any> = {};
  return {
    /**
     * @description: 组装表格行合并条件
     * @param tableData 表格数据
     * @param mergeFields 要合并列
     *            支持多条件，['name','age','score,name']
     *            说明：1、name列和age列值相同合并
     *                  2、score列的值相同且name值也相同，才合并score列
     * @param mergeNullValue 是否合并空白列
     * @return
     */
    setTableRowSpanArr(tableData: Record<string, any>[], mergeFields: string[], mergeNullValue: boolean = true) {
      if (!tableData.length || !mergeFields?.length) {
        return;
      }
      // 计算合并行的之前，重置spanMap对象
      Object.keys(spanMap).forEach((key) => delete spanMap[key]);
      for (let i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 对于第一个元素，初始化每个指定的合并字段在spanMap对象中的信息
          mergeFields.forEach((column) => {
            const item = column.split(",");
            const key = item[0].split(".")[0];
            spanMap[key] = {
              // spanArr用于存储连续元素的数量，初始值为1，因为至少有一个连续元素
              spanArr: [1],
              // pos表示当前处理的连续元素的位置
              pos: 0
            };
          });
        } else {
          // 对于后续元素，逐个处理指定的合并字段
          mergeFields.forEach((field: string) => {
            const columnArr = field.split(",");
            const key = columnArr[0].split(".")[0];
            const res = columnArr.every((item) => {
              // 根据参数，决定空值是否不合并
              if (!mergeNullValue && (tableData[i][item] === "" || tableData[i - 1][item] === "")) {
                return false;
              }
              // 单元格中可能为各种类型，统一转换为json对比
              return JSON.stringify(tableData[i][item]) === JSON.stringify(tableData[i - 1][item]);
            });
            if (res) {
              // 如果当前元素的指定字段与前一个元素的指定字段相同，表示可以合并
              // 则增加spanArr中对应字段的位置处的值，并将0添加到spanArr表示有另一个连续元素
              spanMap[key].spanArr[spanMap[key].pos] += 1;
              spanMap[key].spanArr.push(0);
            } else {
              // 如果当前元素的指定字段与前一个元素的指定字段不同，表示连续序列中断
              // 则将1添加到spanArr表示开始了一个新的连续元素序列，并更新pos为当前位置
              spanMap[key].spanArr.push(1);
              spanMap[key].pos = i;
            }
          });
        }
      }
      // 输出计算得到的spanMap对象，用于调试和查看结果
      // console.log(spanMap, "spanMap");
    },

    /**
     * 使用此方法时，表格的列必须要有property或prop其中一个属性，否则合并失败
     * @description: 取当前单元格的合并参数
     * @param params 表格相关参数
     * @return
     */
    tableRowSpanMethod(params: any) {
      const { column, rowIndex } = params;
      if (spanMap[column.property]) {
        const rowspan = spanMap[column.property].spanArr[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan,
          colspan
        };
      }
    }
  };
}
