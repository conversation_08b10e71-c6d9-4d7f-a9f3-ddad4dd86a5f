/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hooks\useVisitsAndSupervisionOperations.ts
 * Author       : 马超
 * Date         : 2025-02-18 15:45
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-04-13 10:29
 * Description  :
 * CodeIterationRecord:
 */
import { saveClass } from "../types/hierarchicalSaveView";
import { useQcCommonMethod } from "./useQcCommonMethod";
import type { dynamicFormData, formAttribute } from "zhytech-ui";
const { setSaveDetails, setSavePoint, assemblyRemarksByDetails } = useQcCommonMethod();

export const useVisitsAndSupervisionOperations = (
  saveView: saveClass,
  currentRecord: Ref<Record<string, any> | undefined>,
  currentMaintenance: Ref<Record<string, any> | undefined>
) => {
  const getParentIDPointDetail = () => {
    const groupIDArr = Array.from(new Set(saveView.templateDetails.map((item) => item.groupID)));
    const parentDetails = groupIDArr.map((groupID: string) => {
      const parentScore = saveView.templateDetails
        .filter((item) => item.groupID === groupID && item.value.score !== "-1")
        .reduce((acc, item) => acc + Number(item.value.score), 0);
      return {
        itemID: groupID,
        groupID: undefined,
        parentID: undefined,
        value: String(parentScore)
      };
    });
    return parentDetails;
  };
  const qcFormDom = useTemplateRef<Record<string, any>>("qcFormDom");
  const formData = ref<dynamicFormData<formAttribute>>({
    props: {} as formAttribute,
    datas: [],
    components: []
  });
  /**
   * @description:保存检核
   * @param saveView
   * @param saveFlag
   * @return
   */
  const checkRequireCondition = async (saveView: Record<string, any>, saveType: string) => {
    if ((saveView?.details ?? []).length === 0) {
      showMessage("warning", "没有数据变动，无法保存数据");
      return false;
    }
    // 暂存不检核
    if (saveType === "T") {
      return true;
    }
    if (!saveView.details.find((item: any) => item.value !== "-1")) {
      showMessage("warning", "全部选择不考评无法保存！");
      return false;
    }
    let checkFlag: boolean = true;
    if (qcFormDom.value) {
      await qcFormDom.value.rendererForm.validate((flag: boolean) => {
        checkFlag = flag;
      });
    }
    return checkFlag;
  };
  /**
   * @description: 组装访视质控记录保存数据
   * @param saveType
   * @return
   */
  const setQcMainAndDetail = (saveType: string) => {
    return {
      qcMain: {
        status: saveType,
        hierarchicalQCRecordID: currentMaintenance.value?.hierarchicalQCRecordID,
        point: saveView.point,
        guidance: saveView.guidance,
        improvement: saveView.improvement,
        hierarchicalQCMainID: saveView.hierarchicalQCMainID,
        examineDate: saveView.qcDate,
        departmentID: saveView.qcDepartmentID
      },
      qcDetails: saveView.details,
      qcRecordID: currentMaintenance.value?.hierarchicalQCRecordID,
      qcSubjectID: currentRecord.value?.hierarchicalQCSubjectID
    };
  };
  /**
   * @description: 获取质控内容点选数据
   * @param data
   * @param fileList
   * @return
   */
  const getDetails = (data: Record<string, any>[], fileList: Record<string, any>[]) => {
    saveView.templateDetails = data;
    saveView.templateFileList = fileList;
    setSavePoint(saveView, formData.value!);
    assemblyGuidanceByRemarks();
  };
  // 记录用户自己录入信息，解决修改回显问题
  const userAddGuidance = ref<string>();

  /**
   * @description: 组装备注内容及用户自己填写内容
   */
  const assemblyGuidanceByRemarks = () => {
    const remarks = assemblyRemarksByDetails(formData.value!.datas);
    if (!remarks) {
      return;
    }
    // 如果当前guidance包含remarks,说明用户添加了额外内容
    // 提取用户添加的内容并保存
    if (saveView.guidance.includes(remarks)) {
      userAddGuidance.value = saveView.guidance.replace(remarks, "").trim();
    }
    // 组装最终的guidance内容
    saveView.guidance = userAddGuidance.value ? `${remarks} ${userAddGuidance.value}` : remarks;
  };
  /**
   * @description: 组装保存明细
   */
  const getSaveDetails = () => {
    setSaveDetails(saveView);
    if ((saveView.details?.length ?? 0) === 0) {
      return;
    }
    // 合并分组明细
    saveView.details = [...saveView.details, ...getParentIDPointDetail()];
  };
  const save = async (saveType: string, callBack: (qcMainAndDetail: Record<string, any>) => void) => {
    getSaveDetails();
    if (!(await checkRequireCondition(saveView, saveType))) {
      return;
    }
    callBack(setQcMainAndDetail(saveType));
  };
  const record = ref<Record<string, any>>();
  const details = ref<Record<string, any>[]>();
  const add = async (row: Record<string, any>, addFlag: boolean, recordType: string | undefined) => {
    // 置空用户添加的额外内容
    userAddGuidance.value = undefined;
    details.value = [];
    currentRecord.value = row;
    saveView.qcDate = datetimeUtil.getNowDate("yyyy-MM-dd");
    saveView.guidance = "";
    saveView.hierarchicalQCMainID = "";
    saveView.hierarchicalQCRecordID = "";
    saveView.qcDepartmentID = row.nmDepartmentID;
    addFlag && setRecordAndDetail(recordType);
  };
  /**
   * @description:组装待保存患者信息
   */
  const setRecordAndDetail = (recordType: string | undefined) => {
    if (!currentRecord.value) {
      record.value = undefined;
      details.value = undefined;
      return;
    }
    record.value = {
      caseNumber: currentRecord.value.caseNumber,
      chartNo: currentRecord.value.chartNo,
      patientName: currentRecord.value.patientName,
      gender: currentRecord.value.gender,
      age: currentRecord.value.age,
      admissionDateTime: currentRecord.value.admissionDateTime,
      dischargeDateTime: currentRecord.value.dischargeDateTime,
      stationID: currentRecord.value?.stationID ?? 0,
      bedNumber: currentRecord.value.bedNumber,
      sourceType: currentRecord.value.sourceType,
      sourceID: currentRecord.value.sourceID,
      relatedTableName: "HierarchicalQCRecord",
      relatedTableRecordID: "",
      profileID: currentRecord.value.profileID
    };
    if (recordType) {
      record.value.recordType = recordType;
      record.value.occurDateTime = recordType === "1" ? currentRecord.value.tubeStartDateTime : currentRecord.value.riskAssessTime; // recordType === "1" 中的1为导管类型,其余类型为风险类型
    } else {
      record.value.occurDateTime = currentRecord.value.startDateTime;
    }
    details.value = currentRecord.value.details;
  };

  const modify = async (row: Record<string, any>, callBack: () => Promise<dynamicFormData<formAttribute>>) => {
    // 置空用户添加的额外内容
    userAddGuidance.value = undefined;
    // 督导记录修改 患者信息数据不变动
    details.value = undefined;
    record.value = {
      relatedTableRecordID: row.hierarchicalQCRecordID
    };
    // 初始化修改内容
    saveView.qcDate = row.visitDateTime ? datetimeUtil.formatDate(row.visitDateTime, "yyyy-MM-dd") : datetimeUtil.getNowDate("yyyy-MM-dd");
    saveView.qcDepartmentID = row.qcDepartmentID;
    saveView.hierarchicalQCMainID = row.hierarchicalQCMainID;
    saveView.hierarchicalQCRecordID = row.hierarchicalQCRecordID;
    saveView.guidance = row?.guidance ?? "";
    saveView.improvementShowFlag = false;
    saveView.improvement = row?.improvement ?? "";
    initFormData();
    const res = await callBack();
    if (res) {
      formData.value = res;
    }
  };
  /**
   * @description:初始化模板，避免不同记录初始化时的污染
   */
  const initFormData = () => {
    formData.value = {
      props: {} as formAttribute,
      datas: [],
      components: []
    };
  };
  return {
    record,
    details,
    formData,
    getDetails,
    save,
    add,
    modify,
    initFormData
  };
};
