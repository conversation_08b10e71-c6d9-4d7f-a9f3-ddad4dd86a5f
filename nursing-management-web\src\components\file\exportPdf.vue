<!--
 * FilePath     : \src\components\file\exportPdf.vue
 * Author       : 郭鹏超
 * Date         : 2023-10-17 14:56
 * LastEditors  : 苏军志
 * LastEditTime : 2025-01-07 11:53
 * Description  : table数据导入excel文件
                : jspdf会导致项目打包失败，原因未知，先屏蔽 sjz 2025-01-07
 * CodeIterationRecord:
-->

<template>
  <div class="export-excel">
    <!-- <el-button type="primary" @click="exportPdf" class="download-button iconfont icon-download-fill">{{
      exportPdfOption?.buttonName ?? "生成PDF文件"
    }}</el-button> -->
  </div>
</template>

<script lang="ts" setup>
// import html2Canvas from "html2canvas";
// import { jsPDF } from "jspdf";
// const props = defineProps({
//   exportPdfOption: {
//     type: Object as () => ExportPdfView,
//     default: () => {
//       return {};
//     }
//   }
// });
// const exportPdf = () => {
//   if (!props.exportPdfOption.element || !props.exportPdfOption.fileName) {
//     showMessage("error", "导出PDF失败！");
//     return;
//   }
//   setTimeout(() => {
//     html2Canvas(props?.exportPdfOption?.element).then((canvas: HTMLCanvasElement) => {
//       let contentWidth = canvas.width;
//       let contentHeight = canvas.height;
//       // 一页pdf显示html页面生成的canvas高度;
//       let pageHeight = (contentWidth / 592.28) * 841.89;
//       // 未生成pdf的html页面高度
//       let leftHeight = contentHeight;
//       // 页面偏移
//       let position = 0;
//       // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高
//       let imgWidth = 595.28;
//       let imgHeight = (592.28 / contentWidth) * contentHeight;
//       let pageData = canvas.toDataURL("image/jpeg", 1.0);
//       let pdf = new jsPDF("p", "pt", "a4");
//       // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
//       // 当内容未超过pdf一页显示的范围，无需分页
//       if (leftHeight < pageHeight) {
//         pdf.addImage(pageData, "JPEG", 0, 0, imgWidth, imgHeight);
//       } else {
//         while (leftHeight > 0) {
//           pdf.addImage(pageData, "JPEG", 0, position, imgWidth, imgHeight);
//           leftHeight -= pageHeight;
//           position -= 841.89;
//           // 避免添加空白页
//           if (leftHeight > 0) {
//             pdf.addPage();
//           }
//         }
//       }
//       pdf.save(props.exportPdfOption.fileName + ".pdf");
//     });
//   }, 0);
// };
</script>

<style lang="scss">
.export-excel {
  display: inline-block;
}
</style>
