/*
 * FilePath     : \src\api\signUpRecordService.ts
 * Author       : 张现忠
 * Date         : 2024-07-14 10:41
 * LastEditors  : 张现忠
 * LastEditTime : 2024-07-14 10:41
 * Description  : 实现报名记录相关接口请求
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class signUpRecordService {
  // 管理报名记录相关接口的基本URL
  private static controllerBaseUrl: string = "/SignUpRecord";
  // 获取报名记录列表的API接口URL
  private static getSignUpRecordListApi: string = this.controllerBaseUrl + "/GetSignUpRecordList";
  // 保存报名记录的API接口URL
  private static saveSignUpRecordApi: string = this.controllerBaseUrl + "/SaveSignUpRecord";
  // 删除报名记录的API接口URL
  private static deleteSignUpRecordApi: string = this.controllerBaseUrl + "/DeleteSignUpRecord";
  /**
   * 获取报名记录列表
   * @param params 请求参数
   * @returns 返回报名记录列表数据
   */
  public static async getSignUpRecordList(params: any): Promise<any> {
    return await http.get(this.getSignUpRecordListApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * 保存报名记录
   * @param params 报名记录信息
   * @returns 返回保存结果
   */
  public static async saveSignUpRecord(params: any): Promise<any> {
    return await http.post(this.saveSignUpRecordApi, params, { loadingText: Loading.SAVE });
  }

  /**
   * 删除报名记录
   * @param params 待删除的报名记录ID
   * @returns 返回删除结果
   */
  public static async deleteSignUpRecord(params: any): Promise<any> {
    return await http.post(this.deleteSignUpRecordApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }

}
