/*
 * FilePath     : /src/views/annualPlan/maintain/hooks/useAnnualPlanProjectActions.ts
 * Author       : 杨欣欣
 * Date         : 2025-05-08 09:27
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-02 20:26
 * Description  : 年度计划目标任务状态与行为
 * CodeIterationRecord: 
 */
import { deleteConfirm, showMessage } from "@/utils/message";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import type { planGroup, planProject } from "../../types/annualPlanMain";
import type { MaybeRefOrGetter } from "vue";
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";
import { min } from "lodash-es";

export default function useAnnualPlanProjectActions(
  calculateNewSortByParent: <T, P extends { sort: number }>(
    array: { [key: string]: T[] } | T[],
    parentItems: MaybeRefOrGetter<P[]>,
    groupKey: keyof P & keyof T
  ) => Map<string, number>,
  convertToObject: <S extends { sort: number }>(items: { [key: string]: S[] } | S[], itemKey: keyof S) => Record<string, number>
) {
  const planManagementStore = usePlanManagementStore();
  const maintainStore = useAnnualPlanMaintainStore();
  const projectNewSortByGroup = computed(() =>
    calculateNewSortByParent(maintainStore.projectsByGroupID, maintainStore.planGroups, "groupID")
  );

  maintainStore.$onAction(({ name, after }) => {
    if (name === "addUnExpandedPlanGoalIDs") {
      after(async(result) => {
        if (!result) {
          return;
        }
        const params = {
          mainID: planManagementStore.annualPlanMainID,
          mainGoalIDs: result
        };
        await setPlanProjects(params);
      });
    }
  });

  /**
   * @description: 获取并设置完整的目标任务数据
   * @param params 参数
   * @return 
   */
  const setPlanProjects = async (params: { mainID: string; mainGoalIDs: string[] }) => {
    // 获取完整的planProjects
    const resultArr = await annualPlanMainService.getProjectDetails(params);
    maintainStore.$patch(() => {
      maintainStore.planGroups
        .filter((group) => params.mainGoalIDs.includes(group.mainGoalID))
        .forEach((group) => {
          maintainStore.projectsByGroupID[group.groupID] = resultArr.filter((planProject) => planProject.groupID === group.groupID);
        });
    });
  };

  return {
    /**
     * @description: 新增目标任务到目标分组
     * @param newProject 新目标任务
     * @return 
     */
    addNewPlanProjectToPlanGroup: async (newProject: planProject) => {
      // 获取新项目的序号
      const newSort = projectNewSortByGroup.value.get(newProject.groupID);
      if (newSort === undefined) {
        showMessage("error", "新增目标任务失败：无法计算序号");
        throw new Error(`新增目标任务失败：无法计算序号，groupID: ${newProject.groupID}`);
      }
      newProject.sort = newSort;
      // 更新后续项目的序号
      maintainStore.planGroups.forEach((group) => {
        const groupProjects = maintainStore.projectsByGroupID[group.groupID];
        if (groupProjects) {
          groupProjects.filter((project) => project.sort >= newSort).forEach((project) => (project.sort += 1));
        }
      });
      // 添加到分组
      maintainStore.projectsByGroupID[newProject.groupID].push(newProject);

      // 持久化变更
      newProject.detailID = await annualPlanMainService.addProjectDetail(newProject);
      return true;
    },
    /**
     * @description: 更新目标任务
     * @param updateProject 待更新的目标任务
     * @return 
     */
    updatePlanProject: async (updateProject: planProject) => {
      const result = await annualPlanMainService.updateProjectDetail(updateProject);
      return result;
    },
    /**
     * @description: 重新计算目标任务的Sort
     * @param draggedProject 被拖拽的目标任务
     * @param sourcePlanGroup 当前所在分组
     * @return 
     */
    resetPlanProjectSort: async (draggedProject: planProject, sourcePlanGroup: planGroup | undefined = undefined) => {
      // 如果有源分组，说明是跨分组拖拽，需要更新项目的分组ID和目标ID
      let isInGroup = true;
      if (sourcePlanGroup) {
        isInGroup = false;
        draggedProject.mainGoalID = sourcePlanGroup.mainGoalID;
        draggedProject.groupID = sourcePlanGroup.groupID;
      }
      if (isInGroup) {
        const groupID = draggedProject.groupID;
        const groupProjects = maintainStore.projectsByGroupID[groupID];
        let startSort = min(groupProjects.map(({ sort }) => sort))!;
        maintainStore.projectsByGroupID[groupID].forEach((planProject) => {
          startSort !== planProject.sort && (planProject.sort = startSort);
          startSort += 1;
        });
      } else {
        // 重新计算所有目标任务的序号
        let projectSort = 0;
        maintainStore.planGroups.forEach((group) => {
          // 此处使用可空调用，因为策略目标详情为懒加载，此时详情可能未渲染，会导致projectsByGroupID还未产生对应映射
          maintainStore.projectsByGroupID[group.groupID]?.forEach((planProject) => {
            projectSort += 1;
            projectSort !== planProject.sort && (planProject.sort = projectSort);
          });
        });
      }

      // 持久化变更
      const params = {
        mainID: planManagementStore.annualPlanMainID,
        draggedPlanProject: draggedProject,
        planProjectIDAndSort: convertToObject(maintainStore.projectsByGroupID, "detailID")
      };
      await annualPlanMainService.resetAnnualPlanProjectsSort(params);
    },
    /**
     * @description: 删除目标任务
     * @param deleteProject 待删除的目标任务
     * @return 
     */
    deletePlanProject: async (deleteProject: planProject) => {
      await deleteConfirm("确定要删除么？", async (flag: Boolean) => {
        if (!flag) {
          return;
        }

        // 从数据中移除项目
        const targetGroupProjects = maintainStore.projectsByGroupID[deleteProject.groupID];
        const deleteIndex = targetGroupProjects.findIndex((project) => project.detailID === deleteProject.detailID);
        if (deleteIndex === -1) {
          showMessage("error", "删除目标任务失败：未找到目标任务");
          return;
        }
        targetGroupProjects.splice(deleteIndex, 1);

        // 更新被删除目标任务之后的所有目标任务序号
        maintainStore.planGroups.forEach((group) => {
          const groupProjects = maintainStore.projectsByGroupID[group.groupID];
          if (groupProjects) {
            groupProjects.filter((project) => project.sort > deleteProject.sort).forEach((project) => (project.sort -= 1));
          }
        });

        // 持久化变更
        const params = {
          mainID: planManagementStore.annualPlanMainID,
          detailID: deleteProject.detailID
        };
        const result = await annualPlanMainService.deleteProjectDetail(params);
        if (!result) {
          showMessage("error", "删除目标任务失败");
        }
      });
    }
  };
}
