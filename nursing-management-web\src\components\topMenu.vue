<!--
 * FilePath     : \src\components\topMenu.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:32
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-29 18:40
 * Description  : 岗位相关
 * CodeIterationRecord:
-->
<template>
  <base-layout class="top-menu" headerHeight="auto" :showHeader="newMenuList.length > 1">
    <template #header>
      <el-tabs v-model="activeName" @tab-change="changeMenu" :before-leave="beforeLeave">
        <template v-for="(menu, index) in newMenuList" :key="index">
          <el-tab-pane :label="menu.menuName" :name="index"></el-tab-pane>
        </template>
      </el-tabs>
    </template>
    <div class="router-view-wrap">
      <router-view v-slot="{ Component }">
        <!-- 不需要缓存的路由加载位置 -->
        <component ref="childPage" :is="Component" v-if="!route.meta.keepAlive && isRouterAlive" />
        <!-- 需要缓存的路由加载位置 -->
        <keep-alive>
          <component ref="childPage" :is="Component" v-if="route.meta.keepAlive && isRouterAlive" />
        </keep-alive>
      </router-view>
    </div>
  </base-layout>
</template>
<script setup lang="ts">
const router = useRouter();
const route = useRoute();

interface menu {
  menuName: string;
  routerName: string;
  routerPath: string;
}
const props = defineProps({
  menuList: {
    type: Array<menu>,
    default: () => {
      return [];
    }
  },
  routerQuery: {
    type: Object,
    default: () => {}
  },
  beforeLeave: {
    type: Function,
    default: () => {}
  }
});
// 路由是否存活，用于刷新页面
const isRouterAlive = ref(true);
const childPage = ref<any>();
const newMenuList = ref<menu[]>([]);
let routePath = "";
// 页面初始化时默认加载的路由名称
let activeName = ref<number>();
/**
 * @description: 页签改变
 * @param paneName 当前选择页签
 */
const changeMenu = (name:any) => {
  const paneName = typeof name === "object" ? name.paneName : name;
  let menu = newMenuList.value[paneName];
  let path = menu.routerPath;
  // #region 临时判断写死，防止没有开发的菜单因无路由报错问题
  if (!path) {
    path = "/developing";
  }
  // #endregion
  router.replace({
    path: path,
    query: props.routerQuery
  });
};
const getMenuIndex = () => {
  routePath = route.path;
  newMenuList.value = props.menuList;
  return newMenuList.value.findIndex((menu) => menu.routerPath === routePath);
};
// 监听路由变化,重新赋值activeName
watch(
  () => route.path,
  () => {
    let index = getMenuIndex();
    activeName.value = index > 0 ? index : 0;
  },
  { immediate: true, deep: true }
);
// 监听菜单 处理路由相同 菜单不同 显示异常的问题
watch(
  () => props.menuList,
  () => {
    let index = getMenuIndex();
    if (index === -1) {
      changeMenu({ paneName: activeName.value });
    }
  },
  { immediate: true, deep: true }
);

// 暴漏给父组件
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    // 如果子页面有刷新，则调用子页面的刷新
    if (childPage?.value && childPage?.value.refreshData) {
      childPage?.value.refreshData();
      return;
    }
    // 控制router-view的显示或隐藏，从而控制页面的再次加载,模拟刷新页面操作。
    isRouterAlive.value = false;
    nextTick(() => {
      isRouterAlive.value = true;
    });
  }
});
</script>
<style lang="scss">
.top-menu {
  > .base-header {
    margin-bottom: 0;
    padding: 5px 20px 0 20px;
    .el-tabs__header {
      margin: 0 0 10px 0;
    }
  }
}
</style>
