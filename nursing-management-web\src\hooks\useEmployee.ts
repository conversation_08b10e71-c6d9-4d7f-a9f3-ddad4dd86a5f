/*
 * FilePath     : \src\hooks\useEmployee.ts
 * Author       : 苏军志
 * Date         : 2023-11-08 15:13
 * LastEditors  : 苏军志
 * LastEditTime : 2023-11-19 15:42
 * Description  : 人员相关方法hooks
 * CodeIterationRecord:
 */

export function useEmployee() {
  return {
    /**
     * @description: 获取员工照片
     * @param employeeID 员工编号
     * @return 员工照片地址
     */
    getEmployeePhoto(employeeID: string) {
      // 测试账号直接返回内置照片
      if (employeeID === "111111") {
        return "/static/images/employeePhoto.jpg";
      }
      let photoUrl = "http://www.honliv.com.cn/lkoa/HRM/rsda/photo";
      if (common.storage("isInnerServer")) {
        photoUrl = "http://************/lkoa/hrm/rsda/photo";
      }
      return `${photoUrl}/${employeeID}.jpg`;
    }
  };
}
