<!--
 * FilePath     : \src\views\examineManagement\examineQuestion\index.vue
 * Author       : 张现忠
 * Date         : 2024-06-12 16:29
 * LastEditors  : 张现忠
 * LastEditTime : 2025-06-26 17:32
 * Description  : 考核题目管理页面
 * CodeIterationRecord: 4572-作为考核管理人员，我需要可以通过Excel批量导入题目，以利快速维护试卷题目(14)
 -->

<template>
  <base-layout class="examine-question" :drawerOptions="drawerOptions">
    <template #header>
      <question-bank-selector
        v-model="questionBankID"
        :disabled="disableComponentFlag"
        :width="500"
        filterable
        :props="{
          expandTrigger: 'hover',
          emitPath: true,
          checkStrictly: true
        }"
        @select="onSelect"
      ></question-bank-selector>
      <el-button class="right-button edit-button" v-permission:B="1" @click="cloneQuestionBank()">复制题库</el-button>
      <file class="excel-file-operate" :fileOption="fileOption" @getExcelData="importQuestionsFromExcel"></file>
      <el-button class="add-button" v-permission:B="1" @click="addOrSaveRecord()">新增</el-button>
    </template>
    <el-table
      :data="questionList"
      border
      stripe
      height="100%"
      class="questionTable"
      :row-key="(row : questionView) => row.examinationQuestionID +'-' +row.sort"
      v-dragSort="{ el: '.questionTable .el-table__body-wrapper tbody', callBack: dragEnd }"
    >
      <el-table-column :min-width="convertPX(300)" :label="isPractical ? '考核项目' : '题目名称'" prop="questionContent"></el-table-column>
      <template v-if="!isPractical">
        <el-table-column :width="convertPX(160)" label="题目难度" prop="difficultyLevelName" align="center"></el-table-column>
        <el-table-column :min-width="convertPX(160)" label="题目标签" align="center">
          <template #default="scope">
            <el-tag class="question-tag" v-for="(item, index) in scope.row.questionTagName" :key="index" type="success">{{ item }}</el-tag>
          </template>
        </el-table-column>
      </template>
      <el-table-column v-if="isPractical" :width="convertPX(100)" label="分值" prop="score" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" label="题目类型" prop="examinationQuestionTypeName" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" label="修改人" prop="modifyEmployeeName" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="修改时间" prop="modifyDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(80)" align="center">
        <template #default="scope">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" v-permission:B="3" @click="addOrSaveRecord(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteRecord(scope.row.examinationQuestionID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName === 'addQuestion'"
        class="question-form"
        ref="submitRefs"
        :label-width="convertPX(150)"
        :model="drawerData"
        :rules="rules"
      >
        <el-form-item label="题目名称：" prop="questionContent">
          <el-input v-model="drawerData.questionContent" placeholder="请输入题目名称" type="textarea" autosize></el-input>
        </el-form-item>
        <el-form-item label="题目类型：" prop="examinationQuestionType">
          <el-select v-model="drawerData.examinationQuestionType" placeholder="请选择题目类型" class="select">
            <el-option v-for="(item, index) in filterQuestionTypeSetting" :key="index" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="isPractical" label="题目分值：" prop="score">
          <el-input-number v-model="drawerData.score" :min="1" placeholder="请输入题目分值" class="select"> </el-input-number>
        </el-form-item>
        <!-- 非实操类才填写 -->
        <template v-if="!isPractical">
          <el-form-item
            v-if="drawerData.examinationQuestionType"
            :label="drawerData.examinationQuestionType == 'ShortAnswer' ? '关键字：' : '题目明细：'"
            props="questionDetail"
          >
            <item-detail-list
              :itemType="drawerData.examinationQuestionType"
              v-model="drawerData.questionDetail"
              :itemID="drawerData.examinationQuestionID"
              :defaultAnswerOptionCount="4"
              @change="changeDetail"
              dragFlag
            ></item-detail-list>
          </el-form-item>
          <el-form-item label="题目难度：" prop="difficultyLevel">
            <el-select v-model="drawerData.difficultyLevel" placeholder="请选择题目难度" class="select">
              <el-option v-for="(item, index) in difficultyLevelSetting" :key="index" :label="item.value" :value="item.key"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="题目标签：" prop="questionTag">
            <el-cascader
              v-model="drawerData.questionTagArr"
              :options="questionTagSetting"
              :props="{ expandTrigger: 'hover', multiple: true, emitPath: false, checkStrictly: true }"
              :collapse-tags="true"
              :show-all-levels="false"
              class="select"
            />
          </el-form-item>
          <el-form-item label="题目说明：" prop="instructions">
            <el-input v-model="drawerData.instructions" placeholder="请输入题目说明" type="textarea" autosize></el-input>
          </el-form-item>
          <el-form-item label="题目解析：" prop="analysis">
            <el-input v-model="drawerData.analysis" placeholder="请输入题目解析" type="textarea" autosize></el-input>
          </el-form-item>
        </template>
      </el-form>
      <el-table
        v-if="drawerOptions.drawerName === 'cloneQuestionBank'"
        ref="tableRef"
        :data="questionBankList"
        border
        stripe
        height="100%"
        class="questionTable"
      >
        <el-table-column type="selection" />
        <el-table-column label="题库名称" prop="content"></el-table-column>
      </el-table>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入
const convertPX: any = inject("convertPX");
import useImportQuestions from "./hooks/useImportQuestions";
import type { questionView } from "../types/questionView";
type drawerDataType = Omit<questionView, "questionBankID"> & { questionBankID: string | undefined };
// #endregion

// #region 响应式变量
const tagDict = ref<Record<string, any>>({});
const { fileOption, importQuestions, toggleExcelFileOptionByType, transferJsonToSaveParams, setTheoreticalExportTagTableData } =
  useImportQuestions(tagDict);
const { getSettingDictionaryByCodeValue, getCascaderSettingDictionary } = useDictionaryData();
// 题库选择器不可选
const disableComponentFlag = ref<boolean>(false);
// 题目列表
const questionList = ref<Array<questionView>>([]);
const questionBankList = ref<Record<string, any>>([]);
const tableRef = ref<Record<string, any>>([]);
// 选择的题库信息
const questionBankID = ref<string>();
const isPractical = ref<boolean>(false);
const submitRefs = ref<any>();
const sortableObj = ref<any>();
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    if (drawerOptions.value.drawerName === "cloneQuestionBank") {
      saveCloneQuestionBank();
      return;
    }
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveRecord();
  }
});
const drawerData = ref<drawerDataType>({
  questionContent: "",
  examinationQuestionType: "",
  difficultyLevel: "",
  questionTag: "",
  instructions: "",
  analysis: "",
  questionBankID: questionBankID.value,
  questionTagArr: [],
  score: undefined,
  sort: 1
});
// 规则验证rule
const rules = {
  questionContent: [
    {
      required: true,
      message: "请输入题目名称",
      trigger: "change"
    }
  ],
  examinationQuestionType: [
    {
      required: true,
      message: "请选择题目类型",
      trigger: "change"
    }
  ],
  difficultyLevel: [
    {
      required: true,
      message: "请选择题目难度",
      trigger: "change"
    }
  ],
  score: [
    {
      required: true,
      message: "请输入题目分值",
      trigger: "change"
    }
  ]
};
// setting配置
const difficultyLevelSetting = ref<Array<Record<string, any>>>([]);
const questionTagSetting = ref<Array<CascaderList<string>>>([]);
const questionTypeSetting = ref<Array<Record<string, any>>>([]);
// 根据题库类别过滤题目类型 - 非实操类 不显示评分题
const filterQuestionTypeSetting = computed(() => {
  return questionTypeSetting.value.filter((item) => {
    return !isPractical.value ? item.key !== "Scoring" : item.key === "Scoring";
  });
});
// #endregion

//#region 初始化
const props = defineProps({
  propQuestionBankID: {
    type: String,
    default: ""
  },
  propIsPractical: {
    type: Boolean,
    default: false
  }
});

onMounted(async () => {
  if (props.propQuestionBankID) {
    disableComponentFlag.value = true;
    questionBankID.value = props.propQuestionBankID;
    isPractical.value = props.propIsPractical;
  }
  getQuestionList();
  await getDifficultyLevelAndTypeSetting();
  // 设置tag标签sheet数据
  isPractical.value || setTheoreticalExportTagTableData(fileOption, tagDict.value);
  getQuestionTagSetting();
});

watch(questionBankID, () => {
  getQuestionList();
});

watch(isPractical, () => {
  toggleExcelFileOptionByType(isPractical.value);
});
// #endregion

//#region 业务数据
/**
 * @description: 根据选中的题库信息修改是否是实操类
 * @param item 题库信息
 * @return
 */
const onSelect = (item: Record<string, any>) => {
  isPractical.value = item.isPractical;
};

/**
 * @description 根据题库ID获取题目列表
 * @returns
 */
const getQuestionList = () => {
  if (!questionBankID.value) {
    return;
  }
  let params = {
    questionBankID: questionBankID.value
  };
  examineService.getQuestionsByBankID(params).then((resp: any) => {
    if (resp) {
      questionList.value = sortOrUpdateQuestionSort(resp);
      return;
    }
    questionList.value = [];
  });
};
/**
 * @description：对题目进行排序处理 如果存在排序混乱问题，更新排序
 * @param {Array} questions 题目列表
 * @returns
 */
const sortOrUpdateQuestionSort = (questions: questionView[]) => {
  sortByKeys(questions, ["sort"]);
  let reSort: Record<string, any>[] = [];
  questions.forEach((item: Record<string, any>, index: number) => {
    if (index + 1 !== item.sort) {
      item.sort = index + 1;
      reSort.push({ questionID: item.examinationQuestionID, sort: item.sort });
    }
  });
  if (reSort.length) {
    examineService.updateQuestionSort(reSort);
  }
  return questions;
};
/**
 * @description：从excel文件中导入题目
 * @param excelRows 导入的excel数据
 * @returns {Promise<void>}
 */
const importQuestionsFromExcel = (importExcelReturnData: ImportExcelReturnView): void => {
  // 检查当前导入的主-题库
  if (!questionBankID.value) {
    showMessage("error", "请选择导入的题库");
    return;
  }
  // 转换数据成保存参数
  const transferParams = transferJsonToSaveParams(importExcelReturnData, isPractical.value);
  // 检查是否有数据需要导入
  if (!transferParams || (!transferParams?.noHierarchyQuestions?.length && !transferParams.bankAndQuestionTree?.length)) {
    showMessage("error", "无需要导入的数据，请检查文档");
    return;
  }
  // 导入提示信息
  let tip = "是否确定导入题库数据？";
  if (transferParams?.allTips) {
    tip = transferParams.tip + `<br/><b>${tip}</b>`;
  }
  confirmBox(tip, "导入考核题库数据", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let params = {
      questionList: transferParams.noHierarchyQuestions,
      questionBankID: questionBankID.value,
      BankAndQuestionTree: transferParams?.bankAndQuestionTree
    };
    let importSuccess = await importQuestions(params);
    if (importSuccess) {
      showMessage("success", "导入成功:请到题库页面查看导入情况");
    }
    getQuestionList();
  });
};
// 当前题目在当前题库中的最大排序
const maxSort = computed(() => {
  return questionList.value?.length ? Math.max(...questionList.value.map((item: Record<string, any>) => item.sort)) || 0 : 0;
});
/**
 * @description：新增编辑方法
 * @param row 行数据
 */
const addOrSaveRecord = (row?: questionView) => {
  if (row) {
    drawerData.value = common.clone(row);
  } else {
    drawerData.value = {
      modifyDateTime: new Date(),
      questionBankID: questionBankID.value,
      sort: maxSort.value + 1,
      // 实操类题目 -新增默认评分题目
      examinationQuestionType: isPractical.value ? "Scoring" : undefined
    } as drawerDataType;
  }
  if (drawerData.value.difficultyLevel === "0") {
    drawerData.value.difficultyLevel = "";
  }
  // 打开抽屉
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerName = "addQuestion";
  drawerOptions.value.drawerTitle = `${row ? "编辑" : "新增"}题目数据`;
};
/**
 * @description: 保存主记录
 */
const saveRecord = () => {
  if (!drawerData.value.questionDetail?.length && !isPractical.value) {
    showMessage("warning", drawerData.value.examinationQuestionType === "ShortAnswer" ? "请维护关键字" : "请维护题目明细");
    return;
  }
  const answerCount = drawerData.value.questionDetail?.filter((detail) => detail.selectFlag)?.length ?? 0;
  if (drawerData.value.examinationQuestionType === "MultipleChoice" && answerCount < 2) {
    showMessage("warning", "多选题正确答案至少需要两个选项");
    return;
  } else if (drawerData.value.questionDetail?.length && answerCount < 1) {
    showMessage("warning", "题目至少要有一个正确答案");
    return;
  }
  examineService.saveQuestionData(drawerData.value).then((res) => {
    if (res) {
      showMessage("success", "保存成功");
      drawerOptions.value.showDrawer = false;
      getQuestionList();
    }
  });
};
/**
 * @description：删除题目数据
 * @param questionID
 */
const deleteRecord = (questionID: number) => {
  confirmBox("是否删除题目数据？", "删除考核题目数据", (flag: Boolean) => {
    if (flag) {
      if (questionID) {
        let params = {
          questionID: questionID
        };
        examineService.deleteQuestion(params).then((res) => {
          if (res) {
            showMessage("success", "删除成功");
            getQuestionList();
          }
        });
      }
    }
  });
};
/**
 * @description：获取题目题目难度，题目类型配置
 */
const getDifficultyLevelAndTypeSetting = async () => {
  let params = {
    settingTypeCode: "ExaminationQuestion",
    settingTypeValues: ["QuestionTag", "DifficultyLevel", "ExaminationQuestionType"]
  };
  await getSettingDictionaryByCodeValue(params).then((dicts: any) => {
    if (dicts) {
      difficultyLevelSetting.value = dicts["DifficultyLevel"];
      questionTypeSetting.value = dicts["ExaminationQuestionType"];
      tagDict.value = dicts;
    }
  });
};
/**
 * @description：获取题目标签配置
 */
const getQuestionTagSetting = () => {
  let params = {
    settingTypeCode: "ExaminationQuestion",
    settingTypeValues: JSON.stringify(["QuestionTag"])
  };
  getCascaderSettingDictionary(params).then((dicts: any) => {
    if (dicts) {
      questionTagSetting.value = dicts["QuestionTag"];
    }
  });
};
/**
 * @description：题目明细数据变化
 * @param value
 */
const changeDetail = (value: any) => {
  drawerData.value.questionDetail = value;
};
// #endregion

//#region 拖拽排序
onBeforeUnmount(() => {
  sortableObj.value && sortableObj.value.destroy();
});
/**
 * @description: 分类重排序
 * @param newIndex 新下标
 * @param oldIndex 旧下标
 */
const dragEnd = async (newIndex: number, oldIndex: number) => {
  if (newIndex === oldIndex) {
    return;
  }
  let params: Record<string, any>[] = [];
  // 更新页面数据排序
  let movedQuestion = questionList.value.splice(oldIndex, 1)[0];
  questionList.value.splice(newIndex, 0, movedQuestion);
  questionList.value.forEach((question: Record<string, any>, index: number) => {
    if (question.sort !== index + 1) {
      question.sort = index + 1;
      params.push({
        questionID: question.examinationQuestionID,
        sort: question.sort
      });
    }
  });
  examineService.updateQuestionSort(params).then((res: any) => {
    if (res) {
      showMessage("success", "排序成功");
    }
  });
};
// #endregion
/**
 * @description：复制题库
 */
const cloneQuestionBank = () => {
  drawerOptions.value.drawerName = "cloneQuestionBank";
  examineService.getQuestionBankList().then((res: any) => {
    questionBankList.value = res;
  });
  drawerOptions.value.showDrawer = true;
};
/**
 * @description：保存复制题库
 */
const saveCloneQuestionBank = () => {
  if (!tableRef.value.getSelectionRows().length) {
    showMessage("warning", "请选择需要复制的题库");
    return;
  }
  let params = {
    questionBankID: questionBankID.value,
    cloneQuestionBankIDs: tableRef.value.getSelectionRows().map((item: Record<string, any>) => item.questionBankID)
  };
  examineService.cloneQuestionBank(params).then((res: any) => {
    if (res) {
      showMessage("success", "复制成功");
      drawerOptions.value.showDrawer = false;
      getQuestionList();
    }
  });
};
</script>
<style lang="scss">
.examine-question {
  .base-header {
    .question-bank-select {
      display: inline-block;
      width: 200px;
      margin-left: 20px;
    }
    .excel-file-operate {
      float: right;
    }
  }
  .questionTable {
    .question-tag {
      margin: 0px 2px;
    }
  }
  .question-form {
    .select {
      width: 400px;
    }
  }
}
</style>
