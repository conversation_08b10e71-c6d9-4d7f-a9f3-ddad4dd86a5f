<!--
 * FilePath     : \src\components\baseLayout.vue
 * Author       : 苏军志
 * Date         : 2023-08-10 19:41
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-29 17:48
 * Description  : 基础布局组件
 *              参数：
 *                   headerHeight：头部高度，默认40px，支持auto
 *                   footerHeight：底部高度，默认30px，支持auto
 *                   showHeader：是否显示头部，默认true
 *                   showFooter：是否显示底部，默认false
 *                   drawerOptions：抽屉选项，参考utils\types\drawerOptions.ts接口说明
 *              插槽：
 *                   header：头部插槽
 *                   default：默认插槽
 *                   footer：底部插槽
 *                   drawerContent：抽屉插槽
 *                   drawerOtherFooter：抽屉底部除按钮外其他内容插槽
 * CodeIterationRecord:
-->
<template>
  <div class="base-layout">
    <div v-if="showHeader" class="base-header" :style="headerStyle">
      <slot name="header"></slot>
    </div>
    <div class="base-content">
      <!-- 解决默认slot下直接写el-table时，flex下el-table宽度会无限增长的bug -->
      <div class="base-content-wrap">
        <slot name="default"></slot>
      </div>
    </div>
    <div v-if="showFooter" class="base-footer" :style="footerStyle">
      <slot name="footer"></slot>
    </div>
    <el-drawer
      ref="baseDrawer"
      :class="drawerOption.className"
      v-if="drawerOption"
      :title="drawerOption.drawerTitle"
      v-model="drawerOption.showDrawer"
      :destroy-on-close="true"
      :direction="drawerOption.drawerDirection"
      :size="drawerOption.drawerSize"
      :before-close="(close:Function) => drawerOption.beforeClose?.(close)"
      @close="drawerOption.showDrawer && drawerOption.cancel?.()"
      @opened="drawerOption.showDrawer && drawerOption.opened?.()"
    >
      <slot name="drawerContent"></slot>
      <template #footer>
        <slot name="drawerOtherFooter"></slot>
        <el-button v-if="drawerOption.showCancel" @click="() => drawerOption.cancel?.()">{{ drawerOption.cancelText }}</el-button>
        <el-button v-if="drawerOption.showConfirm" v-permission:B="2" type="primary" @click="drawerOption.confirm?.()">
          {{ drawerOption.confirmText }}
        </el-button>
        <slot name="drawerButtonAfter"></slot>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const props = defineProps({
  headerHeight: {
    type: String,
    default: "60px"
  },
  footerHeight: {
    type: String,
    default: "30px"
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  drawerOptions: {
    type: Object as () => DrawerOptions,
    default: () => {}
  }
});
let baseDrawer = ref<any>();
// 双向绑定
const emits = defineEmits(["update:drawerOptions"]);
let drawerOption = useVModel(props, "drawerOptions", emits);
// 如参数没有传值，初始化默认值
if (drawerOption.value) {
  !drawerOption.value.drawerName && (drawerOption.value.drawerName = "");
  !drawerOption.value.drawerDirection && (drawerOption.value.drawerDirection = "rtl");
  !drawerOption.value.drawerSize && (drawerOption.value.drawerSize = "50%");
  drawerOption.value.showCancel === undefined && (drawerOption.value.showCancel = true);
  drawerOption.value.showConfirm === undefined && (drawerOption.value.showConfirm = true);
  !drawerOption.value.cancelText && (drawerOption.value.cancelText = "取消");
  !drawerOption.value.confirmText && (drawerOption.value.confirmText = "保存");
  !drawerOption.value.beforeClose && (drawerOption.value.beforeClose = (close: Function) => close());
  if (!drawerOption.value.cancel) {
    drawerOption.value.cancel = () => baseDrawer.value?.handleClose();
  } else {
    const cancel = drawerOption.value.cancel;
    drawerOption.value.cancel = () => {
      baseDrawer.value?.handleClose();
      cancel();
    };
  }
}
// 获取头部样式
const headerStyle = computed(() => {
  return getStyle(props.headerHeight);
});
// 获取底部样式
const footerStyle = computed(() => {
  return getStyle(props.footerHeight);
});

const getStyle = (value: string) => {
  let height = convertUtil.convert(value);
  if (Number(height) !== 0) {
    height = `${convertPX(height)}px`;
  }
  let style: any = { height: height };
  if (height !== "auto") {
    style.lineHeight = height;
  }
  return style;
};
</script>

<style lang="scss">
.base-layout {
  height: 100%;
  width: 100%;
  padding: 5px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .base-header {
    line-height: 45px;
    background-color: #ffffff;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 0px 10px;
    .right-button,
    .add-button {
      float: right;
      margin-top: 18px;
    }
    .el-button + .el-button {
      margin-right: 10px;
    }
  }

  .base-content {
    position: relative;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    flex: auto;
    overflow: auto;
    background-color: #ffffff;
    .base-content-wrap {
      position: absolute;
      height: 100%;
      width: 100%;
    }
    /* 如果自己嵌套自己，去掉内层的边距 */
    .base-layout {
      padding: 0;
    }
  }

  .base-footer {
    line-height: 30px;
    background-color: #ffffff;
  }
}
</style>
