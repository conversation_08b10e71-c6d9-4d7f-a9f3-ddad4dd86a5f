/*
 * FilePath     : \src\components\transfer\hooks\useCheck.ts
 * Author       : 杨欣欣
 * Date         : 2024-04-03 16:32
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-12 15:25
 * Description  :
 * CodeIterationRecord:
 */
import { usePropsAlias } from "./usePropsAlias";

export const useCheck = (props: Record<string, any>, panelState: Record<string, any>, modelValue: Ref<Record<string, any>[]>) => {
  const propsAlias = usePropsAlias(props as any);
  // 关键字过滤后的数据
  const filterData = computed({
    get: () => {
      if (!modelValue.value) {
        return [];
      }
      return modelValue.value.filter((item: Record<string, any>) => {
        if (typeof props.filterMethod === "function") {
          return props.filterMethod(panelState.query, item);
        }
        const label = item[propsAlias.value.label] || item[propsAlias.value.key];
        return label.toLowerCase().includes(panelState.query.toLowerCase());
      });
    },
    set: (value) => {
      modelValue.value = value;
    }
  });
  const isIndeterminate = computed(() => panelState.checkedCount > 0 && panelState.checkedCount < filterData.value.length);
  const allChecked = computed({
    get: () => filterData.value.length > 0 && panelState.checkedCount === filterData.value.length,
    set: (value) => panelState.checked = value
  });
  return {
    filterData,
    isIndeterminate,
    allChecked
  };
};
