/*
 * FilePath     : \src\stores\modules\session.ts
 * Author       : 苏军志
 * Date         : 2023-06-05 10:52
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:55
 * Description  : session状态管理
 * CodeIterationRecord:
 */
import { defineStore } from "pinia";

const useSessionStore = defineStore({
  id: "session",
  state: () => {
    return {
      // 本地时间和服务器时间间隔毫秒数
      timeDiff: 0,
      // 登录状态
      isLogin: false,
      // 登录Token
      token: "",
      // 登录参数
      loginParams: {} as any,
      // px转rem基数
      currRemSize: (common.session("session") || {}).currRemSize,
      pageTopMenus: {} as any,
      routerList: [],
      componentList: [],
      currRouterListID: 0,
      breadcrumb: [] as Array<Record<string, any>>,
      externalTransfer: false,
      // 调试模式
      debugMode: false
    };
  },
  actions: {
    /**
     * @description: 切换调试模式
     */
    toggleDebugMode() {
      this.debugMode = !this.debugMode;
    },
    /**
     * @description: 设置调试模式快捷键
     */
    setupDebugModeHotkey() {
      console.log(
        "%c💡 调试模式已初始化完毕%c，按下%cCtrl+Alt+D%c可切换%c sessionStore%c的%c debugMode%c变量值",
        "color: #409EFF; font-weight: bold; font-size: 13px",
        "color: #606266",
        "color: #E6A23C; font-weight: bold; background: #fdf6ec; padding: 2px 6px; border-radius: 4px",
        "color: #606266",
        "color: #67C23A; font-weight: bold; background: #f0f9eb; padding: 2px 6px; border-radius: 4px",
        "color: #606266",
        "color: #409EFF; font-weight: bold; background: #ecf5ff; padding: 2px 6px; border-radius: 4px",
        "color: #606266"
      );
      // eslint-disable-next-line camelcase, @typescript-eslint/naming-convention
      const { Ctrl_Alt_D } = useMagicKeys();
      whenever<true>(Ctrl_Alt_D, () => {
        this.toggleDebugMode();
        const debugStatus = this.debugMode ? "开启" : "关闭";
        console.group("%c🔧 调试模式状态变更", "font-size: 13px; color: #409EFF; font-weight: bold;");
        console.log(
          "%c📌 当前状态%c: %c" + debugStatus,
          "color: #E6A23C",
          "color: #909399",
          `color: ${this.debugMode ? "#67C23A" : "#F56C6C"}; font-weight: bold`
        );
        console.groupEnd();
      });
    }
  },
  // 数据持久化
  persist: {
    enabled: true,
    // 指定存储方式：
    strategies: [{ storage: sessionStorage }]
  }
});

export default useSessionStore;
