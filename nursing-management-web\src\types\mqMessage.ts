/*
 * FilePath     : \nursing-management-web\src\types\mqMessage.ts
 * Author       : 苏军志
 * Date         : 2023-12-06 17:40
 * LastEditors  : 张现忠
 * LastEditTime : 2024-12-14 18:36
 * Description  : MQ消息视图
 * CodeIterationRecord:
 */
declare interface MQMessage {
  /**
   * 消息类型
   */
  Type: keyof typeof MQMessageType;
  /**
   * 消息内容
   */
  Message: string | Record<string, any>;
  /**
   * 如果message为对象，需要依据此消息格式化模板解析消息
   */
  MessageFormatter: string;
  /**
   * 链接
   */
  Link: string;
  /**
   * 客户端类型
   */
  ClientType: number;
  /**
   * 路由参数
   */

  UrlParams: Record<string, any>;
}
