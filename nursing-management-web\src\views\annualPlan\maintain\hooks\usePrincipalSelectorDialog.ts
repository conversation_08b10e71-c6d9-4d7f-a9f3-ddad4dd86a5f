/*
 * FilePath     : /src/views/annualPlan/maintain/hooks/usePrincipalSelectorDialog.ts
 * Author       : 杨欣欣
 * Date         : 2025-07-01 16:43
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:59
 * Description  : 负责人选择器弹窗逻辑
 * CodeIterationRecord:
 */
import type { selectedEmployeeOrGroup } from "@/types/advancedEmployeeSelectorTypes";
import type { planWork } from "../../types/planAndWorksVo";

export function usePrincipalDialog() {
  const principalSelectorVisible = ref<boolean>(false);
  const principalDialogCurrentWork = ref<planWork>();
  let selectRow:((row: planWork) => Promise<void>) | undefined = undefined;

  /**
   * @description: 打开负责人选择弹窗
   * @param work 工作
   * @param selectRowFunc 勾选行函数
   * @return
   */
  const openPrincipalDialog = (work: planWork, selectRowFunc: (row: planWork) => Promise<void>) => {
    selectRow = selectRowFunc;
    principalDialogCurrentWork.value = work;
    principalSelectorVisible.value = true;
  };

  /**
   * @description: 更新工作负责人
   * @param principals 负责人
   * @return
   */
  const handleUpdateWorkPrincipals = (principals: selectedEmployeeOrGroup[]) => {
    if (!principalDialogCurrentWork.value) {
      return;
    }
    const nameSet = new Set<string>();
    principals.forEach((principal) => {
      nameSet.add(principal.groupName ? principal.groupName : principal.employeeName);
    });
    principalDialogCurrentWork.value.principalName = Array.from(nameSet.values()).join("，");
    principalDialogCurrentWork.value.principals = principals;
    selectRow?.(principalDialogCurrentWork.value);
    principalSelectorVisible.value = false;
    principalDialogCurrentWork.value = undefined;
  };

  return {
    principalSelectorVisible,
    principalDialogCurrentWork,
    openPrincipalDialog,
    handleUpdateWorkPrincipals
  };
}
