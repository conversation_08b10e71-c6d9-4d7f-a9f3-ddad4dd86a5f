<!--
 * FilePath     : \src\views\approveManagement\approveProcess\components\approverSelector.vue
 * Author       : 杨欣欣
 * Date         : 2023-12-03 14:10
 * LastEditors  : 苏军志
 * LastEditTime : 2024-06-13 10:04
 * Description  : 审批人/角色选择器
 * CodeIterationRecord:
 -->
<template>
  <el-popover teleported class="approver-selector" :visible="visible" width="auto">
    <div class="approver-selector-body">
      <div class="select-department">
        <label>部门：</label>
        <el-checkbox v-model="isInitiatorDepartment" label="发起人部门" />
        <department-selector v-model="newNodeDetail.departmentID" v-show="!isInitiatorDepartment" label="指定部门" clearable />
      </div>
      <div class="approver-selector-content" v-if="isInitiatorDepartment || newNodeDetail.departmentID">
        <el-radio-group v-model="newNodeDetail.nodeDetailType" @change="typeChange">
          <el-radio :label="1" v-if="newNodeDetail.departmentID">选择员工</el-radio>
          <el-radio :label="2">选择职务</el-radio>
          <el-radio :label="3">业务审批人</el-radio>
        </el-radio-group>
        <employee-selector
          v-if="newNodeDetail.nodeDetailType === 1"
          v-model="newNodeDetail.dataValue"
          :departmentID="newNodeDetail.departmentID"
          @select="setNodeDetailName"
        />
        <job-selector
          v-show="newNodeDetail.nodeDetailType === 2"
          v-model="newNodeDetail.dataValue"
          :options="filterOptions"
          @select="setNodeDetailName"
        />
      </div>
    </div>
    <div class="approver-selector-footer">
      <el-button text @click="cancel">取消</el-button>
      <el-button type="primary" @click="save">确定</el-button>
    </div>
    <template #reference>
      <el-button @click="add">添加成员/角色</el-button>
    </template>
  </el-popover>
</template>
<script setup lang="ts">
import type { approveProcessNodeDetail } from "../types/approveProcessNodeDetail";
const visible = ref(false);
let newNodeDetail = ref<Partial<approveProcessNodeDetail>>({});
const filterOptions = computedAsync(async () => {
  if (!newNodeDetail.value.departmentID) {
    return [];
  }
  let newOptions: Record<string, any>[] = [];
  const { getDepartmentToJobs } = useDictionaryData();
  await getDepartmentToJobs(newNodeDetail.value.departmentID).then((data) => (newOptions = data));
  return newOptions;
});
watchEffect(() => {
  if (!newNodeDetail.value.departmentID) {
    return;
  }
  newNodeDetail.value.dataValue = undefined;
});
const isInitiatorDepartment = ref(false);
watch(
  () => isInitiatorDepartment.value,
  () => {
    newNodeDetail.value.departmentID = undefined;
    newNodeDetail.value.nodeDetailType = undefined;
  }
);
/**
 * @description: 新增审批人/角色
 */
const add = () => {
  newNodeDetail.value = {};
  visible.value = !visible.value;
};
/**
 * @description: 取消新增审批人/角色
 */
const cancel = () => {
  newNodeDetail.value = {};
  visible.value = false;
};

const emits = defineEmits(["add"]);
/**
 * @description: 保存新审批人/角色
 */
const save = () => {
  if (!newNodeDetail.value) {
    return;
  }
  // 审批人自选不进行选择提示
  if (newNodeDetail.value.nodeDetailType !== 3 && !newNodeDetail.value.dataValue) {
    showMessage("warning", "请选择审批人/角色");
    return;
  }
  emits("add", newNodeDetail.value);
  newNodeDetail.value = {};
  visible.value = false;
};
/**
 * @description: 设置审批节点明细名称
 * @param type 明细类型
 * @param value 明细值
 * @return
 */
const setNodeDetailName = (value: Record<string, any>) => {
  newNodeDetail.value.name = (value as Record<string, any>).label;
};
/**
 * @description:选择方式切换，清空dataValue
 */
const typeChange = () => {
  newNodeDetail.value.dataValue = undefined;
  if (newNodeDetail.value.nodeDetailType === 3) {
    setNodeDetailName({ label: "业务审批人" });
  }
};
</script>
<style lang="scss">
.approver-selector {
  display: flex;
  flex-wrap: nowrap;
  width: auto;
}
.approver-selector-body {
  .select-department {
    margin-bottom: 8px;
    > * {
      margin-right: 8px;
    }
  }
  .approver-selector-content {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
  }
  .employee-selector {
    margin-top: 8px;
  }
}
.approver-selector-footer {
  text-align: right;
  margin-top: 8px;
}
</style>
