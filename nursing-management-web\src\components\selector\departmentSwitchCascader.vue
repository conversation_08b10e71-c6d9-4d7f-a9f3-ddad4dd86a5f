<!--
 * relative     : \nursing-management-web\src\components\selector\departmentSwitchCascader.vue
 * Author       : 郭鹏超
 * Date         : 2024-10-29 14:55
 * LastEditors  : 苏军志
 * LastEditTime : 2025-06-12 08:46
 * Description  : 用户权限级联选择器
 * CodeIterationRecord:
 -->
<template>
  <el-cascader
    class="department-switch-cascader cascader-component"
    v-model="selectedDepartmentIDs"
    :props="multipleProps"
    collapse-tags
    :disabled="disabled"
    collapse-tags-tooltip
    clearable
    :options="options"
    @change="departmentChange"
    :show-all-levels="false"
  />
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [Number, Array] as PropType<number | number[]>
  },
  /**
   * @description: 用户ID
   */
  employeeID: String,
  /**
   * @description: 禁用
   */
  disabled: Boolean,
  /**
   * @description: 是否启用多选
   */
  multipleFlag: {
    type: Boolean,
    default: false
  },
  /**
   * @description: 传入级联选择器Options
   */
  list: {
    type: Array as PropType<Record<string, any>[]>
  },
  width: {
    type: Number,
    default: 200
  }
});
const emits = defineEmits(["update:modelValue", "select"]);
const options = ref<Record<string, any>[]>([]);
const convertPX: any = inject("convertPX");
const selectorWidth = computed(() => `${convertPX(props.width)}px`);
const multipleProps = {
  multiple: props.multipleFlag
};
onMounted(async () => {
  options.value = props.list ? props.list : await getCascaderOptions();
  getAllDepartments();
  initValue();
});
/**
 * @description: 根据入参填充organizationType 组装级联选择器参数
 * @param computed
 * @return
 */
const selectedDepartmentIDs = ref<number[] | number[][]>();
watch(
  () => props.modelValue,
  (newValue) => {
    if (!props.modelValue) {
      return [];
    }
    initValue();
  }
);

/**
 * @description: 初始化
 */
const initValue = () => {
  if (props.multipleFlag) {
    const multipleValue = (props.modelValue as number[]).map((departmentID: number) => {
      return [allDepartments.value.find((item) => item.value === departmentID)?.parentValue, departmentID];
    });
    selectedDepartmentIDs.value = multipleValue;
  }
  const value = [allDepartments.value.find((item) => item.value === props.modelValue)?.parentValue, props.modelValue];
  selectedDepartmentIDs.value = value;
};
/**
 * @description: 剔除organizationType 只返回选中departmentID
 * @param value
 * @return
 */
const departmentChange = (value: number[] | Array<number[]>) => {
  if ((value ?? []).length === 0) {
    emits("update:modelValue", props.multipleFlag ? [] : undefined);
  } else {
    emits("update:modelValue", props.multipleFlag ? (value as Array<number[]>).map((item: number[]) => item[1]) : value[1]);
  }
  emits("select", value);
};

/**
 * @description: 获取用户权限部门options
 */
const getCascaderOptions = async () => {
  let params = {
    employeeID: props.employeeID
  };
  return (await employeeDepartmentSwitchService.getEmployeeSwitchCascader(params)) as Record<string, any>[];
};

const allDepartments = ref<Record<string, any>[]>([]);
/**
 * @description: 获取所有部门
 */
const getAllDepartments = () => {
  const departments: Record<string, any>[] = options.value.flatMap((option: Record<string, any>) =>
    option.children.map((childrenOption: Record<string, any>) => ({
      ...childrenOption,
      parentValue: option.value
    }))
  );
  allDepartments.value = departments;
};
</script>

<style lang="scss">
.department-switch-cascader {
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include cascader-component-style(v-bind(selectorWidth));
}
</style>
