/*
 * FilePath     : \src\views\approveManagement\approveRecord\types\approveDetailView.ts
 * Author       : 张现忠
 * Date         : 2023-10-24 14:13
 * LastEditors  : 张现忠
 * LastEditTime : 2024-05-19 18:09
 * Description  :
 * CodeIterationRecord:
 */
export interface approveDetailView {
  /**
   * 审批明细ID
   */
  approveDetailID: string;
  /**
   * 一条审批记录的ID
   */
  approveRecordID: string;
  /**
   * 部门科室
   */
  departmentID: number;
  /**
   * 岗位ID
   */
  postID: number;
  /**
   * 岗位类型
   */
  postTypeID: number;
  /**
   * 审批人工号
   */
  approveEmployeeID: string;
  /**
   * 审批人名称（可能为空）
   */
  approveEmployeeName?: string;
  /**
   * 审批类型：1.顺序签、2.会签、3或签
   */
  approveModel: string;
  /**
   * 1：进行中、2：同意， 3拒绝,空代表还没有被审批
   */
  statusCode?: string ;
  /**
   * 审批批注
   */
  approveSuggestions: string;
  /**
   * 预审批人
   */
  preApproveEmployeeIDs: string[];
  /**
   * 审批时间
   */
  approveDateTime: Date;
}

export interface approveView {
  /**
   * 审批节点名称
   */
  approveNodeName: string;
  /**
   * 当前节点的审批状态
   */
  statusCode?: string;
  /**
   * 当前节点下的审批明细，除了顺序签都是多条明细
   */
  approveDetailViews: approveDetailView[];
  /**
   * 审批方式：1.顺序签 2会签 3.或签
   */
  approveModel: string;
  /**
   * 每一个审批节点对应的业务主表ID
   */
  approveMainID: string;
  /**
   * 发起人发起的审批内容
   */
  content: string;
  /**
   * 判断是否为头结点 -发起人节点
   */
  headFlag: Boolean;
}
