/*
 * FilePath     : \src\api\annualPlan\annualScheduleService.ts
 * Author       : 张现忠
 * Date         : 2023-12-31 09:19
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-07 16:41
 * Description  : 年度计划与后端交互Service
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { taskMainView } from "@/views/annualPlan/types/annualScheduleMainView";
import qs from "qs";
/**
 * 年度计划与后端交互Service
 */
export class annualScheduleService {
  private static controllerRoute: string = "/annualSchedule";
  private static getAPSchedulesApi: string = `${this.controllerRoute}/GetAPSchedules`;
  private static getAnnualScheduleDetailsApi: string = `${this.controllerRoute}/GetAnnualScheduleDetails`;
  private static saveAnnualScheduleApi: string = `${this.controllerRoute}/SaveAnnualSchedule`;
  private static deleteAnnualScheduleApi: string = `${this.controllerRoute}/DeleteAnnualSchedule`;
  private static getAnnualScheduleStatisticsApi: string = `${this.controllerRoute}/GetAnnualScheduleStatistics`;
  private static createOrUpdateTasksApi: string = `${this.controllerRoute}/CreateOrUpdateTasks`;
  private static getAnnualScheduleSourceApi: string = `${this.controllerRoute}/GetAnnualScheduleSource`;
  private static addAnnualScheduleManualApi: string = `${this.controllerRoute}/AddAnnualScheduleManual`;
  /**
   * @description: 获取年度计划
   * @param params 过滤条件:执行人| 执行月份
   * @return Promise<AnnualScheduleMain[]>
   */
  public static getAnnualScheduleMains(params: any) {
    return http.get(this.getAPSchedulesApi, params, { loadingText: Loading.LOAD }) as Promise<taskMainView[]>;
  }

  /**
   * @description: 获取执行明细
   * @param params 主表ID
   * @return Promise<AnnualScheduleDetail[]>
   */
  public static getAnnualScheduleDetails(params: any) {
    return http.get(this.getAnnualScheduleDetailsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  }
  /**
   * @description: 保存年度计划
   * @param params:执行明细
   * @return Promise<boolean> true/false
   */
  public static performTask(params: any) {
    return http.post(this.saveAnnualScheduleApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除年度计划
   * @param params：{年度计划主键ID}
   * @return Promise<boolean> true/false
   */
  public static deleteAnnualSchedule(params: any) {
    return http.post(this.deleteAnnualScheduleApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 获取某一年每个月需要执行的统计
   * @param params
   * @return
   */
  public static getAnnualScheduleStatistics(params: any) {
    return http.get(this.getAnnualScheduleStatisticsApi, params, { loadingText: Loading.LOAD }) as Promise<{
      month: number;
      processedCount: number;
      totalCount: number;
    }[]>;
  }
  /**
   * @description: 根据工作生成或更新任务
   * @param params 年份
   * @returns
   */
  public static createOrUpdateTasks(params: any) {
    return http.post(this.createOrUpdateTasksApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 获取年度计划来源
   * @param params ScheduleMainID
   * @returns
   */
  public static getAnnualScheduleSource(params: any) {
    return http.get(this.getAnnualScheduleSourceApi, params, { loadingText: Loading.LOAD }) as Promise<string>;
  }
  /**
   * 手动加入年度计划
   * @param params ScheduleDate ScheduleContent
   * @returns
   */
  public static addAnnualScheduleManual(params: any) {
    return http.post(this.addAnnualScheduleManualApi, qs.stringify(params), { loadingText: Loading.LOAD });
  }
}
