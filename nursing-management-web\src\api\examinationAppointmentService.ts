/*
 * FilePath     : \src\api\examinationAppointmentService.ts
 * Author       : 张现忠
 * Date         : 2024-04-20 18:50
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-05 10:59
 * Description  : 考核监考预约相关接口
 * CodeIterationRecord:
 */
import http from "@/utils/http";

export class examinationAppointmentService {
  private static getAvailableAppointmentListApi: string = "/ExaminationAppointment/GetAvailableAppointmentList";
  private static getAppointmentByExaminationRecordIDApi: string = "/ExaminationAppointment/GetAppointmentByExaminationRecordID";
  private static saveExaminationAppointmentApi: string = "/ExaminationAppointment/SaveExaminationAppointment";
  private static cancelAppointmentApi: string = "ExaminationAppointment/CancelAppointment";
  private static getAppointmentListApi: string = "ExaminationAppointment/GetAppointmentList";

  /**
   * @description: 获取可预约时间段信息
   * @param params 查询参数
   * @returns
   */
  public static getAvailableAppointmentList(params: any) {
    return http.get(this.getAvailableAppointmentListApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description: 获取考核预约详情
   * @param params 查询参数
   * @returns
   */
  public static getAppointmentByExaminationRecordID(params: any) {
    return http.get(this.getAppointmentByExaminationRecordIDApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description: 保存考核预约记录
   * @param params 预约参数
   * @returns
   */
  public static saveExaminationAppointment(params: any) {
    return http.post(this.saveExaminationAppointmentApi, params, { loadingText: Loading.SAVE });
  }

  /**
   * @description: 取消考核预约记录
   * @param params 取消参数
   * @returns
   */
  public static cancelAppointment(params: any) {
    return http.post(this.cancelAppointmentApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description:获取考核预约信息
   * @param params 取消参数
   * @returns
   */
  public static getAppointmentList(params: any) {
    return http.get(this.getAppointmentListApi, params, { loadingText: Loading.LOAD });
  }
}
