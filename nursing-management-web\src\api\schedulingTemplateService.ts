/*
 * FilePath     : \src\api\schedulingTemplateService.ts
 * Author       : 苏军志
 * Date         : 2024-09-29 10:03
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-07 10:35
 * Description  : 排班模板相关接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";
export class schedulingTemplateService {
  private static getTemplateRecordsApi: string = "/schedulingTemplate/GetTemplateRecords";
  private static getShiftSchedulingMarksApi: string = "/schedulingTemplate/GetShiftSchedulingMarks";
  private static getTemplateDataApi: string = "/schedulingTemplate/GetTemplateData";
  private static saveTemplateRecordApi: string = "/schedulingTemplate/SaveTemplateRecord";
  private static copyTemplateApi: string = "/schedulingTemplate/CopyTemplate";
  private static deleteTemplateApi: string = "/schedulingTemplate/DeleteTemplate";
  private static updateTemplateStatusApi: string = "/schedulingTemplate/UpdateTemplateStatus";
  /**
   * @description: 获取部门所有模板记录
   * @param params
   * @return
   */
  public static getTemplateRecords(params: any) {
    return http.get(this.getTemplateRecordsApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取排班标记集合
   * @param params
   * @return
   */
  public static getShiftSchedulingMarks(params: any) {
    return http.get(this.getShiftSchedulingMarksApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取排班模板数据
   * @param params
   * @return
   */
  public static getTemplateData(params: any) {
    return http.get(this.getTemplateDataApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存排班模板
   * @param params
   * @return
   */
  public static saveTemplateRecord(params: any) {
    return http.post(this.saveTemplateRecordApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 复制排班模板
   * @param params
   * @return
   */
  public static copyTemplate(params: any) {
    return http.post(this.copyTemplateApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除排班模板
   * @param params
   * @return
   */
  public static deleteTemplate(params: any) {
    return http.post(this.deleteTemplateApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }
  /**
   * @description: 更新排班模板状态
   * @param params
   * @return
   */
  public static updateTemplateStatus(params: any) {
    return http.post(this.updateTemplateStatusApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
}
