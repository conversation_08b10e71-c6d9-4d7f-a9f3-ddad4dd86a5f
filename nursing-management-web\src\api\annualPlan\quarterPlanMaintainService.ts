/*
 * FilePath     : \src\api\annualPlan\quarterPlanMaintainService.ts
 * Author       : 杨欣欣
 * Date         : 2025-01-26 11:35
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-01 10:08
 * Description  : 季度计划制定-查询与命令
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { quarterPlanPreview } from "@/views/annualPlan/types/quarterPlanPreview";
import type { planAndWorksVo } from "@/views/annualPlan/types/planAndWorksVo";
import type { planWorkImportVo } from "@/views/annualPlan/types/planWorkImportVo";
import type {
  saveQuarterWorksCommand,
  importQuarterWorksCommand,
  updateQuarterPlanWorkCommand,
  resetQuarterPlanWorksSortCommand,
  getQuarterPlanStatusQuery,
  getCanImportQpWorksQuery,
  getQuarterPlanQuickReferenceVosQuery,
  getBrowseQPViewsQuery,
  getQuarterPlanPreviewQuery
} from "@/views/annualPlan/types/quarterPlanTypes";
export class quarterPlanMaintainService {
  // 季度计划查询
  private static getQuarterPlanMainIDApi: string = "/QuarterPlanMaintain/GetQuarterPlanMainID";
  private static getQuarterWorksApi: string = "/QuarterPlanMaintain/GetQuarterWorks";
  private static getQuarterPlanStatusApi: string = "/QuarterPlanMaintain/GetQuarterPlanStatus";
  private static getCanImportQpWorksGroupByPlanThenTypeApi: string = "/QuarterPlanMaintain/GetCanImportQpWorksGroupByPlanThenType";
  private static getQuarterPlanQuickReferenceVosApi: string = "/QuarterPlanMaintain/GetQuarterPlanQuickReferenceVos";
  private static getBrowseQPViewsApi: string = "/QuarterPlanMaintain/GetBrowseQPViews";
  private static getQuarterPlanPreviewApi: string = "/QuarterPlanMaintain/GetQuarterPlanPreview";
  // 季度计划命令
  private static saveQuarterWorksApi: string = "/QuarterPlanMaintain/SaveQuarterWorks";
  private static resetQuarterPlanWorksSortApi: string = "/QuarterPlanMaintain/ResetQuarterPlanWorksSort";
  private static deleteQuarterWorkApi: string = "/QuarterPlanMaintain/DeleteQuarterWork";
  private static updateQuarterWorkApi: string = "/QuarterPlanMaintain/UpdateQuarterWork";
  private static publishQuarterPlanApi: string = "/QuarterPlanMaintain/PublishQuarterPlan";
  private static importQuarterWorksApi: string = "/QuarterPlanMaintain/SaveImportWorks";

  /**
   * @description: 保存季度计划工作内容
   * @param params
   * @return
   */
  public static saveQuarterWorks = (params: saveQuarterWorksCommand) =>
    http.post(this.saveQuarterWorksApi, params, { loadingText: Loading.SAVE }) as Promise<boolean>;
  /**
   * @description: 重排序
   * @param params
   * @return
   */
  public static resetQuarterPlanWorksSort = (params: resetQuarterPlanWorksSortCommand) =>
    http.post(this.resetQuarterPlanWorksSortApi, params, { loadingText: Loading.SAVE }) as Promise<boolean>;
  /**
   * @description: 删除工作
   * @param params
   * @return
   */
  public static deleteQuarterWork = (params: { quarterPlanDetailID: string }) =>
    http.get(this.deleteQuarterWorkApi, params, { loadingText: Loading.DELETE }) as Promise<boolean>;
  /**
   * @description: 更新季度计划工作内容
   * @param params
   * @return
   */
  public static updateQuarterWork = (params: updateQuarterPlanWorkCommand) =>
    http.post(this.updateQuarterWorkApi, params) as Promise<boolean>;
  /**
   * @description: 查询某科室某季度计划主键
   * @param params
   * @return
   */
  public static getQuarterPlanMainID = (params: { annualPlanMainID: string; quarter: number }) =>
    http.get(this.getQuarterPlanMainIDApi, params) as Promise<string>;
  /**
   * @description: 发布季度计划
   * @param params
   * @return
   */
  public static publishQuarterPlan = (params: { quarterPlanMainID: string }) =>
    http.post(this.publishQuarterPlanApi, undefined, {
      params,
      loadingText: Loading.SAVE
    }) as Promise<boolean>;
  /**
   * @description: 查询某科室某季度计划
   * @param params
   * @return
   */
  public static getQuarterWorks = (params: { annualPlanMainID: string; quarterPlanMainID: string }) =>
    http.get(this.getQuarterWorksApi, params, { loadingText: Loading.LOAD }) as Promise<planAndWorksVo[]>;
  /**
   * @description: 查询某科室某季度计划状态
   * @param params
   * @return
   */
  public static getQuarterPlanStatus = (params: getQuarterPlanStatusQuery) =>
    http.get(this.getQuarterPlanStatusApi, params, { loadingText: Loading.LOAD }) as Promise<boolean>;
  //#endregion
  // 初始化
  //#region 参考、导入其它部门数据
  /**
   * @description: 获取全部可导入的工作
   * @param params
   * @return
   */
  public static getCanImportQpWorksGroupByPlanThenType = (params: getCanImportQpWorksQuery) =>
    http.get(this.getCanImportQpWorksGroupByPlanThenTypeApi, params, { loadingText: Loading.LOAD }) as Promise<planWorkImportVo[]>;
  /**
   * @description: 获取快捷引用列表数据
   * @param params
   * @return
   */
  public static getQuarterPlanQuickReferenceVos = (params: getQuarterPlanQuickReferenceVosQuery) =>
    http.get(this.getQuarterPlanQuickReferenceVosApi, params, { loadingText: Loading.LOAD }) as Promise<planWorkImportVo[]>;

  /**
   * @description: 导入季度计划工作
   * @param params
   * @return
   */
  public static importQuarterWorks = (params: importQuarterWorksCommand) =>
    http.post(this.importQuarterWorksApi, params, { loadingText: Loading.SAVE }) as Promise<string>;
  //#endregion
  /**
   * @description: 获取浏览季度计划视图
   * @param params
   * @return
   */
  public static getBrowseQPViews = (params: getBrowseQPViewsQuery) =>
    http.get(this.getBrowseQPViewsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 获取浏览季度计划视图
   * @param params
   * @return
   */
  public static getQuarterPlanPreview = (params: getQuarterPlanPreviewQuery) =>
    http.get(this.getQuarterPlanPreviewApi, params, { loadingText: Loading.LOAD }) as Promise<quarterPlanPreview>;
}
