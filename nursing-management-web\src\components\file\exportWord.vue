<!--
 * FilePath     : \ccc.webe:\NursingManagement\nursing-management-web\src\components\file\exportWord.vue
 * Author       : 郭鹏超
 * Date         : 2023-10-17 14:56
 * LastEditors  : 马超
 * LastEditTime : 2023-11-28 17:39
 * Description  : table数据导入excel文件
 * CodeIterationRecord:
-->

<template>
  <div class="export-excel">
    <el-button type="primary" @click="wordExport" class="download-button iconfont icon-download-fill">{{
      exportWordOption?.buttonName ?? "生成Word文件"
    }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { saveAs } from "file-saver";
import JSZipUtils from "jszip-utils";
import JSZip from "pizzip";
import Docxtemplater from "docxtemplater";
const props = defineProps({
  exportWordOption: {
    type: Object as () => ExportWordView,
    default: () => {
      return {};
    }
  }
});
const wordExport = () => {
  if (!props?.exportWordOption.wordData || !props?.exportWordOption.fileName || !props?.exportWordOption.templateWordUrl) {
    showMessage("error", "导出Word文件失败！");
    return;
  }
  JSZipUtils.getBinaryContent(props.exportWordOption.templateWordUrl, (error: any, content: any) => {
    // 抛出异常
    if (error) {
      throw error;
    }
    // 创建一个JSZip实例，内容为模板的内容
    let zip = new JSZip(content);
    // 创建并加载docxtemplater实例对象
    let doc = new Docxtemplater().loadZip(zip);
    // 设置模板变量的值
    doc.setData({
      tableTitle: props.exportWordOption.fileName,
      table: props.exportWordOption.wordData
    });
    try {
      // 用模板变量的值替换所有模板变量
      doc.render();
    } catch (errorInfo) {
      showMessage("error", "导出word失败");
      return;
    }
    // 生成一个代表docxtemplater对象的zip文件（不是一个真实的文件，而是在内存中的表示）
    let out = doc.getZip().generate({
      type: "blob",
      mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    });
    // 将目标文件对象保存为目标类型的文件，并命名
    saveAs(out, props.exportWordOption.fileName + ".docx");
  });
};
</script>

<style lang="scss">
.export-excel {
  display: inline-block;
}
</style>
