<!--
 * FilePath     : \src\views\signUpRecord\index.vue
 * Author       : 张现忠
 * Date         : 2024-07-14 08:53
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-08-13 14:49
 * Description  : 报名记录页面
 * CodeIterationRecord:
    4650-作为IT人员，我需要开发报名功能，以利培训报名（14）2024-07-14 -zxz
 -->

<template>
  <base-layout class="sign-up-record">
    <template #header>
      <div class="sign-up-search">
        <setting-dictionary-selector
          label="报名项目"
          v-model="sourceType"
          :settingParams="settingParams"
          :disabled="disabledSelector"
          @change="getSignUpRecordList()"
        ></setting-dictionary-selector>
        <span>报名人:</span>
        <el-input v-model="searchQuery" clearable placeholder="请输入搜索内容" @keyup.enter="searchSignUpRecord">
          <template #append>
            <i class="iconfont icon-search" @click="searchSignUpRecord" />
          </template>
        </el-input>
      </div>
    </template>
    <el-table :data="filterSignUpList" border stripe highlight-current-row>
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :align="column.align"
        :min-width="convertPX(column.minWidth)"
      >
        <template v-if="column.slotType" #default="{ row }">
          <template v-if="column.slotType === 'dateTime'">
            <span v-formatTime="{ value: row[column.prop], type: 'dateTime' }" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <div class="operation">
            <el-tooltip content="拒绝">
              <i
                class="iconfont icon-revoke"
                type="text"
                v-visibilityHidden="row.statusCode === '1'"
                v-permission:B="29"
                @click.stop="rejectSignUp(row)"
              ></i>
            </el-tooltip>
            <!-- <el-tooltip content="删除">
              <i class="iconfont icon-delete" type="text" v-permission:B="4" @click="deleteSignUpRecord(row)"></i>
            </el-tooltip> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script setup lang="ts">
import type { signUpRecordView } from "./types/signUpRecordView";
import { useColumns } from "./hooks/useColumns";
import { useSignUpAction } from "./hooks/useSignUpAction";
const { sourceType, sourceDataID, signUpList, filterSignUpList, saveSignUpRecord, getSignUpRecordList } = useSignUpAction();
//#region 变量声明
const { columns } = useColumns();
const convertPX: any = inject("convertPX");
const props = defineProps({
  signUpType: {
    type: String || undefined,
    default: () => undefined
  },
  sourceID: {
    type: String || undefined,
    default: () => undefined
  }
});
const currRow = ref<signUpRecordView>();
// 搜索关键字
const searchQuery = ref<string>("");
let settingParams = {
  settingTypeCode: "TrainingExaminationSignUp",
  settingTypeValue: "SourceType"
};
const disabledSelector = ref<boolean>(false);

//#endregion
// 其他页面跳转，禁用选择器，重新拉去拉取数据
watch(
  [() => props.signUpType, () => props.sourceID],
  async (newValue: any) => {
    if (newValue && newValue[0] && newValue[1]) {
      disabledSelector.value = true;
      sourceDataID.value = String(newValue[1]);
      sourceType.value = String(newValue[0]);
    }
  },
  { immediate: true, deep: true }
);
// 监听查询关键字清除重新显示全部数据
watch(searchQuery, (newVal: string, oldVal: string) => {
  oldVal && !newVal && searchSignUpRecord();
});

// 在组件创建时获取报名记录列表数据
onMounted(async () => {
  await getSignUpRecordList();
});
//#region 事件交互函数
/**
 * @description: 拒绝报名
 * @param row ：当前行数据
 * @return
 */
const rejectSignUp = (row?: signUpRecordView) => {
  currRow.value = row;
  if (!row) {
    showMessage("error", "找不到报名信息");
    return;
  }
  row.statusCode = "0";
  saveSignUpRecord(row);
};

/**
 * @description: 按照关键字搜索报名记录
 * @returns {}
 */
const searchSignUpRecord = () => {
  if (!searchQuery.value || searchQuery.value.trim() === "") {
    filterSignUpList.value = signUpList.value;
  }
  filterSignUpList.value = signUpList.value.filter((signUpRecord: signUpRecordView) =>
    signUpRecord.employeeName.includes(searchQuery.value)
  );
};
// #endregion
</script>

<style lang="scss">
.sign-up-record {
  width: 100%;
  .sign-up-search {
    margin: 0 10px;
    display: inline-flex;
    align-items: center;
    span {
      white-space: nowrap;
      margin-right: 5px;
    }
    .el-input {
      width: 200px;
    }
  }
  .operation {
    & > i {
      margin-right: 10px;
    }
  }
}
</style>
