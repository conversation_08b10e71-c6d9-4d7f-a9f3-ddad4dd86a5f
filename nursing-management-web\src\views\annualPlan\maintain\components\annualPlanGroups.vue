<!--
 * FilePath     : \src\views\annualPlan\maintain\components\annualPlanGroups.vue
 * Author       : 杨欣欣
 * Date         : 2023-10-27 10:10
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 16:53
 * Description  : 目标组件，主体是一个table
 * CodeIterationRecord:
 -->
<template>
  <el-container class="annual-plan-groups">
    <el-header>
      <el-button v-if="!readOnly" v-permission:B="1" class="add-button" @click="maintainStore.addPlanGroupToPlanGoal(sourcePlanGoal)"
        >新增分组</el-button
      >
    </el-header>
    <el-main>
      <el-table
        class="ap-group-table"
        :data="list"
        border
        row-key="groupID"
        @row-contextmenu="showContextMenu"
        :cell-class-name="setCellClassName"
        v-drag-sort="{
          el: '.el-table__body-wrapper tbody',
          handleClass: '.icon-sort',
          ghostClass: 'row-drag-ghost',
          disabled: readOnly,
          callBack: resetGroupSort
        }"
      >
        <el-table-column v-if="sessionStore.debugMode" label="分组序号">
          <template #default="{ row }">
            ${{ row.sort }}<br />
            #{{ row.groupID }}
          </template>
        </el-table-column>
        <el-table-column label="策略指标" data-column-type="indicator" :min-width="convertPX(200)">
          <template #default="{ row: planGroup }">
            <i v-if="!readOnly" class="iconfont icon-sort" />
            <annual-plan-indicators
              :ref="(el:any) => setComponentRef(indicatorDetailRefs, planGroup.groupID, el)"
              v-model="maintainStore.indicatorsByGroupID[planGroup.groupID]"
              :sourcePlanGroup="planGroup"
            />
          </template>
        </el-table-column>
        <el-table-column label="目标任务" data-column-type="project" :min-width="convertPX(200)">
          <template #default="{ row: planGroup }">
            <annual-plan-projects
              :ref="(el:any) => setComponentRef(projectDetailRefs, planGroup.groupID, el)"
              v-model="maintainStore.projectsByGroupID[planGroup.groupID]"
              :sourcePlanGroup="planGroup"
            />
          </template>
        </el-table-column>
        <el-table-column label="负责部门/负责人" align="center" :width="convertPX(180)">
          <template #default="{ row }">
            <el-select
              v-if="!readOnly"
              :ref="overrideHandleBlur"
              v-fixMultipleSelectHeight
              v-model="row.responsibleDepartments"
              class="department-select"
              multiple
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              @change="maintainStore.savePlanGroup(row)"
              placeholder="请输入负责部门"
            >
              <el-option
                v-for="(recommendation, index) in maintainStore.recommendations"
                :key="index"
                :label="recommendation"
                :value="recommendation"
              />
            </el-select>
            <span v-else>{{ row.responsibleDepartments.join("，") }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
  </el-container>
</template>
<script setup lang="ts">
import type { planGoal, planGroup } from "../../types/annualPlanMain";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";

const { list, sourcePlanGoal } = defineProps<{
  list: planGroup[];
  sourcePlanGoal: planGoal;
}>();
const { proxy } = getCurrentInstance() as any;
const convertPX: any = inject("convertPX");
const maintainStore = useAnnualPlanMaintainStore();
const { sessionStore } = useStore();

import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
const { readOnly } = storeToRefs(usePlanManagementStore());

//#region 表格渲染
const setCellClassName = ({ columnIndex }: { columnIndex: number }) => {
  if ((!sessionStore.debugMode && columnIndex === 0) || (sessionStore.debugMode && columnIndex === 1)) {
    return "drag-handle-cell";
  }
  return "";
};
//#endregion

/**
 * @description: 计算真实下标，重排序目标分组
 * @param newIndex 新序号
 * @param oldIndex 旧序号
 * @return
 */
const resetGroupSort = async (newIndex: number, oldIndex: number) => {
  if (newIndex === oldIndex) {
    return;
  }
  const draggedGroup = list[oldIndex];
  const offsetOldIndex = maintainStore.planGroups.findIndex((sourceGroup) => sourceGroup === draggedGroup);
  const offsetNewIndex = maintainStore.planGroups.findIndex((sourceGroup) => sourceGroup === list[newIndex]);
  await maintainStore.resetPlanGroupsSort(offsetNewIndex, offsetOldIndex, draggedGroup);
};

//#region 右键菜单实现
const indicatorDetailRefs = ref<Record<string, any>>({});
const projectDetailRefs = ref<Record<string, any>>({});
/**
 * @description: 记录明细组件实例，key为groupID
 * @param refs 实例集合
 * @param groupID 分组ID
 * @param el 当前实例
 * @return
 */
const setComponentRef = (refs: Record<string, any>, groupID: string, el?: Record<string, any>) => {
  el && (refs[groupID] = el);
};
/**
 * @description: 打开右键菜单
 * @param row 当前行数据
 * @param column 当前列信息
 * @param event 事件信息
 * @return
 */
const showContextMenu = (row: planGroup, column: any, event: MouseEvent) => {
  if (readOnly.value) {
    return;
  }
  let rowEl: HTMLElement = event.target as HTMLElement;
  // 选中当前行
  while (!rowEl?.classList?.contains("el-table__row")) {
    rowEl = rowEl.parentElement!;
  }
  rowEl?.classList.add("context-menu-active");
  proxy.$showContextMenu({
    x: event.clientX,
    y: event.clientY,
    items: [
      {
        label: "新增指标",
        icon: "icon-add-indicator",
        divided: true,
        onClick: () => indicatorDetailRefs.value[row.groupID]?.addDetailByContextMenu()
      },
      {
        label: "新增项目",
        icon: "icon-add-project",
        divided: true,
        onClick: () => projectDetailRefs.value[row.groupID]?.addDetailByContextMenu()
      },
      {
        label: "删除当前分组",
        icon: () =>
          h("i", {
            class: "iconfont icon-delete",
            style: {
              color: "#000000"
            }
          }),
        onClick: () => maintainStore.deletePlanGroup(row)
      }
    ],
    // 关闭时，移除选中状态
    onClose: () => rowEl?.classList.remove("context-menu-active")
  });
};
//#endregion

//#region 覆盖el-select中的input 中的@blur 方法
/**
 * ^V2.3.6版本
 * 覆盖el-select中的input 中的@blur 方法
 * 解决：点击下拉框中的多选标签（tags）,下拉框展开和关闭异常
 */
const overrideHandleBlur = (currDeptRef: any) => {
  currDeptRef?.handleBlur &&
    (currDeptRef.handleBlur = function (e: Event) {
      currDeptRef.$emit("blur", e);
    });
};
//#endregion
</script>
<style lang="scss">
.annual-plan-groups {
  display: flex;
  flex-direction: column;
  width: 100%;
  .el-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 40px;
  }
  .el-main {
    padding: 0px;
    .ap-group-table {
      // 没有context-menu-active类时，背景色为白色
      &.el-table.el-table--striped .el-table__row.el-table__row--striped:hover:not(.context-menu-active) td,
      &.el-table .el-table__body .el-table__row:hover:not(.context-menu-active) > td {
        background-color: #ffffff !important;
      }
      // 有context-menu-active类时，背景高亮
      &.el-table.el-table--striped .el-table__row.el-table__row--striped.context-menu-active td,
      &.el-table .el-table__body .el-table__row.context-menu-active > td {
        background-color: lighten($base-color, 40%) !important;
      }
      td {
        vertical-align: top;
      }
      .row-drag-ghost {
        opacity: 0;
      }
      .drag-handle-cell .cell {
        display: flex;
        align-items: center;
        > *:not(.iconfont) {
          flex: 1;
          min-width: 0;
        }
        > .iconfont {
          cursor: grab;
        }
      }
    }
  }
}
</style>
