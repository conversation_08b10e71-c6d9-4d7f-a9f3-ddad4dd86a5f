{"version": 3, "sources": ["../../@univerjs/engine-render/lib/eu-Cks5q4oj.mjs"], "sourcesContent": ["const Eu = [\n  \"1,12,0022\",\n  '{\"b\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"c\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"d\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"f\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"g\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"j\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"k\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"l\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"m\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"n\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"ñ\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"p\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"l\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"q\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"r\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"s\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":[{\"b\":[{\"r\":2,\"l\":2}]},0]}],\"t\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0,\"s\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"x\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"z\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}],\"r\":[{\"a\":1,\"e\":1,\"o\":1,\"i\":1,\"u\":1}]}],\"v\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"w\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"x\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"y\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}],\"z\":[{\"a\":0,\"e\":0,\"o\":0,\"i\":0,\"u\":0}]}',\n  []\n];\nexport {\n  Eu\n};\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA,CAAC;AACH;", "names": []}