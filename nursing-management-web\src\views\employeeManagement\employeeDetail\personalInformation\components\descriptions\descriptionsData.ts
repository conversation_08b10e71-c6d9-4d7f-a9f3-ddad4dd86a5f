/*
 * FilePath     : \src\views\employeeManagement\employeeDetail\personalInformation\components\descriptions\descriptionsData.ts
 * Author       : 来江禹
 * Date         : 2023-08-01 18:18
 * LastEditors  : 苏军志
 * LastEditTime : 2024-05-05 17:38
 * Description  : 个人信息描述组件使用TS变量配置
 * CodeIterationRecord: 3671-作为护理管理人员，我需要护士个人信息档案，以便查看护士相关档案信息
 */
import type { descriptionsProp } from "@/views/employeeManagement/employeeDetail/personalInformation/types/descriptionsView";
export default {
  basicInformation: [
    // {
    //   prop: "employeeName",
    //   label: "姓名",
    //   span: 1,
    //   required: true
    // },
    // {
    //   prop: "gender",
    //   label: "性别",
    //   span: 1,
    //   required: true
    // },
    // {
    //   prop: "nationality",
    //   label: "国籍",
    //   span: 1,
    //   required: true
    // },
    {
      prop: "nation",
      label: "民族",
      span: 1,
      required: true
    },
    {
      prop: "polity",
      label: "政治面貌",
      span: 1,
      required: true
    },
    {
      prop: "marriage",
      label: "婚姻状况",
      span: 1,
      required: true
    },
    {
      prop: "birthdate",
      label: "出生日期",
      span: 1,
      required: true
    },
    {
      prop: "lunarBirthdate",
      label: "农历生日",
      span: 1,
      required: true
    },
    {
      prop: "deliverCode",
      label: "生育状态",
      span: 2,
      required: true
    },
    {
      prop: "homeAddress",
      label: "家庭住址",
      span: 2,
      required: true
    },
    {
      prop: "actualAddress",
      label: "籍贯",
      span: 2,
      required: true
    }
  ],
  clothingArchives: [
    {
      prop: "height",
      label: "身高(cm)",
      span: 1,
      required: true
    },
    {
      prop: "weight",
      label: "体重(Kg)",
      span: 1,
      required: true
    },
    {
      prop: "clothesSize",
      label: "衣服尺码",
      span: 1,
      required: false
    },
    {
      prop: "pantsSize",
      label: "裤子尺码",
      span: 1,
      required: false
    },
    {
      prop: "shoeSize",
      label: "鞋子尺码",
      span: 1,
      required: false
    }
  ]
} as descriptionsProp;
