/*
 * FilePath     : \src\components\selector\hooks\usePrincipalSelector.ts
 * Author       : 杨欣欣
 * Date         : 2024-04-07 15:54
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-12 15:55
 * Description  : 负责人选择器逻辑
 * CodeIterationRecord:
 */

/**
 * @description: 负责人选择器逻辑
 * @param refKey 选择器模板引用Key
 * @return
 */
export const usePrincipalSelector = ({ refKey }: { refKey: string }) => {
  const employeeIDs = ref<string[]>([]);
  const customGroupName = ref<string>("");
  const jointEmployeeNames = ref<() => string>();
  //#region  如果被弹窗嵌套，才使用
  const selectorVisible = ref<boolean>(false);
  const dialogTitle = "请选择负责人";
  const selectorRef = useTemplateRef<Record<string, any>>(refKey);
  if (selectorRef.value) {
    // 当重新打开弹窗时，需要重新拉取负责人选择器当中的人员信息列表
    watch(selectorVisible, () => {
      if (selectorVisible.value && selectorRef.value) {
        selectorRef.value.setEmployees();
      }
    });
  }
  //#endregion

  return {
    selectorVisible,
    dialogTitle,
    employeeIDs,
    customGroupName,
    /**
     * @description: 初始化选择器
     * @param initialEmployeeIDs 员工工号集合
     * @param initialCustomGroupName 自定义分组名称
     * @return
     */
    init: (initialEmployeeIDs: MaybeRefOrGetter<string[]>, initialCustomGroupName?: MaybeRefOrGetter<string>) => {
      customGroupName.value = toRaw(toValue(initialCustomGroupName)) ?? "";
      employeeIDs.value = toValue(initialEmployeeIDs)?.length ? [...toRaw(toValue(initialEmployeeIDs))] : [];
      selectorVisible.value = true;
    },
    /**
     * @description: 创建拼接员工姓名的函数
     * @param employees 员工信息列表
     * @return
     */
    createJointEmployeeNamesFunc: (employees: Ref<Record<string, any>[]>) => {
      jointEmployeeNames.value = () => {
        return employeeIDs.value.map((id) => {
          const employee = employees.value.find((employee) => employee.value === id);
          if (employee) {
            return employee.label;
          }
          return "";
        }).join("，");
      };
    },
    /**
     * @description: 重置相关变量
     */
    resetVariables: () => {
      selectorVisible.value = false;
      employeeIDs.value = [];
      customGroupName.value = "";
    },
    /**
     * @description: 写入负责人信息
     * @param sourceData 源数据
     * @return
     */
    confirm: (source: MaybeRefOrGetter<Record<string, any>>) => {
      toValue(source).principalIDs = employeeIDs.value;
      toValue(source).principalGroupName = customGroupName.value;
      // 若未设置自定义分组名，则将当前已选择的员工姓名进行拼接做呈现
      !customGroupName.value && (toValue(source).principalName = jointEmployeeNames.value!());
      return true;
    },
    /**
     * @description: 负责人分组推荐选项筛选与选择
     */
    useRecommendOptions: (recommendGroups: Record<string, any>[]) => {
      return {
        /**
         * @description: 获取建议项
         * @param inputValue 检索内容
         * @param cb 回调函数
         * @return
         */
        getSuggestion: (inputValue: string, cb: Function) => {
          const createFilter = (inputValue: string) => (filterData: Record<string, any>) =>
            filterData.principalGroupName.indexOf(inputValue) >= 0;
          const result = inputValue?.length ? useArrayFilter(recommendGroups, createFilter(inputValue)) : recommendGroups;
          cb(unref(result));
        },
        /**
         * @description: 选择建议项，将建议项的负责人列表添加到当前负责人列表中
         * @param option 建议项
         * @return
         */
        setPrincipals: (option: Record<string, any>) => {
          employeeIDs.value = common.clone(option.principalIDs);
        }
      };
    }
  };
};
