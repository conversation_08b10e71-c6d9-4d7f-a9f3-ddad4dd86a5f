<!--
 * FilePath     : \src\components\selector\departmentSelector.vue
 * Author       : 张现忠
 * Date         : 2023-08-20 15:33
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 14:39
 * Description  : 科室级联选择器
 * CodeIterationRecord:
  1.4175-作为护理管理人员，我需要优化、修复年度计划-计划维护，以利年度计划制定（14）zxz 2024-02-02
  2.调整可以支持选择任意节点（叶子节点和非叶子节点
  3.支持传入人员工号，只显示当前用户的跨部门权限列表 2024-03-30
-->

<template>
  <div class="department-selector cascader-component">
    <span v-if="label">{{ label }}：</span>
    <el-cascader
      ref="cascaderRef"
      class="cascader-component"
      v-model="departmentIDs"
      :placeholder="`请选择${label}`"
      :options="deptOptions"
      :show-all-levels="showAllLevels"
      :props="cascaderProps"
      :disabled="disabled"
      :clearable="clearable"
      :collapse-tags="multiCollapse"
      :collapse-tags-tooltip="multiCollapse"
      popper-class="department-popper-panel"
    >
      <template #default="{ node, data }" v-if="!isMultiple && !props.onlySelectEndNode">
        <span class="cascader-component-label" @click="departmentNodeClick(node)">{{ data.label }}</span>
      </template>
    </el-cascader>
  </div>
</template>

<script lang="ts" setup>
// #region props
const props = defineProps({
  modelValue: {
    type: [Number, Array<Number>]
  },
  // label内容
  label: {
    type: String,
    default: "部门"
  },
  // 组织架构类型
  organizationType: {
    type: String,
    default: "1"
  },
  // 清空属性，默认关闭
  clearable: {
    type: Boolean,
    default: false
  },
  // 输入框中是否显示选中值的完整路径，默认不显示
  showAllLevels: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showWholeHospital: {
    type: Boolean,
    default: false
  },
  // 级联选择器配置选项
  props: {
    type: Object,
    default: () => {
      return {
        expandTrigger: "hover"
      };
    }
  },
  multiCollapse: {
    type: Boolean,
    default: true
  },
  // 组件宽度
  width: {
    type: Number,
    default: 200
  },
  disableOptions: {
    type: Array<number>,
    default: []
  },
  /**
   * @remark 单选情况下，只能选择终结点，默认true
   * @type {Boolean} false:可以选择任意节点(与其他节点互斥)，true:只能选择终结点
   */
  onlySelectEndNode: {
    type: Boolean,
    default: true
  },
  employeeID: {
    type: String,
    default: undefined
  },
  list: {
    type: Array as PropType<CascaderList<number>[]>,
    default: () => undefined
  }
});

// #endregion
const deptOptions = ref<CascaderList<number>[]>([]);
const { getDepartmentCascaderData, getEmployeeDepartment } = useDictionaryData();
const { getCascaderFullValue } = useCascaderFullValue();
let departmentIDs = ref<Number[] | Array<number[]>>([]);
const isMultiple = Boolean(props.props.multiple);
const cascaderProps = computed(() => {
  return { ...props.props, expandTrigger: "click" as const, checkStrictly: !isMultiple && !props.onlySelectEndNode };
});
onMounted(async () => {
  if (!props.organizationType) {
    return;
  }
  await init(props.employeeID);
});
watch(
  () => [props.organizationType, props.disableOptions, props.employeeID],
  async () => {
    await init(props.employeeID);
  }
);
watch(
  () => props.modelValue,
  () => {
    initDefaultValue();
  }
);
let { list } = toRefs(props);
const allDepartment = ref<CascaderList<number>>({
  label: "全院",
  value: 999999,
  children: []
});
/**
 * @description: 初始化
 * @param employeeID
 * @return
 */
const init = async (employeeID?: string) => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  if (list?.value) {
    deptOptions.value = list.value;
    initDefaultValue();
    return;
  }
  // 如果传入人员工号，只显示当前用户的跨部门权限列表
  if (employeeID) {
    await getEmployeeDepartmentSwitch(employeeID);
  } else {
    await getDeptCascaderData();
  }
  // 依据参数显示“全院” 选项
  if (props.showWholeHospital) {
    allDepartment.value.disabled = props.disableOptions.includes(allDepartment.value.value);
    deptOptions.value.unshift(allDepartment.value);
  }
  initDefaultValue();
};
/**
 * @description: 初始化默认值
 */
const initDefaultValue = () => {
  /**
   * 使用递归函数，构造返回默认数据显示默认科室
   * 如：入参2，返回(部门ID以及上级部门ID集合)ret[0]: [70,50,2]
   *   ret[1] CascaderList类型集合
   */
  const defaultValue = getCascaderFullValue(
    props.modelValue,
    deptOptions.value,
    "value",
    "children",
    typeof props.modelValue !== "number"
  ) as any;
  ignoreUpdates(
    () =>
      (departmentIDs.value = isMultiple ? (typeof props.modelValue !== "number" ? defaultValue[0] : [defaultValue[0]]) : defaultValue[0])
  );
};

/**
 * @description 获取员工拥有权限的部门集合
 * @param employeeID 员工ID
 * @return
 */
const getEmployeeDepartmentSwitch = async (employeeID: string) => {
  if (!employeeID) {
    return;
  }
  deptOptions.value = await getEmployeeDepartment(employeeID, props.organizationType);
};
// 获取科室字典数据
const getDeptCascaderData = async () => {
  await getDepartmentCascaderData(props.organizationType, props.disableOptions, Math.random()).then((data) => {
    deptOptions.value = data;
  });
};
const emits = defineEmits(["update:modelValue", "change", "select"]);
let departmentItems: CascaderList<number>[] | Array<CascaderList<number>[]>[];
const { ignoreUpdates } = watchIgnorable(
  () => departmentIDs.value,
  (newValue) => {
    if (!newValue || newValue.length <= 0) {
      emits("update:modelValue", undefined);
      emits("change", undefined);
      emits("select", undefined);
      return;
    }
    let chooseValues: number | number[] = [];
    if (isMultiple) {
      // 如果是多选类型，返回每一个数组部门ID数据最后一个参数，选择的部门ID数组
      newValue.forEach((value: any) => (chooseValues as number[]).push(value[value.length - 1]));
    } else {
      // 如果是单选类型，返回部门ID数据最后一个参数，选择的部门ID
      chooseValues = newValue[newValue.length - 1] as number;
    }
    // 处理首次进入监听，组件默认值情况，传入的值newValue不是一个数组，是几个数值，在遍历时chooseValues数组均为undefine，此时取部门ID数据最后一个参数
    if (isMultiple && !(chooseValues as number[])[0] && newValue) {
      chooseValues = newValue[newValue.length - 1] as number;
    }
    // 数据清空时，直接返回
    if (!chooseValues) {
      return;
    }
    // 根据选择的值，获取到对象的位置及对象数据值
    const ret = getCascaderFullValue(chooseValues, deptOptions.value, "value", "children", typeof chooseValues !== "number") as any;
    emits("update:modelValue", chooseValues);
    emits("change", chooseValues);
    departmentItems = ret[1];
    emits("select", departmentItems);
  },
  { deep: true }
);
const cascaderRef = shallowRef();
//#region 自定义呈现内容点击事件
/**
 * @description: 部门级联选择选项点击事件
 * @param node 级联选择器中每一个节点的数据类型对象
 * @return
 */
const departmentNodeClick = (node: any) => {
  if (!cascaderRef.value) {
    return;
  }
  if (isMultiple) {
    let parentNode = node.parent;
    // 选择子节点 取消父节点的选中(互斥)
    while (departmentIDs.value && parentNode) {
      for (let index = 0; index < departmentIDs.value.length; index++) {
        const innerDepartmentIDs = departmentIDs.value[index] as number[];
        if (innerDepartmentIDs.indexOf(parentNode.value) > -1) {
          handleCheckChange(parentNode, false);
          break;
        }
      }
      parentNode = parentNode.parent;
    }
    // 选择父节点，清理子节点(互斥)
    let selectedNodes = cascaderRef.value.getCheckedNodes(false);
    let childNodes = node.children;
    for (let index = 0; index < childNodes.length; index++) {
      const childNode = childNodes[index];
      selectedNodes.find((m: any) => m.uid === childNode.uid) && handleCheckChange(childNode, false);
    }
  }
  // 选中当前节点
  handleCheckChange(node, true);
  // 非多选关闭弹窗
  node.config.multiple || cascaderRef.value.togglePopperVisible(false);
};
/**
 * @description:
 * @param node 级联选择器节点
 * @param flag 是否选择当前节点 true:选择,false:取消选择节点
 * @return
 */
const handleCheckChange = (node: any, flag: Boolean) => {
  cascaderRef.value.cascaderPanelRef.handleCheckChange(node, flag);
};
//#endregion
// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const cascaderWidth = computed(() => `${convertPX(width.value)}px`);
</script>
<style lang="scss">
.department-selector {
  display: inline-block;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include cascader-component-style(v-bind(cascaderWidth));
  .cascader-component {
    &--label {
      display: block;
    }
    .el-input .el-input__wrapper .el-input__inner {
      height: auto;
    }
  }
}
// 去掉部门选择器->级联选择器组件中单选时的单选按钮样式
.department-popper-panel {
  .el-cascader-panel {
    .el-cascader-node {
      > .el-radio {
        display: none;
      }
    }
  }
}
</style>
