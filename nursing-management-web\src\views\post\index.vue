<!--
 * FilePath     : \src\views\post\index.vue
 * Author       : 苏军志
 * Date         : 2023-08-24 15:32
 * LastEditors  : 苏军志
 * LastEditTime : 2023-09-17 10:02
 * Description  : 岗位相关
 * CodeIterationRecord:
-->
<template>
  <top-menu :menuList="tabMenus" ref="childPage"></top-menu>
</template>

<script setup lang="ts">
const { sessionStore } = useStore();
const route = useRoute();
let childPage = ref<any>();
let parentRouterName = route.matched[route.matched.length - 2]?.name as string;
const tabMenus = ref(sessionStore.pageTopMenus[parentRouterName]);

// 暴漏给父路由
defineExpose({
  /**
   * description: 系统顶部刷新按钮触发
   */
  refreshData() {
    nextTick(() => {
      if (childPage?.value) {
        childPage?.value.refreshData();
      }
    });
  }
});
</script>
