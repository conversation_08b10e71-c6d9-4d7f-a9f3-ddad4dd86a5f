/*
 * FilePath     : \src\api\approveRecordService.ts
 * Author       : 张现忠
 * Date         : 2023-10-21 16:51
 * LastEditors  : 马超
 * LastEditTime : 2025-06-30 10:25
 * Description  :
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import qs from "qs";

export class approveRecordService {
  private static getApproveRecordViewApi: string = "/ApproveRecord/GetApproveRecordView";
  private static getApproveDetailViewApi: string = "/ApproveRecord/GetApproveDetailView";
  private static saveApprovalApi: string = "/ApproveRecord/SaveApproval";
  private static getHistoryApproveRecordViewApi: string = "/ApproveRecord/GetHistoryApproveRecordView";
  private static stopApprovalApi: string = "/ApproveRecord/StopApproval";
  private static revokeApprovalApi: string = "/ApproveRecord/RevokeApproval";
  private static getUnApprovalRecordApi: string = "/ApproveRecord/GetUnApprovalRecord";
  private static getApproveCategoryAndCountApi: string = "/ApproveRecord/GetApproveCategoryAndCount";
  private static manualSubmissionApproveApi: string = "/ApproveRecord/ManualSubmissionApprove";
  /**
   * @description: 获取需要审批的记录
   * @param params
   * @return
   */
  public static async getApproveRecordView(params: any) {
    return http.get(this.getApproveRecordViewApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取审批记录对应的审批明细
   * @param params
   * @return
   */
  public static async getApproveDetailView(params: any) {
    return http.get(this.getApproveDetailViewApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存审批结果
   * @param params
   * @return
   */
  public static async saveApproval(params: any) {
    return http.post(this.saveApprovalApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取已经被审批完成的审批内容
   * @return
   */
  public static async getHistoryApproveRecordView(params: any) {
    return http.get(this.getHistoryApproveRecordViewApi, params, { loadingText: Loading.LOAD });
  }

  /**
   * @description: 停止未开始的审批记录
   * @param params
   * @return
   */
  public static stopApproval(params: any) {
    return http.post(this.stopApprovalApi, qs.stringify(params), { loadingText: Loading.SAVE });
  }

  /**
   * @description: 撤销审批记录
   * @param params
   * @return
   */
  public static revokeApproval(params: any) {
    return http.post(this.revokeApprovalApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取未审批的记录(主页待办内容显示使用)
   * @param params
   * @returns
   */
  public static getUnApprovalRecord(params: any) {
    return http.get(this.getUnApprovalRecordApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 获取审批类型及对应数量
   * @param params
   * @return
   */
  public static async getApproveCategoryAndCount(params: any) {
    return http.get(this.getApproveCategoryAndCountApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * 手动提交审批
   * @param params
   * @param params.approveType 审批类型
   * @param params.recordID 业务ID
   * @returns
   */
  public static manualSubmissionApprove = (params: any) =>
    http.post(this.manualSubmissionApproveApi, params, { loadingText: Loading.LOAD });
}
