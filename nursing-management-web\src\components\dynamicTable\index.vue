<!--
 * FilePath     : \src\components\dynamicTable\index.vue
 * Author       : 郭鹏超
 * Date         : 2021-11-08 14:26
 * LastEditors  : 马超
 * LastEditTime : 2024-12-10 16:26
 * Description  : 表格封装组件
 参数说明:
headerList: [
   {
     label: "日期"                       表格列名
     prop: "assessDate"                  列绑定值
     align: "center"                     内容位置
     headerAlign: ""                     列名内容位置
     tableColumnWidth: "110"             列宽度
     minWidthFlag: false                 是否为min-width最小列度
     columnClassName: ""                 列class名
     columnStyle: "date"                 列内容种类
     fixedPosition: ""                   固定位置
     slotName: ""                        插槽名称
     children: []                        多级表头使用
     options: [                          下拉框 多选框使用
       {key:"",value:""}
     ]
   }
 ]
   表格列支持类型
     columnStyle:
             Date      日期选择框
             Time      时间选择框
             date      日期文字内容 自动显示为(yyyy-MM-dd)
             time      时间文字内容 自动显示为(hh:mm)
             dateTime  日期时间文字内容 自动显示为(yyyy-MM-dd hh:mm)
             text      文字(支持Html)
             select    下拉框
             input     输入框
             check     多选框
             slot      插槽 配合slotName使用 用于组件处理不了的逻辑时使用
提供方法：doLayout，直接调用el-table的doLayout方法
-->
<template>
  <div class="dynamic-table">
    <el-table
      ref="tableRef"
      height="100%"
      width="100%"
      border
      :data="tableData"
      v-bind="$attrs"
      :row-class-name="rowClassName ?? getRowClassName"
      header-row-class-name="main-record-header-row"
      @row-click="rowClick"
    >
      <el-table-column
        v-for="(item, index) in innerHeaderList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :width="!item.minWidthFlag ? convertPX(item.tableColumnWidth) : ''"
        :min-width="item.minWidthFlag ? convertPX(item.tableColumnWidth) : ''"
        :header-align="item.headerAlign"
        :align="item.align"
        :fixed="item.fixedPosition"
        :sortable="item.sortFlag"
      >
        <template #header>
          <span v-html="item.label"></span>
        </template>
        <template v-slot="scope">
          <header-column :item="item" v-model:scope="tableData[scope.$index]"></header-column>
          <div v-if="item.columnStyle == 'slot' && item.slotName">
            <slot :name="item.slotName" :row="scope.row"></slot>
          </div>
          <template v-if="item.children.length">
            <el-table-column
              v-for="(childrenItem, index) in item.children"
              :key="index"
              :prop="childrenItem.prop"
              :width="!childrenItem.minWidthFlag ? convertPX(childrenItem.tableColumnWidth) : childrenItem.minWidthFlag"
              :min-width="childrenItem.minWidthFlag ? convertPX(childrenItem.tableColumnWidth) : childrenItem.minWidthFlag"
              :header-align="childrenItem.headerAlign"
              :align="childrenItem.align"
              :fixed="item.fixedPosition"
              :sortable="item.sortFlag"
            >
              <template #header>
                <span v-html="childrenItem.label"></span>
              </template>
              <template v-slot="scope">
                <header-column :item="childrenItem" v-model:scope="tableData[scope.$index]"></header-column>
                <div v-if="childrenItem.columnStyle == 'slot' && item.slotName">
                  <slot :name="childrenItem.slotName" :row="scope.row"></slot>
                </div>
              </template>
            </el-table-column>
          </template>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import headerColumn from "./dynamicColumn.vue";
const convertPX: any = inject("convertPX");
const tableRef = shallowRef();

const props = defineProps({
  headerList: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => {
      return [] as Record<string, any>[];
    }
  },
  modelValue: {
    type: Array as PropType<Record<string, any>[]>,
    default: () => {
      return [];
    }
  },
  dynamicTableList: {
    type: Array,
    default: () => {
      return [];
    }
  },
  rowClassName: {
    type: Function as PropType<(row: any) => string>,
    default: undefined
  }
});
const emits = defineEmits(["rowClick", "update:modeValue"]);
const tableData = useVModel(props, "modelValue", emits);
const innerHeaderList = computed(() => {
  props.headerList.slice().sort((a: any, b: any) => {
    return a.sort - b.sort;
  });
  return props.headerList;
});
/**
 * @description:执行emits方法
 * @param row 行数据
 * @return
 */
const rowClick = (row: any) => {
  emits("rowClick", row);
};
/**
 * @description: 重新渲染表格
 * @return
 */
const doLayout = () => {
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.doLayout();
    }
  });
};
/**
 * @description: 设置行Class
 * @return
 */
const getRowClassName = ({ rowIndex }: any) => `row-${rowIndex} main-record-row`;

watch(
  () => props.headerList,
  () => {
    doLayout();
  },
  { immediate: true }
);
</script>

<style lang="scss">
.dynamic-table {
  height: 100%;
  .dynamic-table {
    float: right;
    margin: 0 10px 10px 0;
  }
}
</style>
