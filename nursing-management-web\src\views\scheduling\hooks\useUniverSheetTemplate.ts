/* eslint-disable id-match */
import type { IWorkbookData } from "@univerjs/core";
import { LocaleType } from "@univerjs/core";
// 导入单元格基础样式
import { cellBaseStyle } from "@/components/univerSheet/style/cellBaseStyle";
import type { sheetParamType } from "@/components/univerSheet/types/sheetParamType";

/**
 * @description: 根据行数和列数产生默认的表格数据
 * @param rowCount 行数
 * @param columnCount 列数
 * @param headerRowCount 标题行数
 * @return
 */
const getDefaultCellData = (rowCount: number, columnCount: number, headerRowCount: number) => {
  let cellData: Record<string, Record<string, any>> = {};
  for (let rowIndex = 0; rowIndex < rowCount; rowIndex++) {
    let columnData: Record<string, any> = {};
    for (let columnIndex = 0; columnIndex < columnCount; columnIndex++) {
      // 判断是否为标题  标题特殊处理
      if (rowIndex < headerRowCount) {
        columnData[columnIndex.toString()] = {
          v: (columnIndex + 1).toString(),
          s: "header-cell-style"
        };
        continue;
      }
      columnData[columnIndex.toString()] = {
        v: "",
        s: "cell-style"
      };
    }
    cellData[rowIndex.toString()] = columnData;
  }
  return cellData;
};

/**
 * @description: 获取排班模板的sheet
 * @param params
 * @return
 */
const getSchedulingTemplateSheet = (params: sheetParamType): IWorkbookData => {
  const rowCount = params.rowCount || 6;
  const columnCount = params.columnCount || 10;
  const headerRowCount = params.headerRowCount || 0;
  if (params.cellData && Object.keys(params.cellData).length) {
    Object.keys(params.cellData).forEach((rowIndex) => {
      // 跳过标题行
      if (Number(rowIndex) < headerRowCount) {
        return;
      }
      const rowData = params.cellData![rowIndex];
      // 循环列
      Object.keys(rowData).forEach((columnIndex) => {
        const columnData = rowData[columnIndex];
        // 样式为对象的，加上基础样式
        if (typeof columnData.s !== "string") {
          columnData.s = { ...cellBaseStyle, ...columnData.s };
        }
      });
    });
  }
  const cellData = params.cellData || getDefaultCellData(rowCount, columnCount, headerRowCount);
  return {
    id: "schedulingTemplateSheet",
    locale: LocaleType.ZH_CN,
    name: "排班模板",
    sheetOrder: ["sheet1"],
    appVersion: "",
    styles: {
      "cell-style": {
        ...cellBaseStyle
      },
      "header-cell-style": {
        ...cellBaseStyle,
        cl: { rgb: params.headerFontColor || "#000000" },
        bg: { rgb: params.headerBackgroundColor || "#ffee99" }
      }
    },
    sheets: {
      sheet1: {
        id: "sheet1",
        name: "排班模板",
        cellData: cellData,
        hidden: 0,
        zoomRatio: 1,
        rowCount: params.rowCount,
        columnCount: params.columnCount,
        defaultRowHeight: params.defaultRowHeight || 40,
        defaultColumnWidth: params.defaultColumnWidth || 40,
        showGridlines: 1,
        rowHeader: {
          width: 35,
          hidden: params.hiddenRowHeader || 0
        },
        columnHeader: {
          height: 20,
          hidden: params.hiddenColumnHeader || 0
        },
        // 默认样式
        defaultStyle: cellBaseStyle
      }
    }
  };
};

/**
 * @description: 获取排班表的sheet
 * @param params
 * @return
 */
const getShiftSchedulingSheet = (params: sheetParamType): IWorkbookData => {
  return {
    id: "shiftSchedulingSheet",
    locale: LocaleType.ZH_CN,
    name: "排班表",
    sheetOrder: ["sheet1"],
    appVersion: "",
    styles: {
      "cell-style": {
        ...cellBaseStyle,
        fs: 9
      },
      "header-cell-style": {
        ...cellBaseStyle,
        fs: 9,
        cl: { rgb: params.headerFontColor || "#000000" },
        bg: { rgb: params.headerBackgroundColor || "#ffee99" }
      },
      "statistics-cell-style": {
        ...cellBaseStyle,
        fs: 9,
        cl: { rgb: params.headerFontColor || "#000000" },
        bg: { rgb: params.headerBackgroundColor || "#ffee99" }
      },
      "monthly-statistics-cell-style": {
        ...cellBaseStyle,
        fs: 9,
        cl: { rgb: params.headerFontColor || "#000000" },
        bg: { rgb: "#f8f8f8" }
      }
    },
    sheets: {
      sheet1: {
        id: "sheet1",
        cellData: params.cellData,
        name: "排班表",
        hidden: 0,
        zoomRatio: 1,
        rowCount: params.rowCount,
        columnCount: params.columnCount,
        defaultRowHeight: params.defaultRowHeight || 40,
        defaultColumnWidth: params.defaultColumnWidth || 40,
        showGridlines: 1,
        rowHeader: {
          width: 35,
          hidden: 0
        },
        columnHeader: {
          height: 20,
          hidden: 1
        },
        // 指定单元格宽度
        columnData: { 0: { w: 40 }, 1: { w: 60 } },
        rowData: { 0: { h: 24 }, 1: { h: 26 } },
        // 指定冻结行列
        freeze: { startColumn: 2, startRow: 2, xSplit: 2, ySplit: 2 },
        // 合并单元格
        mergeData: params.mergeData as any,
        // 默认样式
        defaultStyle: cellBaseStyle
      }
    }
  };
};

export function useUniverSheetTemplate() {
  return {
    /**
     * @description: 获取排班模板的sheet
     * @param params
     * @return
     */
    getSchedulingTemplateSheet,
    /**
     * @description: 获取排班表的sheet
     * @param params
     * @return
     */
    getShiftSchedulingSheet
  };
}
