<!--
 * FilePath     : \src\views\mainLayout\index.vue
 * Author       : 苏军志
 * Date         : 2023-06-04 20:00
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-26 19:50
 * Description  : 系统主画面
 * CodeIterationRecord:
-->
<template>
  <div class="main-layout">
    <main-header
      v-if="!sessionStore.externalTransfer"
      :isFullscreen="isFullscreen"
      :user-name="userStore?.userName"
      :photoUrl="photoUrl"
      :messageCount="mqMessageCount"
      @handle-click="handleHeaderClick"
    ></main-header>
    <div class="main-wrap">
      <div class="left-menu" v-if="!sessionStore.externalTransfer">
        <el-menu class="main-menu" router unique-opened :default-active="activeRouter" :collapse="isCollapse" :collapse-transition="false">
          <nav-menu :navMenus="menuList"></nav-menu>
        </el-menu>
      </div>
      <div class="router-view-wrap">
        <el-breadcrumb v-if="!sessionStore.externalTransfer && activeRouter != '/home'" class="top-breadcrumb" separator=">">
          <template v-for="(bread, index) in sessionStore.breadcrumb" :key="index">
            <el-breadcrumb-item v-if="bread.path" :to="{ path: bread.path }">{{ bread.name }}</el-breadcrumb-item>
            <el-breadcrumb-item v-else>{{ bread.name }}</el-breadcrumb-item>
          </template>
        </el-breadcrumb>
        <div class="router-view screenfull-dom">
          <router-view v-slot="{ Component }">
            <!-- 不需要缓存的路由加载位置 -->
            <component ref="childPage" :is="Component" v-if="!route.meta.keepAlive && isRouterAlive" />
            <!-- 需要缓存的路由加载位置 -->
            <keep-alive>
              <component ref="childPage" :is="Component" v-if="route.meta.keepAlive && isRouterAlive" />
            </keep-alive>
          </router-view>
        </div>
      </div>
      <message-preview v-model="showSystemNotice" :params="systemMessageNotice"></message-preview>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入hooks
import { useBreadcrumb } from "./hooks/useBreadcrumb";
const { createBreadcrumb } = useBreadcrumb();
// 引用缓存
const { userStore, sessionStore } = useStore();
const { createMQByBroadcast } = useMQMessage(useRouter());
const { proxy } = getCurrentInstance() as any;
const router = useRouter();
const route = useRoute();
const { loginOut } = useLogout();
const targetElement = ref<any>(document.body);
// 使用VueUse中的方法实现全屏
const { isFullscreen, toggle } = useFullscreen(targetElement);
// 路由是否存活，用于刷新页面
let isRouterAlive = ref(true);
let mqMessageCount = ref(0);
let menuList = ref([] as any);
let activeRouter = computed(() => {
  return route.meta?.parentPath || route.fullPath;
});
let isCollapse = ref(true);
let childPage = ref<any>();
const showSystemNotice = ref(false);
const systemMessageNotice = ref<Record<string, any>>({});
// 获取人员照片
const { getEmployeePhoto } = useEmployee();
const photoUrl = getEmployeePhoto(userStore.employeeID);
// 监听路由变化，组转面包屑导航数据
watch(
  () => route.fullPath,
  () => {
    sessionStore.currRouterListID = route.meta.routerListID as number;
    sessionStore.breadcrumb = [];
    createBreadcrumb(route.path, menuList.value);
  },
  { immediate: true }
);
// 部门发生变化后，更新后端缓存
watch(
  () => userStore.departmentID,
  async (value) => {
    if (!value || value < 0) {
      return;
    }
    await userLoginService.updateDepartmentIDOfSession({ departmentID: userStore.departmentID });
    location.reload();
  }
);
onMounted(() => {
  if (sessionStore.externalTransfer) {
    return;
  }
  init();
  createMQByBroadcast("MQNotification", "SystemNotice");
});
const init = () => {
  // 获取路由
  routerService.getRouterAndComponentList().then((data: any) => {
    if (data) {
      // 将权限路由和按钮清单放入session
      sessionStore.routerList = data.routerList;
      sessionStore.componentList = data.componentList;
    }
  });
  // 获取菜单
  const params = { menuType: "Main" };
  menuService.getMenuList(params).then((data: any) => {
    if (data) {
      let { mainMenuList, topMenuList } = data;
      menuList.value = mainMenuList;
      sessionStore.pageTopMenus = topMenuList;
      createBreadcrumb(route.path, menuList.value);
    }
  });
};

/**
 * @description: 顶部各种按钮单击事件集合
 * @param name 按钮名称
 */
const handleHeaderClick = (name: string) => {
  const clickSwitch: any = {
    toggleFullscreen: () => toggleScreenfull(false),
    toggleDomFullscreen: () => toggleScreenfull(true),
    // 展开/折叠菜单
    switchMenu: () => {
      isCollapse.value = !isCollapse.value;
    },
    // 回到首页
    home: () => {
      router.replace({ path: "/home" });
    },
    version: () => showSystemUpdateRecord(),
    // 刷新画面
    refresh: () => refresh(),
    // 显示消息
    message: () => {},
    // 退出系统
    logOut: async () => {
      confirmBox(proxy.$t("mainLayout.logOutConfirm"), proxy.$t("tip.systemTip"), async (flag: boolean) => {
        if (flag) {
          await loginOut();
        }
      });
    }
  };
  clickSwitch[name]();
};
/**
 * @description: 全屏/退出全屏
 */
const toggleScreenfull = (screenfullDomFlag: boolean) => {
  targetElement.value = document.body;
  if (screenfullDomFlag) {
    const elements = document.getElementsByClassName("screenfull-dom");
    if (elements?.length) {
      // 取最后一个dom
      targetElement.value = elements[elements.length - 1];
    }
  }
  // 切换全屏
  toggle();
};
/**
 * @description: 显示最近的系统更新记录
 */
const showSystemUpdateRecord = () => {
  showSystemNotice.value = true;
  messageManagementService.getLastSystemUpdateRecord().then((message: any) => {
    systemMessageNotice.value = {
      messageRecordID: message.messageRecordID,
      messageContent: message.messageContent,
      title: message.messageTitle
    };
  });
};
/**
 * @description: 显示系统通知
 * @param message
 * @return
 */
const showSystemNoticeDialog = (message: string) => {
  showSystemNotice.value = true;
  systemMessageNotice.value = {
    messageContent: message,
    title: "系统通知"
  };
};
eventBus.on("SystemNotice", showSystemNoticeDialog);
onBeforeUnmount(() => eventBus.off("SystemNotice", showSystemNoticeDialog));
/**
 * @description: 顶部刷新
 */
const refresh = () => {
  // 刷新前取消未完成的请求
  http.cancelPageRequest();
  // 如果子页面有刷新，则调用子页面的刷新
  if (childPage?.value && childPage.value.refreshData) {
    childPage?.value.refreshData();
    return;
  }
  // 控制router-view的显示或隐藏，从而控制页面的再次加载,模拟刷新页面操作。
  isRouterAlive.value = false;
  nextTick(() => {
    isRouterAlive.value = true;
  });
};
// 向子路由暴漏方法
provide("refresh", refresh);
</script>

<style lang="scss">
/* ---滚动条公共样式--- */
*::-webkit-scrollbar {
  background-color: $base-color;
  border-radius: 10px;
  width: 12px;
  height: 12px;
}
*::-webkit-scrollbar-track {
  background-color: lighten($base-color, 45%);
  border-radius: 10px;
}
*::-webkit-scrollbar-thumb {
  background-color: lighten($base-color, 5%);
  border-radius: 20px;
}
/* ---el-table滚动条公共样式--- */
.el-scrollbar {
  // 横向滚动条
  .el-scrollbar__bar.is-horizontal .el-scrollbar__thumb {
    opacity: 1; // 默认滚动条自带透明度
    height: 8px; // 横向滑块的宽度
    border-radius: 20px; // 圆角度数
    background-color: lighten($base-color, 5%); // 滑块背景色
  }
  // 纵向滚动条
  .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
    opacity: 1;
    width: 8px; // 纵向滑块的宽度
    border-radius: 20px;
    background-color: lighten($base-color, 5%);
  }
}
.main-layout {
  height: 100%;
  width: 100%;
  .main-wrap {
    width: 100%;
    height: calc(100% - 60px);
    display: flex;
    flex-direction: row;
    .left-menu {
      margin-top: -4px;
      height: 100%;
    }
    .router-view-wrap {
      margin-top: -4px;
      flex: auto;
      height: 100%;
      width: 100%;
      padding: 0px 5px 5px 5px;
      box-sizing: border-box;
      overflow-x: hidden;
      display: flex;
      flex-direction: column;
      .top-breadcrumb {
        font-size: 18px;
        @include select-style();
        margin: 0 -10px 5px -10px;
        padding: 8px 20px;
        .el-breadcrumb__inner {
          color: #ffffff;
          font-weight: bold;
          &.is-link:hover {
            color: $base-color;
          }
        }
        .el-breadcrumb__separator {
          color: #ffffff;
        }
      }
      .router-view {
        height: 100%;
        flex: auto;
        overflow: hidden;
      }
    }
  }
}
</style>
