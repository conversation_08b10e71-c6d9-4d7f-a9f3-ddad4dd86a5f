<!--
 * FilePath     : \src\views\setting\ruleMaintain.vue
 * Author       : 胡长攀
 * Date         : 2024-09-14 15:09
 * LastEditors  : 来江禹
 * LastEditTime : 2025-04-03 10:40
 * Description  : 规则维护页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="rule-maintain" :drawerOptions="drawerOptions">
    <template #header>
      <span>类型：</span>
      <el-select v-model="systemType" class="rule-system-type-select" @change="getRuleList">
        <el-option v-for="item in systemTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
      <el-button class="add-button" v-permission:B="1" @click="addOrModifyRule()">新增</el-button>
    </template>
    <el-table :data="ruleList" border stripe height="100%" class="ruleTable">
      <el-table-column :width="convertPX(130)" label="类型" prop="systemTypeName" align="center" />
      <el-table-column :width="convertPX(300)" label="名称" prop="showName" />
      <el-table-column :min-width="convertPX(160)" label="描述" prop="description" />
      <el-table-column :width="convertPX(140)" label="规则类型" prop="componentType" />
      <el-table-column :width="convertPX(180)" label="可选条件" prop="condition" />
      <el-table-column :width="convertPX(160)" label="默认值" prop="defaultValue" />
      <el-table-column :width="convertPX(180)" label="修改人" prop="modifyEmployeeName" align="center" />
      <el-table-column :width="convertPX(180)" label="修改时间" prop="modifyDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(80)" align="center">
        <template #default="scope">
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" v-permission:B="3" @click="addOrModifyRule(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" v-permission:B="4" @click="deleteRule(scope.row.ruleListID)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form ref="submitRefs" :label-width="convertPX(150)" :model="drawerData" :rules="rules">
        <el-form-item label="类型：" prop="systemType">
          <el-select v-model="drawerData.systemType" placeholder="请选择规则类型" class="select">
            <el-option v-for="(item, index) in systemTypeList" :key="index" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称：" prop="showName">
          <el-input v-model="drawerData.showName" placeholder="请输入规则名称" type="textarea" autosize></el-input>
        </el-form-item>
        <el-form-item label="规则描述：" prop="description">
          <el-input v-model="drawerData.description" placeholder="请输入规则描述" type="textarea" autosize></el-input>
        </el-form-item>
        <el-form-item label="规则类型：" prop="componentListID">
          <el-select class="select" v-model="drawerData.componentListID" placeholder="请选择组件类型">
            <el-option v-for="(item, index) in componentList" :key="index" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可选条件：" prop="conditionCode">
          <el-select class="select" v-model="drawerData.conditionCode" multiple clearable placeholder="请选择条件">
            <el-option v-for="(item, index) in conditionList" :key="index" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="drawerData.componentListID === selectComponentListID" label="规则明细：" props="detailValue">
          <item-detail-list
            :itemType="'SingleChoice'"
            v-model="drawerData.ruleDetail"
            :placeholder="'请输入规则明细'"
            :itemID="drawerData.ruleListID"
            :content="'默认值'"
            dragFlag
          ></item-detail-list>
        </el-form-item>
        <el-form-item v-if="drawerData.componentListID !== selectComponentListID" label="默认值：" props="defaultValue">
          <el-input v-model="drawerData.defaultValue" placeholder="请输入默认值" type="textarea" autosize></el-input>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入
const convertPX: any = inject("convertPX");
const { getSettingDictionaryByCodeValue } = useDictionaryData();
// 规则所属系统类型集合
const systemTypeList = ref<Record<string, any>>([]);
// 条件集合
const conditionList = ref<Record<string, any>>([]);
// 规则所属系统类型
const systemType = ref<string>("");
// 规则集合
const ruleList = ref<Record<string, any>>([]);
// 组件集合
const componentList = ref<Record<string, any>>([]);
// 下拉框组件ID
const selectComponentListID = ref<number>(111);
const submitRefs = ref<any>();
// 抽屉参数
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    const { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveRule();
  }
});
const drawerData = ref<Record<string, any>>({});
// 规则验证rule
const rules = reactive({
  systemType: [
    {
      required: true,
      message: "请选择类型",
      trigger: "change"
    }
  ],
  showName: [
    {
      required: true,
      message: "请输入规则名称",
      trigger: "change"
    }
  ],
  description: [
    {
      required: true,
      message: "请输入规则描述",
      trigger: "change"
    }
  ],
  componentListID: [
    {
      required: true,
      message: "请选择规则类型",
      trigger: "change"
    }
  ],
  conditionCode: [
    {
      required: true,
      message: "请选择可选条件",
      trigger: "change"
    }
  ]
});
// #endregion

//#region 初始化
onMounted(async () => {
  await getSystemTypeAndConditionSetting();
  await getComponentList();
});

// #endregion

//#region 业务数据

/**
 * @description: 获取配置字典数据
 */
const getSystemTypeAndConditionSetting = async () => {
  const params = {
    settingTypeCode: "RuleList",
    settingTypeValues: ["SystemType", "ConditionCode"]
  };
  await getSettingDictionaryByCodeValue(params).then(async (dicts: any) => {
    if (dicts) {
      systemTypeList.value = dicts["SystemType"];
      conditionList.value = dicts["ConditionCode"];
      systemType.value = dicts["SystemType"][0].key;
      await getRuleList();
    }
  });
};

/**
 * @description: 获取规则集合
 */
const getRuleList = async () => {
  const params = {
    systemType: systemType.value
  };
  await conditionService.getRuleListByType(params).then((result) => {
    if (result) {
      ruleList.value = result;
    }
  });
};

/**
 * @description: 添加/修改规则
 * @param row
 * @return
 */
const addOrModifyRule = (row?: any) => {
  drawerData.value = row ? common.clone(row) : { systemType: systemType.value, ruleDetail: [] };
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.drawerTitle = `${row ? "编辑" : "新增"}规则`;
};

/**
 * @description: 保存规则数据
 */
const saveRule = () => {
  // CapabilityLevel Department Employee 这三个规则类型不需要维护规则明细，其他规则类型需要维护规则明细
  if (
    drawerData.value.componentListID === selectComponentListID.value &&
    !drawerData.value.ruleDetail?.length &&
    drawerData.value.ruleCode !== "CapabilityLevel" &&
    drawerData.value.ruleCode !== "Department" &&
    drawerData.value.ruleCode !== "Employee"
  ) {
    showMessage("warning", "请维护规则明细");
    return;
  }
  const params = drawerData.value;
  conditionService.saveRule(params).then((result: any) => {
    if (result) {
      drawerOptions.value.showDrawer = false;
      getRuleList();
    }
  });
};

/**
 * @description: 删除规则数据
 * @param ruleListID
 * @return
 */
const deleteRule = (ruleListID: number) => {
  confirmBox("是否删除规则数据？", "删除规则数据", async (flag: Boolean) => {
    if (flag) {
      if (ruleListID) {
        const params = {
          ruleListID: ruleListID
        };
        await conditionService.deleteRule(params).then(async (result: any) => {
          if (result) {
            showMessage("success", "删除成功");
            await getRuleList();
          }
        });
      }
    }
  });
};
/**
 * @description: 获取组件列表
 */
const getComponentList = async () => {
  await dictionaryService.getComponentListByType({ componentType: "DynamicFilter" }).then((result) => {
    if (result) {
      componentList.value = result;
    }
  });
};
// #endregion
</script>
<style lang="scss">
.rule-maintain {
  .rule-system-type-select {
    width: 200px;
  }
}
</style>
