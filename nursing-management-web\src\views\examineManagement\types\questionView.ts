/*
 * FilePath     : \src\views\examineManagement\types\questionView.ts
 * Author       : 来江禹
 * Date         : 2024-06-24 15:31
 * LastEditors  : 张现忠
 * LastEditTime : 2025-02-28 11:12
 * Description  : 题目数据View
 * CodeIterationRecord:
 */
export interface questionView {
  /**
   * 题目ID
   */
  examinationQuestionID?: number;
  /**
   * 考核题目名称
   */
  questionContent: string;
  /**
   * 题目类型，来源SettingDictionary（判断题、多选题、单选题、简答题）
   */
  examinationQuestionType: string;
  /**
   * 考核类型名称
   */
  examinationQuestionTypeName?: string;
  /**
   * 题目难度
   */
  difficultyLevel: string;
  /**
   * 题目难度名称
   */
  difficultyLevelName?: string;
  /**
   * 题目标签
   */
  questionTag: string;
  /**
   * 题目标签数组
   */
  questionTagArr: Array<string>[];
  /**
   * 题目标签名称
   */
  questionTagName?: Array<string>[];
  /**
   * 题目说明
   */
  instructions: string;
  /**
   * 题目解析
   */
  analysis: string;
  /**
   * 修改人
   */
  modifyEmployeeName?: string;
  /**
   * 修改时间
   */
  modifyDateTime?: Date;
  /**
   * 题目权重
   */
  filterWeight?: number;
  /**
   * 题库ID
   */
  questionBankID: string;
  /**
   * 题目明细
   */
  questionDetail?: Array<Record<string, any>>;
  /**
   * 实操类题目分值
   */
  score?: number;
  /**
   * 题目排序
   */
  sort: number;
}
