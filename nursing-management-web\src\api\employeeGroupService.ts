/*
 * FilePath     : \src\api\employeeGroupService.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-11 17:14
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-16 20:46
 * Description  : 用户组请求
 * CodeIterationRecord: 
 */
import http from "@/utils/http";
import qs from "qs";

export class employeeGroupService {
  private static getEmployeeGroupsApi: string = "/EmployeeGroup/GetEmployeeGroups";
  private static getEmployeeGroupsAsOptionsApi: string = "/EmployeeGroup/GetEmployeeGroupsAsOptions";
  private static addEmployeeGroupApi: string = "/EmployeeGroup/AddEmployeeGroup";
  private static updateEmployeeGroupApi: string = "/EmployeeGroup/UpdateEmployeeGroup";
  private static deleteEmployeeGroupApi: string = "/EmployeeGroup/DeleteEmployeeGroup";

  /**
   * @description: 获取用户组列表
   * @param params
   * @returns
   */
  public static getEmployeeGroups = () => http.get(this.getEmployeeGroupsApi, undefined, { loadingText: Loading.LOAD }) as Promise<EmployeeGroupVo[]>;
  /**
   * @description: 获取用户组列表（下拉选项）
   * @param params
   * @returns
   */
  public static getEmployeeGroupsAsOptions = () => http.get(this.getEmployeeGroupsAsOptionsApi, undefined, { loadingText: Loading.LOAD }) as Promise<EmployeeGroupAsOption[]>;
  /**
   * @description: 新建用户组
   * @param params
   * @returns
   */
  public static addEmployeeGroup = (params: AddEmployeeGroupDto) => http.post(this.addEmployeeGroupApi, params) as Promise<number>;
  /**
   * @description: 更新用户组信息
   * @param params
   * @returns
   */
  public static updateEmployeeGroup = (params: UpdateEmployeeGroupDto) => http.post(this.updateEmployeeGroupApi, params);
  /**
   * @description: 删除用户组
   * @param params
   * @return
   */
  public static deleteEmployeeGroup = (params: { groupID: number; }) => http.post(this.deleteEmployeeGroupApi, qs.stringify(params));
}
