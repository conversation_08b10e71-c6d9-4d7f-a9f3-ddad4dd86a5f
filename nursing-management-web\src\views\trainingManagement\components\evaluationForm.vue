<!--
 * FilePath     : \src\views\trainingManagement\components\evaluationForm.vue
 * Author       : 来江禹
 * Date         : 2024-06-05 15:01
 * LastEditors  : 苏军志
 * LastEditTime : 2024-08-29 15:01
 * Description  : 培训清单维护问卷模版组件
 * CodeIterationRecord:
 -->
<template>
  <div class="evaluation-form">
    <zhy-form-designer
      ref="formDesignerRef"
      :formData="formData"
      :saveMethod="formParams.saveFormMethod"
      :componentGroups="componentGroups"
      mergeGroup
    ></zhy-form-designer>
  </div>
</template>
<script setup lang="ts">
import type { dynamicFormData } from "zhytech-ui";
import type { formParam } from "../types/evaluationFormTemplate";
const props = defineProps({
  formParams: {
    type: Object as PropType<formParam>,
    required: true
  }
});
const formData = ref<dynamicFormData<Record<string, any>>>({
  datas: {},
  components: [],
  props: {}
});
const componentGroups = ref<Array<Record<string, any>>>([]);
onMounted(async () => {
  await initFormTemplate();
});
/**
 * @description: 初始化模板
 */
const initFormTemplate = async () => {
  if (props.formParams.formID) {
    formData.value.props.formID = props.formParams.formID;
    getFormTemplate(formData.value.props.formID);
  }
};
const getFormTemplate = (dynamicFormRecordID: string) => {
  let params = {
    dynamicFormRecordID
  };
  dynamicFormService.getFormTemplateByRecordID(params).then((res: any) => {
    if (res) {
      formData.value = res;
    }
  });
};
</script>
<style lang="scss">
.evaluation-form {
  height: 100%;
}
</style>
