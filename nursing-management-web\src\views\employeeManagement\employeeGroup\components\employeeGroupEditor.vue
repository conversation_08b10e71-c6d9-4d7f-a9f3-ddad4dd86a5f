<template>
  <el-form class="employee-group-editor" ref="formRef" :model="editEmployeeGroup" :rules="rules">
    <el-form-item label="组名" prop="groupName">
      <el-input v-model="editEmployeeGroup.groupName" placeholder="请输入用户组名称" maxlength="15" show-word-limit />
    </el-form-item>
    <el-form-item class="member-form-item" label="成员" prop="members">
      <el-input class="search-input" v-model="searchKeyWord" clearable placeholder="搜索用户姓名、简拼" @input="handleSearch">
        <template #prepend>
          <el-select v-model="searchedPanel" @change="handleSearch(searchKeyWord)" class="searched-panel">
            <el-option label="待选择" value="left" />
            <el-option label="已选择" value="right" />
          </el-select>
        </template>
      </el-input>
      <el-transfer
        v-model="editEmployeeGroup.memberIDs"
        class="employee-transfer"
        :props="{
          key: 'employeeID',
          label: 'employeeName'
        }"
        :titles="['可选择用户', '已选择用户']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}'
        }"
        :data="displayedEmployees"
        @change="changeTransfer"
      >
        <template #default="{ option }">
          <div class="member-info">
            <span class="member-name">{{ option.employeeName }}</span>
            <span class="member-detail">{{ option.departmentName }}</span>
          </div>
        </template>
      </el-transfer>
    </el-form-item>
    <el-form-item v-if="slots.default">
      <slot :value="editEmployeeGroup" :validateForm="formRef?.validate"></slot>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { differenceWith, uniqBy } from "lodash-es";
const { employeeGroup } = defineProps<{
  employeeGroup: Partial<EmployeeGroupVo>;
}>();
const { userStore } = useStore();
onMounted(async () => {
  editEmployeeGroup.value = {
    groupName: employeeGroup.groupName || "",
    memberIDs: employeeGroup.members?.map((member) => member.employeeID) || [],
    members: employeeGroup.members || []
  };
  employeesInformation = await employeeService.getEmployeesInformation();
  // 初始呈现本部门的，以及已选择的
  initialEmployees.value = employeesInformation.filter(
    (employeeInformation) =>
      employeeInformation.departmentID === userStore.departmentID ||
      editEmployeeGroup.value.memberIDs.includes(employeeInformation.employeeID)
  );
  displayedEmployees.value = initialEmployees.value;
});
let employeesInformation: EmployeeInformation[] = [];
//#region 表单
const editEmployeeGroup = ref<{
  groupName: string;
  memberIDs: string[];
  members: MemberInformation[];
}>({
  groupName: "",
  memberIDs: [],
  members: []
});
const slots = useSlots();
const rules = ref({
  groupName: [
    { required: true, message: "请输入用户组名称", trigger: "blur" },
    { min: 1, max: 15, message: "用户组名称长度在1-15个字符之间", trigger: "blur" }
  ],
  members: [
    {
      required: true,
      validator: (rule: Record<string, any>, value: any, callback: Function) => {
        if (!editEmployeeGroup.value.memberIDs.length) {
          callback(new Error("至少应有一名成员"));
          return;
        }
        callback();
      },
      trigger: "change"
    }
  ]
});
const formRef = useTemplateRef<Record<string, any>>("formRef");
//#endregion
//#region 人员穿梭框
const searchedPanel = ref<"left" | "right">("left");
const searchKeyWord = ref("");
const initialEmployees = ref<EmployeeInformation[]>([]);
const displayedEmployees = ref<EmployeeInformation[]>([]);
// 防抖搜索处理
const handleSearch = useDebounceFn((query: string) => {
  if (!query.trim() || !employeesInformation?.length) {
    const combined = [
      ...initialEmployees.value,
      ...employeesInformation.filter((employee) => editEmployeeGroup.value.memberIDs.includes(employee.employeeID))
    ];
    displayedEmployees.value = uniqBy(combined, "employeeID");
    return;
  }
  if (searchedPanel.value === "left") {
    const searchResults = employeesInformation.filter(
      (employee) => employee.employeeName.startsWith(query) || employee.namePinyin.startsWith(query)
    );
    const combined = [
      ...employeesInformation!.filter((employee) => editEmployeeGroup.value.memberIDs.includes(employee.employeeID)),
      ...searchResults
    ];
    displayedEmployees.value = uniqBy(combined, "employeeID");
  }
  if (searchedPanel.value === "right") {
    const excludeEmployeeIDs = editEmployeeGroup.value.memberIDs.filter((employeeID) => {
      const emp = employeesInformation.find((info) => info.employeeID === employeeID);
      return !emp?.employeeName.startsWith(query) && !emp?.namePinyin.startsWith(query);
    });
    displayedEmployees.value = differenceWith(displayedEmployees.value, excludeEmployeeIDs, (employee, key) => employee.employeeID === key);
  }
}, 300);
/**
 * @description: 处理穿梭框数据变化
 * @param value 新值
 * @param direction "left" | "right" 移动方向
 * @param movedKeys 本次移动的键值数组
 * @return
 */
const changeTransfer = (value: string[], direction: "left" | "right", movedKeys: string[]) => {
  if (direction === "left") {
    editEmployeeGroup.value.members = differenceWith(
      editEmployeeGroup.value.members,
      movedKeys,
      (member, key) => member.employeeID === key
    );
    return;
  }
  editEmployeeGroup.value.members = [
    ...editEmployeeGroup.value.members,
    ...initialEmployees.value.filter((employee) => movedKeys.includes(employee.employeeID))
  ];
};
//#endregion
</script>
<style scoped lang="scss">
.employee-group-editor {
  height: 100%;
}
/* 调整transfer宽度 */
.employee-transfer {
  width: 100%;
  display: flex;
  :deep(.el-transfer-panel) {
    flex: 4;
  }
  :deep(.el-transfer__buttons) {
    margin: auto;
    flex: 2;
  }
}
.member-form-item {
  height: 85%;
  .searched-panel {
    width: 96px;
  }
  :deep(.el-form-item__content) {
    height: 100%;
    flex-direction: column;
    .employee-transfer {
      flex: 1;
      overflow-y: auto;
      .el-transfer-panel {
        display: flex;
        flex-direction: column;
        .el-transfer-panel__body {
          flex: 1;
          display: flex;
          flex-direction: column;
          .el-transfer-panel__list {
            flex: 1;
          }
        }
      }
    }
  }
}
/* 选项列表 */
.employee-transfer {
  /* 列表其它项和最后一项宽度调整至一致 */
  :deep(.el-transfer-panel__item) {
    margin-right: 16px;
  }
  /* 明细内容和姓名两端对齐，明细内容使用浅色 */
  .member-info {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    .member-detail {
      color: #999999;
    }
  }
}
/* 隐藏右侧面板的搜索框 */
.employee-transfer :deep(.el-transfer-panel:last-child) .el-input.el-transfer-panel__filter {
  display: none;
}
</style>
