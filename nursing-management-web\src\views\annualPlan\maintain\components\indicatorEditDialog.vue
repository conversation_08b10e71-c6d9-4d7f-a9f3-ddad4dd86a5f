<!--
 * FilePath     : \src\views\annualPlan\maintain\components\indicatorEditDialog.vue
 * Author       : 杨欣欣
 * Date         : 2025-05-08 09:27
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 15:30
 * Description  : 策略指标编辑弹窗
 * CodeIterationRecord:
 -->
<template>
  <el-dialog
    class="indicator-edit"
    v-model="maintainStore.dialogVisible"
    :title="maintainStore.dialogTitle"
    @close="closeDialog"
    draggable
    align-center
    destroy-on-close
  >
    <el-form ref="modifyForm" class="indicator-form" :model="indicatorDetail" :rules="indicatorRules" label-width="auto">
      <el-form-item label="参考策略指标：">
        <el-select
          class="indicator-selector"
          v-model="indicatorDetail.annualIndicatorID"
          placeholder="请选择要参考的策略指标"
          @change="indicatorChange"
        >
          <el-option-group v-for="(group, index) in indicatorOptions" :key="index" :label="group.level">
            <el-option
              v-for="indicator in group.options"
              :key="indicator.annualIndicatorID"
              :label="indicator.indicatorContent"
              :value="indicator.annualIndicatorID"
            />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item label="策略指标内容：" prop="localShowName" class="indicator-content">
        <el-input type="textarea" autosize v-model="indicatorDetail.localShowName" />
        <el-select v-model="indicatorDetail.operator" class="indicator-operator">
          <el-option label="" value="" />
          <el-option label=">" value=">" />
          <el-option label="≥" value="≥" />
          <el-option label="=" value="=" />
          <el-option label="<" value="<" />
          <el-option label="≤" value="≤" />
        </el-select>
        <el-input class="ref-value" v-model="indicatorDetail!.referenceValue">
          <template #append>
            <el-select v-model="indicatorDetail!.unit" placeholder="" class="indicator-unit">
              <el-option
                v-for="(indicatorUnit, index) in indicatorUnits"
                :key="index"
                :label="indicatorUnit.value"
                :value="indicatorUnit.value"
              />
              <template #footer>
                <div class="custom-unit">
                  <el-input v-model="customUnit" placeholder="自定义" />
                  <el-button type="primary" @click="addUnitOption"> 确定 </el-button>
                </div>
              </template>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input class="text-area" type="textarea" autosize v-model="indicatorDetail!.remark" />
      </el-form-item>
      <el-form-item label="注记：">
        <el-radio-group v-model="indicatorDetail!.markID">
          <el-radio value="">无（去年达标持续关注）</el-radio>
          <el-radio
            v-for="indicatorMark in markSettings.filter((markSetting) => markSetting.groupID === 'indicator')"
            :key="indicatorMark.administrationIconID"
            :value="indicatorMark.administrationIconID.toString()"
          >
            <span v-html="getHtmlMark(indicatorMark, true)" />
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button v-if="!readOnly" type="primary" @click="saveDetail">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import type { planIndicator } from "../../types/annualPlanMain";
import { useDetailIcon } from "../../hooks/useDetailIcon";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";
import { useAnnualPlanDictionaryStore } from "../hooks/useAnnualPlanDictionaryStore";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
const { readOnly } = storeToRefs(usePlanManagementStore());

const maintainStore = useAnnualPlanMaintainStore();
const isAdd = computed(() => !maintainStore.editingDetail?.detailID);
// 创建一个计算属性，确保类型为 planIndicator
const indicatorDetail = computed<planIndicator>({
  get: () => maintainStore.editingDetail as planIndicator,
  set: (value: planIndicator) => Object.assign(maintainStore.editingDetail!, value)
});

const { markSettings, getHtmlMark } = useDetailIcon();
//#region 单位下拉项处理
onMounted(async () => {
  await getUnits();
});
const customUnit = ref<string>();
let indicatorUnits: Record<string, any>[] = [];
watch(indicatorDetail, (newValue) => {
  if (newValue.unit && !indicatorUnits.find((unit) => unit.value === newValue.unit)) {
    customUnit.value = newValue.unit;
    addUnitOption();
  }
});
/**
 * @description: 获取单位字典
 */
const getUnits = async () => {
  const params: SettingDictionaryParams = {
    settingType: "AnnualPlan",
    settingTypeCode: "AnnualPlanIndicatorDetail",
    settingTypeValue: "Unit"
  };
  indicatorUnits = await settingDictionaryService.getSettingDictionaryDict(params);
};
/**
 * @description: 新增单位选项
 */
const addUnitOption = () => {
  if (!customUnit.value) {
    return;
  }
  indicatorUnits.push({
    value: customUnit.value,
    label: customUnit.value
  });
  indicatorDetail.value.unit = customUnit.value;
  customUnit.value = undefined;
};
//#endregion
//#region 参考策略指标
/**
 * @description: 指标下拉框选项
 * @param computed
 * @return
 */
const indicatorOptions = computed(() => {
  const currentDetailIndicator = dictionaryStore.indicatorList.find(
    (indicator) => indicator.annualIndicatorID === indicatorDetail.value.annualIndicatorID
  );
  const optionArr = currentDetailIndicator
    ? [currentDetailIndicator, ...dictionaryStore.noRefIndicatorList]
    : dictionaryStore.noRefIndicatorList;
  return [
    {
      level: "上级指标",
      options: optionArr.filter((option) => option.level === "upper")
    },
    {
      level: "本级指标",
      options: optionArr.filter((option) => option.level === "current")
    }
  ];
});
/**
 * @description: 自动填充指标内容
 * @param value 指标ID
 * @return
 */
const indicatorChange = (value: number) => {
  const content = indicatorOptions.value
    .flatMap((group) => group.options)
    .find((option) => option.annualIndicatorID === value)!.indicatorContent;
  indicatorDetail.value.localShowName = content.trim();
};
//#endregion

//#region 表单相关
const indicatorRules = ref({
  localShowName: [{ required: true, message: "指标描述不可为空", trigger: "blur" }]
});
const modifyForm = ref<Record<string, any>>();
const dictionaryStore = useAnnualPlanDictionaryStore();
/**
 * @description: 保存
 * @return
 */
const saveDetail = async () => {
  const validateResult = await useForm().validateRule(modifyForm);
  if (!validateResult) {
    showMessage("error", "请检查表单是否填写完整");
    return;
  }
  await (isAdd.value
    ? maintainStore.addNewPlanIndicatorToPlanGroup(indicatorDetail.value)
    : maintainStore.updatePlanIndicator(indicatorDetail.value));
  maintainStore.updateOriginalDetail(indicatorDetail.value);
  closeDialog();
};
/**
 * @description: 关闭弹窗，清理数据
 */
const closeDialog = () => {
  // 如果拖入的字典最后没有保存，需要从指标列表中删除
  if (isAdd.value && "indicatorContent" in indicatorDetail.value) {
    const maintainStore = useAnnualPlanMaintainStore();
    const groupID = maintainStore.originalDetail!.groupID!;
    const detailIndex = maintainStore.indicatorsByGroupID[groupID].findIndex(
      (indicator) => indicator.detailID === maintainStore.originalDetail!.detailID
    );
    maintainStore.indicatorsByGroupID[groupID].splice(detailIndex, 1);
  }
  maintainStore.closeDialog();
};
//#endregion
</script>
<style lang="scss">
.indicator-edit {
  height: inherit;
  width: 40%;
  .past-indicator-table {
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;
    border: 1px solid $border-color;
    .column:not(:last-child) {
      border-right: 1px solid $border-color;
    }
    .column {
      flex-grow: 1;
      text-align: center;

      .cell.header {
        font-weight: bold;
        color: #000000;
        background-color: #ffef99;
        border-bottom: 1px solid $border-color;
        padding: 4px 0;
      }
      .cell.content {
        padding: 4px 0;
      }
    }
  }
  .indicator-form {
    .el-form-item {
      &.indicator-content {
        padding-right: 8px;
        .el-form-item__content {
          flex-wrap: nowrap;
          gap: 8px;
        }
      }
      .indicator-selector {
        width: 100%;
      }
      .indicator-operator,
      .indicator-unit {
        width: 80px;
      }
      .ref-value {
        width: 200px;
        .el-input-group__append {
          padding: 0 20px;
        }
      }
    }
  }
}
.custom-unit .el-button {
  margin-top: 8px;
}
</style>
