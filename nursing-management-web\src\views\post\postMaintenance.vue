<!--
 * FilePath     : \src\views\post\postMaintenance.vue
 * Author       : 马超
 * Date         : 2025-03-17 14:38
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:38
 * Description  : 岗位维护页面
 * CodeIterationRecord:
 -->
<template>
  <div class="post-maintenance">
    <base-layout :drawerOptions="drawerOptions">
      <template #header>
        <el-button @click="addRecord" class="add-button">新增</el-button>
      </template>
      <el-table :data="postList" stripe border height="100%">
        <el-table-column prop="postName" label="岗位名称" min-width="200"></el-table-column>
        <el-table-column label="岗位类型" min-width="200" align="center">
          <template v-slot="scope">
            <span>{{ postTypeSetting.find((m: any) => m.value === scope.row.postTypeID)?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="postNature" label="岗位性质" min-width="200" align="CodeIterationRecord">
          <template v-slot="scope">
            <span>{{ postNatureSetting.find((m: any) => m.value === scope.row.postNatureID)?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="addEmployeeName" label="新增人" min-width="100" align="center"></el-table-column>
        <el-table-column label="新增时间" :width="convertPX(200)" align="center">
          <template v-slot="scope">
            <span v-formatTime="{ value: scope.row.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column prop="modifyEmployeeName" label="修改人" min-width="100" align="center"></el-table-column>
        <el-table-column label="修改时间" min-width="100" align="center">
          <template v-slot="scope">
            <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd' }"></span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="convertPX(100)">
          <template v-slot="scope">
            <el-tooltip content="修改">
              <i class="iconfont icon-edit" @click="editPost(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="停用" v-if="!scope.row.deleteFlag">
              <i class="iconfont icon-stop" @click="stopOrActivatePost(scope.row, true)"></i>
            </el-tooltip>
            <el-tooltip content="启用" v-if="scope.row.deleteFlag">
              <i class="iconfont icon-open" @click="stopOrActivatePost(scope.row, false)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <template #drawerContent>
        <el-form ref="submitRefs" :model="currRow" :label-width="convertPX(180)" class="form-style" :rules="rules">
          <el-form-item label="岗位名称：" prop="postName">
            <el-input v-model="currRow.postName" clearable placeholder="请输入岗位名称" width="100px" />
          </el-form-item>
          <el-form-item label="岗位类型：" prop="postTypeID">
            <el-select v-model="currRow.postTypeID" placeholder="请选择" clearable>
              <el-option v-for="item in postTypeSetting" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="岗位性质：" prop="postNatureID">
            <el-select v-model="currRow.postNatureID" placeholder="请选择" clearable>
              <el-option v-for="item in postNatureSetting" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>
    </base-layout>
  </div>
</template>

<script setup lang="ts">
import { postService } from "@/api/postService";
const convertPX: any = inject("convertPX");
onMounted(() => {
  getPostSetting();
  getPostList();
});
const submitRefs = ref({}) as any;
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    await saveRecord(currRow.value);
  }
});
// 岗位类型配置
const postTypeSetting = ref<Record<string, any>>([]);
// 岗位性质配置
const postNatureSetting = ref<Record<string, any>>([]);
const getPostSetting = async () => {
  let paramType: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "Post",
    settingTypeValue: "PostType"
  };
  await settingDictionaryService.getSettingDictionaryDict(paramType).then((datas: any) => {
    postTypeSetting.value = datas;
  });
  let paramNature: SettingDictionaryParams = {
    settingType: "PositionManagement",
    settingTypeCode: "PostNature",
    settingTypeValue: "PostNature"
  };
  await settingDictionaryService.getSettingDictionaryDict(paramNature).then((datas: any) => {
    postNatureSetting.value = datas;
  });
};
const getPostList = () => {
  postService.getPostList().then((res: any) => {
    postList.value = res;
  });
};
const currRow = ref<{}>() as any;
/**
 * @description: 新增按钮处理
 */
const addRecord = () => {
  drawerOptions.value.drawerTitle = "新增岗位";
  currRow.value = {
    postName: "",
    postTypeID: "",
    postNatureID: ""
  };
  drawerOptions.value.showDrawer = true;
};
const postList = ref([]);
/**
 * @description: 保存岗位
 */
const saveRecord = async (row: any) => {
  let param = {
    postID: row.postID,
    postName: row.postName,
    postTypeID: row.postTypeID,
    postNatureID: row.postNatureID
  };
  await postService.savePostData(param).then((res: any) => {
    if (res) {
      getPostList();
      showMessage("success", "保存成功");
      drawerOptions.value.showDrawer = false;
    }
  });
};
/**
 * @description: 修改岗位
 */
const editPost = (row: any) => {
  drawerOptions.value.drawerTitle = "修改岗位";
  currRow.value = { ...row };
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 停止或启用岗位
 */
const stopOrActivatePost = async (row: any, stopFlag: boolean) => {
  let param = {
    postID: row.postID
  };
  if (stopFlag) {
    let existFlag = false;
    await postService.getPostWhetherData(param).then((res: any) => {
      existFlag = res;
    });
    if (existFlag) {
      confirmBox("停用此岗位将影响部门岗位、岗位说明书、排班、考勤等功能，确定要停用么？", "停用流程", async (flag: Boolean) => {
        if (!flag) {
          return;
        }
        await postService.deletePostData(param).then((res: any) => {
          if (res) {
            getPostList();
            showMessage("success", "停用成功");
          }
        });
      });
    } else {
      await postService.deletePostData(param).then((res: any) => {
        if (res) {
          getPostList();
          showMessage("success", "停用成功");
        }
      });
    }
  } else {
    postService.activatePostData(param).then((res: any) => {
      if (res) {
        getPostList();
        showMessage("success", "启用成功");
      }
    });
  }
};
const rules = reactive({
  postName: [{ required: true, message: "请输入岗位名称", trigger: "change" }],
  postTypeID: [{ required: true, message: "请选择岗位类型", trigger: "change" }],
  postNatureID: [{ required: true, message: "请选择岗位性质", trigger: "change" }]
});
</script>

<style lang="scss">
.post-maintenance {
  height: 100%;
  width: 100%;
  .form-style {
    margin: 0;
    padding-top: 2%;
  }
}
</style>
