import {
  ABCToNumber,
  AbsoluteRefType,
  ActionIterator,
  AlignTypeH,
  AlignTypeV,
  ArrangeTypeEnum,
  ArrowsAndMarkersShapes,
  AuthzIoLocalService,
  AutoFillSeries,
  BaselineOffset,
  BasicShapes,
  BlockType,
  BooleanNumber,
  BorderStyleTypes,
  BorderType,
  BuildTextUtils,
  BulletAlignment,
  COLORS,
  CellValueType,
  Color,
  ColorBuilder,
  ColorKit,
  ColorType,
  ColumnSeparatorType,
  CommandService,
  CommandType,
  CommonHideTypes,
  ConfigService,
  ContextService,
  CopyPasteType,
  CustomCommandExecutionError,
  CustomDecorationType,
  CustomRangeType,
  DEFAULT_CELL,
  DEFAULT_DOC,
  DEFAULT_DOCUMENT_SUB_COMPONENT_ID,
  DEFAULT_EMPTY_DOCUMENT_VALUE,
  DEFAULT_RANGE,
  DEFAULT_RANGE_ARRAY,
  DEFAULT_SELECTION,
  DEFAULT_SLIDE,
  DEFAULT_STYLES,
  DEFAULT_WORKSHEET_COLUMN_COUNT,
  DEFAULT_WORKSHEET_COLUMN_COUNT_KEY,
  DEFAULT_WORKSHEET_COLUMN_TITLE_HEIGHT,
  DEFAULT_WORKSHEET_COLUMN_TITLE_HEIGHT_KEY,
  DEFAULT_WORKSHEET_COLUMN_WIDTH,
  DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY,
  DEFAULT_WORKSHEET_ROW_COUNT,
  DEFAULT_WORKSHEET_ROW_COUNT_KEY,
  DEFAULT_WORKSHEET_ROW_HEIGHT,
  DEFAULT_WORKSHEET_ROW_HEIGHT_KEY,
  DEFAULT_WORKSHEET_ROW_TITLE_WIDTH,
  DEFAULT_WORKSHEET_ROW_TITLE_WIDTH_KEY,
  DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY,
  DOCS_NORMAL_EDITOR_UNIT_ID_KEY,
  DOCS_ZEN_EDITOR_UNIT_ID_KEY,
  DOC_RANGE_TYPE,
  DashStyleType,
  DataStreamTreeNodeType,
  DataStreamTreeTokenType,
  DataValidationErrorStyle,
  DataValidationImeMode,
  DataValidationOperator,
  DataValidationRenderMode,
  DataValidationStatus,
  DataValidationType,
  DeleteDirection,
  DependentOn,
  DesktopLogService,
  DeveloperMetadataVisibility,
  Dimension,
  Direction,
  Disposable,
  DisposableCollection,
  DocStyleType,
  DocumentDataModel,
  DocumentFlavor,
  DrawingTypeEnum,
  EDITOR_ACTIVATED,
  EXTENSION_NAMES,
  ErrorService,
  EventState,
  EventSubject,
  FOCUSING_COMMON_DRAWINGS,
  FOCUSING_DOC,
  FOCUSING_EDITOR_BUT_HIDDEN,
  FOCUSING_EDITOR_INPUT_FORMULA,
  FOCUSING_EDITOR_STANDALONE,
  FOCUSING_FX_BAR_EDITOR,
  FOCUSING_SHEET,
  FOCUSING_SLIDE,
  FOCUSING_UNIT,
  FOCUSING_UNIVER_EDITOR,
  FOCUSING_UNIVER_EDITOR_STANDALONE_SINGLE_MODE,
  FORMULA_EDITOR_ACTIVATED,
  FollowNumberWithType,
  FontItalic,
  FontStyleType,
  FontWeight,
  FormatType,
  GlyphType,
  GridType,
  HLSColor,
  HorizontalAlign,
  IAuthzIoService,
  ICommandService,
  IConfigService,
  IContextService,
  ILocalStorageService,
  ILogService,
  IPermissionService,
  IResourceLoaderService,
  IResourceManagerService,
  IS_ROW_STYLE_PRECEDE_COLUMN_STYLE,
  IUndoRedoService,
  IUniverInstanceService,
  Inject,
  Injector,
  InterceptorEffectEnum,
  InterceptorManager,
  InterpolationPointType,
  JSONX,
  LRUHelper,
  LRUMap,
  LifecycleService,
  LifecycleStages,
  LocalUndoRedoService,
  LocaleService,
  LocaleType,
  LogLevel,
  LookUp,
  MOVE_BUFFER_VALUE,
  Many,
  MemoryCursor,
  NamedStyleType,
  NilCommand,
  NumberUnitType,
  ObjectMatrix,
  ObjectRelativeFromH,
  ObjectRelativeFromV,
  Optional,
  OtherShapes,
  PRESET_LIST_TYPE,
  PageElementType,
  PageOrientType,
  PageType,
  ParagraphElementType,
  PermissionService,
  PermissionStatus,
  Plugin,
  PluginService,
  PositionedObjectLayoutType,
  PresetListType,
  ProtectionType,
  Quantity,
  QuickListType,
  QuickListTypeMap,
  RANGE_DIRECTION,
  RANGE_TYPE,
  RCDisposable,
  RGBA_PAREN,
  RGB_PAREN,
  ROTATE_BUFFER_VALUE,
  RTree,
  Range,
  Rectangle,
  RediConsumer,
  RediContext,
  RediError,
  RediProvider,
  RedoCommand,
  RedoCommandId,
  RefAlias,
  Registry,
  RegistryAsMap,
  RelativeDate,
  RelativeSlideLink,
  ResourceManagerService,
  RgbColor,
  RxDisposable,
  SectionType,
  Self,
  SheetTypes,
  SheetViewModel,
  SkipSelf,
  SliceBodyType,
  SlideDataModel,
  SpacingRule,
  SpecialShapes,
  Styles,
  THEME_COLORS,
  TabStopAlignment,
  TableAlignmentType,
  TableCellHeightRule,
  TableLayoutType,
  TableSizeType,
  TableTextWrapType,
  TextDecoration,
  TextDirection,
  TextDirectionType,
  TextX,
  TextXActionType,
  ThemeColor,
  ThemeColorType,
  ThemeColors,
  ThemeService,
  Tools,
  UndoCommand,
  UndoCommandId,
  UnitModel,
  Univer,
  UniverInstanceService,
  UpdateDocsAttributeType,
  UserManagerService,
  VerticalAlign,
  VerticalAlignmentType,
  WithDependency,
  WithNew,
  Workbook,
  Worksheet,
  WrapStrategy,
  WrapTextType,
  _,
  afterInitApply,
  awaitTime,
  binarySearchArray,
  bufferDebounceTime,
  cellToRange,
  characterSpacingControlType,
  checkForSubstrings,
  checkIfMove,
  checkParagraphHasBullet,
  checkParagraphHasIndent,
  checkParagraphHasIndentByStyle,
  codeToBlob,
  composeBody,
  composeInterceptors,
  composeStyles,
  concatMatrixArray,
  connectDependencies,
  connectInjector,
  convertBodyToHtml,
  covertTextRunToHtml,
  createDefaultUser,
  createIdentifier,
  createInterceptorKey,
  createInternalEditorID,
  createRowColIter,
  debounce,
  dedupe,
  deepCompare,
  deleteContent,
  extractPureTextFromCell,
  forwardRef,
  fromCallback,
  fromEventSubject,
  fromObservable,
  generateRandomId,
  get,
  getArrayLength,
  getBodySlice,
  getBodySliceHtml,
  getBorderStyleType,
  getCellInfoInMergeData,
  getCellValueType,
  getColorStyle,
  getCustomDecorationSlice,
  getCustomRangeSlice,
  getDocsUpdateBody,
  getIntersectRange,
  getOriginCellValue,
  getReverseDirection,
  getWorksheetUID,
  groupBy,
  handleStyleToString,
  hashAlgorithm,
  horizontalLineSegmentsSubtraction,
  index$1,
  insertMatrixArray,
  insertTextToContent,
  isAsyncDependencyItem,
  isAsyncHook,
  isBlackColor,
  isBooleanString,
  isCellCoverable,
  isCellV,
  isClassDependencyItem,
  isCtor,
  isDisposable,
  isEmptyCell,
  isFactoryDependencyItem,
  isFormulaId,
  isFormulaString,
  isICellData,
  isInternalEditorID,
  isNullCell,
  isNumeric,
  isRangesEqual,
  isRealNum,
  isSafeNumeric,
  isSameStyleTextRun,
  isUnitRangesEqual,
  isValidRange,
  isValueDependencyItem,
  isWhiteColor,
  makeArray,
  makeCellRangeToRangeData,
  makeCellToSelection,
  makeCustomRangeStream,
  merge$1,
  mergeOverrideWithDependencies,
  mergeSets,
  mergeWith$1,
  mergeWorksheetSnapshotWithDefault,
  mixinClass,
  moveMatrixArray,
  moveRangeByOffset,
  nameCharacterCheck,
  normalizeBody,
  normalizeTextRuns,
  numberToABC,
  numberToListABC,
  numfmt,
  orderSearchArray,
  queryObjectMatrix,
  registerDependencies,
  remove,
  repeatStringNumTimes,
  replaceInDocumentBody,
  requestImmediateMacroTask,
  rotate,
  searchArray,
  selectionToArray,
  sequence,
  sequenceAsync,
  sequenceExecute,
  sequenceExecuteAsync,
  set,
  setDependencies,
  shallowEqual,
  skipParseTagNames,
  sliceMatrixArray,
  sortRules,
  sortRulesByDesc,
  sortRulesFactory,
  spliceArray,
  splitIntoGrid,
  takeAfter,
  throttle,
  toDisposable,
  touchDependencies,
  updateAttributeByDelete,
  updateAttributeByInsert,
  useDependency,
  useDependencyContext,
  useDependencyContextValue,
  useDependencyValue,
  useInjector,
  useObservable,
  useUpdateBinder
} from "./chunk-DVIEXIMM.js";
import "./chunk-YYCUISGT.js";
import "./chunk-PLDDJCW6.js";
export {
  ABCToNumber,
  AbsoluteRefType,
  ActionIterator,
  AlignTypeH,
  AlignTypeV,
  ArrangeTypeEnum,
  ArrowsAndMarkersShapes,
  AuthzIoLocalService,
  AutoFillSeries,
  BaselineOffset,
  BasicShapes,
  BlockType,
  BooleanNumber,
  BorderStyleTypes,
  BorderType,
  BuildTextUtils,
  BulletAlignment,
  COLORS,
  CellValueType,
  Color,
  ColorBuilder,
  ColorKit,
  ColorType,
  ColumnSeparatorType,
  CommandService,
  CommandType,
  CommonHideTypes,
  ConfigService,
  ContextService,
  CopyPasteType,
  CustomCommandExecutionError,
  CustomDecorationType,
  CustomRangeType,
  DEFAULT_CELL,
  DEFAULT_DOC,
  DEFAULT_DOCUMENT_SUB_COMPONENT_ID,
  DEFAULT_EMPTY_DOCUMENT_VALUE,
  DEFAULT_RANGE,
  DEFAULT_RANGE_ARRAY,
  DEFAULT_SELECTION,
  DEFAULT_SLIDE,
  DEFAULT_STYLES,
  DEFAULT_WORKSHEET_COLUMN_COUNT,
  DEFAULT_WORKSHEET_COLUMN_COUNT_KEY,
  DEFAULT_WORKSHEET_COLUMN_TITLE_HEIGHT,
  DEFAULT_WORKSHEET_COLUMN_TITLE_HEIGHT_KEY,
  DEFAULT_WORKSHEET_COLUMN_WIDTH,
  DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY,
  DEFAULT_WORKSHEET_ROW_COUNT,
  DEFAULT_WORKSHEET_ROW_COUNT_KEY,
  DEFAULT_WORKSHEET_ROW_HEIGHT,
  DEFAULT_WORKSHEET_ROW_HEIGHT_KEY,
  DEFAULT_WORKSHEET_ROW_TITLE_WIDTH,
  DEFAULT_WORKSHEET_ROW_TITLE_WIDTH_KEY,
  DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY,
  DOCS_NORMAL_EDITOR_UNIT_ID_KEY,
  DOCS_ZEN_EDITOR_UNIT_ID_KEY,
  DOC_RANGE_TYPE,
  DashStyleType,
  DataStreamTreeNodeType,
  DataStreamTreeTokenType,
  DataValidationErrorStyle,
  DataValidationImeMode,
  DataValidationOperator,
  DataValidationRenderMode,
  DataValidationStatus,
  DataValidationType,
  DeleteDirection,
  DependentOn,
  DesktopLogService,
  DeveloperMetadataVisibility,
  Dimension,
  Direction,
  Disposable,
  DisposableCollection,
  DocStyleType,
  DocumentDataModel,
  DocumentFlavor,
  DrawingTypeEnum,
  EDITOR_ACTIVATED,
  EXTENSION_NAMES,
  ErrorService,
  EventState,
  EventSubject,
  FOCUSING_COMMON_DRAWINGS,
  FOCUSING_DOC,
  FOCUSING_EDITOR_BUT_HIDDEN,
  FOCUSING_EDITOR_INPUT_FORMULA,
  FOCUSING_EDITOR_STANDALONE,
  FOCUSING_FX_BAR_EDITOR,
  FOCUSING_SHEET,
  FOCUSING_SLIDE,
  FOCUSING_UNIT,
  FOCUSING_UNIVER_EDITOR,
  FOCUSING_UNIVER_EDITOR_STANDALONE_SINGLE_MODE,
  FORMULA_EDITOR_ACTIVATED,
  FollowNumberWithType,
  FontItalic,
  FontStyleType,
  FontWeight,
  FormatType,
  GlyphType,
  GridType,
  HLSColor,
  HorizontalAlign,
  IAuthzIoService,
  ICommandService,
  IConfigService,
  IContextService,
  ILocalStorageService,
  ILogService,
  IPermissionService,
  IResourceLoaderService,
  IResourceManagerService,
  IS_ROW_STYLE_PRECEDE_COLUMN_STYLE,
  IUndoRedoService,
  IUniverInstanceService,
  Inject,
  Injector,
  InterceptorEffectEnum,
  InterceptorManager,
  InterpolationPointType,
  index$1 as JSON1,
  JSONX,
  LRUHelper,
  LRUMap,
  LifecycleService,
  LifecycleStages,
  LocalUndoRedoService,
  LocaleService,
  LocaleType,
  LogLevel,
  LookUp,
  MOVE_BUFFER_VALUE,
  Many,
  MemoryCursor,
  NamedStyleType,
  NilCommand,
  NumberUnitType,
  ObjectMatrix,
  ObjectRelativeFromH,
  ObjectRelativeFromV,
  Optional,
  OtherShapes,
  PRESET_LIST_TYPE,
  PageElementType,
  PageOrientType,
  PageType,
  ParagraphElementType,
  PermissionService,
  PermissionStatus,
  Plugin,
  PluginService,
  PositionedObjectLayoutType,
  PresetListType,
  ProtectionType,
  Quantity,
  QuickListType,
  QuickListTypeMap,
  RANGE_DIRECTION,
  RANGE_TYPE,
  RCDisposable,
  RGBA_PAREN,
  RGB_PAREN,
  ROTATE_BUFFER_VALUE,
  RTree,
  Range,
  Rectangle,
  RediConsumer,
  RediContext,
  RediError,
  RediProvider,
  RedoCommand,
  RedoCommandId,
  RefAlias,
  Registry,
  RegistryAsMap,
  RelativeDate,
  RelativeSlideLink,
  ResourceManagerService,
  RgbColor,
  RxDisposable,
  SectionType,
  Self,
  SheetTypes,
  SheetViewModel,
  SkipSelf,
  SliceBodyType,
  SlideDataModel,
  SpacingRule,
  SpecialShapes,
  Styles,
  THEME_COLORS,
  TabStopAlignment,
  TableAlignmentType,
  TableCellHeightRule,
  TableLayoutType,
  TableSizeType,
  TableTextWrapType,
  TextDecoration,
  TextDirection,
  TextDirectionType,
  TextX,
  TextXActionType,
  ThemeColor,
  ThemeColorType,
  ThemeColors,
  ThemeService,
  Tools,
  UndoCommand,
  UndoCommandId,
  UnitModel,
  Univer,
  UniverInstanceService,
  _ as UniverInstanceType,
  UpdateDocsAttributeType,
  UserManagerService,
  VerticalAlign,
  VerticalAlignmentType,
  WithDependency,
  WithNew,
  Workbook,
  Worksheet,
  WrapStrategy,
  WrapTextType,
  afterInitApply,
  awaitTime,
  binarySearchArray,
  bufferDebounceTime,
  cellToRange,
  characterSpacingControlType,
  checkForSubstrings,
  checkIfMove,
  checkParagraphHasBullet,
  checkParagraphHasIndent,
  checkParagraphHasIndentByStyle,
  codeToBlob,
  composeBody,
  composeInterceptors,
  composeStyles,
  concatMatrixArray,
  connectDependencies,
  connectInjector,
  convertBodyToHtml,
  covertTextRunToHtml,
  createDefaultUser,
  createIdentifier,
  createInterceptorKey,
  createInternalEditorID,
  createRowColIter,
  debounce,
  dedupe,
  deepCompare,
  deleteContent,
  extractPureTextFromCell,
  forwardRef,
  fromCallback,
  fromEventSubject,
  fromObservable,
  generateRandomId,
  get,
  getArrayLength,
  getBodySlice,
  getBodySliceHtml,
  getBorderStyleType,
  getCellInfoInMergeData,
  getCellValueType,
  getColorStyle,
  getCustomDecorationSlice,
  getCustomRangeSlice,
  getDocsUpdateBody,
  getIntersectRange,
  getOriginCellValue,
  getReverseDirection,
  getWorksheetUID,
  groupBy,
  handleStyleToString,
  hashAlgorithm,
  horizontalLineSegmentsSubtraction,
  insertMatrixArray,
  insertTextToContent,
  isAsyncDependencyItem,
  isAsyncHook,
  isBlackColor,
  isBooleanString,
  isCellCoverable,
  isCellV,
  isClassDependencyItem,
  isCtor,
  isDisposable,
  isEmptyCell,
  isFactoryDependencyItem,
  isFormulaId,
  isFormulaString,
  isICellData,
  isInternalEditorID,
  isNullCell,
  isNumeric,
  isRangesEqual,
  isRealNum,
  isSafeNumeric,
  isSameStyleTextRun,
  isUnitRangesEqual,
  isValidRange,
  isValueDependencyItem,
  isWhiteColor,
  makeArray,
  makeCellRangeToRangeData,
  makeCellToSelection,
  makeCustomRangeStream,
  merge$1 as merge,
  mergeOverrideWithDependencies,
  mergeSets,
  mergeWith$1 as mergeWith,
  mergeWorksheetSnapshotWithDefault,
  mixinClass,
  moveMatrixArray,
  moveRangeByOffset,
  nameCharacterCheck,
  normalizeBody,
  normalizeTextRuns,
  numberToABC,
  numberToListABC,
  numfmt,
  orderSearchArray,
  queryObjectMatrix,
  registerDependencies,
  remove,
  repeatStringNumTimes,
  replaceInDocumentBody,
  requestImmediateMacroTask,
  rotate,
  searchArray,
  selectionToArray,
  sequence,
  sequenceAsync,
  sequenceExecute,
  sequenceExecuteAsync,
  set,
  setDependencies,
  shallowEqual,
  skipParseTagNames,
  sliceMatrixArray,
  sortRules,
  sortRulesByDesc,
  sortRulesFactory,
  spliceArray,
  splitIntoGrid,
  takeAfter,
  throttle,
  toDisposable,
  touchDependencies,
  updateAttributeByDelete,
  updateAttributeByInsert,
  useDependency,
  useDependencyContext,
  useDependencyContextValue,
  useDependencyValue,
  useInjector,
  useObservable,
  useUpdateBinder
};
//# sourceMappingURL=@univerjs_core.js.map
