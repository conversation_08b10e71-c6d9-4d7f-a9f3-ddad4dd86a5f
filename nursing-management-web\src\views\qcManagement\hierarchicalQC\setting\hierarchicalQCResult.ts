/*
 * relative     : \src\views\qcManagement\hierarchicalQC\setting\hierarchicalQCResult.ts
 * Author       : 郭鹏超
 * Date         : 2024-11-01 09:50
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:43
 * Description  :考核画面差异配置
 * CodeIterationRecord:
 */
let { userStore } = useStore();

class hierarchicalQCResultOption {
  qcLevel: string;
  departmentID?: number;
  formType: string | undefined;
  subjectOptions: Record<string, any>[] = [];
  qcDepartmentOptions: Record<string, any>[] = [];
  qcEmployeeOptions: Record<string, any>[] = [];
  restrictExamineDateFlag: boolean = false;
  qcType: "nodeQCFormType" | undefined;
  notShowApprovalContentFlag: boolean = false;
  constructor(props: Record<string, any>) {
    this.qcType = "nodeQCFormType";
    this.qcLevel = props.routerQcLevel;
    this.departmentID = props.routerQcLevel === "1" ? userStore.departmentID : undefined;
    this.formType = props.formType;
    this.getNotShowApprovalContentFlag();
    this.getAddRecordRestrictDateSetting();
  }
  getNotShowApprovalContentFlag() {
    let params: SettingDictionaryParams = {
      settingType: "HierarchicalQC",
      settingTypeCode: "SystemSwitch",
      settingTypeValue: "NotShowApprovalContent"
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      this.notShowApprovalContentFlag = Boolean(respBool);
    });
  }
  /**
   * @description: 质控时间范围获取
   */
  getAddRecordRestrictDateSetting() {
    let params: SettingDictionaryParams = {
      settingType: "HierarchicalQC",
      settingTypeCode: "SystemSwitch",
      settingTypeValue: `AddRecordRestrictDate_${this.qcLevel}`,
      index: Math.random()
    };
    settingDictionaryService.getSettingSwitch(params).then((respBool: any) => {
      this.restrictExamineDateFlag = Boolean(respBool);
    });
  }
  /**
   * @description:组装以table 数据为基础的options
   * @param tableData
   * @return
   */
  getSearchOptions(tableData: Record<string, any>[]) {
    this.subjectOptions = [];
    this.qcDepartmentOptions = [];
    this.qcEmployeeOptions = [];
    if (tableData?.length === 0) {
      return;
    }

    // 使用 Set 来去重
    const subjectSet = new Set();
    const departmentSet = new Set();
    const employeeSet = new Set();

    tableData.forEach((item: any) => {
      // 去重 subjectOptions
      const subjectKey = item.hierarchicalQCSubjectID;
      if (!subjectSet.has(subjectKey)) {
        subjectSet.add(subjectKey);
        this.subjectOptions.push({
          label: item.formName,
          value: subjectKey
        });
      }

      // 去重 qcDepartmentOptions
      const departmentKey = item.examineDepartmentID;
      if (!departmentSet.has(departmentKey)) {
        departmentSet.add(departmentKey);
        this.qcDepartmentOptions.push({
          label: item.examineObject,
          value: departmentKey
        });
      }

      // 去重 qcEmployeeOptions
      const examineEmployeeNameList = (item.examineEmployee ?? "").split("、");
      (item.examineEmployeeIDList ?? []).forEach((employeeID: string, index: number) => {
        if (!employeeSet.has(employeeID)) {
          employeeSet.add(employeeID);
          this.qcEmployeeOptions.push({
            label: examineEmployeeNameList[index],
            value: employeeID
          });
        }
      });
    });
  }
}

export default hierarchicalQCResultOption;
