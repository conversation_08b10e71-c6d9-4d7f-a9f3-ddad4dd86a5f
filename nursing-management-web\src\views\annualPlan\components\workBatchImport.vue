<!--
 * FilePath     : \src\views\annualPlan\components\workBatchImport.vue
 * Author       : 杨欣欣
 * Date         : 2025-02-19 18:06
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 18:44
 * Description  : 批量导入工作
 * CodeIterationRecord:
 -->
<template>
  <div class="work-batch-import">
    <div class="left-wrapper">
      <div class="title">可选工作列表</div>
      <el-tree class="source-tree" ref="sourceTree" :props="treeProps" :data="sourceData" default-expand-all @node-click="selectWork">
        <template #default="{ node, data }">
          <template v-if="node.level >= 3">
            <div
              :class="[sessionStore.debugMode && (workInterventionIDSourceCount.get(data.apInterventionID) ?? 0) > 1 ? 'highlight' : '']"
              class="content"
            >
              {{ node.label }}
            </div>
            <div v-if="sessionStore.debugMode" class="requirement">{{ data.apInterventionID }}</div>
            <div class="requirement">{{ data.requirement }}</div>
          </template>
        </template>
      </el-tree>
    </div>
    <div class="right-wrapper">
      <div class="title">已选工作列表</div>
      <el-tree
        class="target-tree"
        :props="treeProps"
        :data="targetData"
        default-expand-all
        draggable
        :allow-drag="(node:Node) => node.level !== 1"
        :allow-drop="allowDrop"
      >
        <template #default="{ node, data }">
          <template v-if="typeGroupTypeGuard(data)">
            <div class="content">
              <el-badge :value="data.children.length" class="type-badge">
                {{ node.label }}
              </el-badge>
            </div>
          </template>
          <template v-else-if="workTypeGuard(data)">
            <div class="content">{{ node.label }}</div>
            <div class="requirement">{{ data.requirement }}</div>
          </template>
        </template>
      </el-tree>
    </div>
  </div>
</template>
<script setup lang="ts">
import type Node from "element-plus/es/components/tree/src/model/node";
import type { TreeNodeData } from "element-plus/es/components/tree/src/tree.type";
import { groupBy, pickBy } from "lodash-es";
import workSelector from "./workSelector.vue";
import type { Action, MessageBoxState } from "element-plus/es/components/message-box/src/message-box.type";
import type { planWorkImportVo } from "../types/planWorkImportVo";
type typeGroup = planWorkImportVo["children"][number];
type work = typeGroup["children"][number];
const { sourceData, departmentID } = defineProps<{
  sourceData: planWorkImportVo[];
  departmentID: number;
}>();
const targetData = defineModel<typeGroup[]>({ required: true });
const { sessionStore } = useStore();
//#region 属性组件props准备
const planGroupTypeGuard = (data: Record<string, any>): data is planWorkImportVo => "departmentID" in data;
const typeGroupTypeGuard = (data: Record<string, any>): data is typeGroup => "typeName" in data;
const workTypeGuard = (data: Record<string, any>): data is work => "workContent" in data;
const treeProps = {
  children: "children",
  label: (data: planWorkImportVo | typeGroup | work) => {
    let label = planGroupTypeGuard(data) ? data.planName : undefined;
    label ??= typeGroupTypeGuard(data) ? data.typeName : undefined;
    label ??= workTypeGuard(data) ? data.workContent : undefined;
    return label;
  },
  class: (treeNode: TreeNodeData, node: Node) => {
    let className = planGroupTypeGuard(node.data) ? "plan-group-node" : undefined;
    className ??= typeGroupTypeGuard(node.data) ? "type-group-node" : undefined;
    className ??= workTypeGuard(node.data) ? "work-node" : undefined;
    return className;
  }
};
/**
 * @description: 控制工作只能放入分类分组
 * @param draggingNode 当前正在拖拽的节点
 * @param dropNode 要放入的节点
 * @param type 放置类型，inner表示插入内部，prev表示放置在目标节点之前，next表示放置在目标节点之后
 * @return
 */
const allowDrop = (draggingNode: Node, dropNode: Node, type: string) => {
  // 当放置类型是前/后时，需要父节点是type-group
  if (type !== "inner") {
    return typeGroupTypeGuard(dropNode.parent.data);
  }
  // 当放置类型是插入内部时，需当前放置到的节点是type-group
  return typeGroupTypeGuard(dropNode.data);
};
//#endregion
const workInterventionIDSourceCount = computed(() => {
  return sourceData.reduce((pre, cur) => {
    cur.children
      .flatMap((type) => type.children)
      .forEach((work) => {
        if (!work.apInterventionID || work.isTemp) {
          return;
        }
        const count = pre.has(work.apInterventionID) ? pre.get(work.apInterventionID)! + 1 : 1;
        pre.set(work.apInterventionID, count);
      });
    return pre;
  }, new Map<number, number>());
});
/**
 * @description: 选择工作
 * @param data 当前选择的节点数据
 * @param node 当前节点对象
 * @return
 */
const selectWork = (data: planWorkImportVo | typeGroup | work, node: Node) => {
  if (!("workContent" in data)) {
    return;
  }
  const noSameWorksOrNoDict =
    data.isTemp || !data.apInterventionID ? true : workInterventionIDSourceCount.value.get(data.apInterventionID)! === 1;
  if (noSameWorksOrNoDict) {
    // 移出sourceTree
    const typeGroupNode = node.parent;
    const planGroup = typeGroupNode.parent.data as planWorkImportVo;
    moveOut(planGroup, data);
    // 移入targetTree
    moveIn(planGroup.departmentID, data);
    return;
  }
  showWorkSelectorMessageBox(data.apInterventionID!);
};

/**
 * @description: 显示工作选择弹窗
 * @param interventionID 字典值
 * @return
 */
const showWorkSelectorMessageBox = (interventionID: number) => {
  const planToWorkMap = sourceData.reduce((pre, plan) => {
    const work = plan.children.flatMap((type) => type.children).find((work) => work.apInterventionID === interventionID)!;
    pre.set(plan.planName, work);
    return pre;
  }, new Map<string, work>());
  const currentSelectWork = ref<work>();
  // 开启弹窗，供选择
  ElMessageBox({
    message: h(workSelector, {
      planToWorkMap,
      onSelect: (work: work) => {
        currentSelectWork.value = work;
      }
    }),
    customStyle: { maxWidth: "448px" },
    showCancelButton: true,
    beforeClose: (action: Action, instance: MessageBoxState, done: () => void) => {
      if (action !== "confirm") {
        currentSelectWork.value = undefined;
        done();
        return;
      }
      if (!currentSelectWork.value) {
        showMessage("error", "请选择一个工作！");
        return;
      }
      let selectWorkFromPlan = undefined;
      // 当前work及其同字典works，全部从sourceData中移除
      // TODO：此处破坏了单向数据流
      for (const [planName, work] of planToWorkMap) {
        const fromPlan = sourceData.find((plan) => plan.planName === planName);
        selectWorkFromPlan ??= work === currentSelectWork.value ? fromPlan : undefined;
        moveOut(fromPlan, work);
      }
      // 移入targetTree
      selectWorkFromPlan && moveIn(selectWorkFromPlan.departmentID, currentSelectWork.value);
      currentSelectWork.value = undefined;
      done();
    }
  });
};

/**
 * @description: 从sourceData中移除工作
 * @param planGroup 计划分组
 * @param work 工作
 * @return
 */
const moveOut = (planGroup: planWorkImportVo | undefined, work: work) => {
  if (!planGroup) {
    return;
  }
  const workList = planGroup.children.find((byType) => byType.typeID === work.typeID)?.children;
  if (!workList) {
    return;
  }
  const workIndex = workList.findIndex((workView) => workView === work);
  workIndex !== -1 && workList.splice(workIndex, 1);
};

/**
 * @description: 将工作移入targetData
 * @param workDepartmentID 工作所属部门
 * @param work 工作
 * @return
 */
const moveIn = (workDepartmentID: number, work: work) => {
  if (workDepartmentID === departmentID) {
    // 本部门的，移入对应分类
    targetData.value.find((type) => type.typeID === work.typeID)!.children.push(work);
  } else {
    // 非本部门的，移入未分类分组
    work.isTemp = true;
    targetData.value[targetData.value.length - 1].children.push(work);
  }
};
</script>
<style scoped lang="scss">
.work-batch-import {
  display: flex;
  gap: 24px;
  height: 100%;
  justify-content: space-around;
  flex: 1;
  background-color: #f3f3f3;
  .left-wrapper,
  .right-wrapper {
    background-color: #ffffff;
    border-radius: 8px;
    overflow-y: auto;
    width: 48%;
    .title {
      font-size: 18px;
      font-weight: 600;
      padding: 16px;
      border-bottom: 1px solid #ebeef5;
      color: #303133;
    }
  }
  :deep(.el-tree).target-tree,
  :deep(.el-tree).source-tree {
    .el-tree-node__content:hover {
      background-color: #a2fbd6;
    }
    .plan-group-node > .el-tree-node__content {
      font-size: 32px;
      padding: 8px 0;
      font-weight: 600;
    }
    .type-group-node > .el-tree-node__content {
      font-size: 24px;
      padding: 8px 0;
      font-weight: 500;
      .type-badge {
        .el-badge__content {
          border: none;
          font-size: 14px;
          height: 20px;
          top: 16px;
          right: -4px;
        }
      }
    }
    .work-node {
      width: 100%;
      > .el-tree-node__content {
        padding: 8px 0;
      }
      .content {
        font-size: 20px;
      }
      .requirement {
        margin-left: 8px;
        font-size: 16px;
        color: #9196a1;
      }
      .highlight {
        color: #ff0000;
      }
    }
  }
  :deep(.el-tree).target-tree .work-node > .el-tree-node__content {
    cursor: move;
  }
  :deep(.el-tree).source-tree .work-node {
    position: relative;
    &:hover::after {
      content: "点击添加到右侧";
      position: absolute;
      background: #ffffff;
      color: #303133;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 14px;
      white-space: nowrap;
      top: 50%;
      right: 24px;
      transform: translateY(-50%);
      pointer-events: none;
      z-index: 999;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border: 1px solid #e4e7ed;
    }
  }
}
</style>
