<!--
 * FilePath     : \src\views\examineManagement\questionBank.vue
 * Author       : 来江禹
 * Date         : 2024-04-23 15:54
 * LastEditors  : 苏军志
 * LastEditTime : 2025-05-15 19:00
 * Description  : 考核题库维护
 * CodeIterationRecord:
 -->

<template>
  <base-layout class="question-bank" :drawerOptions="drawerOptions">
    <template #header>
      <el-button class="add-button" v-permission:B="1" @click="addOrEditRecord(undefined, 'add')">新增</el-button>
      <label
        >题库名称：
        <el-input clearable v-model="searchName" class="name-search-input" placeholder="请输入题库名称" @change="searchQuestionBank">
          <template #append>
            <i @click="searchQuestionBank" class="iconfont icon-search" />
          </template>
        </el-input>
      </label>
      <el-button type="primary" class="right-button" v-permission:B="34" @click="editClassified">题库分类</el-button>
    </template>
    <el-table
      ref="questionBankTableRef"
      :data="questionBankList"
      height="100%"
      :row-key="(row: questionBankView) => row.questionBankID + '-' + row.sort"
      :tree-props="{
        children: 'children'
      }"
      border
      stripe
      highlight-current-row
      :row-class-name="rowClassName"
      @row-click="handleRowClick"
      @expand-change="handleExpandChange"
    >
      <el-table-column :min-width="convertPX(300)" label="题库名称" prop="content"></el-table-column>
      <el-table-column :width="convertPX(300)" label="题库分类" prop="questionBankTypeName"></el-table-column>
      <el-table-column :min-width="convertPX(100)" label="题库类型" align="center">
        <template #default="scope"> {{ scope.row.isPractical ? "实操类" : "理论类" }} </template>
      </el-table-column>
      <el-table-column :min-width="convertPX(260)" label="培训课程" prop="trainingCourse"></el-table-column>
      <el-table-column :width="convertPX(140)" label="部门" prop="departmentName" align="center"></el-table-column>
      <el-table-column :width="convertPX(110)" label="题库层级" prop="organizationalDepartment" align="center"></el-table-column>
      <el-table-column :width="convertPX(90)" label="年份" prop="year" align="center"></el-table-column>
      <el-table-column :width="convertPX(120)" label="修改人" prop="modifyEmployee" align="center"></el-table-column>
      <el-table-column :width="convertPX(160)" label="修改时间" prop="modifyDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(190)" align="left">
        <template #default="scope">
          <el-tooltip content="新增">
            <i class="iconfont icon-add" @click.stop="addOrEditRecord(scope.row, 'inlineAdd')" v-permission:B="1"></i>
          </el-tooltip>
          <el-tooltip content="编辑">
            <i class="iconfont icon-edit" @click.stop="addOrEditRecord(scope.row, 'edit')" v-permission:B="3"></i>
          </el-tooltip>
          <el-tooltip content="题目预览">
            <i class="iconfont icon-preview" @click.stop="previewQuestions(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="题目录入">
            <i class="iconfont icon-question-entry1" @click.stop="enterQuestion(scope.row)" v-permission:B="3"></i>
          </el-tooltip>
          <el-tooltip content="复制" v-if="!scope.row.parentID">
            <i class="iconfont icon-copy" @click.stop="copyQuestionBank(scope.row)" v-permission:B="3"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i class="iconfont icon-delete" @click.stop="deleteRecord(scope.row)" v-permission:B="4"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <setting-dictionary-maintain
        v-if="drawerOptions.drawerName == 'editClassified'"
        :settingTypeCode="settingTypeCode"
        :settingType="settingType"
        :treeTableFlag="true"
      ></setting-dictionary-maintain>
      <el-form
        v-if="['addQuestionBank', 'editQuestionBank','copyQuestionBank'].includes(drawerOptions.drawerName as string)"
        ref="editQuestionBankRefs"
        class="drawer-form"
        :label-width="convertPX(150)"
        :model="drawerData"
        :rules="rules"
      >
        <template v-if="operationFlag !== 'inlineAdd'">
          <el-form-item label="题库分类：" prop="questionBankType">
            <setting-dictionary-type-selector
              v-model="drawerData.questionBankType"
              label=""
              :settingTypeCode="settingTypeCode"
              :settingType="settingType"
              :showAllLevels="true"
              :width="300"
              @change="getCourseSettingList"
            ></setting-dictionary-type-selector>
          </el-form-item>
          <el-form-item label="题库层级：" prop="organizationalDepartmentCode">
            <el-select
              class="form-input"
              v-model="drawerData.organizationalDepartmentCode"
              placeholder="请选择题库层级"
              clearable
              :disabled="drawerOptions.drawerName == 'editQuestionBank'"
              @change="checkSelectOrganizationalDepartment"
            >
              <el-option
                v-for="item in organizationalDepartmentOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :disabled="!roleIDs.includes(item.type)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="部门：" prop="departmentID">
            <department-switch-cascader class="form-input" v-model="drawerData.departmentID"></department-switch-cascader>
          </el-form-item>
          <el-form-item label="年份：" prop="year">
            <el-date-picker
              v-model="year"
              type="year"
              placeholder="请选择年份"
              :clearable="false"
              @change="drawerData!.year = year?.getFullYear()"
            />
          </el-form-item>
        </template>

        <el-form-item label="题库名称：" prop="content">
          <el-input class="form-input" v-model="drawerData.content" placeholder="请输入题库名称"></el-input>
        </el-form-item>
        <el-form-item v-if="drawerOptions.drawerName == 'addQuestionBank'" label="参考题库：" prop="referenceQuestionBankID">
          <el-select class="form-input" v-model="drawerData.referenceQuestionBankID" placeholder="请选择参考的上级组织部门的题库" clearable>
            <el-option v-for="item in referenceQuestionBankOptions" :key="item.label" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="培训课程：" v-if="courseList.length">
          <el-cascader
            v-model="drawerData.sourceID"
            clearable
            :options="courseOptions"
            :show-all-levels="false"
            :props="{ expandTrigger: 'hover', emitPath: false }"
          />
        </el-form-item>
        <el-form-item label="实操类：">
          <el-switch v-model="drawerData.isPractical" active-text="是" inactive-text="否" :disabled="disableSwitch" />
        </el-form-item>
      </el-form>
      <examine-question
        v-if="drawerOptions.drawerName == 'enterQuestion'"
        :propQuestionBankID="propQuestionBankID"
        :propIsPractical="propIsPractical"
      ></examine-question>
      <paper-preview v-if="drawerOptions.drawerName == 'previewQuestion'" :formData="questionFormData"></paper-preview>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入
const convertPX: any = inject("convertPX");
import type { settingDictionaryView } from "@/components/settingDictionaryMaintain/types/settingDictionaryView";
import type { questionBankView } from "./types/questionBankView";
import type { courseSetting } from "@/views/trainingManagement/types/courseSetting";
import examineQuestion from "./examineQuestion/index.vue";
import sortable from "sortablejs";
const { commonStore, userStore } = useStore();
type treeQuestionBankView = questionBankView & { children?: treeQuestionBankView[] };
// #endregion

// #region 响应式变量
// 当前用户角色ID集合
const roleIDs = ref<string[]>(userStore.roles?.map(String) ?? []);
const editQuestionBankRefs = ref<any>();
const questionBankTableRef = shallowRef();
const courseOptions = ref<CascaderList<string>[]>([]);
const courseList = ref<Array<courseSetting>>([]);
const searchName = ref<string>("");
const questionBankList = ref<Array<questionBankView>>([]);
const copyQuestionBankList = ref<Array<questionBankView>>([]);
const settingTypeCode = ref<string>("ExamineClassification");
const settingType = ref<string>("ExaminationManagement");
const year = ref<Date>();
// 编辑弹窗中的显示数据
const drawerData = ref<Partial<questionBankView> & { referenceQuestionBankID?: string; upperParentID?: string }>({} as any);
const referenceQuestionBankOptions = ref<Array<Record<string, any>>>([]);
const copyReferenceQuestionBankOptions = ref<Array<Record<string, any>>>([]);
const organizationalDepartmentOptions = ref<Array<Record<string, any>>>([]);
const propQuestionBankID = ref<string>("");
const propIsPractical = ref<boolean>(false);
const parentOrganizationDepartmentCode = ref<string>("");
const operationFlag = ref<string>("");
const flatTableData = computed(() => flattenTableData(questionBankList.value));
const disableSwitch = ref<boolean>(false);
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(editQuestionBankRefs))) {
      return;
    }
    saveRecord();
  },
  cancel: () => {
    initBankPage();
  }
});
// 规则验证rule
const rules = reactive({
  content: [
    {
      required: true,
      message: "请输入课程名称",
      trigger: "change"
    }
  ],
  questionBankType: [
    {
      required: true,
      message: "请选择课程分类",
      trigger: "change"
    }
  ],
  organizationalDepartmentCode: [
    {
      required: true,
      message: "请选择组织部门",
      trigger: "change"
    }
  ],
  year: [
    {
      required: true,
      message: "请选择年份",
      trigger: "change"
    }
  ],
  departmentID: [
    {
      required: true,
      message: "请选择部门",
      trigger: "change"
    }
  ]
});
// #endregion
//#region watch hooks
// 监听组织部门变化，重置和清空上级参考题库选项
watch(
  () => drawerData.value.organizationalDepartmentCode,
  (newVal, oldVal) => {
    if (newVal && newVal !== oldVal) {
      referenceQuestionBankOptions.value = copyReferenceQuestionBankOptions.value.filter((option: any) => option.type <= newVal);
      drawerData.value.referenceQuestionBankID = "";
    }
  }
);
// #endregion

// #region 初始化
onMounted(() => {
  initBankPage(true);
  getQuestionBankDict();
});
/**
 * @description: 初始化主表格拖拽、tree 的expand状态
 * @param isMounted 是否是初始化
 */
const initBankPage = (isMounted: boolean = false) => {
  getQuestionBank();
  // 初始化主表格拖拽
  initSortable();
  if (isMounted) {
    return;
  }
  rowClickQuestionBank.value = new Map<string, boolean | undefined>();
};
// #region 业务数据
/**
 * @description: 获取考核题库维护数据
 */
const getQuestionBank = async () => {
  await examineService.getQuestionBankList().then((res: any) => {
    questionBankList.value = convertToTree(res);
    copyQuestionBankList.value = res;
    // 初始化搜索框
    searchName.value = "";
  });
};
/**
 * @description: 新增/编辑数据
 * @param row 操作按钮所在行数据
 * @param flag 标记：标识是否在行内新增
 * @return
 */
const addOrEditRecord = async (row?: treeQuestionBankView, flag: "inlineAdd" | "edit" | "add" = "add") => {
  initBeforeAddOrEdit();
  let cloneDefaultValue = common.clone(defaultValues);
  operationFlag.value = flag;
  // 处理tree结构中记录的上下级关系
  if (flag === "inlineAdd" && row) {
    drawerData.value = cloneDefaultValue;
    drawerData.value.parentID = row.questionBankID;
    // 记录当前点击记录的上级parentID
    drawerData.value.upperParentID = row.parentID;
    drawerData.value.questionBankType = row.questionBankType;
    drawerData.value.departmentID = row.departmentID;
    drawerData.value.year = row.year;
    drawerData.value.organizationalDepartmentCode = row.organizationalDepartmentCode;
    drawerData.value.isPractical = row.isPractical;
    disableSwitch.value = true;
    // 记录当前行的组织部门编码，用作后续选择组织部门的时候做上下级关系判断
    parentOrganizationDepartmentCode.value = row!.organizationalDepartmentCode;
  } else {
    drawerData.value = row || cloneDefaultValue;
  }
  year.value = row && row.year ? new Date(row.year, 0, 1) : new Date(new Date().getFullYear(), 0, 1);
  drawerOptions.value.drawerTitle = row ? "编辑题库数据" : "新增题库数据";
  drawerOptions.value.drawerName = row && flag !== "inlineAdd" ? "editQuestionBank" : "addQuestionBank";
  drawerOptions.value.drawerSize = "40%";
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 复制题库数据
 * @param row 操作按钮所在行数据
 * @return
 */
const copyQuestionBank = (row: treeQuestionBankView) => {
  initBeforeAddOrEdit();
  row.content += "副本";
  drawerData.value = row;
  parentOrganizationDepartmentCode.value = "";
  year.value = row.year ? new Date(row.year, 0, 1) : undefined;
  drawerOptions.value.drawerTitle = "复制题库数据";
  drawerOptions.value.drawerName = "copyQuestionBank";
  drawerOptions.value.drawerSize = "40%";
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.showDrawer = true;
};
/**
 * @description: 在处理新增和编辑数据和弹窗显示之前，初始化一些字典设置
 */
const initBeforeAddOrEdit = () => {
  disableSwitch.value = false;
  parentOrganizationDepartmentCode.value = "";
  organizationalDepartmentOptions.value.length || getOrganizationalDepartmentOptions();
  // 初始化上级参考题库选项
  referenceQuestionBankOptions.value = copyReferenceQuestionBankOptions.value;
};
/**
 * @description: 获取培训课程数据
 * @param
 * @return Promise
 */
const getCourseSettingList = async (): Promise<any> => {
  let params = {
    courseTypeID: drawerData.value.questionBankType
  };
  courseList.value = await trainingService.getCourseSettingList(params);
  courseOptions.value = convertToOptions(courseList.value);
};
/**
 * @description: 转为级联数据格式
 * @param data
 * @return
 */
const convertToOptions = (data: (settingDictionaryView | Record<string, any>)[]): CascaderList<string>[] => {
  return data.map((item) => {
    const option: CascaderList<string> = {
      value: "",
      label: "",
      children: []
    };
    if ("settingValue" in item) {
      const classifiedItem = item as settingDictionaryView;
      option.value = classifiedItem.settingValue;
      option.label = classifiedItem.localShowName;
      option.children = convertToOptions(classifiedItem.children);
    } else if ("courseSettingID" in item) {
      const courseItem = item as Record<string, any>;
      option.value = courseItem.courseSettingID;
      option.label = courseItem.courseName;
      option.children = convertToOptions(courseItem.childCourses);
    }
    return option;
  });
};
/**
 * @description: 按照考核名称查询数据
 */
const searchQuestionBank = () => {
  if (searchName.value) {
    questionBankList.value = copyQuestionBankList.value.filter((m) => m.content.includes(searchName.value));
  } else {
    questionBankList.value = convertToTree(copyQuestionBankList.value);
  }
};
/**
 * @description: 保存数据
 */
const saveRecord = () => {
  let params = {
    ...drawerData.value
  };
  if (drawerOptions.value.drawerName === "copyQuestionBank") {
    examineService.copyQuestionBank(params).then((res: any) => {
      if (res) {
        showMessage("success", "复制成功");
        getQuestionBank();
        drawerOptions.value.showDrawer = false;
      }
    });
  } else {
    examineService.saveQuestionBank(params).then((res: any) => {
      if (res) {
        showMessage("success", "保存成功");
        getQuestionBank();
        drawerOptions.value.showDrawer = false;
      }
    });
  }
};
/**
 * @description: 删除数据
 * @param row
 * @return
 */
const deleteRecord = (row: questionBankView) => {
  confirmBox("是否删除考核题库数据？", "删除考核考核题库数据", (flag: Boolean) => {
    if (flag) {
      if (row.questionBankID) {
        let params = {
          questionBankID: row.questionBankID
        };
        examineService.deleteQuestionBank(params).then((res: any) => {
          if (res) {
            showMessage("success", "删除成功");
            getQuestionBank();
          }
        });
      }
    }
  });
};
/**
 * @description: 题库分类维护
 */
const editClassified = () => {
  drawerOptions.value.drawerTitle = "维护题库分类";
  drawerOptions.value.drawerName = "editClassified";
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showConfirm = false;
};
/**
 * @description: 将数据转成tree结构
 * @param list 需要呈现的数据集合
 * @return
 */
const convertToTree = (list: Array<questionBankView>) => {
  // 创建一个Map来存储所有节点
  const nodeMap = new Map<string, treeQuestionBankView>();
  // 初始化所有节点
  list.forEach((item) => {
    nodeMap.set(item.questionBankID, {
      ...item,
      children: undefined
    });
  });
  // 用于存储根节点
  const roots: treeQuestionBankView[] = [];
  // 建立父子关系
  nodeMap.forEach((node) => {
    if (node.parentID && nodeMap.has(node.parentID)) {
      // 如果有父节点，将当前节点添加到父节点的children中
      const parent = nodeMap.get(node.parentID)!;
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(node);
    } else {
      // 如果没有父节点或父节点不存在，作为根节点
      roots.push(node);
    }
  });
  // 重排序
  sortBankAfterTreeing(roots);
  // 重置排序值
  let reSortData = assignSortValues(roots);
  reSortData.length && examineService.updateBankSort(reSortData);
  return roots;
};
/**
 * @description: 排序
 * @param nodes
 * @param startIndex
 */
const assignSortValues = (nodes: questionBankView[], startIndex = 1): Record<string, any>[] => {
  let reSort: Record<string, any>[] = [];
  nodes.forEach((node, index) => {
    if (!node.sort) {
      node.sort = startIndex + index; // 设置当前节点的排序值
      reSort.push({ questionBankID: node.questionBankID, sort: node.sort });
    } else if (node.sort !== startIndex + index) {
      node.sort = startIndex + index; // 设置当前节点的排序值
      reSort.push({ questionBankID: node.questionBankID, sort: node.sort });
    }
    // 检查是否有子节点，并递归赋值
    if (node.children && node.children.length > 0) {
      // 递归调用，传入子节点数组和新的起始索引
      let childReSort = assignSortValues(node.children);
      reSort.push(...childReSort);
    }
  });
  return reSort;
};
/**
 * @description: 递归排序每一个层级的题库
 * @param list
 */
const sortBankAfterTreeing = (list: Array<questionBankView>) => {
  sortByKeys(list, ["sort"]);
  list.forEach((bankTreeItem: questionBankView) => {
    if (bankTreeItem.children) {
      sortBankAfterTreeing(bankTreeItem.children);
    }
  });
};
/**
 * @description: 跳转到考核题目维护页面
 * @param row
 * @return
 */
const enterQuestion = (row: questionBankView) => {
  propQuestionBankID.value = row.questionBankID;
  propIsPractical.value = row.isPractical;
  // 设置弹窗配置
  drawerOptions.value.drawerName = "enterQuestion";
  drawerOptions.value.drawerTitle = "题目录入";
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.showDrawer = true;
};
// 记录当前行点击状态( 记录当前行的courseSettingID 和 子树的展开状态)
const rowClickQuestionBank = ref<Map<string, boolean | undefined>>(new Map<string, boolean | undefined>());
/**
 * @description:处理行点击事件
 * @param {courseSetting} currentRow - 当前行数据对象
 * @return
 */
const handleRowClick = (currentRow: treeQuestionBankView) => {
  if (!currentRow) {
    return;
  }
  // 获取点击行的展开状态，默认为false
  const expand = rowClickQuestionBank.value.get(currentRow.questionBankID) ?? false;
  // 将展开状态存入map
  rowClickQuestionBank.value.set(currentRow.questionBankID, !expand);
  // 切换展开状态
  questionBankTableRef.value.toggleRowExpansion(currentRow, !expand);
};

/**
 * @description: 获取题库下拉框数据
 */
const getQuestionBankDict = async () => {
  await examineService.getQuestionBankDict().then((result: any) => {
    referenceQuestionBankOptions.value = result || [];
    copyReferenceQuestionBankOptions.value = result || [];
  });
};

/**
 * @description: 获取组织部门下拉框数据
 */
const getOrganizationalDepartmentOptions = () => {
  const params: SettingDictionaryParams = {
    settingType: settingType.value,
    settingTypeCode: "OrganizationalDepartmentCode"
  };
  settingDictionaryService.getSettingDictionaryDict(params).then((dataOptions: any) => {
    organizationalDepartmentOptions.value = dataOptions;
  });
};
/**
 * @description: 检查上下级关系是否正确
 * @param organizationalDepartmentCode 组织部门码
 * @return
 */
const checkSelectOrganizationalDepartment = (organizationalDepartmentCode: string) => {
  if (parentOrganizationDepartmentCode.value && parentOrganizationDepartmentCode.value > organizationalDepartmentCode) {
    showMessage("error", "不能选择上级组织部门！");
    drawerData.value.organizationalDepartmentCode = "";
  }
};
// #endregion
onBeforeUnmount(() => {
  sortableObj.value && sortableObj.value.destroy();
});
// #region 拖拽相关
const sortableObj = ref<any>();
/**
 * @description: 初始化拖拽排序
 * @param {parentId} parentId - 父节点ID
 * @return
 */
const initSortable = (parentId = null) => {
  const mainTableBody = questionBankTableRef.value.$el.querySelector(".el-table__body-wrapper tbody");
  sortableObj.value && sortableObj.value.destroy();
  sortableObj.value = sortable.create(mainTableBody, {
    animation: 150,
    ghostClass: "ghost",
    group: { name: `sortable-group-${parentId || "root"}`, put: false },
    draggable: `.sortable-group-${parentId || "root"}`,
    onEnd: (evt: any) => {
      const { oldIndex, newIndex } = evt;
      // 解决轻微拖拽,但是没有拖拽完成的情况下更新数据库的情况
      if (oldIndex === newIndex) {
        return;
      }
      let oldData = common.clone(flatTableData.value[oldIndex]);
      let newData = common.clone(flatTableData.value[newIndex]);
      let clonedData = common.clone(questionBankList.value);
      sortBankIterator(oldData, newData, clonedData);
      // 排序后重新渲染table 以及初始化拖拽
      questionBankList.value = [];
      nextTick(() => {
        questionBankList.value = clonedData;
        initSortable();
      });
    }
  });
};
/**
 * @description: 排序排序迭代器
 * @param oldBank 旧数据
 * @param newBank 新数据
 * @param bankList 题库列表
 */
const sortBankIterator = (oldBank: questionBankView, newBank: questionBankView, bankList: Array<questionBankView>) => {
  if (bankList?.length && bankList[0].parentID === oldBank.parentID) {
    let oldIndex = bankList.findIndex((newBankItem: questionBankView) => newBankItem.questionBankID === oldBank.questionBankID);
    let newIndex = bankList.findIndex((newBankItem: questionBankView) => newBankItem.questionBankID === newBank.questionBankID);
    if (oldIndex === -1 || newIndex === -1) {
      showMessage("error", "题目排序异常！");
      // 结束循环
      return true;
    }
    // 更新页面数据排序
    let movedQuestion = bankList.splice(oldIndex, 1)[0];
    bankList.splice(newIndex, 0, movedQuestion);
    let updateSortParams = bankList.map((item: questionBankView, index: number) => {
      item.sort = index + 1;
      return { questionBankID: item.questionBankID, sort: item.sort };
    });
    // 更新排序值
    updateSortParams.length &&
      examineService.updateBankSort(updateSortParams).then((resp: any) => showMessage("success", resp ? "排序成功" : "排序失败"));
    return true;
  }
  // 遍历子节点 寻找排序所在的层级
  let breakFlag = false;
  for (let i = 0; i < bankList.length; i++) {
    const item: questionBankView = bankList[i];
    if (item.children?.length && sortBankIterator(oldBank, newBank, item.children)) {
      breakFlag = true;
      break;
    }
  }
  return breakFlag;
};
/**
 * @description: 平铺数据，跟dom顺序一致
 * @param tableData
 * @returns
 */
const flattenTableData = (tableData: any) => {
  return tableData.flatMap((item: any) => {
    const flattenedItem = { ...item };
    if (flattenedItem.children) {
      const children = flattenTableData(flattenedItem.children);
      return [flattenedItem, ...children]; // 添加父项和子项
    }
    return [flattenedItem]; // 仅返回父项
  });
};
/**
 * @description: 设置row的class
 * @param {row} row - 当前行数据对象
 * @param {index} index - 当前行索引
 * @return
 */
const rowClassName = ({ row }: { row: any; index: number }) => {
  return `sortable-group-${row.parentID || "root"}`;
};
/**
 * @description: 展开和收缩事件
 * @param {row} row - 当前行数据对象
 * @param {expanded} expanded - 当前行是否展开
 * @return
 */
const handleExpandChange = (row: any, expanded: boolean) => {
  initSortable(expanded ? row.questionBankID : row.parentID ?? "root");
};
// #endregion
// #region 题库题目预览相关逻辑
import type { dynamicFormData } from "zhytech-ui";
const defaultFormData = {
  datas: {},
  components: [],
  props: {}
};
const questionFormData = ref<dynamicFormData<Record<string, any>>>(defaultFormData);
/**
 * @description: 预览题目
 * @param examinationPaperMainID
 */
const previewQuestions = async (row: questionBankView) => {
  // 重置表单数据
  questionFormData.value = defaultFormData;
  drawerOptions.value.drawerName = "previewQuestion";
  drawerOptions.value.drawerTitle = "预览题目";
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.showDrawer = true;
  // 实操类试卷题目预览
  let params: Record<string, any> = {
    questionBankID: row.questionBankID
  };
  if (row.isPractical) {
    params.isPractical = row.isPractical;
    params.paperTitle = row.content;
    await examineService.getPaperTemplateByBankID(params).then((data: any) => {
      questionFormData.value = data ?? {};
    });
  } else {
    examineService.getTheoryPaperTemplateByBankID(params).then((data: any) => {
      questionFormData.value = data ?? {};
    });
  }
  questionFormData.value.props.formType = "2";
  questionFormData.value.props.labelPosition = "top";
};
// #endregion
// #region 一些常量预设
const defaultValues: treeQuestionBankView = {
  questionBankID: "",
  content: "",
  questionBankTypeName: "",
  questionBankType: "",
  sourceType: "",
  sourceID: undefined,
  trainingCourse: "",
  modifyEmployeeID: "",
  modifyEmployee: "",
  modifyDateTime: undefined,
  hospitalID: commonStore.hospital.hospitalID,
  isPractical: false,
  organizationalDepartmentCode: "",
  sort: 0,
  year: new Date().getFullYear()
};
// #endregion
</script>
<style lang="scss">
.question-bank {
  .base-header {
    .name-search-input {
      width: 240px;
    }
  }
  .drawer-form {
    .form-input {
      width: 280px;
    }
  }
}
</style>
