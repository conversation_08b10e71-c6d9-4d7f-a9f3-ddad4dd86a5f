<!--
 * FilePath     : \src\views\examineManagement\components\examinationTypeRadio.vue
 * Author       : 来江禹
 * Date         : 2024-08-14 09:22
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-24 14:47
 * Description  : 考核类型下拉框
 * CodeIterationRecord:
 -->
<template>
  <div class="examination-type-radio">
    考核类型：
    <el-radio-group v-model="typeID" @change="change" :disabled="props.disabled">
      <el-radio-button v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-radio-group>
  </div>
</template>
<script setup lang="ts">
const typeID = defineModel();
const props = defineProps({
  type: {
    type: String,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emits = defineEmits(["change"]);

let typeOptions = ref<Array<Record<any, any>>>([]);
const params: SettingDictionaryParams = {
  settingType: "ExaminationManagement",
  settingTypeCode: "ExaminationType",
  settingTypeValue: props.type,
  index: Math.random()
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  typeOptions.value = datas;
  // 如果默认值为空，默认选择第一个
  if (!typeID.value && typeOptions.value.length > 0) {
    typeID.value = typeOptions.value[0].value;
  }
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  emits("change", value);
};
</script>

<style lang="scss">
.examination-type-radio {
  display: inline-flex;
  align-items: center;
  margin: 0 15px;
}
</style>
