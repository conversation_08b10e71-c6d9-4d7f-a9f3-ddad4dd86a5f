/*
 * FilePath     : \src\hooks\useButton.ts
 * Author       : 杨欣欣
 * Date         : 2024-03-19 14:21
 * LastEditors  : 苏军志
 * LastEditTime : 2024-06-07 16:12
 * Description  : 按钮逻辑hooks
 * CodeIterationRecord:
 */
export function useButton() {
  return {
    /**
     * @description: 获取按钮是否显示
     * @param row 当前行
     * @return
     */
    getButtonShow: (row: Record<string, any>) => {
      // 待审批状态 且 申请人是登录用户
      return row.statusCode === "0" && useStore().userStore.employeeID === row.addEmployeeID;
    },
    /**
     * @description: 获取撤销按钮是否显示
     * @param statusCode 审批状态码
     * @param employeeID 检核人工号
     * @returns
     */
    showRevokeButton: (statusCode: string, employeeID: string | string[]) => {
      // 非未审批状态& 非已撤销状态 & 申请人是登录用户
      return statusCode !== "0" && statusCode !== "4" && employeeID.includes(useStore().userStore.employeeID);
    }
  };
}
