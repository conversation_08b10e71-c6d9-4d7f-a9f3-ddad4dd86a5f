/*
 * FilePath     : \src\views\scheduling\types\schedulingTableParam.ts
 * Author       : 苏军志
 * Date         : 2024-10-14 17:49
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:58
 * Description  : 排班表格参数
 * CodeIterationRecord:
 */
export interface schedulingTableParam {
  /**
   * @description: 是否只读
   */
  readonly: boolean;
  /**
   * @description: 排班月份
   */
  month: string;
  /**
   * @description: 排班类型，1月值班；2周排班
   */
  schedulingType: string;
  /**
   * @description: 排班类型，1平铺；2鼠标悬停
   */
  restDayShowStyle: string;
  /**
   * @description: 排班记录id集合
   */
  shiftSchedulingRecordIDs: string[];
  /**
   * @description: 午别列表
   */
  noonList: Record<string, string>[];
  /**
   * @description: 部门岗位列表
   */
  departmentPostList: Record<string, string>[];
  /**
   * @description: 排班标记列表
   */
  shiftSchedulingMarkList: Record<string, string>[];
  /**
   * @description: 排班限制层级开关
   */
  restrictCapabilityLevel: boolean;
  /**
   * @description: 排班画面是否一屏显示完标记
   */
  oneScreenDisplay: boolean;

  /**
   * @description: 实时休假Map
   */
  restMap: Map<string, Map<string, Record<string, number>>>;

  /**
   * @description: 中午值班标记编码
   */
  noonDutyMarkID: number;
  /**
   * @description: 是否显示日统计
   */
  showSummary?: boolean;
  /**
   * @description: 要显示的日统计条件
   */
  dailyStatisticsPostCondition: Record<string, string>[];
  /**
   * @description: 月统计条件
   */
  monthlyStatisticsPostCondition: Record<string, string>[];
  /**
   * @description: 月统计数据
   */
  shiftSchedulingStatisticsTable: TableView;
  /**
   * @description: 人员可休假天数
   */
  employeeRemainingRestDaysDict: Record<string, number | undefined>;
  /**
   * @description: 排班模板列表
   */
  schedulingTemplateList: Record<string, string>[];
}
