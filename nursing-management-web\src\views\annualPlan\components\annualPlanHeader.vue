<!--
 * FilePath     : \src\views\annualPlan\components\annualPlanHeader.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-23 08:28
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-06-11 14:35
 * Description  : 年度计划通用头部组件
 * CodeIterationRecord:
 -->
<template>
  <div class="annual-plan-header">
    <el-radio-group v-model="annualPlanStore.departmentID">
      <el-radio-button
        v-for="{ departmentID, localShowName } in departmentDict"
        :key="departmentID"
        :label="localShowName"
        :value="departmentID"
      />
    </el-radio-group>
    {{ year }}年
    <el-select v-if="model" v-model="model">
      <el-option v-for="option in options" :key="option.value" :label="option.label" :value="option.value" />
    </el-select>
  </div>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";

const { year } = defineProps<{
  year: number;
}>();

const [model, modifiers] = defineModel({
  get(v: string | number | undefined) {
    if (!v) {
      return undefined;
    }
    return v.toString();
  },
  required: false
});
const options = computedAsync(async () => {
  const modelValueType = modifiers?.month ? "Monthly" : "Quarter";
  const params = {
    settingType: "Common",
    settingTypeCode: "TimePeriods",
    settingTypeValue: modelValueType
  };
  return await settingDictionaryService.getSettingDictionaryDict(params);
});
const annualPlanStore = usePlanManagementStore();
const departmentDict = ref<Record<string, any>>({});
onMounted(async () => {
  await getDepartmentDict();
});

/**
 * @description: 获取科室字典
 */
const getDepartmentDict = async () => {
  const params = {
    employeeID: useStore().userStore.employeeID
  };
  departmentDict.value = await employeeService.getEmployeeDepartmentsByEmployeeID(params);
};
</script>
<style scoped lang="scss">
.annual-plan-header {
  font-size: 20px;
  display: flex;
  gap: 16px;
  align-items: center;
  .el-select {
    width: 160px;
  }
  .red {
    color: #ff0000;
  }
  .green {
    color: $base-color;
  }
}
</style>
