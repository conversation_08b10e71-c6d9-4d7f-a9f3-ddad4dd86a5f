<!--
 * FilePath     : \src\views\annualPlan\components\workSelector.vue
 * Author       : 杨欣欣
 * Date         : 2025-03-22 17:16
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 18:35
 * Description  : 多个同名字典工作，需要选择一个
 * CodeIterationRecord:
 -->
<template>
  <div class="work-selector">
    <div class="title">请选择采用哪一个计划的工作内容：</div>
    <div v-for="([key, value], index) in planToWorkMap" :key="index">
      <div class="plan-name">{{ key ?? "" }}</div>
      <work-card :class="[ selectedWork && selectedWork === value ? 'highlight' : '' ]" :content="value.workContent" :tip="value.requirement" @click="selectWork(value)" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import type { planWorkImportVo } from "../types/planWorkImportVo";
import workCard from "./workCard.vue";
type work = planWorkImportVo["children"][number]["children"][number];
const { planToWorkMap } = defineProps<{
  planToWorkMap: Map<string, work>;
}>();
const emit = defineEmits<{(e: "select", work: work): void }>();
const selectedWork = ref<work>();
/**
 * @description: 选择工作
 * @param work 选择的工作
 * @return 
 */
const selectWork = (work: work) => {
  selectedWork.value = work;
  emit("select", work);
};
</script>
<style scoped lang="scss">
.work-selector {
  padding: 24px;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #1890ff;
      border-radius: 2px;
    }
  }

  .plan-name {
    font-size: 16px;
    line-height: 1.5;
    color: #262626;
    font-weight: 600;
    margin: 16px 0 12px;
    transition: color 0.3s ease;

    &:hover {
      color: #1890ff;
    }
  }

  :deep(.work-card) {
    margin-bottom: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .highlight {
    border: 2px solid #1890ff !important;
    background-color: #e6f7ff;
  }
}
</style>
