/*
 * FilePath     : \src\views\qcManagement\hierarchicalQC\types\hierarchicalSaveView.ts
 * Author       : 郭鹏超
 * Date         : 2023-09-21 10:11
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-04-13 10:24
 * Description  :
 * CodeIterationRecord:
 */

/**
 * 考核保存View
 */
export class saveClass {
  /**
   * @description: 得分
   */
  point: string;
  /**
   * @description: 指导和建议
   */
  guidance: string;
  /**
   * @description:指导和建议输入框说明
   */
  guidanceLabel: string | undefined;
  /**
   * @description: 指导和建议是否显示
   */
  guidanceShowFlag: boolean;
  /**
   * @description: 计划措施
   */
  improvement: string;
  /**
   * @description: 计划措施是否显示
   */
  improvementShowFlag: boolean;
  /**
   * @description: 计划措施输入框说明
   */
  improvementLabel: string | undefined;
  /**
   * @description: 模板OCde
   */
  templateCode: string;
  /**
   * @description: 主题ID
   */
  hierarchicalQCSubjectID: string;
  /**
   * @description: 质控主记录ID
   */
  hierarchicalQCRecordID: string;
  /**
   * @description: 质控明细记录ID
   */
  hierarchicalQCMainID: string;
  /**
   * @description: 动态模板返回勾选明细数据
   */
  templateDetails: Record<string, any>[];
  /**
   * @description: 动态模板返回文件数据
   */
  templateFileList: Record<string, any>[];
  /**
   * @description: 组黄后的保存明细数据
   */
  details: Record<string, any>[];
  /**
   * @description: 是否为追踪考核
   */
  trackFlag: boolean;
  /**
   * @description: 质控日期
   */
  qcDate: string;
  /**
   * @description: 被质控部门
   */
  qcDepartmentID?: number;
  /**
   * @description: 质控开始日期
   */
  startDate: string;
  /**
   * @description: 质控结束日期
   */
  endDate: string;
  constructor() {
    this.point = "";
    this.guidance = "";
    this.guidanceShowFlag = true;
    this.improvement = "";
    this.improvementShowFlag = true;
    this.templateCode = "";
    this.hierarchicalQCSubjectID = "";
    this.hierarchicalQCRecordID = "";
    this.hierarchicalQCMainID = "";
    this.details = [];
    this.trackFlag = false;
    this.qcDate = "";
    this.templateDetails = [];
    this.templateFileList = [];
    this.startDate = "";
    this.endDate = "";
  }
}
