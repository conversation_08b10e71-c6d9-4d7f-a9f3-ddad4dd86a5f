<!--
 * relative     : \nursing-management-web\src\views\examineManagement\components\examineCondition.vue
 * Author       : 张现忠
 * Date         : 2025-01-26 15:38
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-10 11:56
 * Description  : 题目数量和题目分数选择组件
 * CodeIterationRecord:
 -->

<template>
  <div class="examine-condition">
    <el-table
      v-if="dynamicColumns && dynamicColumns.length > 0"
      class="examine-table"
      cell-class-name="cell-wrapper"
      ref="tableRef"
      :data="tableData"
      border
      show-summary
      :summary-method="getColumnSummaryMethod"
      height="100%"
      @row-click="handleRowClick"
    >
      <!-- 题库选择 -->
      <el-table-column label="题库" :min-width="convertPX(150)">
        <template #default="{ row }">
          <question-bank-selector
            v-if="!row.initRow"
            v-model="row.questionBankID"
            label=""
            :width="0"
            :props="{ multiple: false }"
            :list="questionBankOptions"
            :isPractical="false"
            @change="changeQuestionBank($event, row)"
            @afterInitChange="changeQuestionBank($event, row, true)"
          ></question-bank-selector>
        </template>
      </el-table-column>
      <!-- 动态列 -->
      <el-table-column v-for="(column, index) in dynamicColumns" :key="index" :label="column.label" :prop="column.prop.toString()">
        <el-table-column
          :key="childIndex"
          :label="childColumn.label"
          :min-width="childColumn.label === '数量' ? convertPX(140) : convertPX(100)"
          :prop="childColumn.prop.toString()"
          v-for="(childColumn, childIndex) in column.children"
        >
          <template #default="{ row }">
            <el-input
              v-if="row[childColumn.prop]"
              :class="{ 'no-focus': row[childColumn.prop].disabled || false }"
              size="small"
              type="number"
              min="1"
              :max="row[childColumn.prop].total"
              :disabled="row[childColumn.prop].disabled || false"
              v-model="row[childColumn.prop].current"
              @change="checkInputData($event, row, childColumn)"
            >
              <template #append v-if="row[childColumn.prop]?.total || row[childColumn.prop]?.total === 0">
                <span>/ {{ row[childColumn.prop].total }} </span>
              </template>
            </el-input>
          </template>
        </el-table-column>
      </el-table-column>
      <!-- 操作列 -->
      <el-table-column label="操作" align="center" :width="convertPX(50)">
        <template #default="{ $index, row }">
          <i class="iconfont icon-delete" v-if="!row.initRow" @click="deleteConditionRow($index)"></i>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { h } from "vue";
import { zhyIcon } from "zhytech-ui";
const convertPX: any = inject("convertPX");
// 动态列配置
const dynamicColumns = ref<dynamicColumn[]>([]);
// 可选题库列表
const questionBankOptions = ref<any[]>([]);
// 表格数据
const tableData = ref<Record<string, any>[]>([]);
// 表格引用
const tableRef = shallowRef();
// 总分
const calcTotalScore = ref(0);
// 当前编辑行
const currEditRow = ref<Record<string, any> | null>(null);
const props = defineProps({
  defaultValue: {
    type: Array as PropType<{ questionBankID: string; detail: Record<string, any>; [prop: string]: any }[]>,
    default: () => []
  }
});
const emits = defineEmits(["questionBankChange"]);
onMounted(async () => {
  await getQuestionBankOptions();
  await getTableColumns();
  initData();
});
// #region 组件挂载时的获取配置和初始化数据相关方法
/**
 * @description: 组件挂载时，初始化数据
 * @returns
 */
const initData = () => {
  let defaultValue = props.defaultValue;
  if (defaultValue.length <= 0) {
    // 初始化时默认添加一行空的规则记录
    addConditionRow();
    return;
  }
  for (let index = 0; index < defaultValue.length; index++) {
    const rowDefaultValue = defaultValue[index];
    if (!rowDefaultValue || !rowDefaultValue.questionBankID) {
      continue;
    }
    let rowDetails: Record<string, any> = {};
    Object.keys(colProps.value).forEach((key) => {
      rowDetails[key] = { current: rowDefaultValue.detail[key] || undefined, total: undefined };
    });
    tableData.value.push({
      questionBankID: rowDefaultValue.questionBankID,
      ...rowDetails
    });
  }
};
// 分组后的动态列配置 --根据group（题型code）字段分组
const groupColumns = ref<Record<string, Record<string, any>[]>>({});
// 存储平铺后的列属性
const colProps = ref<Record<string, Record<string, number | undefined | string[]>>>({});
/**
 * @description 获取table动态列配置
 */
const getTableColumns = async () => {
  // 获取table列配置
  await conditionService.getConditionTableFormat({}).then(async (respData: any) => {
    if (!respData || respData.length <= 0) {
      showMessage("error", "获取条件列失败");
      return;
    }
    dynamicColumns.value = respData as dynamicColumn[];
    // 將dynamicColumns按照group字段分組
    groupColumns.value = dynamicColumns.value.reduce((prev: any, column: any) => {
      let key = column.group;
      prev[key] = prev[key] || [];
      column.children &&
        column.children.length > 0 &&
        column.children.forEach((child: any) => {
          prev[key].push(child);
        });
      return prev;
    }, {});
    // (二级标题)平铺后的列属性
    colProps.value = respData.reduce((colPropsAcc: Record<string, any>, columnHeaderData: dynamicColumn) => {
      if (columnHeaderData.children && columnHeaderData.children.length > 0) {
        columnHeaderData.children.forEach((child: dynamicColumn) => {
          colPropsAcc[child.prop] = {
            current: undefined,
            total: undefined,
            label: child.label,
            group: child.group,
            brother: groupColumns.value[child.group]
              ?.filter((item: Record<string, any>) => item.prop !== child.prop)
              ?.map((item: Record<string, any>) => item.prop)
          };
        });
        return colPropsAcc;
      }
      colPropsAcc[columnHeaderData.prop] || (colPropsAcc[columnHeaderData.prop] = { current: undefined, total: undefined });
      return colPropsAcc;
    }, {});
  });
};

/**
 * @description 获取题库选项
 * @returns
 */
const getQuestionBankOptions = async () => {
  // 模拟后端请求
  await examineService.getQuestionBankSelectList({ isPractical: false, index: Math.random() }).then((respData: any) => {
    questionBankOptions.value = respData;
  });
};
// #endregion

// #region 点击事件操作相关方法
/**
/**
 * @description 新增条件行
 */
const addConditionRow = () => {
  // 初始化table数据的时候，默认占了一个空行(使用initRow字段标记)，用于显示汇总列，这里需要替换掉
  if (tableData.value.length === 1 && tableData.value[0].initRow) {
    tableData.value[0] = {
      questionBankID: "",
      ...common.clone(colProps.value)
    };
    return;
  }
  // 校验上一行题库是否选择
  if (tableData.value.length && !tableData.value[tableData.value.length - 1].questionBankID) {
    showMessage("warning", "请先完善上一行题库选择！");
    return;
  }
  tableData.value.push({
    questionBankID: "",
    ...common.clone(colProps.value)
  });
  // 新增行的时候自动滚动到最后一行
  scrollToBottom();
};
/**
 * @description 题库选择变更，显示题库对应的各个题型的数量
 * @param row 当前行
 */
const changeQuestionBank = async (
  selectedQuestionBankID: string | undefined,
  clickedRow: Record<string, any>,
  initFlag: boolean = false
) => {
  if (!selectedQuestionBankID) {
    return;
  }
  // 初始化时，不做检核和清空操作
  if (!initFlag) {
    // 检核题库是否选择重复
    const existsQuestionBank = tableData.value.filter((row) => {
      return row !== clickedRow && row.questionBankID === selectedQuestionBankID;
    });
    if (existsQuestionBank?.length) {
      showMessage("warning", "该题库已选择，请勿重复选择！");
      clickedRow.questionBankID = undefined;
      return;
    }
    // 切换题库，清空之前的值
    for (const key in clickedRow) {
      clickedRow[key] && clickedRow[key].current && (clickedRow[key].current = undefined);
    }
  }

  // 获取题库的题型数量
  let row = clickedRow;
  currEditRow.value = row;
  // 添加index参数，防止请求拦截
  await examineService
    .getQuestionTypeCount({
      questionBankIDs: [selectedQuestionBankID],
      index: Math.random()
    })
    .then((respData: any) => {
      if (!respData || (Array.isArray(respData) && respData.length <= 0) || !respData[selectedQuestionBankID]) {
        // 获取数据为空时，提示错误信息
        showMessage("error", "获取题库题型数量失败,请检查题库中是否有题目！");
        // 清除上一次题库选择设置的题型数量信息
        for (const colKey in currEditRow.value) {
          // 排除固定列 questionBankID
          if (colKey === "questionBankID") {
            continue;
          }
          currEditRow.value[colKey] && (currEditRow.value[colKey].disabled = true);
          if (currEditRow.value[colKey]?.total || currEditRow.value[colKey]?.total === 0) {
            currEditRow.value[colKey].total = NaN;
          }
        }
        return;
      }
      // 填充题型实际数量数据、禁用没有题目的题型文本输入框
      for (const colKey in respData[selectedQuestionBankID]) {
        let total = respData[selectedQuestionBankID][colKey];
        let disabled = !total || total === 0;
        // 设置题目数量输入框状态
        if (row[colKey]) {
          row[colKey].total = total;
          row[colKey].disabled = disabled;
        } else {
          row[colKey] = { current: undefined, total: total, disabled: disabled };
        }
        // 禁用同组题型下的（非题目数量）输入框
        let brother = colProps.value[colKey]?.brother;
        if (brother && Array.isArray(brother) && brother.length > 0) {
          for (let index = 0; index < brother.length; index++) {
            const brotherColKey = brother[index];
            row[brotherColKey].disabled = disabled;
          }
        }
      }
    });
  emitQuestionBankChange();
};
/**
 * @description: 监听表格数据中题库信息变化，通知题库选择变更
 */
const emitQuestionBankChange = () => {
  let allSelectedQuestionBankIDs = tableData.value
    .filter((row: Record<string, any>) => row.questionBankID)
    .map((row: Record<string, any>) => row.questionBankID);
  emits("questionBankChange", allSelectedQuestionBankIDs);
};

/**
 * @description 删除条件行
 * @param index 删除行的索引
 */
const deleteConditionRow = (index: number) => {
  if (tableData.value.length === 1) {
    showMessage("warning", "必须保留至少一条题型规则！");
    return;
  }
  // 删除选中行数据
  tableData.value.splice(index, 1);
  // 通知题库选择变更
  emitQuestionBankChange();
};
// #endregion

// #region table渲染呈现、行为监听交互相关方法
/**
 * @description 行点击事件 获取当前正在编辑的数据行
 * @param row 当前行
 */
const handleRowClick = (row: Record<string, any>) => (currEditRow.value = row);
// #region 汇总列自定义、总分计算方法
/**
 * @description：自定义汇总行内容呈现
 * @param columns 列配置
 * @param data 表格数据
 * @returns 自定义汇总行内容
 */
const getColumnSummaryMethod = ({ columns, data }: { columns: any[]; data: any[] }) => {
  // 在汇总数据获取后的下一个时刻重新渲染汇总行
  reRenderSummaryCell();
  // 计算总分和每组题型的分数
  let [totalScore, summaryResult] = calcScore(data);
  // 最后一列中显示新增图标
  summaryResult[columns.length - 2] = h(zhyIcon, {
    name: "plus",
    color: "#14d8d8",
    style: "vertical-align: middle;",
    onClick: () => addConditionRow()
  });
  return [`试卷总分：${totalScore}分`].concat(summaryResult);
};
/**
 * @description: 计算总分和每组题型的分数
 * @param data 表格数据
 * @returns [总分, 每组题型分数数组]
 */
const calcScore = (data: any[]) => {
  let summaryResult: any = [];
  let groupKey = Object.keys(groupColumns.value);
  // 遍历每个题型 --计算总分和每组题型的分数
  calcTotalScore.value = groupKey.reduce((totalScoreAcc, key) => {
    let columns = groupColumns.value[key];
    if (columns.length <= 0) {
      return 0;
    }
    // 按照题型分组获取每一组的题型分数
    let [groupSumArray, groupScore] = getSummaryDataByGroup(data, columns);
    // 拼接每个题型的文本
    // 按照题型分组 如： （单元题目数据 * 单选题分数） 为一组
    let summaryText = groupSumArray.map((item) => `${item[0]}*${item[1]}`).reduce((prev, curr) => (prev ? `${prev}+${curr}` : curr), "");
    summaryResult.push(summaryText ? summaryText + `=${groupScore}` : "");
    summaryResult.push([]);
    return totalScoreAcc + groupScore;
  }, 0);
  return [calcTotalScore.value || 0, summaryResult];
};
/**
 * @description: 根据题型分组获取每一组的题型分数
 * @param data 表格数据
 * @param columns 列配置
 * @returns [每组题型分数数组, 总分]
 */
const getSummaryDataByGroup = (data: any[], columns: any[]): [number[][], number] => {
  let groupSumArray = [];
  let groupScore = 0;
  for (let index = 0; index < data.length; index++) {
    const tableDataRow = data[index];
    let sumArray = [];
    for (const column of columns) {
      if (tableDataRow[column.prop]?.current) {
        sumArray.push(tableDataRow[column.prop].current);
      }
    }
    // 如果填入的数值少于2个，说明题型和对应的题型分数数据不完整，则不计算
    if (sumArray.length !== 2) {
      continue;
    }
    groupSumArray.push(sumArray);
    groupScore += sumArray.reduce((prev, curr) => prev * curr, 1);
  }
  return [groupSumArray, groupScore];
};
/**
 * @description: 重新渲染表格汇总行
 */
const reRenderSummaryCell = () => {
  nextTick(() => {
    let current = document.querySelector(".el-table__footer-wrapper")?.querySelector(".el-table__footer");
    let cells = (current as any)?.rows[0].cells;
    // 按照分组 合并动态列下面的汇总行的单元格
    for (let cellIndex = 1; cellIndex < cells.length - 1; cellIndex++) {
      if (cellIndex % 2) {
        cells[cellIndex].colSpan = "2";
        continue;
      }
      cells[cellIndex].style.display = "none";
    }
  });
};
/**
 * @description: 滚动到表格底部
 */
const scrollToBottom = () => {
  nextTick(() => {
    // 获取表格主体滚动高度
    const tableBodyWrapper = tableRef.value.$el.querySelector(".el-table__body");
    if (!tableBodyWrapper?.scrollHeight) {
      return;
    }
    // sheet滚动到底部
    const scrollWrapper = tableRef.value.$el.querySelector(".el-scrollbar__wrap");
    scrollWrapper && (scrollWrapper.scrollTop = tableBodyWrapper.scrollHeight);
  });
};
/**
 * @description 校验输入数据是否满足限制条件
 * @param inputValue 输入值
 * @param row 编辑的单元格所在行的数据
 * @param column 编辑的单元格所在列的配置（第二层）
 */
const checkInputData = (inputValue: any, row: any, column: any) => {
  let parseValue = parseInt(inputValue);
  if (inputValue && isNaN(parseValue)) {
    row[column.prop].current = undefined;
    showMessage("warning", `${column.label}中只能填写数值！`);
    return;
  }
  if (Number.isNaN(row[column.prop].total)) {
    row[column.prop].current = undefined;
    showMessage("warning", `无法获取题库中题目信息，${column.label}禁止填写！`);
    return;
  }
  if ((row[column.prop].total || row[column.prop].total === 0) && parseValue > row[column.prop].total) {
    row[column.prop].current = undefined;
    showMessage("warning", `${column.label}中数值不能大于${row[column.prop].total}！`);
  }
};
// #endregion
// #region 外部调用方法
/**
 * @description 外部调用获取组卷条件的方法
 * @returns 组卷条件数据
 */
const getFilterData = () => {
  let filterData = tableData.value
    .map((row: Record<string, any>) => {
      // 每行必须有选择题库
      if (!row.questionBankID) {
        return undefined;
      }
      let filterRows: any[] | undefined = [];
      Object.keys(colProps.value).forEach((key) => {
        if (row[key].current) {
          let filterRow = {
            groupType: "QuestionBank",
            groupTypeValue: row.questionBankID,
            dataType: colProps.value[key].group,
            dataTypeValue: key,
            conditionExpression: `'{${key}}'='${row[key].current}'`,
            conditionContent: `${colProps.value[key].label}等于'${row[key].current}'`,
            conditions: [
              {
                itemID: key,
                condition: "EQUALS",
                value: row[key].current,
                conditionType: "",
                children: []
              }
            ]
          } as Record<string, any>;
          filterRows!.push(filterRow);
        }
      });
      return filterRows;
    })
    .flat();
  return filterData.filter((data: any) => data);
};
defineExpose({
  getFilterData,
  getTotalScore: () => calcTotalScore.value
});
// #endregion

// #region 类型声明
interface dynamicColumn {
  key: string;
  label: string;
  prop: string;
  group: string;
  children: dynamicColumn[];
}
// #endregion
</script>

<style lang="scss">
.examine-condition {
  width: 100%;
  height: 100%;

  .examine-table {
    .cell-wrapper {
      padding: 10px 2px;

      .no-focus {
        // 隐藏不可编辑的输入框光标
        caret-color: transparent;
      }
    }

    .el-table__footer {
      tr:first-child {
        td > .cell {
          padding: 10px 2px;
          font-size: larger;
          font-weight: bold;
          color: #a70f0f;
          text-align: center;
          display: inline-block;
        }

        td {
          // 水平垂直居中显示
          text-align: center;

          & > .cell {
            vertical-align: middle;
          }
        }
      }
    }
  }
}
</style>
