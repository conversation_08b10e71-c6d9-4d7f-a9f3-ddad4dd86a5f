<!--
 * FilePath     : \src\views\annualPlan\formulate\index.vue
 * Author       : 杨欣欣
 * Date         : 2023-08-24 10:10
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 15:42
 * Description  : 年度计划制定首页
 * CodeIterationRecord:
-->
<template>
  <base-layout class="annual-intervention-maintain">
    <template #header>
      <annual-plan-header :year="year" @change="getInterventionList" />
      <el-button v-if="!readOnly" type="primary" @click="save" v-permission:B="2">保存</el-button>
    </template>
    <annual-intervention-maintain v-if="annualPlanMainID" ref="maintainRef" :interventionList="interventionList" />
  </base-layout>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../hooks/usePlanManagementStore";
const maintainRef = ref<Record<string, any>>({});
const year = usePlanTime().getPlanAnnual();
const interventionList = ref<Record<string, any>[]>([]);
const annualPlanStore = usePlanManagementStore();
const { annualPlanMainID, departmentID, readOnly } = storeToRefs(annualPlanStore);

onMounted(async () => {
  await loadInterventionListData();
});
watch(departmentID, async () => {
  await loadInterventionListData(true);
});
/**
 * @description: 加载措施字典数据
 */
const loadInterventionListData = async (reset: boolean = false) => {
  await annualPlanStore.setAnnualPlanMainID(reset);
  annualPlanStore.setReadOnly(annualPlanStore.annualPlanPublished);
  await getInterventionList();
};
/**
 * @description: 获取措施字典
 */
const getInterventionList = async () => {
  const result = await annualPlanSettingService.getAnnualInterventionList({
    departmentID: departmentID.value,
    showUpperIntervention: true
  });
  interventionList.value = Object.freeze(result) as Record<string, any>[];
};
/**
 * @description: 保存执行项目
 */
const save = () => {
  const saveInterventions = maintainRef.value.getSaveInterventions();
  if (!saveInterventions.length) {
    showMessage("warning", "没有待保存数据");
    return;
  }
  const aPInterventions = saveInterventions.flat();
  const params = {
    annualPlanMainID: annualPlanMainID.value,
    aPInterventions,
    departmentID: departmentID.value
  };
  annualPlanInterventionService.saveAnnualInterventions(params).then((res: any) => {
    res && showMessage("success", "保存成功");
    maintainRef.value.getInterventionsGroupByGoal();
    getInterventionList();
  });
};
</script>
<style lang="scss">
.annual-intervention-maintain {
  .base-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .el-drawer__header {
    margin-bottom: 0;
  }
  .goal-intervention-table .base-content-wrap {
    position: relative;
  }
}
</style>
