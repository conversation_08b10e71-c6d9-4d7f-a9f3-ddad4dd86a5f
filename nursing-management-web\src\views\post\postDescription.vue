<!--
 * FilePath     : \src\views\post\postDescription.vue
 * Author       : LX
 * Date         : 2023-08-07 19:23
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:37
 * Description  : 岗位说明
-->
<template>
  <base-layout class="post-description" :drawerOptions="drawerOptions">
    <template #header>
      <post-selector
        v-model="postID"
        label="岗位"
        :props="{ expandTrigger: 'hover', emitPath: false }"
        @change="filterPostDesc"
      ></post-selector>
      <span class="header-label">说明书名称：</span>
      <el-input v-model="desc" placeholder="说明书名称" class="desc-name" @change="filterPostDesc"></el-input>
      <File class="header-right-file" :fileOption="fileOption" @getExcelData="getExcelData"></File>
      <el-button v-permission:B="1" class="add-button" @click="addPostDesc()">新增</el-button>
    </template>
    <el-table :data="departmentPostDesc" stripe border height="100%" width="100%">
      <el-table-column prop="postDescriptionCode" label="说明书编码" :min-width="convertPX(130)"></el-table-column>
      <el-table-column prop="postDescriptionName" label="说明书名称" :min-width="convertPX(140)"></el-table-column>
      <el-table-column prop="postName" label="岗位" :min-width="convertPX(80)"></el-table-column>
      <el-table-column prop="superiors" label="直接上级" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column prop="junior" label="直接下级" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column prop="postNumber" label="岗位定员" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column prop="headCount" label="所辖人数" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column prop="createDepartment" label="制定部门" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column prop="approverName" label="审批人" align="center" :width="convertPX(100)"></el-table-column>
      <el-table-column label="审批时间" align="center" :width="convertPX(150)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.approveDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="signerName" label="签发人" align="center" :width="convertPX(110)"></el-table-column>
      <el-table-column label="签发时间" align="center" :width="convertPX(150)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.signDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本号" align="center" :width="convertPX(85)"></el-table-column>
      <el-table-column prop="status" label="状态" align="center" :width="convertPX(75)"></el-table-column>
      <el-table-column prop="modifyPerson" label="修订人" align="center" :width="convertPX(100)"></el-table-column>
      <el-table-column prop="modifyDateTime" label="修订时间" align="center" :width="convertPX(150)">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(120)" fixed="right">
        <template #default="scope">
          <el-tooltip content="查看" placement="top" :enterable="false">
            <i @click="showDetail(scope.row)" class="iconfont icon-preview"></i>
          </el-tooltip>
          <el-tooltip content="编辑" placement="top" :enterable="false">
            <i v-permission:B="3" @click="addPostDesc(scope.row)" class="iconfont icon-edit"></i>
          </el-tooltip>
          <el-tooltip content="删除" placement="top" :enterable="false">
            <i v-permission:B="4" @click="deleteData(scope.row)" class="iconfont icon-delete"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-descriptions border :column="2" class="post-detail">
        <el-descriptions-item label="说明书编码" :min-width="convertPX(160)">
          <span v-if="!selectedDesc.addFlag"> {{ selectedDesc.postDescriptionCode }}</span>
          <el-input v-else v-model="selectedDesc.postDescriptionCode" placeholder="请输入说明书编码"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="说明书名称" :min-width="convertPX(160)">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.postDescriptionName }}</span>
          <el-input v-else v-model="selectedDesc.postDescriptionName" placeholder="请输入说明书名称"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="岗位名称">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.postName }}</span>
          <post-selector
            v-else
            v-model="selectedDesc.postID"
            label=""
            :props="{ expandTrigger: 'hover', emitPath: false }"
            :width="convertPX(300)"
          ></post-selector>
        </el-descriptions-item>
        <el-descriptions-item label="制定部门">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.createDepartment }}</span>
          <department-selector v-else v-model="selectedDesc.createDepartmentID" label="" :width="convertPX(350)"></department-selector>
        </el-descriptions-item>
        <el-descriptions-item label="直接上级">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.superiors }}</span>
          <el-input v-else v-model="selectedDesc.superiors" placeholder="请输入直接上级" class="post-detail-input"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="直接下级">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.junior }}</span>
          <el-input v-else v-model="selectedDesc.junior" placeholder="请输入直接下级"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="岗位定员">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.postNumber }}</span>
          <el-input v-else v-model="selectedDesc.postNumber" placeholder="请输入岗位定员" class="post-detail-input"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="所辖人数">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.headCount }}</span>
          <el-input v-else v-model="selectedDesc.headCount" placeholder="请输入所辖人数"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.status }}</span>
          <el-select v-else v-model="selectedDesc.statusCode" placeholder="请选择状态" class="post-detail-input">
            <el-option v-for="item in postDescriptionStatusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-descriptions-item>
        <el-descriptions-item label="版本号">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.version }}</span>
          <el-input v-else v-model="selectedDesc.version" placeholder="请输入版本号"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="创建人" v-if="selectedDesc.addFlag">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.addPerson }}</span>
          <employee-selector
            v-else
            label=""
            v-model="selectedDesc.addEmployeeID"
            :departmentID="userStore.departmentID"
            :width="convertPX(300)"
            :disabled="true"
          />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" v-if="selectedDesc.addFlag"
          ><span
            v-if="!selectedDesc.editFlag"
            v-formatTime="{ value: selectedDesc.addDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
          ></span>
          <el-date-picker
            v-else
            disabled
            v-model="selectedDesc.addDateTime"
            type="datetime"
            placeholder="请选择创建时间"
            format="YYYY-MM-DD HH:mm"
          />
        </el-descriptions-item>
        <el-descriptions-item label="修订人" v-if="selectedDesc.addFlag">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.modifyPerson }}</span>
          <employee-selector
            v-else
            label=""
            v-model="selectedDesc.modifyEmployeeID"
            :departmentID="userStore.departmentID"
            :width="convertPX(300)"
            :disabled="true"
        /></el-descriptions-item>
        <el-descriptions-item label="修订时间" v-if="selectedDesc.addFlag">
          <span
            v-if="!selectedDesc.editFlag"
            v-formatTime="{ value: selectedDesc.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
          ></span>
          <el-date-picker
            v-else
            v-model="selectedDesc.modifyDateTime"
            type="datetime"
            placeholder="请选择修订时间"
            format="YYYY-MM-DD HH:mm"
            disabled
          />
        </el-descriptions-item>
        <el-descriptions-item label="审批人" v-if="selectedDesc.addFlag">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.approver }}</span>
          <employee-selector
            v-else
            label=""
            v-model="selectedDesc.approver"
            :departmentID="userStore.departmentID"
            :width="convertPX(300)"
          />
        </el-descriptions-item>
        <el-descriptions-item label="审批时间" v-if="selectedDesc.addFlag">
          <span
            v-if="!selectedDesc.editFlag"
            v-formatTime="{ value: selectedDesc.approveDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
          ></span>
          <el-date-picker
            v-else
            v-model="selectedDesc.approveDateTime"
            type="datetime"
            placeholder="请选择审批时间"
            format="YYYY-MM-DD HH:mm"
          />
        </el-descriptions-item>
        <el-descriptions-item label="签发人">
          <span v-if="!selectedDesc.editFlag">{{ selectedDesc.signerName }}</span>
          <employee-selector v-else label="" v-model="selectedDesc.signer" remote filterable :width="convertPX(300)" />
        </el-descriptions-item>
        <el-descriptions-item label="签发时间">
          <span
            v-if="!selectedDesc.editFlag"
            v-formatTime="{ value: selectedDesc.signDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"
          ></span>
          <el-date-picker
            v-else
            v-model="selectedDesc.signDateTime"
            type="datetime"
            placeholder="请选择签发时间"
            format="YYYY-MM-DD HH:mm"
          />
        </el-descriptions-item>
        <el-descriptions-item label="从业资格要求" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.qualificationRequirements"></span>
          <el-input v-else v-model="selectedDesc.qualificationRequirements" autosize type="textarea" placeholder="请输入从业资格要求" />
        </el-descriptions-item>
        <el-descriptions-item label="教育水平" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.educationalLevel"></span>
          <el-input v-else v-model="selectedDesc.educationalLevel" autosize type="textarea" placeholder="请输入教育水平" />
        </el-descriptions-item>
        <el-descriptions-item label="培训经历" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.trainingRecord"></span>
          <el-input v-else v-model="selectedDesc.trainingRecord" autosize type="textarea" placeholder="请输入培训经历" />
        </el-descriptions-item>
        <el-descriptions-item label="其他" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.other"></span>
          <el-input v-else v-model="selectedDesc.other" autosize type="textarea" placeholder="请输入其他" />
        </el-descriptions-item>
        <el-descriptions-item label="岗位职责" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.responsibility"></span>
          <el-input v-else v-model="selectedDesc.responsibility" autosize type="textarea" placeholder="请输入岗位职责" />
        </el-descriptions-item>
        <el-descriptions-item label="绩效评价" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.performanceEvaluation"></span>
          <el-input v-else v-model="selectedDesc.performanceEvaluation" autosize type="textarea" placeholder="请输入绩效评价" />
        </el-descriptions-item>
        <el-descriptions-item label="岗位SOP标准" span="2">
          <span v-if="!selectedDesc.editFlag" class="multiple-line" v-html="selectedDesc.sop"></span>
          <el-input v-else v-model="selectedDesc.sop" autosize type="textarea" placeholder="请输入岗位SOP标准" />
        </el-descriptions-item>
      </el-descriptions>
    </template>
  </base-layout>
</template>

<script setup lang="ts">
// #region 变量定义
import { departmentPost, postDescription } from "./types/postDescriptionView";
const { proxy } = getCurrentInstance() as any;
const { userStore } = useStore();
const convertPX: any = inject("convertPX");
let desc = ref<string>("");
let departmentPostDesc = ref<Array<postDescription>>([]);
let postID = ref(undefined as number | undefined);
let selectedDesc = ref<postDescription>({} as any);
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "岗位说明书详情",
  drawerSize: "60%",
  showDrawer: false,
  showConfirm: true,
  confirm: async () => {
    savePostDescriptionData();
  }
});
let departmentPostDescList = ref<Array<postDescription>>([]);
let postDescriptionStatusList = ref<Array<Record<any, any>>>([]);
let dictionData = ref<Array<departmentPost>>([]);
// #endregion

// #region 初始化onMounted钩子函数
onMounted(() => {
  getPostDescriptionList();
});
// #endregion

// #region 业务逻辑：获取数据
let { getPostData } = useDictionaryData();
getPostData().then((datas) => {
  datas.forEach((post) => {
    dictionData.value.push({
      postID: post.value,
      departmentPostName: post.label
    });
  });
});
/**
 * @description: 获取岗位说明书数据
 */
const getPostDescriptionList = () => {
  let param = { departmentID: userStore.departmentID };
  postService.getPostDescriptionList(param).then((res: any) => {
    if (res) {
      departmentPostDesc.value = res;
      departmentPostDescList.value = res;
    }
  });
};
/**
 * @description: 获取状态字典配置
 */
const params: SettingDictionaryParams = {
  settingType: "PositionManagement",
  settingTypeCode: "PostDescription",
  settingTypeValue: "StatusCode"
};
settingDictionaryService.getSettingDictionaryDict(params).then((datas: any) => {
  postDescriptionStatusList.value = datas;
});
// #endregion

// #region 业务逻辑：前端逻辑处理
/**
 * @description: 过滤筛选数据
 */
const filterPostDesc = () => {
  let tmpList = common.clone(departmentPostDescList.value);
  desc.value && (tmpList = tmpList.filter((item: postDescription) => item.postDescriptionName.includes(desc.value)));
  postID.value && (tmpList = tmpList.filter((item: postDescription) => item.postID === postID.value));
  departmentPostDesc.value = tmpList;
};
/**
 * @description: 查看岗位说明书明细内容
 * @param row
 * @return
 */
const showDetail = (row: any) => {
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.showCancel = false;
  let params = {
    editFlag: false
  };
  selectedDesc.value = Object.assign(row, params);
};
/**
 * @description: 新增数据，打开抽屉
 */
const addPostDesc = (row?: postDescription) => {
  drawerOptions.value.showDrawer = true;
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.showCancel = true;
  let params = {
    editFlag: true,
    modifyEmployeeID: userStore.employeeID,
    modifyDateTime: datetimeUtil.getNow("yyyy-MM-dd hh:mm"),
    addEmployeeID: row?.addPerson ?? userStore.employeeID,
    addDateTime: row?.addDateTime ?? datetimeUtil.getNow("yyyy-MM-dd hh:mm"),
    addFlag: !row,
    postDescriptionName: row?.postDescriptionName ?? ""
  };
  if (row) {
    let value = Object.assign(row, params);
    selectedDesc.value = Object.assign({}, value);
    return;
  }
  selectedDesc.value = params;
};
// #endregion

// #region 业务逻辑：数据删除，保存逻辑
/**
 * @description: 删除数据
 * @param row
 * @return
 */
const deleteData = (row: postDescription) => {
  confirmBox("确定删除？", proxy.$t("tip.systemTip"), (flag: boolean) => {
    if (flag) {
      let params = {
        postID: row.postID,
        postDescriptionCode: row.postDescriptionCode,
        departmentID: row.departmentID,
        version: row.version
      };
      postService.deletePostDescription(params).then((res: any) => {
        if (res) {
          showMessage("success", "删除成功!");
          getPostDescriptionList();
        }
      });
    }
  });
};
/**
 * @description: 保存方法
 */
const savePostDescriptionData = () => {
  if (!checkSaveData(selectedDesc.value, 0, false)) {
    return;
  }
  let params = toRaw(selectedDesc.value);
  params.departmentID = userStore.departmentID;
  postService.savePostDescription(params).then((res: any) => {
    drawerOptions.value.showDrawer = false;
    if (res) {
      showMessage("success", "保存成功!");
      getPostDescriptionList();
    }
  });
};
// #endregion

// #region 业务逻辑：导入导出逻辑
let excelTemplateColumn = ref<Record<string, any>>({
  postDescriptionCode: "说明书编码",
  postDescriptionName: "说明书名称",
  postID: "岗位名称编码",
  postName: "岗位",
  superiors: "直接上级",
  junior: "直接下级",
  postNumber: "岗位定员",
  headCount: "所辖人数",
  createDepartment: "制定部门",
  approverName: "审批人",
  approveDateTime: "审批时间(格式：例：2023-12-08 08:00)",
  signerName: "签发人",
  signDateTime: "签发时间(格式：例：2023-12-08 08:00)",
  version: "版本号",
  status: "状态(0停用、1签发、2待审批、3审批通过、4审批驳回)",
  modifyPerson: "修改人",
  modifyDateTime: "修改时间(格式：例：2023-12-08 08:00)",
  addEmployee: "创建人",
  addDateTime: "创建时间",
  qualificationRequirements: "从业资格要求",
  educationalLevel: "教育水平",
  trainingRecord: "培训经历",
  other: "其他",
  responsibility: "岗位职责",
  performanceEvaluation: "绩效评价",
  sop: "岗位SOP标准"
});
/**
 * @description: 获取导入Excel数据内容
 * @param importData
 * @return
 */
const getExcelData = (importData: any) => {
  if (importData.length === 0) {
    return;
  }
  let params: Array<any> = [];
  importData.forEach((importItem: any, index: number) => {
    importItem.addFlag = true;
    if (!checkSaveData(importItem, index, true)) {
      return;
    }
    let param: any = {
      postID: importItem.postID,
      departmentID: userStore.departmentID,
      postDescriptionCode: importItem.postDescriptionCode,
      postDescriptionName: importItem.PostDescriptionName,
      superiors: importItem.superiors,
      junior: importItem.junior,
      postNumber: String(importItem.postNumber || ""),
      headCount: String(importItem.headCount || ""),
      createDepartment: importItem.createDepartment,
      version: importItem.version,
      statusCode: String(importItem.status || ""),
      approverName: importItem.approverName,
      approveDateTime: datetimeUtil.formatDate(importItem.approveDateTime, "yyyy-MM-dd hh:mm"),
      signerName: importItem.signerName,
      signDateTime: datetimeUtil.formatDate(importItem.signDateTime, "yyyy-MM-dd hh:mm"),
      qualificationRequirements: importItem.qualificationRequirements,
      educationalLevel: importItem.educationalLevel,
      trainingRecord: importItem.trainingRecord,
      other: importItem.other,
      responsibility: importItem.responsibility,
      performanceEvaluation: importItem.performanceEvaluation,
      sop: importItem.sop,
      modifyPerson: importItem.modifyPerson,
      addEmployee: importData.addEmployee,
      addEmployeeID: userStore.employeeID,
      addDateTime: datetimeUtil.getNow("yyyy-MM-dd hh:mm:ss"),
      modifyEmployeeID: userStore.employeeID,
      modifyDateTime: datetimeUtil.getNow("yyyy-MM-dd hh:mm:ss")
    };
    params.push(param);
  });

  if (params.length === 0) {
    return;
  }
  postService.batchSavePostDescription(params).then(() => {
    getPostDescriptionList();
  });
};
/**
 * @description: Excel导出参数
 */
const exportExcelOption = reactive<ExportExcelView[]>([
  {
    buttonName: "导出模板",
    fileName: "岗位说明书",
    sheetName: "岗位说明书",
    columnData: excelTemplateColumn,
    tableData: []
  },
  {
    buttonName: "导出模板",
    fileName: "岗位名称",
    sheetName: "岗位名称字典",
    columnData: {
      postID: "岗位名称编码",
      departmentPostName: "岗位名称"
    },
    tableData: dictionData.value
  }
]);
/**
 * @description: Excel导入参数
 */
const importExcelOption = reactive<ImportExcelView>({
  columnData: excelTemplateColumn,
  buttonName: "导入数据"
});
const fileOption = reactive<FilePropsView>({
  typeArr: ["exportExcel", "importExcel"],
  exportExcelOption,
  importExcelOption
});
import { useUtils } from "../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 检核数据
 * @param data
 * @param index
 * @param batchFlag
 * @return
 */
const checkSaveData = (data: any, index: number, batchFlag: boolean) => {
  const flag = ref<boolean>(true);
  const message = ref<string>("");
  if (batchFlag) {
    message.value = `第${index + 1}行`;
  }
  if (!checkVersionFormat(data.version) && data.addFlag) {
    message.value = `${message.value}版本号格式错误（版本号格式为三段，例如：1.0.0）；<br/>`;
    flag.value = false;
  }
  if (!data.postDescriptionCode) {
    message.value = `${message.value}说明书编码为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (batchFlag && !data.postID) {
    message.value = `${message.value}导入数据位名称编码为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (batchFlag && !data.postName) {
    message.value = `${message.value}岗位名称为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (!batchFlag && !data.postID) {
    message.value = `${message.value}岗位名称为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (batchFlag && data.status?.length) {
    message.value = `${message.value}状态为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (!batchFlag && !data.statusCode) {
    message.value = `${message.value}状态为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (!data.version) {
    message.value = `${message.value}版本号为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (batchFlag && !data.createDepartment) {
    message.value = `${message.value}制定部门为空，请填写完整；<br/>`;
    flag.value = false;
  }
  if (!batchFlag && !data.createDepartmentID) {
    message.value = `${message.value}制定部门为空，请填写完整；<br/>`;
    flag.value = false;
  }
  let findTableData = departmentPostDesc.value.find((desc) => desc.postDescriptionCode == data.postDescriptionCode);
  if (findTableData && data.addFlag) {
    message.value = `${message.value}说明书编码已存在，请更改；<br/>`;
    flag.value = false;
  }
  if (!flag.value) {
    showAlert("warning", message.value);
    return false;
  }
  return true;
};
/**
 * @description: 检核版本号格式
 * @param data
 * @return
 */
const checkVersionFormat = (data: string) => {
  const versionPattern = /^\d+\.\d+\.\d+$/;
  return versionPattern.test(data);
};
// #endregion
</script>

<style lang="scss">
.post-description {
  width: 100%;
  .header-label {
    margin-left: 20px;
  }
  .header-right-file {
    float: right;
  }
  .desc-name {
    width: 240px;
  }
  .post-detail.el-descriptions {
    .el-descriptions__label,
    .el-descriptions__content {
      font-size: 24px;
    }
    .post-detail-input {
      width: 300px;
    }
  }
}
</style>
