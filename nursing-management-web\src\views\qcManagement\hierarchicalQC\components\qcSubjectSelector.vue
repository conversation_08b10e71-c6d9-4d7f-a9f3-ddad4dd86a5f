<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\qcSubjectSelector.vue
 * Author       : 郭鹏超
 * Date         : 2023-09-07 16:22
 * LastEditors  : 郭鹏超
 * LastEditTime : 2024-11-05 10:41
 * Description  :质控主题下拉框组件
 * CodeIterationRecord:
-->
<template>
  <div class="qc-form-selector">
    <label v-if="label">{{ label + "：" }}</label>
    <el-select @change="change" :clearable="clearable" :disabled="disabled" class="form-select" v-model="qcSubjectID">
      <el-option v-for="(item, index) in formOptions" :key="index" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import type { subjectSearchView } from "../types/subjectSearchView";
const props = defineProps({
  modelValue: {
    type: String,
    default: undefined
  },
  label: {
    type: String
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  defaultFirstFlag: {
    type: Boolean,
    default: false
  },
  params: {
    type: Object as () => subjectSearchView,
    default: () => ({})
  }
});
const emit = defineEmits(["update:modelValue", "change"]);
let qcSubjectID = ref<string>();

let formOptions = ref<any[]>([]);
const change = (value: any) => {
  emit("update:modelValue", value);
  emit(
    "change",
    formOptions.value.find((option) => option.value === value)
  );
};
const getSubjectData = async () => {
  let params = {
    ...props.params,
    startYearMonth: props.params.yearMonth,
    endYearMonth: props.params.yearMonth
  };
  await hierarchicalQCService.getQCSubjectSelectOptions(params).then((res: any) => {
    formOptions.value = res;
    !formOptions.value?.length && (qcSubjectID.value = undefined);
    if (qcSubjectID.value) {
      change(qcSubjectID.value);
      return;
    }
    // 是否默认选中第一个
    if (props.defaultFirstFlag) {
      qcSubjectID.value = formOptions.value[0]?.value;

      change(qcSubjectID.value);
    }
  });
};
watch(
  () => props.params,
  async (newVal, oldVal) => {
    if (newVal.yearMonth !== oldVal.yearMonth) {
      await getSubjectData();
    }
  }
);
onMounted(() => {
  getSubjectData();
});
</script>

<style lang="scss">
.qc-form-selector {
  display: inline-block;
  .form-select {
    width: 400px;
  }
}
</style>
