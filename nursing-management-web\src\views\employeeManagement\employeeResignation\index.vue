<!--
 * FilePath     : \src\views\employeeManagement\employeeResignation\index.vue
 * Author       : 马超
 * Date         : 2025-06-23 14:47
 * LastEditors  : 马超
 * LastEditTime : 2025-06-28 15:57
 * Description  : 离职员工查询
 * CodeIterationRecord: 
 -->
<template>
  <base-layout class="employee-resignation-list" headerHeight="auto">
    <template #header>
      <div>
        <department-selector
          v-permission:DL="13"
          v-model="departmentID"
          :props="{ expandTrigger: 'hover', multiple: true }"
          :clearable="true"
          :width="245"
          @change="getEmployeeResignationList"
        ></department-selector>
        <entry-date-selector label="申请离职日期" @change="getEmployeeResignationList" v-model="resignationApplyDate"></entry-date-selector>
        <entry-date-selector label="离职日期" @change="getEmployeeResignationList" v-model="resignationDate"></entry-date-selector>
      </div>
    </template>
    <el-table
      :data="employeeResignationList"
      border
      :max-height="tableMaxHeight"
      stripe
      highlight-current-row
      @row-dblclick="showEmployeeDetail"
      style="width: 100%"
      v-loading="loading"
      element-loading-text="加载中……"
    >
      <el-table-column prop="employeeName" label="姓名" align="center" />
      <el-table-column prop="gender" label="性别" align="center" />
      <el-table-column prop="departmentName" label="部门" align="center" />
      <el-table-column prop="title" label="职务" align="center" />
      <el-table-column prop="resignationApplyDate" label="申请离职日期" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.resignationApplyDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="resignationDate" label="离职日期" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.resignationDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :width="convertPX(80)">
        <template #default="scope">
          <el-tooltip content="查看档案">
            <i class="iconfont icon-search action-icon" @click="showEmployeeDetail(scope.row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </base-layout>
</template>

<script setup lang="ts">
const convertPX: any = inject("convertPX");
const router = useRouter();
const { userStore } = useStore();

// 筛选项
let departmentID = ref<number | number[] | undefined>(userStore.departmentID);
let resignationApplyDate = ref<string[]>([]);
let resignationDate = ref<string[]>([]);

// 数据
let employeeResignationList = ref<Record<string, any>[]>([]);
let loading = ref<boolean>(false);

/**
 * 计算表格最大高度
 */
const tableMaxHeight = computed(() => {
  // 获取视窗高度，减去页面其他元素的高度（header、padding等）
  const windowHeight = window.innerHeight;
  const headerHeight = 120; // 预估header高度
  const padding = 40; // 页面padding
  return windowHeight - headerHeight - padding;
});

onMounted(() => {
  getEmployeeResignationList();
});

/**
 * 获取离职员工列表
 */
const getEmployeeResignationList = async () => {
  loading.value = true;
  let params = {
    departmentIDs: typeof departmentID.value === "number" ? [departmentID.value] : departmentID.value,
    resignationApplyDate: resignationApplyDate.value,
    resignationDate: resignationDate.value
  };
  const data = await employeeService.getEmployeeResignationList(params);
  if (data && Array.isArray(data)) {
    employeeResignationList.value = data;
  } else {
    employeeResignationList.value = [];
  }
  loading.value = false;
};

/**
 * 跳转到人员档案
 */
const showEmployeeDetail = (item: any) => {
  router.push({
    path: "/personalInformation",
    query: {
      employeeID: item.employeeID,
      readonly: "true"
    }
  });
};
</script>

<style lang="scss">
.employee-resignation-list {
  width: 100%;
  background-color: #ffffff;
}
</style>
