.el-input {
  .el-input__wrapper {
    box-shadow: 0 0 0 1px #999999 inset !important;
    .el-input__inner {
      color: #000000 !important;
    }
  }
  .el-input-group__append {
    padding: 0 10px;
    .icon-search {
      display: inherit;
    }
  }
}
.el-input--small {
  height: 24px !important;
  .el-input__wrapper {
    height: 100%;
    box-sizing: border-box;
    padding: 1px 7px !important;
  }
}
//日期范围
.el-range-editor--small {
  height: 24px !important;
  padding: 0 10px !important;
  vertical-align: middle;
}
.el-textarea {
  height: auto !important;
}
.el-date-editor--timerange {
  flex-grow: 0 !important;
  width: 160px !important;
  box-shadow: 0 0 0 1px #999999 inset !important;
  .el-range-input {
    color: #000000 !important;
  }
}
