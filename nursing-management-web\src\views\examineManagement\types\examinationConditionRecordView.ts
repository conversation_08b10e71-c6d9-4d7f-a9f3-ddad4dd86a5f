/*
 * relative     : \nursing-management-web\src\views\examineManagement\types\examinationConditionRecordView.ts
 * Author       : 张现忠
 * Date         : 2025-01-20 16:12
 * LastEditors  : 张现忠
 * LastEditTime : 2025-01-20 16:14
 * Description  :
 * CodeIterationRecord:
 */
/**
 * 考核条件视图接口
 */
export interface examinationConditionRecordView {
  /**
   * 考核条件表ID
   */
  examinationConditionRecordID: string;
  /**
   * 条件名称
   */
  conditionName: string;
  /**
   * 条件明细内容
   */
  conditionContent: string;
  /**
   * 规则分数（依据明细计算出的总分）
   */
  score: number;
  /**
   * 新增人工号
   */
  addEmployeeID: string;
  /**
   * 新增人
   */
  addEmployeeName: string;
  /**
   * 新增日期
   */
  addDateTime: Date;
  /**
   * 修改人工号
   */
  modifyEmployeeID: string;
  /**
   * 修改人
   */
  modifyEmployeeName: string;
  /**
   * 修改时间
   */
  modifyDateTime: Date;
}
