/*
 * FilePath     : \src\views\scheduling\components\univerMenuIcon\index.ts
 * Author       : 苏军志
 * Date         : 2024-10-24 19:25
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:47
 * Description  : 菜单
 * CodeIterationRecord:
 */
// import schedulingTemplateMenuIcon from "./schedulingTemplateMenuIcon.vue";
// import postMenuIcon from "./postMenuIcon.vue";
// import restMenuIcon from "./restMenuIcon.vue";
import restButtonMenuIcon from "./restButtonMenuIcon.vue";
// import markMenuIcon from "./markMenuIcon.vue";
// import amNoonMenuIcon from "./amNoonMenuIcon.vue";
// import pmNoonMenuIcon from "./pmNoonMenuIcon.vue";

// 导出菜单图标集合
// TODO: 临时屏蔽 因有图标 二级菜单滑动时 菜单会消失，后续升级univer版本号解决
export default {
  // schedulingTemplateMenuIcon,
  // postMenuIcon, restMenuIcon,
  restButtonMenuIcon
  // markMenuIcon,
  // amNoonMenuIcon,
  // pmNoonMenuIcon
};

// 导出菜单图标枚举
export enum menuIconEnum {
  /**
   * 排班模板菜单图标
   */
  TEMPLATE_MENU_ICON = "schedulingTemplateMenuIcon",
  /**
   * 岗位菜单图标
   */
  POST_MENU_ICON = "postMenuIcon",
  /**
   * 休假菜单图标
   */
  REST_MENU_ICON = "restMenuIcon",
  /**
   * 休假菜单图标
   */
  REST_BUTTON_MENU_ICON = "restButtonMenuIcon",
  /**
   * 标记菜单图标
   */
  MARK_MENU_ICON = "markMenuIcon",
  /**
   * 上午别菜单图标
   */
  AM_NOON_MENU_ICON = "amNoonMenuIcon",
  /**
   * 下午别菜单图标
   */
  PM_NOON_MENU_ICON = "pmNoonMenuIcon"
}
