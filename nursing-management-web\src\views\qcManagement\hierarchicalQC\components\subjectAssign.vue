<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\subjectAssign.vue
 * Author       : 郭鹏超
 * Date         : 2023-09-08 08:52
 * LastEditors  : 苏军志
 * LastEditTime : 2025-01-02 18:25
 * Description  : 主题指派
 * CodeIterationRecord:
-->
<template>
  <div class="subject-assign">
    <div class="left-view">
      <div class="view top">
        <div class="header">
          <label class="label">病区：</label>
          <el-input
            class="input"
            @keyup.enter="searchAssignDepartment"
            v-model="assignDepartmentSearchInputData"
            placeholder="请输入病区名称"
          >
            <template #append>
              <i @click="searchAssignDepartment" class="iconfont icon-search" />
            </template>
          </el-input>
          <label class="label" v-if="upperDepartmentOptions.length > 0">片区：</label>
          <el-select
            v-if="upperDepartmentOptions.length > 0"
            class="upper-dept-select"
            v-model="upperDepartmentID"
            clearable
            @change="searchAssignDepartment"
          >
            <el-option
              v-for="upperItem in upperDepartmentOptions"
              :key="upperItem.value"
              :label="upperItem.label"
              :value="upperItem.value"
            />
          </el-select>
        </div>
        <div class="content">
          <drag
            v-if="subjectAssignDepartmentList.length"
            ref="departmentSourceRef"
            :draggableOption="{ itemKey: 'departmentID', groupName: 'department', put: false }"
            :multiFlag="true"
            @scrollLoad="departmentLoad"
            @remove="departmentRemove"
            height="100%"
            v-model="subjectAssignDepartmentList"
          >
            <template #content="{ element }">
              <tag
                v-if="element"
                :name="element.department"
                :title="'部门名称：' + element.department + ' ，部门ID：' + element.departmentID"
                class="department-tag"
              >
              </tag>
            </template>
          </drag>
        </div>
      </div>
      <div class="view bottom">
        <div class="header">
          <department-switch-selector
            v-if="formLevel === '1'"
            v-model="selectedDepartmentID"
            :clearable="true"
            :employeeID="currEmployeeID"
            :width="190"
            @change="searchAssignEmployee"
          ></department-switch-selector>
          <department-selector
            v-else
            v-model="selectedDepartmentID"
            :clearable="true"
            :width="190"
            @change="searchAssignEmployee"
          ></department-selector>
          <label class="label">人员：</label>
          <el-input class="input" @keyup.enter="searchAssignEmployee" v-model="assignEmployeeSearchInputData" placeholder="输入姓名或简拼">
            <template #append>
              <i @click="searchAssignEmployee" class="iconfont icon-search" />
            </template>
          </el-input>
        </div>
        <div class="content">
          <drag
            :draggableOption="{ itemKey: 'employeeID', groupName: 'people', put: false, pull: 'clone' }"
            :multiFlag="true"
            @scrollLoad="employeeLoad"
            height="100%"
            :moveCheck="checkRemove"
            :infinite-scroll-distance="5"
            :infinite-scroll-immediate="true"
            v-if="subjectAssignEmployeeList.length"
            v-model="subjectAssignEmployeeList"
          >
            <template #content="{ element }">
              <tag
                :name="element.employeeName"
                :title="'部门：' + element.departmentName + ' ，层级：' + element.capabilityLevel + ' ，工号：' + element.employeeID"
              >
              </tag>
            </template>
          </drag>
        </div>
      </div>
    </div>
    <div class="right-view">
      <div class="header">
        <el-button v-permission:B="1" type="primary" @click="subjectPlanAdd" class="assign-add-button">新增</el-button>
      </div>
      <el-table class="content" :data="subjectAssignTableView.assignTableData" border height="100%">
        <el-table-column prop="date" label="质控人" :min-width="convertPX(120)">
          <template #default="scope">
            <drag
              class="table-content"
              v-model="scope.row.hierarchicalQCEmploy"
              :draggableOption="{ itemKey: 'employeeID', groupName: 'people' }"
              :multiFlag="true"
              :drugMinHeight="100"
            >
              <template #content="{ element, index }">
                <tag
                  :closeable="true"
                  @remove="deleteEmployee(scope.row.hierarchicalQCEmploy, index)"
                  :name="element.employeeName"
                  :title="'人员名称：' + element.employeeName + ' ，人员ID：' + element.employeeID"
                >
                </tag>
              </template>
            </drag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="被质控病区" :min-width="convertPX(200)">
          <template #default="scope">
            <drag
              class="table-content"
              :draggableOption="{ itemKey: 'departmentID', groupName: 'department' }"
              v-model="scope.row.departmentViews"
              :drugMinHeight="100"
              @right-click="rightClick(scope.row.departmentViews, $event)"
            >
              <template #content="{ element, index }">
                <tag
                  v-if="element"
                  :closeable="true"
                  @remove="deleteDepartment(scope.row.departmentViews, index)"
                  :name="element.department"
                  :title="'部门名称：' + element.department + ' ，部门ID：' + element.departmentID"
                >
                </tag>
              </template>
              <template #footer>
                <div class="department-count">
                  {{ "总数：" + (scope.row.departmentViews?.length ?? 0) }}
                </div>
              </template>
            </drag>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="审核人" :min-width="convertPX(80)">
          <template #default="scope">
            <drag
              class="table-content"
              draggableViewClass="verifier-employee-view"
              v-model="scope.row.verifierEmployee"
              :draggableOption="{ itemKey: 'employeeID', groupName: 'people', multiFlag: true }"
              :drugMinHeight="100"
            >
              <template #content="{ element, index }">
                <tag
                  :closeable="true"
                  @remove="deleteEmployee(scope.row.verifierEmployee, index)"
                  :name="element.employeeName"
                  :title="'人员名称：' + element.employeeName + ' ，人员ID：' + element.employeeID"
                >
                </tag>
              </template>
            </drag>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(80)" align="center" label="操作">
          <template #default="scope">
            <i v-permission:B="4" @click="deleteSubjectPlanItem(scope.$index)" class="iconfont icon-delete"></i>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
const convertPX: any = inject("convertPX");
import { subjectAssignControl } from "../hooks/useSubjectAssignControl";
import { subjectAssignUseData } from "../hooks/useSubjectData";
const { userStore } = useStore();
const {
  currEmployeeID,
  formLevel,
  selectedDepartmentID,
  subjectAssignTableView,
  subjectAssignDepartmentList,
  assignDepartmentSearchInputData,
  subjectAssignEmployeeList,
  assignEmployeeSearchInputData,
  subjectAssignCopyDepartmentList,
  upperDepartmentID,
  getAssignDepartment,
  searchAssignDepartment,
  getAssignEmployee,
  searchAssignEmployee,
  employeeLoad,
  departmentLoad,
  getSubjectAssignView
} = subjectAssignUseData();
const { subjectPlanAdd, deleteEmployee, deleteDepartment, deleteSubjectPlanItem, departmentRemove, saveSubjectAssign } =
  subjectAssignControl(subjectAssignTableView, subjectAssignDepartmentList, subjectAssignCopyDepartmentList);
const upperDepartmentOptions = ref<Record<string, any>[]>([]);
/**
 * description: 页面初始化
 * param {*} subject
 * return {*}
 */
let subjectAssign = async (subject: any) => {
  assignDepartmentSearchInputData.value = "";
  assignEmployeeSearchInputData.value = "";
  subjectAssignTableView.hierarchicalQCSubjectID = subject.hierarchicalQCSubjectID;
  formLevel.value = subject.hierarchicalQCFormLevel;
  currEmployeeID.value = userStore.employeeID;
  getAssignDepartment();
  await getAssignEmployee();
  subject.assignFlag ? getSubjectAssignView(subject.hierarchicalQCSubjectID) : subjectAssignTableView.reset();
  selectedDepartmentID.value = userStore.departmentID;
  searchAssignEmployee();
  // 非一级质控，指派页面，显示片区筛选
  formLevel.value !== "1" && (await getUpperDeptOptions());
};
/**
 * description: 指控人和审核人不能重复添加
 * param {*} value
 * return {*}
 */
const checkRemove = (value: any) => {
  if (value.to.innerText.includes(value.draggedContext.element.employeeName)) {
    return false;
  }
  return true;
};
const departmentSourceRef = ref<Record<string, any>>();
/**
 * @description: 右键移动选中项
 * @param list 目标列表
 * @param {groupName, dragID} 目标组名，目标实例ID
 * @return
 */
const rightClick = (list: Record<string, any>[], { groupName, dragID }: Record<string, any>) => {
  if (!departmentSourceRef.value) {
    return;
  }
  const { dragID: sourceID, groupName: sourceGroupName, selectedItems } = departmentSourceRef.value.dragState;
  // 不能从自己拖拽到自己、不能组名不同拖拽、不能没有选中项
  if (sourceID === dragID || sourceGroupName !== groupName || !selectedItems.size) {
    return;
  }
  list.push(...selectedItems.values());
  // 点选移动不会触发remove事件，需要手工处理
  for (const item of selectedItems) {
    departmentRemove(item);
  }
  // 从subjectAssignDepartmentList中删除
  subjectAssignDepartmentList.value = subjectAssignDepartmentList.value.filter((item) => !selectedItems.has(item));
  // 清空已选择项记录
  departmentSourceRef.value && departmentSourceRef.value.toggleSelection("deselect", departmentSourceRef.value.$el);
};
/**
 * @description: 获取上一级部门选项
 */
const getUpperDeptOptions = async () => {
  await dictionaryService.getUpperDeptOptions().then((resp: any) => {
    upperDepartmentOptions.value = resp || [];
  });
};
/**
 * description:初始化方式及保存方式暴漏出去
 * return {*}
 */
defineExpose({
  subjectAssign,
  saveSubjectAssign
});
</script>

<style lang="scss">
.subject-assign {
  height: 100%;
  & > div {
    float: left;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid $border-color;
  }
  .left-view {
    margin-right: 5px;
    width: 30%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    & > div {
      flex: 1;
      max-height: 50%;
    }
    .view {
      display: flex;
      flex-direction: column;
      height: 100%;
      .header {
        padding: 10px 10px;
        .input {
          width: 190px;
          margin-right: 5px;
        }
        .upper-dept-select {
          width: 190px;
        }
      }
      &.bottom {
        .header {
          .input {
            width: 190px;
          }
        }
      }
      .content {
        border-bottom: 1px solid $border-color;
        flex: auto;
        overflow-y: auto;
        .draggable-view {
          width: 29%;
          height: 40px;
        }
      }
    }
    .bottom {
      .content {
        border-bottom: none;
      }
    }
  }
  .right-view {
    border: none;
    width: calc(70% - 5px);
    padding: 5px 0;
    display: flex;
    flex-direction: column;
    .header {
      height: 30px;
      line-height: 30px;
      .assign-add-button {
        float: right;
      }
    }
    .content {
      flex-grow: 1;
      .table-content {
        height: 100%;
        min-height: 100px;
        position: relative;
        .draggable-view {
          width: 30%;
          height: 30px;
        }
        .verifier-employee-view {
          width: 60%;
          height: 30px;
        }
      }
      .department-count {
        text-align: right;
        padding: 5px 10px;
      }
    }
  }
}
</style>
