<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\components\qcFormTypeSelector.vue
 * Author       : 郭鹏超
 * Date         : 2023-09-07 16:22
 * LastEditors  : 苏军志
 * LastEditTime : 2025-03-06 17:39
 * Description  :质控主题下拉框组件
 * CodeIterationRecord:
-->
<template>
  <div class="qc-form-type-selector">
    <el-select clearable :disabled="disabled" @change="formTypeChange" class="form-type-select" v-model="formTypeID">
      <el-option v-for="item in formTypeOptions" :key="item.hierarchicalformTypeID" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: undefined
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  qcType: {
    type: String as PropType<"nodeQCFormType" | "normalWorkingFormType" | "visitsFormType" | "specialFormType" | undefined>,
    default: undefined
  },
  width: {
    type: Number,
    default: 250
  },
  list: {
    type: Array<Record<any, any>>,
    default: () => undefined
  }
});
onMounted(() => {
  getFormTypeList();
  formTypeID.value = props.modelValue;
});
let formTypeOptions = ref<any[]>([]);
let { list } = toRefs(props);
const getFormTypeList = async () => {
  // 如果传值了就使用传的值，否则就通过hooks从数据库获取数据
  if (list?.value) {
    formTypeOptions.value = list.value;
    return;
  }
  let params = {
    settingType: "HierarchicalQC",
    settingTypeCode: "HierarchicalQCFormType",
    index: Math.random(),
    qcType: props.qcType
  };
  await hierarchicalQCService.getQCFormType(params).then((res: any) => (formTypeOptions.value = res));
  formTypeChange(props.modelValue);
};
const emit = defineEmits(["update:modelValue", "change"]);
let formTypeID = useVModel(props, "modelValue", emit);
watch(
  () => props.qcType,
  () => {
    getFormTypeList();
  }
);
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);
const formTypeChange = (value?: string) => {
  emit(
    "change",
    formTypeOptions.value.find((option) => option.hierarchicalformTypeID === value)
  );
};
</script>

<style lang="scss">
.qc-form-type-selector {
  display: inline-block;
  width: v-bind(selectorWidth);
}
</style>
