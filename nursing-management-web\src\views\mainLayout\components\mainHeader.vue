<!--
 * FilePath     : \src\views\mainLayout\components\mainHeader.vue
 * Author       : 苏军志
 * Date         : 2023-06-17 09:26
 * LastEditors  : 苏军志
 * LastEditTime : 2025-01-24 17:01
 * Description  : 主画面的头部
 * CodeIterationRecord:
-->
<template>
  <div class="main-header-component">
    <div class="bottom-divider"></div>
    <img class="logo" src="/static/images/hospital-logo.png" @click="handleClick('switchMenu')" />
    <div class="system-name" @click="handleClick('home')">{{ i18nText.systemName }}</div>
    <div class="system-version" @click="handleClick('version')">{{ `V ${commonStore.hospital.systemVersion}` }}</div>
    <div class="header-right">
      <el-dropdown trigger="click" @command="selectLanguage">
        <div class="select-language">
          <span v-if="commonStore.language.language">
            {{ commonStore.language.languageName }}
          </span>
          <i class="iconfont icon-arrow-down"></i>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="select-hospital-dropdown">
            <el-dropdown-item v-for="(language, index) in commonStore.languageList" :key="index" :command="language" divided>
              {{ language.languageName }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-tooltip :content="isFullscreen ? i18nText.exitFullscreen : i18nText.fullscreen">
        <i
          :class="['iconfont', `icon${isFullscreen ? '-exit' : ''}-fullscreen`]"
          @click="handleClick('toggleFullscreen')"
          @contextmenu="handleClick('toggleDomFullscreen')"
        ></i>
      </el-tooltip>
      <el-tooltip :content="i18nText.home">
        <i class="iconfont icon-home" @click="handleClick('home')"></i>
      </el-tooltip>
      <el-tooltip :content="i18nText.refresh">
        <i class="iconfont icon-refresh" @click="handleClick('refresh')"></i>
      </el-tooltip>
      <el-tooltip :content="i18nText.message">
        <el-badge v-if="messageCount > 0" :value="messageCount" :max="99">
          <i class="iconfont icon-message" @click="handleClick('message')"></i>
        </el-badge>
        <i v-else class="iconfont icon-message" @click="handleClick('message')"></i>
      </el-tooltip>
      <el-tooltip :content="i18nText.logOut">
        <i class="iconfont icon-exit" @click="handleClick('logOut')"></i>
      </el-tooltip>
      <div class="line"></div>
      <div class="user-info">
        <el-image class="user-img" :src="photoUrl" :preview-src-list="[photoUrl]" />
        <span class="text" v-if="userName">{{ userName }}</span>
      </div>
      <department-switch-selector label="" v-model="userStore.departmentID" :employeeID="userStore.employeeID" filterable :width="220" />
    </div>
  </div>
</template>
<script setup lang="ts">
const { proxy } = getCurrentInstance() as any;
const { commonStore, userStore } = useStore();
defineProps({
  isFullscreen: {
    type: Boolean,
    default: false
  },
  userName: {
    type: String,
    default: ""
  },
  photoUrl: {
    type: String,
    default: ""
  },
  messageCount: {
    type: Number,
    default: 0
  }
});
// 多语言处理
const i18nText = computed(() => {
  return {
    systemName: proxy.$t("label.systemName"),
    fullscreen: proxy.$t("tip.fullscreen"),
    exitFullscreen: proxy.$t("tip.exitFullscreen"),
    home: proxy.$t("mainLayout.home"),
    refresh: proxy.$t("mainLayout.refresh"),
    message: proxy.$t("mainLayout.message"),
    logOut: proxy.$t("mainLayout.logOut")
  };
});
/**
 * @description: 选择语言
 * @param language
 */
const selectLanguage = (language: Record<string, any>) => {
  commonStore.language = language;
  proxy.$i18n.locale = language.languageCode;
};
// 注册事件
const emits = defineEmits(["handleClick"]);
/**
 * @description: 抛出事件
 */
const handleClick = (name: string) => {
  emits("handleClick", name);
};
</script>
<style lang="scss">
.main-header-component {
  width: 100%;
  height: 60px;
  line-height: 60px;
  background-color: #ffffff;
  padding: 0 10px;
  box-sizing: border-box;
  .bottom-divider {
    height: 2px;
    width: 100%;
    position: absolute;
    top: 60px;
    left: 0;
    @include select-style();
  }
  .logo {
    float: left;
    width: 48px;
    height: 48px;
    margin: 6px 15px 6px 5px;
    cursor: pointer;
  }
  .system-name {
    float: left;
    font-size: 24px;
    color: $base-color;
    font-weight: bold;
    letter-spacing: 3px;
  }
  .system-version {
    float: left;
    font-size: 14px;
    color: #ff0000;
    margin: 4px 0 0 10px;
    font-weight: bold;
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  .header-right {
    float: right;
    display: flex;
    align-items: center;
    .select-language {
      // 目前还用不到，先隐藏
      display: none;
      margin-right: 30px;
      height: 35px;
      color: #000000;
      font-size: 16px;
      cursor: pointer;
      z-index: 1000;
      &:focus {
        outline: 0;
      }
      i {
        margin-left: 5px;
        color: $border-color;
        font-size: 14px;
      }
    }
    .iconfont {
      margin-right: 15px;
      font-size: 24px;
      color: $base-color;
      cursor: pointer;
      &[class*="-fullscreen"] {
        margin-right: 30px;
        font-weight: bold;
        color: #ff0000;
      }
    }
    .el-badge {
      height: 24px;
      .el-badge__content {
        top: 16px;
        right: 28px;
        padding: 0 3px;
        min-width: 12px;
      }
    }
    .line {
      height: 38px;
      width: 1px;
      margin: 10px 5px;
      background-color: #dddddd;
    }
    .user-info {
      height: 64px;
      margin-left: 5px;
      float: left;
      margin-right: 10px;
      .user-img {
        height: 42px;
        width: 42px;
        margin: 8px;
        border: 1px solid $base-color;
        border-radius: 50px;
      }
      .text {
        font-size: 14px;
        vertical-align: top;
        color: $base-color;
        font-weight: bold;
      }
    }
    .department-switch-selector {
      margin: -8px 0 0 5px;
    }
  }
}
</style>
