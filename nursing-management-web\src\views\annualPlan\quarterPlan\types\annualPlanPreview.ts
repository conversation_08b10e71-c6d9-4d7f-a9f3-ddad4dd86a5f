/*
 * FilePath     : /src/views/annualPlan/quarterPlan/types/annualPlanPreview.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-22 08:41
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-07-03 15:32
 * Description  : 
 * CodeIterationRecord: 
 */

/**
 * @description: 年度计划预览
 */
export interface previewAnnualPlan {
  /**
   * @description: 年度计划ID
   */
  annualPlanMainId: string;
  /**
   * @description: 分类分组
   */
  planTypes: previewPlanType[];
}

/**
 * @description: 分类分组
 */
export interface previewPlanType {
  /**
   * @description: 分类ID
   */
  typeId: number;
  /**
   * @description: 目标分组
   */
  planGoals: previewPlanGoal[];
}

/**
 * @description: 目标分组
 */
export interface previewPlanGoal {
  /**
   * @description: 目标ID
   */
  goalId: number;
  /**
   * @description: 序号
   */
  sort: number;
  /**
   * @description: 计划分组
   */
  planGroups: previewPlanGroup[];
}

/**
 * @description: 计划分组
 */
interface previewPlanGroup {
  /**
   * @description: 负责部门
   */
  responsibleDepartments: string[];
  /**
   * @description: 计划指标
   */
  planIndicators: previewPlanIndicator[];
  /**
   * @description: 计划项目
   */
  projectDetails: previewPlanProject[];
}

/**
 * @description: 计划指标
 */
interface previewPlanIndicator {
  /**
   * @description: 显示名称
   */
  localShowName: string;
  /**
   * @description: 运算符
   */
  operator: string;
  /**
   * @description: 目标值
   */
  referenceValue?: number;
  /**
   * @description: 单位
   */
  unit: string;
  /**
   * @description: 标记ID
   */
  markId: string;
  /**
   * @description: 备注
   */
  remark: string;
  /**
   * @description: 序号
   */
  sort: number
}

/**
 * @description: 计划项目
 */
interface previewPlanProject {
  /**
   * @description: 显示名称
   */
  content: string;
  /**
   * @description: 标记Id
   */
  markId: string;
  /**
   * @description: 序号
   */
  sort: number;
}