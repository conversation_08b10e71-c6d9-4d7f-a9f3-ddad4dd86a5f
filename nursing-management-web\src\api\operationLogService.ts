/*
 * FilePath     : \src\api\operationLogService.ts
 * Author       : 苏军志
 * Date         : 2023-06-04 19:25
 * LastEditors  : 苏军志
 * LastEditTime : 2023-09-16 11:22
 * Description  : 用户操作日志相关Api接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";

export class operationLogService {
  private static saveLogApi: string = "/operationLog/SaveLog";

  /**
   * 保存日志
   * @param params
   * @returns
   */
  public static saveLog(params: any) {
    return http.post(this.saveLogApi, params);
  }
}
