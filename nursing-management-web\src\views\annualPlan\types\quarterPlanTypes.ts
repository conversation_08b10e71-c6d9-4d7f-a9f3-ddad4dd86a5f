/*
 * FilePath     : /src/views/annualPlan/types/quarterPlanTypes.ts
 * Author       : 杨欣欣
 * Date         : 2025-06-27 20:18
 * Description  : 季度计划DTO类型定义（包含Command和Query）
 * CodeIterationRecord: 
 */
import type { annualPrincipal, workType } from "./common";
import type { importWorkDto } from "./importWorkDto";

/**
 * 保存季度计划工作命令 - 季度工作项
 */
interface quarterWork {
  /**
   * 季度计划详情ID
   */
  quarterPlanDetailID: string;
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 参考执行项目ID
   */
  apInterventionID?: number | null;
  /**
   * 工作类型
   */
  workType: workType;
  /**
   * 序号
   */
  sort?: number | null;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作内容
   */
  workContent: string;
  /**
   * 是否临时工作
   */
  isTemp: boolean;
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人详细信息
   */
  principals: annualPrincipal[];
}

/**
 * 保存季度计划工作命令
 */
export interface saveQuarterWorksCommand {
  /**
   * 季度计划主键
   */
  quarterPlanMainID: string;
  /**
   * 工作项列表
   */
  workViews: quarterWork[];
}

/**
 * 导入季度计划工作命令
 */
export interface importQuarterWorksCommand {
  /**
   * 年度计划主表ID
   */
  annualPlanMainID: string;
  /**
   * 季度计划主表ID
   */
  quarterPlanMainID: string;
  /**
   * 年度
   */
  annual: number;
  /**
   * 工作所属季度
   */
  quarter: number;
  /**
   * 科室ID
   */
  departmentID: number;
  /**
   * 工作内容
   */
  workViews: importWorkDto[];
  /**
   * 是否是首次导入
   */
  isFirstImport: boolean;
}

/**
 * 更新季度计划工作命令
 */
export interface updateQuarterPlanWorkCommand {
  /**
   * 主键
   */
  quarterPlanDetailID: string;
  /**
   * 工作内容
   */
  workContent: string;
  /**
   * 要求
   */
  requirement: string;
  /**
   * 工作类别
   */
  workType: workType;
  /**
   * 序号
   */
  sort?: number | undefined;
  /**
   * 负责人名称
   */
  principalName: string;
  /**
   * 负责人集合
   */
  principals: annualPrincipal[];
}

/**
 * 重排序季度计划工作命令
 */
export interface resetQuarterPlanWorksSortCommand {
  /**
   * 季度计划主表ID
   */
  quarterPlanMainID: string;
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 工作ID与新序号的映射
   */
  planWorkIDAndSort: Record<string, number>;
}

/**
 * 查询季度计划状态参数
 */
export interface getQuarterPlanStatusQuery {
  /**
   * 季度计划主键
   */
  quarterPlanMainID: string;
}

/**
 * 查询可导入季度计划工作参数
 */
export interface getCanImportQpWorksQuery {
  /**
   * 年度计划主表ID
   */
  annualPlanMainID: string;
  quarterPlanMainID: string;
  annual: number;
  /**
   * 季度
   */
  quarter: number;
  /**
   * 科室ID
   */
  departmentID: number;
}

/**
 * 查询上级部门季度计划工作参数
 */
export interface getQuarterPlanQuickReferenceVosQuery {
  /**
   * 年度
   */
  annual: number;
  /**
   * 季度
   */
  quarter: number;
  /**
   * 科室ID
   */
  departmentID: number;
  /**
   * 指定执行项目字典ID
   */
  apInterventionID?: number;
}

/**
 * 查询浏览季度计划视图参数
 */
export interface getBrowseQPViewsQuery {
  /**
   * 年度
   */
  year: number;
}

/**
 * 查询季度计划预览参数
 */
export interface getQuarterPlanPreviewQuery {
  /**
   * 季度计划主键
   */
  quarterPlanMainID: string;
}