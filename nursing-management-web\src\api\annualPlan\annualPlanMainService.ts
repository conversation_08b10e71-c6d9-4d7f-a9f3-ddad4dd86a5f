/*
 * FilePath     : \src\api\annualPlanMainService.ts
 * Author       : 杨欣欣
 * Date         : 2023-08-10 17:12
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-08 10:34
 * Description  : 年度计划维护-查询与命令
 * CodeIterationRecord:
 */
import http from "@/utils/http";
import type { annualPlanMaintainView, planIndicator, planProject } from "@/views/annualPlan/types/annualPlanMain";
export class annualPlanMainService {
  private static getBrowseAPViewsApi: string = "/AnnualPlanMain/GetBrowseAPViews";
  private static getBrowseAPDepartmentsApi: string = "/AnnualPlanMain/GetBrowseAPDepartments";
  private static publishAnnualPlanApi: string = "/AnnualPlanMain/PublishAnnualPlan";
  private static getAnnualPlanAttachmentsApi: string = "/AnnualPlanMain/GetAnnualPlanAttachments";
  private static getAnnualPlanMainIDAndStatusApi: string = "/AnnualPlanMain/GetAnnualPlanMainIDAndStatus";
  private static getAnnualPlanMainGoalListApi: string = "/AnnualPlanMain/GetAnnualPlanMainGoalList";
  private static getAnnualPlanApi: string = "/AnnualPlanMain/GetAnnualPlan";
  private static resetAnnualPlanTypesSortApi: string = "/AnnualPlanMain/ResetAnnualPlanTypesSort";
  private static resetAnnualPlanGoalsSortApi: string = "/AnnualPlanMain/ResetAnnualPlanGoalsSort";
  private static getDepartmentOptionsApi: string = "/AnnualPlanMain/GetDepartmentOptions";
  private static saveAnnualPlanGroupApi: string = "/AnnualPlanMain/SaveAnnualPlanGroup";
  private static deleteAnnualGoalGroupApi: string = "/AnnualPlanMain/DeleteAnnualGoalGroup";
  private static getSortedMainGoalIDsByMainIDApi: string = "/AnnualPlanMain/GetSortedMainGoalIDsByMainID";
  private static resetAnnualPlanGroupsSortApi: string = "/AnnualPlanMain/ResetAnnualPlanGroupsSort";
  private static getAPDetailsApi: string = "/AnnualPlanMain/GetAPDetails";
  private static getIndicatorDetailsApi: string = "/AnnualPlanMain/GetIndicatorDetails";
  private static getProjectDetailsByGroupIDApi: string = "/AnnualPlanMain/GetProjectDetailsByGroupID";
  private static getRefIndicatorIDsApi: string = "/AnnualPlanMain/GetRefIndicatorIDs";
  private static getPastYearIndicatorsApi: string = "/AnnualPlanMain/GetPastYearIndicators";
  private static addIndicatorDetailApi: string = "/AnnualPlanMain/AddIndicatorDetail";
  private static updateIndicatorDetailApi: string = "/AnnualPlanMain/UpdateIndicatorDetail";
  private static resetAnnualPlanIndicatorsSortApi: string = "/AnnualPlanMain/ResetAnnualPlanIndicatorsSort";
  private static deleteIndicatorDetailApi: string = "/AnnualPlanMain/DeleteIndicatorDetail";
  private static getProjectDetailsApi: string = "/AnnualPlanMain/GetProjectDetails";
  private static getIndicatorDetailsByGroupIDApi: string = "/AnnualPlanMain/GetIndicatorDetailsByGroupID";
  private static addProjectDetailApi: string = "/AnnualPlanMain/AddProjectDetail";
  private static updateProjectDetailApi: string = "/AnnualPlanMain/UpdateProjectDetail";
  private static resetAnnualPlanProjectsSortApi: string = "/AnnualPlanMain/ResetAnnualPlanProjectsSort";
  private static deleteProjectDetailApi: string = "/AnnualPlanMain/DeleteProjectDetail";
  private static getSuperiorProjectDetailApi: string = "/AnnualPlanMain/GetSuperiorProjectDetail";

  /**
   * @description: 查询本人及上下级已制定的年度计划
   * @param params
   * @return
   */
  public static getBrowseAPViews = (params?: any) => http.get(this.getBrowseAPViewsApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 查询本人部门及上下级部门
   * @param params
   * @return
   */
  public static getBrowseAPDepartments = (params?: any) => http.get(this.getBrowseAPDepartmentsApi, params, { loadingText: Loading.LOAD });
  /**
   * @description: 发布年度计划
   * @param params
   * @return
   */
  public static publishAnnualPlan = (params?: any) => http.get(this.publishAnnualPlanApi, params);
  /**
   * @description: 获取年度计划详情
   * @param params
   * @return
   */
  public static getAnnualPlan = (params?: any) =>
    http.get(this.getAnnualPlanApi, params, { loadingText: Loading.LOAD }) as Promise<annualPlanMaintainView>;
  /**
   * @description: 获取年度计划附件
   * @param params
   * @return
   */
  public static getAnnualPlanAttachments = (params?: any) => http.get(this.getAnnualPlanAttachmentsApi, params) as Promise<Record<string, any>[]>;
  /**
   * @description: 获取年度计划ID与状态
   * @param params
   * @return
   */
  public static getAnnualPlanMainIDAndStatus = (params: {
    departmentID: number;
    year: number;
  }) => http.get(this.getAnnualPlanMainIDAndStatusApi, params) as Promise<{
    item1: string;
    item2: boolean
  }>;
  /**
   * @description: 排序分类
   * @return
   */
  public static resetAnnualPlanTypesSort = (params?: any) => http.post(this.resetAnnualPlanTypesSortApi, params);
  /**
   * @description: 策略目标重排序
   * @return
   */
  public static resetAnnualPlanGoalsSort = (params?: any) => http.post(this.resetAnnualPlanGoalsSortApi, params);
  /**
   * @description: 获取排序后的分类目标表ID集合
   * @param params
   * @return
   */
  public static getSortedMainGoalIDsByMainID = (params?: any) => http.get(this.getSortedMainGoalIDsByMainIDApi, params);

  /**
   * @description: 获取负责部门候选项
   * @param params
   * @return
   */
  public static getDepartmentOptions = (params?: any) => http.get(this.getDepartmentOptionsApi, params) as Promise<string[]>;
  /**
   * @description: 保存分组
   * @param params
   * @return
   */
  public static saveAnnualPlanGroup = (params?: any) => http.post(this.saveAnnualPlanGroupApi, params) as Promise<string>;
  /**
   * @description: 删除目标分组
   * @param params
   * @return
   */
  public static deleteAnnualGoalGroup = (params?: any) => http.get(this.deleteAnnualGoalGroupApi, params);
  /**
   * @description: 获取明细
   * @param params
   * @return
   */
  public static getAPDetails = (params?: any) => http.get(this.getAPDetailsApi, params);
  /**
   * @description: 获取部分指标明细
   * @return
   */
  public static getIndicatorDetails = (params?: any) =>
    http.post(this.getIndicatorDetailsApi, params, { loadingText: Loading.LOAD }) as Promise<planIndicator[]>;
  /**
   * @description: 获取指标明细
   * @param params
   * @return
   */
  public static getIndicatorDetailsByGroupID = (params?: any) => http.get(this.getIndicatorDetailsByGroupIDApi, params);
  /**
   * @description: 获取按组ID分组的指标明细
   * @param params.mainID 主表ID
   * @return
   */
  public static getRefIndicatorIDs = (params?: any) => http.get(this.getRefIndicatorIDsApi, params) as Promise<number[]>;
  /**
   * @description: 获取历年指标
   * @param params
   * @return
   */
  public static getPastYearIndicators = (params?: any) => http.get(this.getPastYearIndicatorsApi, params) as Promise<Record<string, any>[]>;
  /**
   * @description: 新增指标明细
   * @param params
   * @return
   */
  public static addIndicatorDetail = (params?: any) => http.post(this.addIndicatorDetailApi, params) as Promise<planIndicator>;
  /**
   * @description: 删除指标明细
   * @param params
   * @return
   */
  public static deleteIndicatorDetail = (params?: any) => http.get(this.deleteIndicatorDetailApi, params);
  /**
   * @description: 策略指标更新
   * @param params
   * @return
   */
  public static updateIndicatorDetail = (params?: any) => http.post(this.updateIndicatorDetailApi, params);
  /**
   * @description: 策略指标重排序
   * @param params
   * @return
   */
  public static resetAnnualPlanIndicatorsSort = (params?: any) => http.post(this.resetAnnualPlanIndicatorsSortApi, params);

  /**
   * @description: 获取项目明细
   * @param params
   * @return
   */
  public static getProjectDetails = (params?: any) =>
    http.post(this.getProjectDetailsApi, params, { loadingText: Loading.LOAD }) as Promise<planProject[]>;
  /**
   * @description: 获取项目明细
   * @param params
   * @return
   */
  public static getProjectDetailsByGroupID = (params?: any) => http.get(this.getProjectDetailsByGroupIDApi, params);
  /**
   * @description: 新增项目明细
   * @param params
   * @return
   */
  public static addProjectDetail = (params?: any) => http.post(this.addProjectDetailApi, params) as Promise<string>;
  /**
   * @description: 更新项目明细
   * @param params
   * @return
   */
  public static updateProjectDetail = (params?: any) => http.post(this.updateProjectDetailApi, params) as Promise<boolean>;
  /**
   * @description: 项目明细重排序
   * @param params
   * @return
   */
  public static resetAnnualPlanProjectsSort = (params?: any) => http.post(this.resetAnnualPlanProjectsSortApi, params);
  /**
   * @description: 删除项目明细
   * @param params
   * @return
   */
  public static deleteProjectDetail = (params?: any) => http.get(this.deleteProjectDetailApi, params);
  /**
   * @description: 获取年度计划分类目标对应关系
   * @param params
   * @return
   */
  public static getAnnualPlanMainGoalList = (params?: any) =>
    http.get(this.getAnnualPlanMainGoalListApi, params, { loadingText: Loading.LOAD }) as Promise<Record<string, any>[]>;
  /**
   * @description: 获取获取上级部门制定的项目明细
   * @param params
   * @returns
   */
  public static getSuperiorProjectDetail = (params: any) =>
    http.get(this.getSuperiorProjectDetailApi, params, { loadingText: Loading.LOAD }) as Promise<OptionView[]>;
  /**
   * @description: 分组重排序
   * @param params
   * @returns
   */
  public static resetAnnualPlanGroupsSort = (params: any) =>
    http.post(this.resetAnnualPlanGroupsSortApi, params, { loadingText: Loading.LOAD });
}
