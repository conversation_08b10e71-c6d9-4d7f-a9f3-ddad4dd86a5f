<!--
 * FilePath     : \src\views\annualPlan\formulate\components\annualInterventionMaintain.vue
 * Author       : 杨欣欣
 * Date         : 2023-12-17 11:12
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 14:41
 * Description  : 年度计划制定
 * CodeIterationRecord:
 -->
<template>
  <div class="annual-intervention-maintain">
    <el-collapse v-if="interventionsGroupByGoal?.length > 1" v-model="expandPanels" @change="loadInterventionTable">
      <el-collapse-item
        v-for="interventionGroup in interventionsGroupByGoal"
        :title="interventionGroup.sort + '. ' + interventionGroup.mainGoalContent"
        :key="interventionGroup.sort"
        :name="interventionGroup.sort"
      >
        <base-layout class="goal-intervention-table" v-if="expandedPanels[interventionGroup.sort]">
          <template #header>
            <el-button v-if="!planManagementStore.readOnly" type="primary" @click="addRow(interventionGroup.sort)" class="add-button"
              >新增</el-button
            >
          </template>
          <annual-intervention-table
            :ref="'tableRef' + interventionGroup.sort"
            v-model="interventionGroup.interventions"
            :mainGoalID="interventionGroup.mainGoalID"
            :projectDetails="projectDetailsGroupedByMainGoal[interventionGroup.mainGoalID]"
            :annualInterventionList="interventionList"
            :monthMap="monthMap"
            :recommendPrincipalGroups="recommendPrincipalGroups"
          />
        </base-layout>
      </el-collapse-item>
    </el-collapse>
    <annual-intervention-table
      ref="tableRef"
      v-if="interventionsGroupByGoal?.length === 1"
      v-model="interventionsGroupByGoal[0].interventions"
      :mainGoalID="interventionsGroupByGoal[0].mainGoalID"
      :projectDetailID="projectDetailID"
      :annualInterventionList="interventionList"
      :monthMap="monthMap"
      :recommendPrincipalGroups="recommendPrincipalGroups"
    />
  </div>
</template>
<script setup lang="ts">
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanFormulateStore } from "../hooks/useAnnualPlanFormulateStore";

const props = defineProps<{
  projectDetailID?: string;
  mainGoalID?: string;
  interventionList: Record<string, any>[];
}>();
const planManagementStore = usePlanManagementStore();
const { proxy } = getCurrentInstance() as any;
const expandPanels = ref<string[]>([]);
const tableRef = ref<any[] | any>();
const monthMap: Map<number, string> = new Map<number, string>();
let projectDetailsGroupedByMainGoal: Record<string, Record<string, any>[]> = {};
const interventionsGroupByGoal = ref<Record<string, any>[]>([]);
let expandedPanels: Record<string, boolean> = {};
const recommendPrincipalGroups = ref<Record<string, any>[]>([]);

onMounted(async () => {
  await getMonthsSetting();
  if (!props.projectDetailID) {
    getProjectDetails();
  }
  getInterventionsGroupByGoal();
  getUsedInterventionIDs();
  getRecommendPrincipalOptions();
});
//#region 数据获取
/**
 * @description: 月份字典获取
 */
const getMonthsSetting = async () => {
  const params: SettingDictionaryParams = {
    settingType: "Common",
    settingTypeCode: "TimePeriods",
    settingTypeValue: "Monthly"
  };
  await settingDictionaryService.getSettingDictionaryDict(params).then((data: any) => {
    data.reduce((map: Map<number, string>, item: Record<string, any>) => {
      map.set(item.value, item.label);
      return map;
    }, monthMap);
  });
};
/**
 * @description: 获取项目明细
 */
const getProjectDetails = () => {
  const param = {
    mainID: planManagementStore.annualPlanMainID
  };
  annualPlanInterventionService
    .getProjectDetailsGroupedByMainGoal(param)
    .then((res: any) => (projectDetailsGroupedByMainGoal = common.deepFreeze(res)));
};
const { getUsedInterventionIDs } = useAnnualPlanFormulateStore();
/**
 * @description: 获取按目标分组的目标任务
 */
const getInterventionsGroupByGoal = async () => {
  const params = {
    mainID: planManagementStore.annualPlanMainID,
    projectDetailID: props.projectDetailID
  };
  // 目标任务业务列表
  const result =
    props.projectDetailID && props.projectDetailID.startsWith("temp_")
      ? []
      : await annualPlanInterventionService.getAnnualInterventions(params);
  // 目标下没有目标任务，模拟基础对象
  if (!result.length && props.mainGoalID) {
    interventionsGroupByGoal.value = [
      {
        sort: 1,
        mainGoalID: props.mainGoalID,
        mainGoalContent: props.projectDetailID,
        interventions: []
      }
    ];
    return;
  }
  interventionsGroupByGoal.value = result;
  // 初始化已展开的面板
  if (!Object.keys(expandedPanels).length) {
    expandedPanels = interventionsGroupByGoal.value.reduce((accumulator, currentValue) => {
      accumulator[currentValue.sort] = false;
      return accumulator;
    }, {});
  }
};
/**
 * @description: 获取推荐负责人
 */
const getRecommendPrincipalOptions = () => {
  // 获取当前计划已有的负责人选项情况
  annualPlanInterventionService
    .getRecPrincipalOptions({ planMainID: planManagementStore.annualPlanMainID })
    .then((result: any) => (recommendPrincipalGroups.value = result));
};
//#endregion

watch(
  () => planManagementStore.annualPlanMainID,
  () => {
    expandPanels.value = [];
  }
);

/**
 * @description: 新增一行
 * @param sort 计划目标序号
 * @return
 */
const addRow = (sort?: number) => {
  if (sort) {
    const tableInstance = proxy.$refs?.[`tableRef${sort}`]?.[0];
    tableInstance?.addRow();
  } else {
    tableRef.value?.addRow();
  }
};
/**
 * @description: 首次展开时，再渲染表格
 */
const loadInterventionTable = () => {
  expandPanels.value.forEach((panel) => (expandedPanels[panel] = true));
};

/**
 * @description: 获取所有Table的待保存数据
 */
const getSaveInterventions = () => {
  if (interventionsGroupByGoal.value.length > 1) {
    const allTableSaveViews = Object.values(proxy.$refs).map((ref) => (ref as Array<Record<string, any>>)?.[0].getSaveInterventions());
    const saveInterventions = allTableSaveViews.filter((view) => view && view.length).flat();
    return saveInterventions;
  }
  return tableRef.value.getSaveInterventions();
};
defineExpose({
  getInterventionsGroupByGoal,
  getSaveInterventions,
  addRow
});
</script>
<style lang="scss">
.annual-intervention-maintain {
  height: 100%;
  .el-collapse-item__header {
    height: 40px;
    font-size: 16px;
  }
  .el-collapse-item.is-active .el-collapse-item__content {
    padding: 0 8px 4px 8px;
  }
  .base-layout.goal-intervention-table {
    padding: 0;
    .base-header {
      height: auto !important;
      .add-button {
        margin-left: auto;
        margin-top: 0 !important;
      }
    }
  }
}
</style>
