<!--
 * FilePath     : \src\components\recordAndMainLayout.vue
 * Author       : 郭鹏超
 * Date         : 2024-07-11 10:54
 * LastEditors  : 马超
 * LastEditTime : 2024-12-10 10:39
 * Description  :主记录维护记录切换组件
 * CodeIterationRecord:
 -->
<template>
  <div class="record-and-main-layout">
    <base-layout :headerHeight="headerHeight" v-if="switchArr[0]" :class="['layout-record', !switchArr[1] ? 'fullHeight' : '']">
      <template #header>
        <slot name="recordHeader"></slot>
      </template>
      <slot name="recordContent"></slot>
    </base-layout>
    <base-layout headerHeight="45px" class="layout-main" v-if="switchArr[1]">
      <template #header>
        <slot name="mainHeader"></slot>
      </template>
      <slot name="mainContent"></slot>
    </base-layout>
  </div>
</template>

<script lang="ts" setup>
const convertPX: any = inject("convertPX");
const props = defineProps({
  modelValue: {
    type: Array<Boolean>,
    required: true,
    // 验证数组的长度是否为2
    validator: (value: [boolean, boolean]) => {
      return value.length === 2;
    }
  },
  // 维护记录显示时主记录的高度
  recordHiddenHeight: {
    type: Number,
    default: 115
  },
  headerHeight: {
    type: String,
    default: "45px"
  }
});
const emit = defineEmits(["update:modelValue", "change"]);
let switchArr = useVModel(props, "modelValue", emit);
const recordHiddenHeight = computed(() => `${convertPX(props.recordHiddenHeight)}px`);
</script>

<style lang="scss">
.record-and-main-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  .layout-record {
    height: v-bind(recordHiddenHeight);
    transition: height 0.3s;
  }
  .fullHeight {
    height: 100%;
  }

  .layout-main {
    flex: 1;
    transition: height 0.3s;
  }
}
</style>
