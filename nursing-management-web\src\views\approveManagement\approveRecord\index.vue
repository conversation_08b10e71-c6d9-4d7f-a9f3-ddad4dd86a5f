<!--
 * FilePath     : \src\views\approveManagement\approveRecord\index.vue
 * Author       : 张现忠
 * Date         : 2023-12-29 16:22
 * LastEditors  : 马超
 * LastEditTime : 2025-06-23 10:02
 * Description  : 审批主页面
 * CodeIterationRecord:4435-作为IT人员，我需要重构审批画面，以利符合线上使用（PC端）（21）(军志)
 -->
<template>
  <base-layout class="approve-record" :drawerOptions="drawerOptions" headerHeight="auto">
    <template #header>
      <el-radio-group class="approve-record-page-switch" v-model="pageSwitch">
        <el-radio-button value="0">待审批</el-radio-button>
        <el-radio-button value="1">审批记录</el-radio-button>
      </el-radio-group>
      <template v-if="pageSwitch === '1'">
        <span class="select-label">审批日期：</span>
        <el-date-picker
          class="date-picker"
          v-model="selectedStartDate"
          type="date"
          :clearable="true"
          :disabled-date="(val:any)=>pickerOptions(selectedEndDate,val)"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择开始日期"
        ></el-date-picker>
        <span>--</span>
        <el-date-picker
          class="date-picker"
          type="date"
          v-model="selectedEndDate"
          :clearable="true"
          :disabled-date="(val:any)=>pickerOptions(val,selectedStartDate)"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择结束日期"
        ></el-date-picker>
      </template>
    </template>
    <el-radio-group class="approval-category" v-model="proveCategory" @change="refreshPage">
      <el-radio-button v-for="(item, index) in approvalCategoryAndCount" :value="item.value" :key="index">
        {{ item.label }}<span v-if="item.count > 0" class="prove-category-count">&nbsp;({{ item.count }})</span>
      </el-radio-button>
    </el-radio-group>
    <dynamic-table v-model="approveDataList" :headerList="tableHeaderList">
      <template #approveStatusCode="scope">
        <el-tag :type="getApproveStatusTag(scope.row.approveStatusCode)">{{ scope.row.approveStatus }}</el-tag>
      </template>
      <!-- 操作 -->
      <template #operate="scope">
        <el-tooltip content="审批">
          <span class="iconfont icon-approveRecord" @click="startApprove(scope.row)" />
        </el-tooltip>
        <el-tooltip content="撤销审批">
          <span
            class="iconfont icon-revoke"
            v-visibilityHidden="scope.row.approveStatusCode !== '4' && showRevokeFlag"
            @click="startRevoke(scope.row)"
          />
        </el-tooltip>
      </template>
    </dynamic-table>
    <template #drawerContent>
      <transfer-qc-template
        v-if="drawerOptions.drawerName === 'transfer' && approvalJumpDict[proveCategory]"
        :approveCareMainID="sourceID"
      />
      <revoke-approval
        v-if="drawerOptions.drawerName == 'revoke'"
        v-model:drawer-options="drawerOptions"
        :revokeFormData="revokeFormData!"
        :id="currRow!.approveRecordID"
        :isSource="false"
        @refreshData="refreshPage"
      />
      <div v-if="drawerOptions.drawerName === 'approve'" class="approve-drawer-content">
        <!-- 左侧审批详情 -->
        <div class="detail-section">
          <transfer-qc-template
            v-if="approvalJumpDict[proveCategory]"
            :approveCareMainID="currRow?.sourceID"
            @row-change="handleRowChange"
          />
        </div>
        <!-- 右侧审批操作 -->
        <div class="approve-section">
          <approve
            :approveRecordID="approveRecordID"
            :employeeID="userStore.employeeID"
            :approveDecision="approveDecision"
            :approveRecord="currRow"
            @save-approval="saveApprovalData"
          ></approve>
        </div>
      </div>
    </template>
    <template #drawerOtherFooter v-if="drawerOptions.drawerName === 'approve'">
      <template v-if="!hasCompleted">
        <el-button type="danger" @click="approveDecision = '-1'">拒绝</el-button>
        <el-button type="primary" @click="approveDecision = '1'">同意</el-button>
      </template>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import { useApproveRecord } from "./hooks/useApproveRecord";
import type { approveRecordView } from "./types/approveRecordView";
import { useApproveDynamicTable } from "./hooks/useApproveDynamicTable";
import transferQcTemplate from "./components/transferPage/transferQcTemplate.vue";
import revokeApproval from "./components/revokeApproval.vue";
const { getApproveRecordView, saveApproval, getApproveCategoryAndCount } = useApproveRecord();
const { tableHeaderList, getTableHeaderList } = useApproveDynamicTable();
const { getHistoryApproveRecordView } = useApproveRecord();
const { showRevokeFlag } = useApproval();
const { getApproveStatusTag } = useStatusTag();
const { getApprovalJumpSetting } = useDictionaryData();
const saveData = ref<Record<string, any>>({});
const { userStore } = useStore();
// 存储传递给approve组件的审批记录表ID
const approveRecordID = ref<string>("");
const approveDataList = ref<approveRecordView[]>([]);
const approvalCategoryAndCount = ref<Record<string, any>[]>([]);
const approveDecision = ref<string>("");
const hasCompleted = ref<boolean>(false);
const pageSwitch = ref<"0" | "1">("0");
const proveCategory = ref<string>((useRoute().query?.proveCategory as string) || "AA-021");
const approvalJumpDict = ref<Record<string, boolean>>({});
const sourceID = ref<string>("");
const selectedStartDate = ref(datetimeUtil.addDate(Date.now(), -5, "yyyy-MM-dd"));
const selectedEndDate = ref(datetimeUtil.getNowDate("yyyy-MM-dd"));
const currRow = ref<Record<string, any>>();
const revokeFormData = ref<Record<string, any>>();
onMounted(async () => {
  // 获取动态表头 去掉 同步请求await
  getTableHeaderList(proveCategory.value);
  await refreshPage();
  approvalJumpDict.value = await getApprovalJumpSetting();
});
watch(
  () => [pageSwitch.value, selectedStartDate.value, selectedEndDate.value],
  async () => {
    await refreshPage();
  }
);
watch(
  () => proveCategory.value,
  async () => {
    await getTableHeaderList(proveCategory.value);
  }
);
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "审批流程",
  drawerSize: "50%",
  showDrawer: false,
  showCancel: false,
  showConfirm: false
});
/**
 * @description：开关审批弹窗
 * @param flag
 * @returns
 */
const toggleDrawer = (flag: boolean) => {
  drawerOptions.value.showDrawer = flag;
};
/**
 * @description：打开待审批记录前的处理
 * @param row: 待审批记录
 * @returns
 */
const startApprove = async (row: Record<string, any>) => {
  const latestData =
    pageSwitch.value === "1"
      ? await getHistoryApproveRecordView(proveCategory.value, selectedStartDate.value, selectedEndDate.value)
      : await getApproveRecordView(proveCategory.value);
  const currentRow = latestData.find((item: any) => item.approveRecordID === row.approveRecordID);
  if (currentRow) {
    toggleDrawer(false);
    await nextTick();
    approveRecordID.value = currentRow.approveRecordID;
    currRow.value = currentRow;
    hasCompleted.value = Boolean(currentRow.completeDateTime) || currentRow.approveStatusCode === "4";
    drawerOptions.value.drawerTitle = `${currentRow.approveCategoryName ?? ""}审批`;
    drawerOptions.value.drawerName = "approve";
    drawerOptions.value.drawerSize = "80%";
    approveDecision.value = "";
    await nextTick();
    toggleDrawer(true);
  }
};
/**
 * @description：接受保存审批方法回调的结果
 * @param data ：审批结果的回调
 * @returns
 */
const saveApprovalData = async (data: any) => {
  saveData.value = data;
  await saveApproval(toRaw(saveData.value)).then(async (respBool: any) => {
    toggleDrawer(false);
    if (respBool) {
      await refreshPage();
      const latestData =
        pageSwitch.value === "1"
          ? await getHistoryApproveRecordView(proveCategory.value, selectedStartDate.value, selectedEndDate.value)
          : await getApproveRecordView(proveCategory.value);
      if (currRow.value && currRow.value.approveRecordID) {
        const updatedRow = latestData.find((item: any) => item.approveRecordID === currRow.value?.approveRecordID);
        if (updatedRow) {
          currRow.value = updatedRow;
        }
      }
      showMessage("success", "审批完成");
    }
  });
  approveDecision.value = "";
};
/**
 * @description：重新加载页面
 * @returns
 */
const refreshPage = async () => {
  approveDataList.value = [];
  approvalCategoryAndCount.value = await getApproveCategoryAndCount(
    pageSwitch.value === "1",
    selectedStartDate.value,
    selectedEndDate.value
  );
  let data =
    pageSwitch.value === "1"
      ? await getHistoryApproveRecordView(proveCategory.value, selectedStartDate.value, selectedEndDate.value)
      : await getApproveRecordView(proveCategory.value);
  approveDataList.value = data || [];
  toggleDrawer(false);
};
const startRevoke = (row: Record<string, any>) => {
  currRow.value = row;
  revokeFormData.value = [
    {
      label: "审批内容",
      value: row.content
    }
  ];
  drawerOptions.value.drawerName = "revoke";
  drawerOptions.value.drawerSize = "50%";
  toggleDrawer(true);
};
/**
 * @description: 开始时间不可大于结束时间
 * @param {any} dateOne
 * @param {any} dateTwo
 * @returns {boolean}
 */
const pickerOptions = (dateOne: any, dateTwo: any) => {
  if (!dateOne || !dateTwo) {
    return;
  }
  let dateTimeOne = datetimeUtil.formatDate(dateOne, "yyyy-MM-dd");
  let dateTimeTwo = datetimeUtil.formatDate(dateTwo, "yyyy-MM-dd");
  return dateTimeOne <= dateTimeTwo;
};
/**
 * @description: 处理行切换
 * @param row: 审批记录
 * @returns
 */
const handleRowChange = async (row: Record<string, any>) => {
  const currentRecord = approveDataList.value.find((item: any) => item.sourceID === row.hQcMainId);
  if (currentRecord) {
    toggleDrawer(false);
    await nextTick();
    approveRecordID.value = currentRecord.approveRecordID;
    currRow.value = currentRecord;
    hasCompleted.value = Boolean(currentRecord.completeDateTime) || currentRecord.approveStatus === "4";
    drawerOptions.value.drawerTitle = `${currentRecord.approveCategoryName ?? ""}审批`;
    await nextTick();
    toggleDrawer(true);
  }
};
</script>
<style lang="scss">
.approve-record {
  height: 100%;
  width: 100%;
  .date-picker {
    margin: 0 5px;
    width: 200px;
  }
  .select-label {
    vertical-align: middle;
    margin-left: 10px;
  }
  .approval-category {
    margin-bottom: 5px;
    .el-radio-button--small .el-radio-button__inner {
      padding: 10px;
      font-size: 12px;
      border-radius: 0;
    }
    .prove-category-count {
      color: red;
      font-weight: bold;
    }
  }
  .approve-record-page-switch {
    vertical-align: middle;
    margin-left: 10px;
  }
  .approve-drawer-content {
    display: flex;
    height: 100%;
    .detail-section {
      flex: 1;
      padding-right: 20px;
      border-right: 1px solid #eee;
      overflow-y: auto;
    }
    .approve-section {
      width: 400px;
      padding-left: 20px;
      overflow-y: auto;
    }
  }
}
</style>
