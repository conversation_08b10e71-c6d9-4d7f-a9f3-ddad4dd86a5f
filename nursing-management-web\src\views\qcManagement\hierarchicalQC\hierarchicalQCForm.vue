<!--
 * FilePath     : \src\views\qcManagement\hierarchicalQC\hierarchicalQCForm.vue
 * Author       : 苏军志
 * Date         : 2024-03-30 14:50
 * LastEditors  : 苏军志
 * LastEditTime : 2024-10-13 14:22
 * Description  : 质控模板维护
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="hierarchical-qc-form" :drawerOptions="drawerOptions">
    <template #header>
      <el-form label-width="auto" :inline="true" label-suffix="：" class="where-form">
        <el-form-item prop="departmentID" label="适用部门">
          <department-selector v-model="departmentID" label="" clearable :disabled="!getIsAdministrators()" />
        </el-form-item>
        <el-form-item prop="hierarchicalQCFormLevel" label="质控级别">
          <level-selector v-model="qcFormLevel" @change="formType = ''"></level-selector>
        </el-form-item>
        <el-form-item prop="formType" label="质控类别">
          <form-type-selector label="" v-model="formType" :qcLevel="qcFormLevel"></form-type-selector>
        </el-form-item>
      </el-form>
      <el-button @click="editForm()" v-permission:B="1" class="add-button">新增</el-button>
    </template>
    <el-table height="100%" :data="showFormList" stripe border>
      <el-table-column prop="hierarchicalQCFormLevelName" label="质控级别" :width="convertPX(120)" align="center"></el-table-column>
      <el-table-column prop="formTypeName" label="类型" :width="convertPX(210)" align="center"></el-table-column>
      <el-table-column prop="formName" label="模板名称" :min-width="convertPX(300)" align="left"></el-table-column>
      <el-table-column prop="statusCodeName" label="状态" :width="convertPX(80)" align="center">
        <template #default="{ row }">
          <el-tag :type="row.statusCode ? 'success' : 'info'">{{ row.statusCodeName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="addDepartmentName" label="部门" :width="convertPX(180)" align="center"></el-table-column>
      <el-table-column prop="modifyEmployee" label="维护人员" :width="convertPX(120)" align="center"></el-table-column>
      <el-table-column label="维护时间" :width="convertPX(160)" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'dateTime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="convertPX(80)" align="center">
        <template #default="{ row }">
          <el-tooltip content="修改">
            <i v-permission:B="3" class="iconfont icon-edit" @click.stop="editForm(row)"></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i v-permission:B="4" class="iconfont icon-delete" @click.stop="deleteForm(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <form-template :formParams="formParams" :refresh="refresh"> </form-template>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
import type { dynamicFormData } from "zhytech-ui";
import formTemplate from "./components/formTemplate.vue";
import formTypeSelector from "./components/qcFormTypeSelector.vue";
import levelSelector from "./components/qcLevelSelector.vue";
import { useQcCommonMethod } from "./hooks/useQcCommonMethod";
import type { formParam } from "./types/qcFormTemplate";
const { userStore } = useStore();
const { getIsAdministrators } = useQcCommonMethod();
const convertPX: any = inject("convertPX");
const qcFormList = ref<Record<string, any>[]>([]);
const showFormList = ref<Record<string, any>[]>([]);
const qcFormLevel = ref<string>();
const formType = ref<string>();
const departmentID = ref<number>(userStore.departmentID);
onBeforeMount(() => {
  getHierarchicalQCFormList();
});
watch([departmentID, qcFormLevel, formType], () => {
  filterData();
});
/**
 * @description: 获取质控表单清单
 */
const getHierarchicalQCFormList = () => {
  hierarchicalQCService.getHierarchicalQCFormList().then((result: any) => {
    if (result) {
      qcFormList.value = result;
      showFormList.value = result;
      filterData();
    }
  });
};
/**
 * @description: 过滤数据
 */
const filterData = () => {
  showFormList.value = qcFormList.value;
  if (departmentID.value) {
    showFormList.value = showFormList.value.filter((form) => form.addDepartmentID === departmentID.value);
  }
  if (qcFormLevel.value) {
    showFormList.value = showFormList.value.filter((form) => form.hierarchicalQCFormLevel === qcFormLevel.value);
  }
  if (formType.value) {
    showFormList.value = showFormList.value.filter((form) => form.formType === formType.value);
  }
};
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "100%",
  showCancel: false,
  showConfirm: false
});
const formParams = ref<formParam>({} as formParam);
let hierarchicalQCFormID: string | undefined = undefined;
/**
 * @description: 编辑质控模板
 * @param row
 * @return
 */
const editForm = (row?: Record<string, any>) => {
  refresh.value = 0;
  drawerOptions.value.showDrawer = true;
  if (row) {
    hierarchicalQCFormID = row.hierarchicalQCFormID;
    drawerOptions.value.drawerTitle = "修改质控模板";
    formParams.value.formID = row.templateCode;
    formParams.value.formLevel = row.hierarchicalQCFormLevel;
    formParams.value.qcFormType = row.formType;
    formParams.value.formName = row.formName;
    formParams.value.departmentID = row.addDepartmentID;
  } else {
    hierarchicalQCFormID = undefined;
    drawerOptions.value.drawerTitle = "新增质控模板";
    formParams.value = {} as formParam;
  }
  formParams.value.saveFormMethod = saveQCForm;
  formParams.value.formList = qcFormList.value.map((form) => {
    return {
      formID: form.templateCode,
      formName: form.formName,
      formType: form.formType,
      formLevel: form.hierarchicalQCFormLevel,
      addDepartmentID: form.addDepartmentID
    };
  });
};
const refresh = ref<number>(0);
/**
 * @description: 保存质控模板
 * @param saveData 模板数据
 * @return
 */
const saveQCForm = (saveData: dynamicFormData<Record<string, any>>) => {
  const params: Record<string, any> = {
    hierarchicalQCFormLevel: saveData.props.formLevel,
    formType: saveData.props.qcFormType,
    formName: saveData.props.formName,
    addDepartmentID: saveData.props.departmentID,
    formTemplateView: saveData
  };
  if (hierarchicalQCFormID) {
    params.hierarchicalQCFormID = hierarchicalQCFormID;
  }
  hierarchicalQCService.saveQCForm(params).then((res: any) => {
    if (res) {
      showMessage("success", "保存成功！");
      // 如果是新增保存成功关闭弹窗 否则不关闭，只刷新模板
      if (!hierarchicalQCFormID) {
        // 如果是新增保存关闭弹窗，刷新列表
        drawerOptions.value.showDrawer = false;
      } else {
        formParams.value.formLevel = saveData.props.formLevel;
        formParams.value.qcFormType = saveData.props.qcFormType;
        formParams.value.formName = saveData.props.formName;
        formParams.value.departmentID = saveData.props.departmentID;
      }
      // 标记需要刷新
      refresh.value = Math.random();
    }
  });
};
watch(
  () => drawerOptions.value.showDrawer,
  () => {
    // 如果弹窗关闭且有保存操作时
    if (!drawerOptions.value.showDrawer && refresh.value !== 0) {
      getHierarchicalQCFormList();
    }
  }
);
/**
 * @description: 删除质控模板
 * @param row
 * @return
 */
const deleteForm = async (row: Record<string, any>) => {
  deleteConfirm("", (res: boolean) => {
    if (res) {
      hierarchicalQCService.deleteQCForm({ hierarchicalQCFormID: row.hierarchicalQCFormID }).then((respBool: any) => {
        if (respBool) {
          showMessage("success", "删除成功!");
          getHierarchicalQCFormList();
        }
      });
    }
  });
};
</script>
<style lang="scss">
.hierarchical-qc-form {
  height: 100%;
  width: 100%;
  .where-form {
    display: inline-block;
  }
  .el-drawer {
    .el-drawer__header {
      margin-bottom: 0;
    }
    .el-drawer__body {
      padding: 0 0 10px 0;
    }
    .el-drawer__footer {
      padding: 0;
    }
  }
}
</style>
