{
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "cSpell.words": [
    "Avenir",
    "camelcase",
    "cascader",
    "Chao",
    "chemic",
    "Consolas",
    "controler",
    "datas",
    "daterange",
    "docxtemplater",
    "dont",
    "eact",
    "ecommend",
    "endregion",
    "esbenp",
    "Fanout",
    "fileheader",
    "flexble",
    "FONTFACE",
    "Gridlines",
    "iconfont",
    "iemobile",
    "imengyu",
    "Intubator",
    "jszip",
    "mikeburgh",
    "monthrange",
    "msword",
    "numfmt",
    "officedocument",
    "openxmlformats",
    "passcode",
    "pizzip",
    "pxtorem",
    "qrcode",
    "rowspan",
    "rushstack",
    "screenfull",
    "Secondmen",
    "sortablejs",
    "spreadsheetml",
    "stompjs",
    "Triggerable",
    "univer",
    "univerjs",
    "unplugin",
    "unref",
    "vnode",
    "vsicons",
    "vsintellicode",
    "vuedraggable",
    "vueuse",
    "vuex",
    "webe",
    "Wechat",
    "wordprocessingml",
    "Yahei",
    "yzhang",
    "zhytech"
  ],
  // 方法体注释模板
  "fileheader.cursorMode": {
    "description": "",
    "param": "",
    "return": ""
  },
  // 通用配置
  "fileheader.configObj": {
    // 自动添加头部注释黑名单
    "prohibitAutoAdd": ["md"],
    "dateFormat": "YYYY-MM-DD HH:mm", // 默认格式
    "autoAdd": true, // 默认开启,
    "autoAlready": true, // 只有支持的语言才自动生成
    "wideSame": true, // 设置为true开启
    "wideNum": 13, // 字段长度 默认为13
    "functionParamsShape": "no type", // 移除参数外形`{*}`
    "moveCursor": true, // 自动移动光标到Description所在行,
    "NoMatchParams": "no show param and return", // 匹配不到参数和返回值 不显示param那一行
    "language": {
      "vue": {
        "head": "<!--",
        "middle": " * ",
        "end": " -->",
        // 函数自定义注释符号：如果有此配置 会默认使用
        "functionSymbol": {
          "head": "/**", // 统一增加几个*号
          "middle": " * @",
          "end": " */"
        },
        "functionParams": "typescript" // 函数注释使用ts语言的解析逻辑
      },
      "ts/js": {
        "head": "/*",
        "middle": " * ",
        "end": " */",
        // 函数自定义注释符号：如果有此配置 会默认使用
        "functionSymbol": {
          "head": "/**", // 统一增加几个*号
          "middle": " * @",
          "end": " */"
        },
        "functionParams": "typescript" // 函数注释使用ts语言的解析逻辑
      }
    }
  },
  "template-string-converter.validLanguages": ["svelte", "typescript", "javascript", "typescriptreact", "javascriptreact", "vue"],
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    ".eslintrc.cjs": ".prettierrc.*,.eslintrc-auto-import.json",
    ".gitignore": ".eslintignore,.prettierignore",
    "vite.config.ts": "unocss.runtime.js,uno.config.ts,postcss.config.cjs,externalVariable.d.ts,env.d.ts,components.d.ts,auto-imports.d.ts",
  },
  "vue.inlayHints.destructuredProps": true,
  "vue.inlayHints.vBindShorthand": true,
  "vue.inlayHints.missingProps": false,
  "vue.inlayHints.inlineHandlerLeading": false,
}
