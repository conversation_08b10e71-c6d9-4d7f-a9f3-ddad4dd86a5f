<!--
 * FilePath     : \src\views\annualPlan\maintain\components\annualPlanGoals.vue
 * Author       : 杨欣欣
 * Date         : 2024-07-03 19:03
 * LastEditors  : 胡长攀
 * LastEditTime : 2025-05-31 15:20
 * Description  : 年度计划维护-策略目标列表
 * CodeIterationRecord:
 -->
<template>
  <drag
    tag="el-collapse"
    v-model="list"
    :draggableOption="{
      itemKey: 'mainGoalID',
      groupName: 'goal',
      componentData: getComponentData(),
      handle: '.icon-sort',
      ghostClass: 'collapse-item-ghost'
    }"
    :disabled="readOnly"
    @start="foldMainGoals"
    @moved="maintainStore.resetPlanGoalsSort"
    @added="maintainStore.resetPlanGoalsSort"
  >
    <!-- 目标组件 -->
    <template #content="{ element: planGoal, index }">
      <el-collapse-item class="goal-item" :key="index" :name="planGoal.mainGoalID">
        <template #title>
          <i :class="{ 'iconfont icon-sort': !readOnly }"></i>
          策略目标{{ `${planGoal.sort}、${planGoal.goalContent} ${sessionStore.debugMode ? `#${planGoal.mainGoalID}` : ""}` }}
        </template>
        <annual-plan-groups
          v-if="maintainStore.expandedPlanGoalIDs.has(planGoal.mainGoalID)"
          :list="maintainStore.planGroups.filter((group) => group.mainGoalID === planGoal.mainGoalID)"
          :sourcePlanGoal="planGoal"
        />
      </el-collapse-item>
    </template>
  </drag>
</template>
<script setup lang="ts">
import type { planGoal } from "../../types/annualPlanMain";
import { useAnnualPlanMaintainStore } from "../hooks/useAnnualPlanMaintainStore";
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";

const { typeID } = defineProps<{
  typeID: number;
}>();
const list = defineModel<planGoal[]>();
const maintainStore = useAnnualPlanMaintainStore();
const { sessionStore } = useStore();
const { readOnly } = storeToRefs(usePlanManagementStore());

/**
 * @description: 目标折叠面板组件参数配置
 */
const getComponentData = () => {
  // 函数式组件传入参数时，回调函数须以on开头，才会被正确识别为事件监听器
  return {
    "onUpdate:modelValue": (value: string[]) => (maintainStore.expandPlanGoalIDs = value),
    modelValue: maintainStore.expandPlanGoalIDs,
    onChange: (activePlanGoalIDs: string[]) => {
      maintainStore.addUnExpandedPlanGoalIDs(activePlanGoalIDs);
    },
    // eslint-disable-next-line @typescript-eslint/naming-convention
    "data-type-ID": typeID
  };
};
/**
 * @description: 折叠目标
 */
const foldMainGoals = () => {
  maintainStore.expandPlanGoalIDs = [];
};
</script>
<style lang="scss">
.drag {
  display: flex;
  flex-direction: column;
}
.el-collapse.drag {
  border-radius: 0;
  padding-bottom: 4px;
}
.collapse-item-ghost {
  opacity: 0;
}
.el-collapse-item.goal-item {
  .el-collapse-item__header {
    height: 40px;
    font-size: 20px;
    .iconfont.icon-sort {
      cursor: grab;
    }
  }
  .el-collapse-item__wrap .el-collapse-item__content {
    padding: 0 8px 4px 8px;
  }
  .item-title-input {
    width: 500px;
  }
}
.drag-item-view {
  margin: 0 8px;
  overflow: hidden;
  flex-shrink: 0;
}
</style>
