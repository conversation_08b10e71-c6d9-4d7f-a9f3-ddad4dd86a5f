<!--
 * FilePath     : \src\views\employeeManagement\employeeSecondmentMaintenance\index.vue
 * Author       : 来江禹
 * Date         : 2023-11-06 10:29
 * LastEditors  : 苏军志
 * LastEditTime : 2025-07-01 15:24
 * Description  : 人员借调维护画面
 * CodeIterationRecord:
 -->
<template>
  <base-layout class="employee-secondment-record" :drawerOptions="drawerOptions" :headerHeight="'auto'">
    <template #header>
      <el-radio-group class="header-radio-group" v-model="recordsButtonGroup" @change="changeButtonTypeGetSecondment()">
        <el-radio-button value="effective">有效记录</el-radio-button>
        <el-radio-button value="old">历史记录</el-radio-button>
      </el-radio-group>
      <department-selector
        class="header-select"
        v-permission:DL="13"
        v-model="departmentIDs"
        :props="{ expandTrigger: 'hover', multiple: true }"
        :width="convertPX(140)"
        clearable
        @change="filterEmployeeSecondmentList"
      ></department-selector>
      <span v-permission:S="12">
        <span class="header-label">显示全部：</span>
        <el-switch v-model="showAllSwitch" @change="getTableData()" />
      </span>
      <span class="header-label">借调类型：</span>
      <el-select class="search-secondment-type-select" v-model="searchSecondmentType" @change="filterEmployeeSecondmentList" clearable>
        <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <span v-permission:S="12">
        <span class="header-label">人员：</span>
        <el-input
          class="search-employee-input"
          v-model="searchEmployeeName"
          clearable
          @keyup.enter="filterEmployeeSecondmentList"
          @change="filterEmployeeSecondmentList"
          placeholder="请输入姓名或简拼"
        />
      </span>
      <span>
        <span class="header-label">日期：</span>
        <el-date-picker
          class="export-excel-date-picker"
          v-model="employeeSecondmentStartDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="开始日期"
          :disabled-date="(date:Date)=>disabledFilterDate(date,'start')"
          @change="filterEmployeeSecondmentList"
        ></el-date-picker>
      </span>
      <span>
        <span class="header-label">至</span>
        <el-date-picker
          class="export-excel-date-picker"
          v-model="employeeSecondmentEndDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="结束日期"
          :disabled-date="(date:Date)=>disabledFilterDate(date,'end')"
          @change="filterEmployeeSecondmentList"
        ></el-date-picker>
      </span>
      <export-excel class="header-export" :exportExcelOption="exportExcelOption">
        <el-button class="print-button" @click="createExportExcelParam"> 导出数据 </el-button>
      </export-excel>
      <el-button v-permission:B="1" class="add-button" @click="addTableData()">新增</el-button>
    </template>
    <el-table :data="showEmployeeSecondmentList" stripe border height="100%">
      <el-table-column prop="departmentName" :min-width="convertPX(160)" label="部门" />
      <el-table-column prop="employeeName" :width="convertPX(100)" label="姓名" />
      <el-table-column prop="secondmentDepartmentName" :min-width="convertPX(160)" label="借调部门" />
      <el-table-column prop="secondmentPurposeName" :width="convertPX(80)" label="借调目的" align="center" />
      <el-table-column prop="secondmentTypeName" :width="convertPX(100)" label="借调类型" align="center" />
      <el-table-column :min-width="convertPX(120)" label="开始日期" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.startDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="startNoonName" :width="convertPX(80)" label="开始午别" align="center" />
      <el-table-column :min-width="convertPX(120)" label="结束日期" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.endDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="endNoonName" :width="convertPX(80)" label="结束午别" align="center" />
      <el-table-column :min-width="convertPX(120)" label="实际结束日期" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.actualEndDate, type: 'date', format: 'yyyy-MM-dd' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="actualEndNoonName" :width="convertPX(80)" label="实际结束午别" align="center" />
      <el-table-column prop="secondmentDays" :width="convertPX(80)" label="借调天数"></el-table-column>
      <el-table-column label="状态" align="center" :min-width="convertPX(100)">
        <template v-slot="scope">
          <el-tag v-if="scope.row.status" :type="getApproveStatusTag(scope.row.statusCode)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" :min-width="convertPX(155)">
        <template v-slot="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <el-table-column prop="approveEmployeeName" :min-width="convertPX(130)" label="审批人" align="center" />
      <el-table-column :min-width="convertPX(145)" label="操作" align="center">
        <template #default="scope">
          <el-tooltip content="编辑" v-if="scope.row.statusCode < 1">
            <i v-permission:B="3" class="iconfont icon-edit" @click="editData(scope.row)"></i>
          </el-tooltip>
          <el-tooltip content="提前结束借调">
            <i v-permission:B="17" class="iconfont icon-stop" @click="editData(scope.row, true)"></i>
          </el-tooltip>
          <el-tooltip content="延迟借调">
            <i v-permission:B="28" class="iconfont icon-delay" @click="editData(scope.row, true, true)"></i>
          </el-tooltip>
          <el-tooltip content="删除" v-if="scope.row.statusCode < 1">
            <i v-permission:B="4" class="iconfont icon-delete" @click="deleteData(scope.row, scope.$index)"></i>
          </el-tooltip>
          <el-tooltip content="提交审批">
            <i
              v-permission:B="3"
              v-visibilityHidden="!scope.row.approveFlag"
              @click="manualSubmissionApprove('EmployeeSecondmentMaintenance', scope.row.employeeSecondmentRecordID, getTableData)"
              class="iconfont icon-save"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <template #drawerContent>
      <el-form
        v-if="drawerOptions.drawerName == 'ModifyEndDate'"
        ref="submitRefs"
        label-width="auto"
        :model="drawerData"
        class="drawer-form-style"
      >
        <el-form-item :label="prolongEndDateFlag ? '延迟结束日期：' : '提前结束日期：'" prop="actualEndDate">
          <el-date-picker
            v-model="drawerData.actualEndDate"
            class="form-select"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledActualEndDate"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item :label="prolongEndDateFlag ? '延迟结束午别：' : '提前结束午别：'" prop="actualEndNoon">
          <noon-selector :width="200" v-model="drawerData.actualEndNoon" label=""></noon-selector>
        </el-form-item>
      </el-form>
      <el-form v-else ref="submitRefs" label-width="auto" :model="drawerData" class="drawer-form-style" :rules="rules">
        <el-form-item label="部门">
          <department-selector v-model="drawerData.departmentID" :label="''"></department-selector>
        </el-form-item>
        <el-form-item label="姓名" prop="employeeID">
          <employee-selector label="" v-model="drawerData.employeeID" :departmentID="drawerData.departmentID" />
        </el-form-item>
        <el-form-item label="借调部门" prop="secondmentDepartmentID">
          <department-selector
            v-model="drawerData.secondmentDepartmentID"
            :clearable="true"
            :label="''"
            @change="getSecondmentDepartmentID"
          ></department-selector>
        </el-form-item>
        <el-form-item label="借调目的" prop="secondmentPurpose">
          <el-select v-model="drawerData.secondmentPurpose" clearable class="form-select">
            <el-option v-for="(item, index) in purposeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="借调类型" prop="secondmentType">
          <el-select v-model="drawerData.secondmentType" clearable class="form-select">
            <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期：" prop="startDate">
          <el-date-picker
            class="form-select"
            v-model="drawerData.startDate"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="(time:Date)=>disabledDate(time,'start')"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="开始午别：" prop="startNoon">
          <noon-selector :width="200" v-model="drawerData.startNoon" label=""></noon-selector>
        </el-form-item>
        <el-form-item label="结束日期：" prop="endDate">
          <el-date-picker
            v-model="drawerData.endDate"
            class="form-select"
            type="date"
            value-format="YYYY-MM-DD"
            :disabled-date="(time:Date)=>disabledDate(time,'end')"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束午别：" prop="endNoon">
          <noon-selector v-model="drawerData.endNoon" label="" :width="200"></noon-selector>
        </el-form-item>
        <el-form-item label="借调天数：" prop="days">
          <span v-if="!!days">{{ days }} 天</span>
        </el-form-item>
      </el-form>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
// #region 引入type
import type { record } from "./types/employeeSecondment";
// #endregion

// #region 定义变量
const { proxy } = getCurrentInstance() as any;
const convertPX: any = inject("convertPX");
let { userStore } = useStore();
const { getApproveStatusTag } = useStatusTag();
const departmentIDs = ref<number | number[]>(userStore.departmentID);
const showAllSwitch = ref<Boolean>(true);
const effectiveRecordSwitch = ref<Boolean>(true);
const recordsButtonGroup = ref<string>("effective");
const searchSecondmentType = ref<string>("");
const searchEmployeeName = ref<string>("");
// 表格数据
const employeeSecondmentList = ref<Record<string, any>[]>([]);
const showEmployeeSecondmentList = ref<Record<string, any>[]>([]);
const prolongEndDateFlag = ref<boolean>(false);
const { manualSubmissionApprove } = useApproval();
let drawerOptions = ref<DrawerOptions>({
  drawerTitle: "借调记录",
  showDrawer: false,
  drawerSize: "40%",
  confirm: async () => {
    let { validateRule } = useForm();
    if (!(await validateRule(submitRefs))) {
      return;
    }
    saveData(drawerData.value);
  }
});
const drawerData = ref<record>({});
const days = computed(() => {
  if (!drawerData.value) {
    return;
  }
  return totalDays(drawerData.value);
});
const submitRefs = ref({}) as any;
const rules = reactive({
  employeeID: [
    {
      required: true,
      message: "请选择借调人员",
      trigger: "change"
    }
  ],
  secondmentDepartmentID: [
    {
      required: true,
      message: "请选择借调部门",
      trigger: "change"
    }
  ],
  secondmentPurpose: [
    {
      required: true,
      message: "请选择借调目的",
      trigger: "change"
    }
  ],
  secondmentType: [
    {
      required: true,
      message: "请选择借调类型",
      trigger: "change"
    }
  ],
  startDate: [
    {
      required: true,
      message: "请选择开始日期",
      trigger: "change"
    }
  ],
  startNoon: [
    {
      required: true,
      message: "请选择开始午别",
      trigger: "change"
    }
  ],
  endDate: [
    {
      required: true,
      message: "请选择结束日期",
      trigger: "change"
    }
  ],
  endNoon: [
    {
      required: true,
      message: "请选择结束午别",
      trigger: "change"
    }
  ]
});
// 导出的Excel参数
const exportExcelOption = ref<ExportExcelView[]>([]);
// 查询开始日期 默认为当前日期秋安
const employeeSecondmentStartDate = ref<string>(datetimeUtil.getMonthFirstDay());
// 查询结束日期
const employeeSecondmentEndDate = ref<string>(datetimeUtil.getMonthLastDay());
// 导出Excel列配置
const exportExcelColumns = reactive({
  departmentName: "部门",
  employeeName: "姓名",
  secondmentDepartmentName: "借调部门",
  secondmentPurposeName: "借调目的",
  secondmentTypeName: "借调类型",
  startDate: "开始日期",
  startNoonName: "开始午别",
  endDate: "结束日期",
  actualEndDate: "实际结束日期",
  endNoonName: "结束午别",
  secondmentDays: "借调天数",
  status: "状态",
  addDateTime: "申请时间",
  actualEndNoonName: "实际结束午别",
  approveEmployeeName: "审批人"
});
// #endregion
// #region 初始化
onMounted(() => {
  getTableData();
});
// #endregion

// #region 业务方法(增删改查)
/**
 * @description: 获取表格数据
 */
const getTableData = () => {
  let param = {
    employeeID: showAllSwitch.value ? "" : userStore.employeeID,
    effectiveRecordSwitch: effectiveRecordSwitch.value
  };
  employeeService.getEmployeeSecondmentRecordList(param).then((res: any) => {
    if (res) {
      employeeSecondmentList.value = res;
      filterEmployeeSecondmentList();
    }
  });
};
/**
 * @description: 新增方法
 */
const addTableData = () => {
  drawerOptions.value.drawerTitle = "新增借调记录";
  drawerOptions.value.showDrawer = true;
  drawerData.value.departmentID = userStore.departmentID;
  drawerData.value.employeeID = "";
  drawerData.value.secondmentDepartmentID = undefined;
  drawerData.value.secondmentPurpose = "";
  drawerData.value.secondmentType = "";
  drawerData.value.startDate = undefined;
  drawerData.value.endDate = undefined;
  drawerData.value.startNoon = "";
  drawerData.value.endNoon = "";
  drawerData.value.employeeSecondmentRecordID = undefined;
  drawerOptions.value.drawerName = "AddRecord";
};
/**
 * @description: 删除数据
 * @param row
 * @param index
 * @return
 */
const deleteData = (row: record, index: number) => {
  if (!row.employeeSecondmentRecordID) {
    employeeSecondmentList.value.splice(index, 1);
    return;
  }
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let param = {
      employeeSecondmentRecordID: row.employeeSecondmentRecordID
    };
    employeeService.deleteEmployeeSecondmentRecord(param).then((res: any) => {
      if (res) {
        showMessage("success", "删除成功！");
        getTableData();
        showAllSwitch.value = true;
      }
    });
  });
};
/**
 * @description: 编辑数据
 * @param row
 * @return
 */
const editData = (row: record, modifyEndDateFlag?: Boolean, prolongFlag?: boolean) => {
  drawerOptions.value.drawerTitle = "编辑借调记录";
  drawerOptions.value.drawerName = "ModifyRecord";
  if (modifyEndDateFlag) {
    drawerOptions.value.drawerTitle = prolongFlag ? "延迟借调" : "提前结束借调";
    drawerOptions.value.drawerName = "ModifyEndDate";
    drawerData.value.actualEndDate = row.actualEndDate;
    drawerData.value.actualEndNoon = row.actualEndNoon;
    prolongEndDateFlag.value = prolongFlag ?? false;
  }
  drawerOptions.value.showDrawer = true;
  drawerData.value.departmentID = row.departmentID;
  drawerData.value.employeeID = row.employeeID;
  drawerData.value.secondmentDepartmentID = row.secondmentDepartmentID;
  drawerData.value.secondmentPurpose = row.secondmentPurpose;
  drawerData.value.secondmentType = row.secondmentType;
  drawerData.value.startDate = row.startDate;
  drawerData.value.startNoon = row.startNoon;
  drawerData.value.endDate = row.endDate;
  drawerData.value.endNoon = row.endNoon;
  drawerData.value.employeeSecondmentRecordID = row.employeeSecondmentRecordID;
  nextTick(() => {
    proxy.$refs.submitRefs.validateField();
  });
};
import { useUtils } from "../../../hooks/useUtils";
const { showAlert } = useUtils();
/**
 * @description: 保存数据
 * @param row
 * @return
 */
const saveData = (row: record) => {
  if (!saveCheckData(row)) {
    return;
  }
  let params = {
    employeeID: row.employeeID,
    departmentID: row.departmentID,
    secondmentDepartmentID: row.secondmentDepartmentID,
    startDate: row.startDate,
    endDate: row.endDate,
    employeeSecondmentRecordID: row.employeeSecondmentRecordID,
    secondmentPurpose: row.secondmentPurpose,
    secondmentType: row.secondmentType,
    startNoon: row.startNoon,
    endNoon: row.endNoon,
    secondmentDays: days.value,
    actualEndDate: row.actualEndDate,
    actualEndNoon: row.actualEndNoon,
    earlyClosureFlag: !prolongEndDateFlag.value
  };
  employeeService.saveEmployeeSecondmentRecord(params).then(async (res: any) => {
    showAllSwitch.value = true;
    if (!res.recordSaveFlag) {
      res.responseMessage ? showMessage("warning", res.responseMessage) : showMessage("warning", "保存失败！");
    } else if (!res.approveSaveFlag) {
      showAlert("warning", "审批流程未配置，请联系护士长或护理部！", "审批失败", "确定");
    } else {
      showMessage("success", "保存成功！");
    }
    drawerOptions.value.showDrawer = false;
    recordsButtonGroup.value = "effective";
    changeButtonTypeGetSecondment();
  });
};
/**
 * @description: 检核保存数据
 * @param row
 * @return
 */
const saveCheckData = (row: record) => {
  if (!row.employeeID) {
    showMessage("warning", "姓名不可为空！");
    return false;
  }
  if (!row.departmentID) {
    showMessage("warning", "请选择部门！");
    return false;
  }
  if (!row.secondmentDepartmentID) {
    showMessage("warning", "请选择借调部门！");
    return false;
  }
  if (!row.startDate) {
    showMessage("warning", "请选择开始时间！");
    return false;
  }
  if (!row.endDate) {
    showMessage("warning", "请选择结束时间！");
    return false;
  }
  if (row.departmentID === row.secondmentDepartmentID) {
    showMessage("warning", "原部门和借调部门不可以相同！");
    return false;
  }
  return true;
};
/**
 * @description: 根据开始时间禁止选择开始日期之前的日期
 * @param time
 * @return
 */
const disabledDate = (time: Date, type: string) => {
  const date = datetimeUtil.formatDate(time, "yyyy-MM-dd");
  if (type === "start") {
    if (!drawerData.value.endDate) {
      return false;
    }
    return date > datetimeUtil.formatDate(drawerData.value.endDate, "yyyy-MM-dd");
  }
  if (type === "end") {
    if (!drawerData.value.startDate) {
      return false;
    }
    return date < datetimeUtil.formatDate(drawerData.value.startDate, "yyyy-MM-dd");
  }
  return false;
};
/**
 * @description: 计算借调天数
 * @param row
 * @return
 */
const totalDays = (row: record) => {
  return datetimeUtil.getDateNoonDays(row);
};
/**
 * @description: 获取借调目的配置
 */
let purposeOptions = ref<Array<Record<any, any>>>([]);
const purposeParams: SettingDictionaryParams = {
  settingType: "ShiftManagement",
  settingTypeCode: "EmployeeSecondmentRecord",
  settingTypeValue: "SecondmentPurpose"
};
settingDictionaryService.getSettingDictionaryDict(purposeParams).then((datas: any) => {
  purposeOptions.value = datas;
});
/**
 * @description: 获取借调类型配置
 */
let typeOptions = ref<Array<Record<any, any>>>([]);
const typeParams: SettingDictionaryParams = {
  settingType: "ShiftManagement",
  settingTypeCode: "EmployeeSecondmentRecord",
  settingTypeValue: "SecondmentType"
};
settingDictionaryService.getSettingDictionaryDict(typeParams).then((datas: any) => {
  typeOptions.value = datas;
});
/**
 * @description: 抽屉部门下拉框change事件
 */
const getSecondmentDepartmentID = () => {
  nextTick(() => {
    proxy.$refs.submitRefs.validateField();
  });
};
/**
 * @description: 过滤数据
 */
const filterEmployeeSecondmentList = () => {
  if (!searchSecondmentType.value) {
    showEmployeeSecondmentList.value = employeeSecondmentList.value;
  } else {
    showEmployeeSecondmentList.value = employeeSecondmentList.value.filter((item) => {
      return item.secondmentType === searchSecondmentType.value;
    });
  }
  if (departmentIDs.value) {
    showEmployeeSecondmentList.value = showEmployeeSecondmentList.value.filter((item) => {
      if (typeof departmentIDs.value === "number") {
        return item.departmentID === departmentIDs.value || item.secondmentDepartmentID === departmentIDs.value;
      }
      if (Array.isArray(departmentIDs.value)) {
        return departmentIDs.value.includes(item.departmentID) || departmentIDs.value.includes(item.secondmentDepartmentID);
      }
    });
  }
  if (searchEmployeeName.value) {
    showEmployeeSecondmentList.value = showEmployeeSecondmentList.value.filter((item) => {
      return item.employeeName.includes(searchEmployeeName.value) || item.namePinyin.includes(searchEmployeeName.value);
    });
  }
  // 日期筛选
  if (employeeSecondmentStartDate.value || employeeSecondmentEndDate.value) {
    let result: record[] = [];
    showEmployeeSecondmentList.value.forEach((item) => {
      const startDateCondition = employeeSecondmentStartDate.value
        ? item.actualEndDate
          ? employeeSecondmentStartDate.value! <= datetimeUtil.formatDate(item.actualEndDate, "yyyy-MM-dd")
          : employeeSecondmentStartDate.value! <= datetimeUtil.formatDate(item.endDate, "yyyy-MM-dd")
        : true;
      const endDateCondition = employeeSecondmentEndDate.value
        ? employeeSecondmentEndDate.value! >= datetimeUtil.formatDate(item.startDate, "yyyy-MM-dd")
        : true;
      if (startDateCondition && endDateCondition) {
        result.push(item);
      }
    });
    showEmployeeSecondmentList.value = result;
  }
};
/**
 * @description: 切换按钮获取借调记录
 */
const changeButtonTypeGetSecondment = () => {
  if (recordsButtonGroup.value === "effective") {
    effectiveRecordSwitch.value = true;
    getTableData();
    return;
  }
  effectiveRecordSwitch.value = false;
  getTableData();
};
/**
 * @description: 根据获取实际结束日期的禁用选择范围
 * @param time
 * @return
 */
const disabledActualEndDate = (time: Date) => {
  if (!drawerData.value.endDate) {
    return false;
  }
  if (!prolongEndDateFlag.value && drawerData.value.startDate && !drawerData.value.actualEndDate) {
    return (
      datetimeUtil.formatDate(time, "yyyy-MM-dd") < datetimeUtil.formatDate(drawerData.value.startDate, "yyyy-MM-dd") ||
      datetimeUtil.formatDate(time, "yyyy-MM-dd") > datetimeUtil.formatDate(drawerData.value.endDate, "yyyy-MM-dd")
    );
  }
  if (!prolongEndDateFlag.value && drawerData.value.startDate && drawerData.value.actualEndDate) {
    return (
      datetimeUtil.formatDate(time, "yyyy-MM-dd") < datetimeUtil.formatDate(drawerData.value.startDate, "yyyy-MM-dd") ||
      datetimeUtil.formatDate(time, "yyyy-MM-dd") > datetimeUtil.formatDate(drawerData.value.actualEndDate, "yyyy-MM-dd")
    );
  }
  if (prolongEndDateFlag.value && drawerData.value.actualEndDate) {
    return datetimeUtil.formatDate(time, "yyyy-MM-dd") < datetimeUtil.formatDate(drawerData.value.actualEndDate, "yyyy-MM-dd");
  }
  return datetimeUtil.formatDate(time, "yyyy-MM-dd") < datetimeUtil.formatDate(drawerData.value.endDate, "yyyy-MM-dd");
};
/**
 * @description: 开始结束日期禁用函数
 */
const disabledFilterDate = (date: Date, type: string) => {
  if (type === "start" && employeeSecondmentEndDate.value) {
    return datetimeUtil.formatDate(date, "yyyy-MM-dd") > datetimeUtil.formatDate(employeeSecondmentEndDate.value, "yyyy-MM-dd");
  }
  if (type === "end" && employeeSecondmentStartDate.value) {
    return datetimeUtil.formatDate(date, "yyyy-MM-dd") < datetimeUtil.formatDate(employeeSecondmentStartDate.value, "yyyy-MM-dd");
  }
};
/**
 * @description: 创建导出Excel参数
 */
const createExportExcelParam = () => {
  exportExcelOption.value = [];
  let cloneData = common.clone(showEmployeeSecondmentList.value);
  cloneData.forEach((item: any) => {
    if (item.actualEndDate > employeeSecondmentEndDate.value) {
      item.actualEndNoon = "2";
    }
    calculateSecondmentDays(item, employeeSecondmentStartDate.value, employeeSecondmentEndDate.value);
  });
  exportExcelOption.value.push({
    buttonName: "导出数据",
    fileName: "借调记录",
    sheetName: "借调记录",
    columnData: exportExcelColumns,
    tableData: cloneData
  });
};
/**
 * @description: 计算借调天数
 */
const calculateSecondmentDays = (item: any, startDate: string, endDate: string) => {
  const filterStartDate = startDate < item.startDate ? item.startDate : startDate;
  const filterEndDate = endDate > (item.actualEndDate || item.endDate) ? item.actualEndDate || item.endDate : endDate;
  item.secondmentDays = datetimeUtil.getDateNoonDays({
    startDate: filterStartDate,
    endDate: filterEndDate,
    startNoon: item.startNoon,
    endNoon: item.actualEndNoon
  });
};

// #endregion
</script>
<style lang="scss">
.employee-secondment-record {
  .header-label {
    margin: 0px 10px 0px 10px;
  }
  .header-radio-group {
    margin-left: 10px;
    .el-radio-button {
      display: inline-flex;
    }
  }
  .header-select {
    margin-left: 10px;
  }
  .search-employee-input {
    width: 180px;
  }
  .search-secondment-type-select {
    width: 130px;
  }
  .secondment-date-picker {
    width: 180px;
  }
  .export-excel-date-picker {
    width: 180px;
  }
  .drawer-form-style {
    .form-select {
      width: 200px;
    }
  }
  .header-export {
    float: right;
  }
  .add-button {
    margin-top: 13px !important;
  }
}
</style>
