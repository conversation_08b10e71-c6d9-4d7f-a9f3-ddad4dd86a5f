<!--
 * FilePath     : \src\views\developing.vue
 * Author       : 苏军志
 * Date         : 2023-08-31 11:37
 * LastEditors  : 苏军志
 * LastEditTime : 2023-10-31 15:33
 * Description  : 404
 * CodeIterationRecord:
-->
<template>
  <div class="developing">
    <el-tooltip content="返回上一页">
      <i class="iconfont icon-back" @click="router.go(-1)"></i>
    </el-tooltip>
    <el-empty description="开发中……" />
  </div>
</template>
<script setup lang="ts">
const router = useRouter();
</script>
<style lang="scss">
.developing {
  font-size: 12px;
  height: 100%;
  overflow: hidden;
  .icon-back {
    float: right;
    font-size: 50px;
    margin: 10px 30px 0 0;
  }
  .el-empty {
    margin-top: 10%;
    .el-empty__description {
      > p {
        color: #ff0000;
        font-weight: bold;
      }
    }
  }
}
</style>
