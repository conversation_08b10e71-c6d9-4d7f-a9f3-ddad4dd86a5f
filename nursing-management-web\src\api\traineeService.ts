/*
 * FilePath     : \src\api\traineeService.ts
 * Author       : 张现忠
 * Date         : 2024-09-20 11:34
 * LastEditors  : 胡长攀
 * LastEditTime : 2024-12-16 17:02
 * Description  : 培训学员管理service
 * CodeIterationRecord:
 */
import qs from "qs";
export class traineeService {
  private static getTraineeListApi: string = "TrainingLearner/GetTraineeList";
  private static updateTraineeRecordApi: string = "TrainingLearner/UpdateTraineeRecord";
  private static deleteTraineeApi: string = "TrainingLearner/DeleteTrainee";
  private static setTraineeMonitorApi: string = "TrainingLearner/SetTraineeMonitor";
  private static getTrainTimelineApi: string = "TrainingLearner/GetTrainTimeline";
  // 保存课程满意度和课程建议
  private static saveCourseRecommendationsApi: string = "TrainingLearner/SaveCourseRecommendations";

  /**
   * @description: 获取学员信息列表
   * @param params 查询条件
   * @returns
   */
  public static getTraineeList(params?: any) {
    return http.get(this.getTraineeListApi, params, {
      loadingText: Loading.LOAD,
      paramsSerializer: (param: any) => {
        return qs.stringify(param, { arrayFormat: "repeat" });
      }
    });
  }
  /**
   * @description: 更新学员信息
   * @param params
   * @returns
   */
  public static updateTraineeRecord(params: any) {
    return http.post(this.updateTraineeRecordApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 删除学员记录
   * @param params 学员ID等信息
   * @returns 删除完成的状态
   */
  public static deleteTrainee(params: any) {
    return http.post(this.deleteTraineeApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * @description: 设置班长
   * @param params
   * @returns
   */
  public static setTraineeMonitor(params: any) {
    return http.post(this.setTraineeMonitorApi, params, { loadingText: Loading.SAVE });
  }
  /**
   * @description: 获取本人培训时间线
   * @param params
   * @return
   */
  public static getTrainTimeline(params: any) {
    return http.post(this.getTrainTimelineApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 保存课程满意度和课程建议
   * @param params
   * @return
   */
  public static async saveCourseRecommendations(params: any): Promise<any> {
    return await http.post(this.saveCourseRecommendationsApi, params, { loadingText: Loading.SAVE });
  }
}
