/*
 * FilePath     : \nursing-management-web\src\api\fileService.ts
 * Author       : 苏军志
 * Date         : 2024-01-05 14:33
 * LastEditors  : 张现忠
 * LastEditTime : 2024-10-21 09:52
 * Description  : 文件相关接口
 * CodeIterationRecord:
 */

import http from "@/utils/http";
import qs from "qs";
export class fileService {
  private static getFileListByClassAndSourceApi: string = "/file/GetFileListByClassAndSource";
  private static deleteFileApi: string = "/file/DeleteFile";
  private static getFileUrlApi: string = "/file/GetFileUrl";
  private static uploadFileApi: string = "/file/UploadFile";
  private static uploadRichTextFileApi: string = "/file/UploadRichTextFile";
  /**
   * @description: 根据文件分类与文件来源获取文件集合
   * @param params {sourceID fileClass}
   * @return
   */
  public static getFileListByClassAndSource(params: any) {
    return http.get(this.getFileListByClassAndSourceApi, params, { loadingText: Loading.LOAD });
  }
  /**
   * @description: 删除文件
   * @param params
   * @return
   */
  public static async deleteFile(params: any) {
    return http.post(this.deleteFileApi, qs.stringify(params), { loadingText: Loading.DELETE });
  }
  /**
   * 上传文件
   * @param params
   * @returns
   */
  public static async uploadFile(params: any) {
    return http.post(this.uploadFileApi, params, { contentType: "multipart/form-data" });
  }
  /**
   * @description: 上传富文本组件中相关文件信息（image、file、video）
   * @param params
   * @return 
   */
  public static async uploadRichTextFile(params: any) {
    return http.post(this.uploadRichTextFileApi, params, { contentType: "multipart/form-data" });
  }
}
