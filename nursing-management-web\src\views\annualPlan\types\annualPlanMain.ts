/*
 * FilePath     : \src\views\annualPlan\types\annualPlanMain.ts
 * Author       : 杨欣欣
 * Date         : 2024-03-27 08:39
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-04-27 08:07
 * Description  : 年度计划数据结构定义
 * CodeIterationRecord: 
 */

/**
 * 年度计划维护视图
 */
export interface annualPlanMaintainView {
  /**
   * 计划类型列表
   */
  planTypes: planType[];
  /**
   * 按类型ID分组的目标集合
   */
  goalsByTypeID: {
    [typeID: number]: planGoal[];
  };
  /**
   * 计划分组列表
   */
  planGroups: planGroup[];
  /**
   * 按分组ID分组的指标集合
   */
  indicatorsByGroupID: {
    [groupID: string]: planIndicator[];
  };
  /**
   * 按分组ID分组的项目集合
   */
  projectsByGroupID: {
    [groupID: string]: planProject[];
  };
}
/**
 * 年度计划分类
 */
export interface planType {
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 分类名称
   */
  typeContent: string;
}
/**
 * 年度计划目标
 */
export interface planGoal {
  /**
   * 目标ID
   */
  mainGoalID: string;
  /**
   * 分类ID
   */
  typeID: number;
  /**
   * 目标ID
   */
  goalID: number;
  /**
   * 目标名称
   */
  goalContent: string;
  /**
   * 排序号
   */
  sort: number;
}
/**
 * 年度计划分组
 */
export interface planGroup {
  /**
   * 主键ID
   */
  mainID: string;
  /**
   * 分组ID
   */
  groupID: string;
  /**
   * 主目标ID
   */
  mainGoalID: string;
  /**
   * 负责部门列表
   */
  responsibleDepartments: string[];
  /**
   * 排序号
   */
  sort: number;
}

// 年度计划明细类型，指标 | 项目
export type detailType = "indicator" | "project";
/**
 * 年度计划指标
 */
export interface planIndicator {
  /**
   * 主键ID
   */
  mainID: string;
  /**
   * 详情ID
   */
  detailID: string;
  /**
   * 主目标ID
   */
  mainGoalID: string;
  /**
   * 分组ID
   */
  groupID: string;
  /**
   * 年份
   */
  year: number;
  /**
   * 年度指标ID
   */
  annualIndicatorID: number;
  /**
   * 本地显示名称
   */
  localShowName: string;
  /**
   * 操作符
   */
  operator: string;
  /**
   * 参考值
   */
  referenceValue: number;
  /**
   * 单位
   */
  unit: string;
  /**
   * 标记ID
   */
  markID: string;
  /**
   * 备注
   */
  remark: string;
  /**
   * 排序号
   */
  sort: number;
}
/**
 * 年度计划项目
 */
export interface planProject {
  /**
   * 主键ID
   */
  mainID: string;
  /**
   * 详情ID
   */
  detailID: string;
  /**
   * 主目标ID
   */
  mainGoalID: string;
  /**
   * 分组ID
   */
  groupID: string;
  /**
   * 项目内容
   */
  content: string;
  /**
   * 标记ID
   */
  markID: string;
  /**
   * 排序号
   */
  sort: number;
}
