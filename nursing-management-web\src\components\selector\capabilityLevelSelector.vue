<!--
 * FilePath     : \src\components\selector\capabilityLevelSelector.vue
 * Author       : 孟昭永
 * Date         : 2023-08-13 11:48
 * LastEditors  : 马超
 * LastEditTime : 2024-07-04 08:57
 * Description  : 能力层级选择器
 * CodeIterationRecord:
-->
<template>
  <div class="capability-level-selector">
    <span v-if="label">{{ label }}：</span>
    <el-select
      class="selector-component"
      v-model="capabilityIDs"
      :multiple="multiple"
      :clearable="clearable"
      :filterable="filterable"
      :disabled="disabled"
      :placeholder="`请选择${label}`"
      collapse-tags
      collapse-tags-tooltip
      @change="change"
    >
      <el-option v-for="(item, index) in capabilityLevelOptions" :key="index" :label="item.label" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { useExposeSelectorEvent } from "./hooks/useExposeSelectorEvent";
const { exposeChange, exposeSelect } = useExposeSelectorEvent();
const props = defineProps({
  label: {
    type: String,
    default: "层级"
  },
  modelValue: {
    type: [Number, Array<number>, String],
    default: undefined
  },
  type: {
    type: String,
    default: undefined
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  width: {
    type: Number,
    default: 180
  }
});

// 计算自适应宽度
const convertPX: any = inject("convertPX");
const { width } = toRefs(props);
const selectorWidth = computed(() => `${convertPX(width.value)}px`);

// 双向绑定
const emits = defineEmits(["update:modelValue", "change", "select"]);
let capabilityIDs = useVModel(props, "modelValue", emits);

let capabilityLevelOptions = ref<Array<Record<any, any>>>([]);
// 通过hooks从数据库获取数据
let { getCapabilityLevelData } = useDictionaryData();
getCapabilityLevelData(props.type, Math.random()).then((datas) => {
  capabilityLevelOptions.value = datas;
});
/**
 * 选择数据异动时暴漏事件
 * @param value 异动数据
 */
const change = (value: string | Array<string>) => {
  exposeChange(value, emits);
  exposeSelect(value, capabilityLevelOptions.value, "value", props.multiple, emits);
};
</script>

<style lang="scss">
.capability-level-selector {
  display: inline-block;
  margin-right: 14px;
  /* 使用scss的mixin方法，使用v-bind绑定ts变量 */
  @include selector-component-style(v-bind(selectorWidth));
}
</style>
