<!--
 * FilePath     : /src/views/examineManagement/examinationRecord.vue
 * Author       : 来江禹
 * Date         : 2024-08-14 09:08
 * LastEditors  : 张现忠
 * LastEditTime : 2025-07-12 10:27
 * Description  : 考核主记录维护页面
 * CodeIterationRecord:
 -->
<template>
  <base-layout :drawerOptions="drawerOptions" class="examination-record">
    <template #header>
      <examination-type-radio v-model="examinationType" :type="routeType"></examination-type-radio>
      日期：
      <el-date-picker
        v-model="filterDate"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        class="header-date"
        @change="getExaminationRecordList"
      />
      <el-button class="add-button" v-permission:B="1" @click="addOrEditRecord()">新增</el-button>
    </template>
    <el-table :data="examinationRecordList" border stripe height="100%">
      <el-table-column :min-width="convertPX(200)" label="考核名称" prop="examinationName"> </el-table-column>
      <template v-if="examinationType !== paperType.exerciseType">
        <el-table-column :width="convertPX(120)" label="考核级别" prop="examinationLevelName"> </el-table-column>
        <el-table-column :width="convertPX(120)" label="部门" prop="departmentName" align="center"> </el-table-column>
        <el-table-column :min-width="convertPX(140)" :label="examineEmployeeLabel" prop="examineEmployeeName"> </el-table-column>
        <el-table-column :width="convertPX(150)" label="开始时间" prop="startDateTime" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.startDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
        <el-table-column :width="convertPX(150)" label="结束时间" prop="endDateTime" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.endDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
        <el-table-column v-if="examinationType === theoreticalType" :width="convertPX(160)" label="考核地点" prop="location" align="center">
        </el-table-column>

        <el-table-column
          :width="convertPX(110)"
          :label="examinationType === theoreticalType ? '考核时长' : '操作时长'"
          prop="durationName"
          align="center"
        >
        </el-table-column>
        <el-table-column
          v-if="examinationType === theoreticalType"
          :width="convertPX(90)"
          label="总分"
          prop="totalScore"
          align="center"
        ></el-table-column>
        <el-table-column
          :width="convertPX(110)"
          :label="examinationType === theoreticalType ? '及格分数' : '达标分数'"
          prop="passingScore"
          align="center"
        ></el-table-column>
        <template v-if="examinationType === theoreticalType">
          <el-table-column :width="convertPX(80)" label="一页一题" align="center">
            <template #default="scope">
              <span v-if="scope.row.onePageQuestionFlag">√</span>
            </template>
          </el-table-column>
          <el-table-column :width="convertPX(80)" label="扫码签到" align="center">
            <template #default="scope">
              <span v-if="scope.row.signInFlag">√</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column :min-width="convertPX(150)" label="说明" prop="instructions"></el-table-column>
      </template>
      <el-table-column v-else :width="convertPX(200)" label="练习进度" prop="practiceProgress" align="center"> </el-table-column>
      <el-table-column :width="convertPX(120)" label="新增人" prop="addEmployeeName" align="center"></el-table-column>
      <el-table-column :width="140" label="新增时间" prop="addDateTime" align="center">
        <template #default="scope">
          <span v-formatTime="{ value: scope.row.addDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
        </template>
      </el-table-column>
      <template v-if="examinationType !== paperType.exerciseType">
        <el-table-column :width="convertPX(120)" label="修改人" prop="modifyEmployeeName" align="center"></el-table-column>
        <el-table-column :width="convertPX(140)" label="修改时间" prop="modifyDateTime" align="center">
          <template #default="scope">
            <span v-formatTime="{ value: scope.row.modifyDateTime, type: 'datetime', format: 'yyyy-MM-dd hh:mm' }"></span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" :width="convertPX(200)" align="center" fixed="right">
        <template #default="scope">
          <el-tooltip content="编辑">
            <i
              class="iconfont icon-edit"
              v-permission:B="3"
              v-visibilityHidden="!scope.row.publishFlag"
              @click="addOrEditRecord(scope.row)"
            ></i>
          </el-tooltip>
          <template v-if="examinationType !== paperType.exerciseType">
            <el-tooltip content="取消发布" v-if="scope.row.publishFlag">
              <i class="iconfont icon-stop" v-permission:B="3" @click="stopPublishRecord(scope.row)"></i>
            </el-tooltip>
            <el-tooltip content="发布" v-else>
              <i class="iconfont icon-publish" v-permission:B="3" @click="publishRecord(scope.row)"></i>
            </el-tooltip>
          </template>
          <el-tooltip content="签到码">
            <i
              v-if="examinationType === theoreticalType"
              class="iconfont icon-sign-in-code"
              v-permission:B="3"
              v-visibilityHidden="
                scope.row.publishFlag && scope.row.signInFlag && (scope.row.conditions.length > 0 || scope.row.type == '3')
              "
              @click="generateQRCode(scope.row)"
            ></i>
          </el-tooltip>
          <el-tooltip content="查看人员考核数据">
            <i
              class="iconfont icon-user-data-record"
              v-visibilityHidden="scope.row.publishFlag"
              @click="goMainPage(scope.row.examinationRecordID)"
            ></i>
          </el-tooltip>
          <el-tooltip content="批量设置补考">
            <i
              class="iconfont icon-supplement"
              v-visibilityHidden="scope.row.publishFlag"
              @click="setRetakeExamine(scope.row.examinationRecordID)"
            ></i>
          </el-tooltip>
          <el-tooltip content="删除">
            <i
              class="iconfont icon-delete"
              v-permission:B="4"
              v-visibilityHidden="!scope.row.publishFlag"
              @click="deleteData(scope.row.examinationRecordID)"
            ></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog class="examination-code-dialog" v-model="dialogFlag" destroy-on-close draggable title="签到码">
      <qr-code type="examination" :url="jumpPageUrl" :params="qrCodeParams" />
    </el-dialog>
    <template #drawerContent>
      <examination-record-form
        v-if="drawerOptions.drawerName == 'AddAndModify'"
        ref="submitRefs"
        v-model="drawerData"
        :routeType="routeType"
        :examinationType="examinationType"
        :questionBanks="questionBanks"
      />
      <examination-main
        v-if="drawerOptions.drawerName == 'examinationMain'"
        :examineRecordID="chooseExaminationRecordID"
        :examinationType="examinationType"
      ></examination-main>
      <el-form
        v-if="drawerOptions.drawerName == 'retakeExamine'"
        :model="retakeExamine"
        ref="retakeExamineFormRef"
        label-width="80"
        :rules="rules"
      >
        <el-form-item label="开始时间：" prop="startDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.startDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择开始时间"
            :disabled-date="(date: Date) => disabledDate(date, 'start')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间：" prop="endDateTime">
          <el-date-picker
            class="drawer-select"
            v-model="retakeExamine.endDateTime"
            type="datetime"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            placeholder="选择结束时间"
            :disabled-date="(date: Date) => disabledDate(date, 'end')"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="补考人员：" prop="employeeIDs">
          <employee-selector
            label=""
            v-model="retakeExamine.employeeIDs"
            :width="320"
            :filterable="true"
            :multiple="true"
            :multiCollapse="false"
            :clear="false"
          />
        </el-form-item>
      </el-form>
    </template>
    <template
      #drawerButtonAfter
      v-if="drawerOptions.drawerName == 'AddAndModify' && [theoreticalType, practicalType].includes(examinationType)"
    >
      <el-button class="edit-button" v-permission:B="2" @click="saveRecord(true)">发布</el-button>
    </template>
  </base-layout>
</template>
<script setup lang="ts">
//#region 引入
const convertPX: any = inject("convertPX");
import examinationMain from "./examinationMain.vue";
import examinationTypeRadio from "./components/examinationTypeRadio.vue";
// #endregion

//#region 变量定义
const theoreticalType = PaperType.theoreticalType.toString();
const practicalType = PaperType.practicalType.toString();
const filterDate = ref<string[]>([datetimeUtil.getMonthFirstDay(), datetimeUtil.getMonthLastDay()]);
const examinationType = ref<string>("");
const examinationRecordList = ref<Record<string, any>[]>([]);
const drawerData = ref<Record<string, any>>({});
const submitRefs = ref<any>();
const chooseExaminationRecordID = ref<string>();
const dialogFlag = ref<boolean>(false);
const jumpPageUrl = ref<string>("");
const qrCodeParams = ref<Record<string, any>>({});
const questionBanks = ref<Record<string, any>[]>([]);
const drawerOptions = ref<DrawerOptions>({
  drawerTitle: "",
  showDrawer: false,
  drawerSize: "40%",
  showConfirm: true
});
// 监考人标签
const examineEmployeeLabel = computed(() =>
  examinationType.value === theoreticalType ? "监考人" : examinationType.value === practicalType ? "可监考人" : "考核人"
);
const paperType = computed(() => PaperType);
// #endregion
watch(
  () => examinationType.value,
  async () => {
    await getExaminationRecordList();
    if (examinationType.value === PaperType.exerciseType.toString()) {
      await examineService.getQuestionBankSelectList({ isPractical: false }).then((res: any) => {
        questionBanks.value = res;
      });
    }
  }
);

// #region  解决 路由带参数时，切换路由不刷新问题
const route = useRoute();
let routeType: string = route.params?.type as any;
const refresh = inject("refresh") as Function;
watch(
  () => route.params,
  () => {
    if (route.params?.type !== routeType) {
      routeType = route.params?.type as any;
      refresh();
    }
  },
  { immediate: true }
);
//#endregion

// #region 初始化
onMounted(async () => {
  await getExaminationRecordList();
});
// #endregion
//#region 业务逻辑

/**
 * @description: 新增/编辑考核主记录
 * @param row
 */
const addOrEditRecord = (row?: Record<string, any>) => {
  drawerOptions.value.drawerSize = "40%";
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.drawerName = "AddAndModify";
  drawerOptions.value.drawerTitle = row ? "编辑考核计划" : "新增考核计划";
  drawerOptions.value.cancel = () => {
    drawerData.value = {};
    drawerOptions.value.showDrawer = false;
  };
  drawerOptions.value.confirm = async () => await saveRecord();
  drawerOptions.value.showDrawer = true;
  if (row) {
    let cloneRow = common.clone(row);
    // 理论试卷监考人只能选择一个（获取列表的时候，返回的是一个集合）
    if ([theoreticalType, PaperType.simulationType.toString()].includes(examinationType.value)) {
      cloneRow.examineEmployeeID = row.examineEmployeeID.length ? row.examineEmployeeID[0] : "";
    }
    drawerData.value = cloneRow;
  }
};

/**
 * @description: 保存考核记录
 * @param isPublish 是否发布
 * @returns
 */
const saveRecord = async (isPublish: boolean = false) => {
  let successFlag = await submitRefs.value.saveRecord(isPublish);
  if (successFlag) {
    drawerData.value = {};
    drawerOptions.value.showDrawer = false;
    await getExaminationRecordList();
  }
};
/**
 * @description: 删除数据
 * @param recordID
 */
const deleteData = (recordID: string) => {
  if (!recordID) {
    return;
  }
  deleteConfirm("确定要删除么？", async (flag: Boolean) => {
    if (!flag) {
      return;
    }
    let param = {
      recordID: recordID
    };
    examineService.deleteExaminationRecordData(param).then((res: any) => {
      if (res) {
        showMessage("success", "删除成功！");
        getExaminationRecordList();
      }
    });
  });
};
/**
 * @description: 获取考核主记录数据
 */
const getExaminationRecordList = async () => {
  examinationRecordList.value = [];
  if (!examinationType.value) {
    return;
  }
  let params = {
    startDate: filterDate.value[0],
    endDate: filterDate.value[1],
    type: examinationType.value
  };
  const getExaminationRecordListDebounced = useDebounceFn(async () => {
    await examineService.getExaminationRecordList(params).then((res: any) => {
      if (res) {
        examinationRecordList.value = res;
      }
    });
  }, 300);
  await getExaminationRecordListDebounced();
};
// #region 发布\停止考核后端交互逻辑
/**
 * @description: 发布考核
 */
const publishRecord = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "获取考核信息失败！");
    return;
  }
  if (examinationType.value !== "3" && (!row.conditions?.length || !row.conditionContent || !row.conditionExpression)) {
    showMessage("warning", "请设置考核发布条件！");
    return;
  }
  let params = {
    examinationRecordID: row.examinationRecordID,
    examineEmployeeID: row.examineEmployeeID,
    type: row.type
  };
  examineService.publishExamine(params).then((res) => {
    if (res) {
      showMessage("success", "发布成功！");
      drawerOptions.value.showDrawer = false;
      getExaminationRecordList();
      return;
    }
  });
};
/**
 * @description: 停止发布考核
 */
const stopPublishRecord = (row: Record<string, any>) => {
  if (!row) {
    showMessage("warning", "获取考核信息失败！");
    return;
  }
  let params = {
    examinationRecordID: row.examinationRecordID
  };
  examineService.stopPublishExamine(params).then((res: any) => {
    if (res) {
      showMessage("success", "停止发布成功！");
      drawerOptions.value.showDrawer = false;
      getExaminationRecordList();
      return;
    }
  });
};
// #endregion
/**
 * @description: 弹出抽屉显示其他页面
 * @param examinationRecordID
 * @param showDrawerPage 用于判断抽屉显示的页面
 * @returns
 */
const goMainPage = (examinationRecordID: string) => {
  let toggleDrawerTitle = "考核记录";
  chooseExaminationRecordID.value = examinationRecordID;
  drawerOptions.value.showCancel = false;
  drawerOptions.value.showConfirm = false;
  drawerOptions.value.drawerName = "examinationMain";
  drawerOptions.value.drawerTitle = toggleDrawerTitle;
  drawerOptions.value.drawerSize = "100%";
  drawerOptions.value.showDrawer = true;
};
//#region 补考相关逻辑
const retakeExamine = ref<Record<string, any>>({});
/**
 * @description: 获取时间选择禁用区间
 * @param date 当前选择的日期
 * @param flag {"start" | "end"} 标识是限制开始时间还是结束时间的方法
 * @return
 */
const disabledDate = (time: Date, type: string) => {
  const date = datetimeUtil.formatDate(time, "yyyy-MM-dd");
  if (type === "start") {
    if (!retakeExamine.value.endDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.getNowDate("yyyy-MM-dd") || date > datetimeUtil.formatDate(retakeExamine.value.endDateTime, "yyyy-MM-dd");
  }
  if (type === "end") {
    if (!retakeExamine.value.startDateTime) {
      return date < datetimeUtil.getNowDate("yyyy-MM-dd");
    }
    return date < datetimeUtil.formatDate(retakeExamine.value.startDateTime, "yyyy-MM-dd");
  }
  return false;
};
const rules = {
  startDateTime: [{ required: true, message: "请选择补考开始时间", trigger: "change" }],
  endDateTime: [{ required: true, message: "请选择补考结束时间", trigger: "change" }],
  employeeIDs: [{ required: true, message: "请选择补考人员", trigger: "change" }]
};
/**
 * @description: 打开补考设置
 * @param examinationRecordID
 * @return
 */
const setRetakeExamine = async (examinationRecordID: string) => {
  let employeeIDs: string[] = [];
  // 获取考核计划符合补考的人员
  await examineService.getRetakeEmployeeIDsByRecordID({ examinationRecordID }).then((res: any) => {
    if (res) {
      employeeIDs = res;
    }
  });
  retakeExamine.value = {
    examinationRecordID: examinationRecordID,
    employeeIDs: employeeIDs
  };
  drawerOptions.value.drawerSize = "30%";
  drawerOptions.value.showCancel = true;
  drawerOptions.value.showConfirm = true;
  drawerOptions.value.drawerName = "retakeExamine";
  drawerOptions.value.drawerTitle = "设置补考";
  drawerOptions.value.cancel = () => {
    drawerOptions.value.showDrawer = false;
  };
  drawerOptions.value.confirm = async () => await saveRetakeExamine();
  drawerOptions.value.showDrawer = true;
};
const retakeExamineFormRef = shallowRef();
const { validateRule } = useForm();
/**
 * @description: 保存补考
 * @return
 */
const saveRetakeExamine = async () => {
  // 校验表单
  if (!(await validateRule(retakeExamineFormRef))) {
    return false;
  }
  examineService.saveRetakeExamine(retakeExamine.value).then((res) => {
    if (res) {
      showMessage("success", "设置补考成功！");
      drawerOptions.value.showDrawer = false;
    }
  });
};
// #endregion
/**
 * @description: 组装二维码参数
 * @param row 当前行数据
 * @return
 */
const generateQRCode = async (row: any) => {
  let url = "/pages/examineManagement/examineList";
  jumpPageUrl.value = `${url}?examinationType=${examinationType.value}`;
  qrCodeParams.value = {
    examinationRecordID: row.examinationRecordID,
    qrCodeRefreshTime: row.qrCodeRefreshTime
  };
  dialogFlag.value = true;
};
// #endif
</script>
<style lang="scss">
.examination-record {
  .header-date {
    margin-right: 5px;
    width: 320px;
  }
  .el-table {
    .el-table__row td:not(.is-hidden):last-child {
      border-left: 1px solid #ebeef5;
    }

    .el-table__header th:not(.is-hidden):last-child {
      border-left: 1px solid #ebeef5;
    }
  }
  .drawer-from {
    .drawer-select {
      width: 300px;
    }
    .form-item-hint {
      color: #ff0000;
      font-size: 12px;
      margin-left: 10px;
    }
  }
  .icon-user-data-record {
    color: #14d8d8;
  }
  .examination-code-dialog {
    width: 60%;
  }
}
</style>
