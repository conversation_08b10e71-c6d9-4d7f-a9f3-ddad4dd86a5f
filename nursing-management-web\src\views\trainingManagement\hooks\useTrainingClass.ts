/*
 * FilePath     : \src\views\trainingManagement\hooks\useTrainingClass.ts
 * Author       : 孟昭永
 * Date         : 2024-07-10 16:22
 * LastEditors  : 孟昭永
 * LastEditTime : 2024-07-12 16:13
 * Description  : 培训群组数据和相关操作逻辑钩子
 * CodeIterationRecord:
 */
import { trainingClassService } from "@/api/trainingClassService";
import { onMounted, ref } from "vue";
import type { trainingClass } from "../types/trainingClass";

/**
 * @description 培训群组数据和相关操作逻辑
 */
export function useTrainingClass() {
  const trainingClassList = ref<trainingClass[]>([]);
  const copyTrainingClassList = ref<trainingClass[]>([]);

  /**
   * @description 获取培训群组列表数据
   * @returns Promise
   */
  const getTrainingClassList = async (): Promise<any> => {
    await trainingClassService.getTrainingClassList().then((resp: any) => {
      if (resp) {
        trainingClassList.value = resp;
        copyTrainingClassList.value = resp;
      }
    });
  };

  /**
   * @description 添加培训群组
   * @param trainingClass - 待添加的培训群组
   * @returns
   */
  const saveTrainingClass = async (trainingClass: trainingClass) => {
    let params = { ...trainingClass };
    await trainingClassService.saveTrainingClass(params).then((respBool: any) => {
      if (!respBool) {
        showMessage("error", "保存失败！");
        return false;
      }
    });
    // 保存成功后刷新培训群组列表数据
    await getTrainingClassList();
    return true;
  };

  /**
   * @description 删除培训群组
   * @param trainingClassMainID - 待删除的培训群组的ID
   * @return
   */
  const deleteTrainingClassByID = async (trainingClassMainID: string) => {
    deleteConfirm("确定要删除么？", async (confirmFlag: Boolean) => {
      if (!confirmFlag) {
        return;
      }
      await trainingClassService.deleteTrainingClassByID({ trainingClassMainID: trainingClassMainID }).then((res: any) => {
        if (res) {
          showMessage("success", "删除成功!");
          getTrainingClassList();
          return;
        }
        showMessage("error", "删除失败!");
      });
    });
  };

  // 在组件创建时获取课程列表数据
  onMounted(async () => await getTrainingClassList());

  // 返回从逻辑中获取的响应式数据和方法
  return {
    /**
     * @description 培训群组列表数据
     * @returns Promise<void>
     */
    trainingClassList,
    /**
     * @description 复制的培训群组列表数据
     * @returns Promise<void>
     */
    copyTrainingClassList,

    /**
     * @description 获取培训群组列表数据
     * @returns Promise<void>
     */
    getTrainingClassList,
    /**
     * @description 保存培训群组
     * @param trainingClass - 待保存的培训群组
     * @returns
     */
    saveTrainingClass,
    /**
     * @description 删除培训群组
     * @param trainingClassMainID - 待删除的删除培训群组的ID
     * @return
     */
    deleteTrainingClassByID
  };
}
