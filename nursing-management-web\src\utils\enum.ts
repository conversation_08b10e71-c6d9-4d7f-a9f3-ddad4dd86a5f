/*
 * FilePath     : \src\utils\enum.ts
 * Author       : 苏军志
 * Date         : 2023-09-08 16:23
 * LastEditors  : 张现忠
 * LastEditTime : 2025-03-06 15:36
 * Description  :
 * CodeIterationRecord:
 */

/**
 * LoadingText
 */
export enum Loading {
  /**
   * 登录中……
   */
  LOGIN = "登录中……",
  /**
   * 正在退出系统……
   */
  EXIT = "正在退出系统……",
  /**
   * 保存中……
   */
  SAVE = "保存中……",
  /**
   * 删除中……
   */
  DELETE = "删除中……",
  /**
   * 加载中……
   */
  LOAD = "加载中……"
}

/**
 * 消息类型
 */
export enum MessageType {
  success,
  warning,
  error,
  info
}
export enum FileType {
  /**
   * 表格数据导入
   */
  importExcel,
  /**
   * 页面数据生成Excel文件
   */
  exportExcel,
  /**
   * 页面生成Word
   */
  exportWord,
  /**
   * 页面生成Pdf
   */
  exportPdf
}

/**
 * MQ消息类型
 */
export enum MQMessageType {
  /**
   * 通知 用于系统级通知的被动提醒
   */
  Notification = "Notification",
  /**
   * 提示 用于页面中展示重要的提示信息
   */
  Alert = "Alert",
  /**
   * 确认框 弹出一个简单的确认框，会打断用户操作
   */
  Confirm = "Confirm",
  /**
   * 消息 主动操作后的反馈提示
   */
  Message = "Message"
}
/**
 * 文件分类
 */
export enum FileClass {
  /**
   * 公共文件
   */
  CommonFile = "1",
  /**
   * 消息附件
   */
  MessageAttachment = "2",
  /**
   * 年度计划文件
   */
  AnnualPlanFile = "3"
}

export enum ApprovalStatus {
  /*
待审批。
*/
  Waiting = 0,
  /*
表示审批正在进行中。
*/
  InProgress = 1,
  /*
表示审批已通过。
*/
  Completed = 2,
  /*
表示审批已驳回。
*/
  Rejected = 3,
  /*
撤销
*/
  Revoke = 4
}
/**
 * 试卷类型
 */
export enum PaperType {
  /**
   * 理论试卷类型
   */
  theoreticalType = "1",
  /**
   * 实操试卷类型
   */
  practicalType = "2",
  /**
   * 模拟考核试卷类型
   */
  simulationType = "3",
  /**
   * 练习题类型
   */
  exerciseType = "4"
}
