/*
 * relative     : \src\views\qcManagement\hierarchicalQC\setting\index.ts
 * Author       : 郭鹏超
 * Date         : 2024-11-01 09:55
 * LastEditors  : 苏军志
 * LastEditTime : 2025-04-27 15:44
 * Description  :质控页面差异配置
 * CodeIterationRecord:
 */
import hierarchicalQCResult from "./hierarchicalQCResult";
import hierarchicalQCSubject from "./hierarchicalQCSubject";
import hierarchicalQcTrack from "./hierarchicalQcTrack";
import normalWorkingProcessControl from "./normalWorkingProcessControl";
class qcOptions {
  pageOptions: Record<string, any> = {
    hierarchicalQCSubject,
    hierarchicalQCResult,
    hierarchicalQcTrack,
    normalWorkingProcessControl
  } as Record<string, any>;
  pageOption: hierarchicalQCResult | hierarchicalQCSubject | normalWorkingProcessControl;
  qcType: "nodeQCFormType" | "normalWorkingFormType" | "visitsFormType" | "specialFormType" | undefined;
  departmentToFormTypeArr: Record<string, string | number>[] = [];
  qcTypeToFormType: Record<string, string[]> = {
    nodeQCFormType: ["1", "2", "3", "4", "5"],
    normalWorkingFormType: ["6"],
    visitsFormType: ["7"],
    specialFormType: ["8"]
  };
  constructor(
    typeCode: "hierarchicalQCSubject" | "hierarchicalQCResult" | "hierarchicalQcTrack" | "normalWorkingProcessControl",
    props: Record<string, any>
  ) {
    this.pageOption = new this.pageOptions[typeCode](props);
    this.qcType = this.pageOption.qcType ?? "nodeQCFormType";
    this.getDepartmentToQCFormType();
  }
  /**
   * @description: 获取部门与主题类型关系
   */
  getDepartmentToQCFormType() {
    hierarchicalQCService.getDeparmentToQCFormType().then((res: any) => {
      this.departmentToFormTypeArr = res;
    });
  }
  /**
   * @description: 依据配置组织与主题类别进行关联
   * @param searchView
   * @return
   */
  changeFormType(searchView: Record<string, any>) {
    if (!searchView.departmentID) {
      searchView.formType = undefined;
      searchView.qcType = this.qcType;
      return;
    }
    const sucDepartmentToFormType = this.departmentToFormTypeArr.find(
      (item: Record<string, string | number>) => (item.departmentID as number) === searchView.departmentID
    )?.formType;
    if (!sucDepartmentToFormType) {
      searchView.qcType = this.qcType;
      searchView.formType = this.pageOption?.formType;
      return;
    }
    const sucQcTypeToFormType = Object.entries(this.qcTypeToFormType).find((item: any) =>
      item[1].includes(sucDepartmentToFormType as string)
    );
    if (!sucQcTypeToFormType) {
      searchView.qcType = this.qcType;
      searchView.formType = this.pageOption?.formType;
      return;
    }
    searchView.qcType = sucQcTypeToFormType[0];
  }
}
export default qcOptions;
