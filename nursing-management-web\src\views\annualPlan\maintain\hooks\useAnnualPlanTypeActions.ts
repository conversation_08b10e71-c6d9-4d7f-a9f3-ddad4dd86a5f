/*
 * FilePath     : \src\views\annualPlan\maintain\hooks\useAnnualPlanTypeActions.ts
 * Author       : 杨欣欣
 * Date         : 2025-04-27 14:33
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-08 10:41
 * Description  : 年度计划分类状态与行为
 * CodeIterationRecord:
 */
import { usePlanManagementStore } from "../../hooks/usePlanManagementStore";
import { useAnnualPlanMaintainStore } from "./useAnnualPlanMaintainStore";

export default function useAnnualPlanTypeActions(
  resetGoalsSort: () => void,
  convertToObject: <S extends { sort: number }>(items: { [key: string]: S[] } | S[], itemKey: keyof S) => Record<string, number>
) {
  const planManagementStore = usePlanManagementStore();
  const maintainStore = useAnnualPlanMaintainStore();

  return {
    /**
     * @description: 重新计算计划策略目标的Sort
     * @param newIndex 分类的新下标
     * @param oldIndex 分类的旧下标
     * @return
     */
    resetPlanTypesSort: async (newIndex: number, oldIndex: number) => {
      if (newIndex === oldIndex) {
        return;
      }
      // 互换位置
      const planType = maintainStore.planTypes.splice(oldIndex, 1)[0];
      maintainStore.planTypes.splice(newIndex, 0, planType);

      resetGoalsSort();
      // 发起请求，将新序号持久化
      const params = {
        mainID: planManagementStore.annualPlanMainID,
        planGoalIDAndSort: convertToObject(maintainStore.goalsByTypeID, "mainGoalID"),
        planGroupIDAndSort: convertToObject(maintainStore.planGroups, "groupID"),
        planIndicatorIDAndSort: convertToObject(maintainStore.indicatorsByGroupID, "detailID"),
        planProjectIDAndSort: convertToObject(maintainStore.projectsByGroupID, "detailID")
      };
      await annualPlanMainService.resetAnnualPlanTypesSort(params);
    }
  };
}
