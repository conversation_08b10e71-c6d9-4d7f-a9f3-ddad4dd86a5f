<!--
 * FilePath     : \src\components\transfer\transfer-panel.vue
 * Author       : 杨欣欣
 * Date         : 2024-03-28 09:08
 * LastEditors  : 杨欣欣
 * LastEditTime : 2025-05-06 15:52
 * Description  : 穿梭框面板组件
 * CodeIterationRecord:
 -->
<template>
  <div class="transfer-panel">
    <div class="header">
      <span class="check" v-if="dragOption.multiple">
        <el-checkbox v-model="allChecked" :indeterminate="isIndeterminate" @change="handleAllCheckedChange">
          {{ title }}
        </el-checkbox>
      </span>
      <span class="header-item">
        <slot name="header" />
      </span>
      <span class="statistics" v-if="dragOption.multiple">
        {{ panelState.checkedCount + "/" + filterData.length }}
      </span>
    </div>
    <div class="filter" v-if="filterable">
      <el-input class="query-input" v-model="panelState.query" clearable>
        <template #prefix>
          <el-icon class="iconfont icon-search" />
        </template>
      </el-input>
    </div>
    <div class="body">
      <drag
        ref="dragRef"
        v-model="filterData"
        :multiFlag="draggableOption.multiple"
        :draggableOption="{ itemKey: propsAlias.key, put: draggableOption.put, pull: draggableOption.pull }"
        :sortable="draggableOption.sortable"
        @added="emitEvent('added', $event)"
        @remove="emitEvent('remove', $event)"
        @select="selectOne"
        @deselect="deselectOne"
        height="100%"
      >
        <template #content="{ element }">
          <tag :closeable="draggableOption.closeable" @remove="removeElement(element)">
            {{ element[propsAlias.label] }}
          </tag>
        </template>
      </drag>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useCheck } from "./hooks/useCheck";
import { usePropsAlias } from "./hooks/usePropsAlias";

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  props: {
    type: Object,
    required: true
  },
  filterable: {
    type: Boolean,
    default: false
  },
  filterMethod: {
    type: Function,
    default: undefined
  },
  dragOption: {
    type: Object,
    default: () => ({
      closeable: false,
      put: true,
      pull: true,
      multiple: false,
      sortable: false
    })
  }
});

const modelValue = defineModel<Record<string, any>[]>("data",{
  required: true
});

const emits = defineEmits(["added", "remove"]);
const propsAlias = usePropsAlias(props);
const dragRef = ref();
const panelState = reactive({
  checkedCount: 0,
  query: ""
});
const draggableOption = {
  closeable: false,
  put: true,
  pull: true,
  multiple: false,
  sortable: false,
  ...props.dragOption
};

const { filterData, allChecked, isIndeterminate } = useCheck(props, panelState, modelValue);
const selectOne = () => {
  panelState.checkedCount++;
};
const deselectOne = () => {
  panelState.checkedCount--;
};
/**
 * @description: 事件发射函数
 * @param eventName 事件名称
 * @param evt 回调返回值
 * @return
 */
const emitEvent = (eventName: "added" | "remove", evt: Record<string, any> | Record<string, any>[]) => {
  if (Array.isArray(evt)) {
    const elements = (evt as any).reduce((pre: any, cur: any) => {
      pre.push(cur.element);
      if (eventName === "remove") {
        deselectOne();
      }
      return pre;
    }, []);

    emits(eventName, elements);
    return;
  }
  const { element } = evt;
  emits(eventName, element);
};
/**
 * @description: 点击按钮移除负责人
 * @param element 被移除的负责人
 * @return
 */
const removeElement = (element: Record<string, any>) => {
  emits("remove", element);
};
/**
 * @description: 全选复选框改变回调
 * @param value 复选框状态
 * @return
 */
const handleAllCheckedChange = (value: boolean) => {
  dragRef.value && dragRef.value.toggleSelection(value ? "select" : "deselect", dragRef.value.$el);
};
</script>
<style scoped lang="scss">
.transfer-panel {
  border: 1px solid #ebeef5;
  overflow: hidden;
  width: 48%;
  height: 100%;
  box-sizing: border-box;
  .header {
    display: flex;
    align-items: center;
    height: 40px;
    background: #f5f7fa;
    margin: 0;
    padding: 0px 15px;
    border: 1px solid #ebeef5;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    box-sizing: border-box;
    color: #000;
    .statistics {
      flex-grow: 1;
      text-align: right;
    }
  }
  .body {
    height: 100%;
  }
  .filter {
    padding: 15px;
  }
}
</style>
